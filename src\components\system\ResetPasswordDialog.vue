<template>
  <el-dialog
    v-model="visible"
    title="重置密码"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="user-info">
      <el-alert
        :title="`为用户「${userData?.realName}」重置密码`"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="reset-form"
      @submit.prevent
    >
      <el-form-item label="新密码"
prop="newPassword">
        <el-input
          v-model="formData.newPassword"
          type="password"
          placeholder="请输入新密码"
          show-password
          clearable
        />
      </el-form-item>

      <el-form-item label="确认密码"
prop="confirmPassword">
        <el-input
          v-model="formData.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          show-password
          clearable
        />
      </el-form-item>

      <div class="password-tips">
        <h4>密码要求：</h4>
        <ul>
          <li>长度至少6位，最多20位</li>
          <li>必须包含字母和数字</li>
          <li>建议包含特殊字符以提高安全性</li>
        </ul>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitting ? '重置中...' : '确定重置' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import type { FormInstance, FormRules } from 'element-plus'
  import type { UserInfo } from '@/types/user'
  import { useUserManagement } from '@/composables/useUserManagement'

  interface Props {
    modelValue: boolean
    userData?: UserInfo | null
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'success'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    userData: null
  })

  const emit = defineEmits<Emits>()

  const { resetUserPassword } = useUserManagement()

  // 对话框显示状态
  const visible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  // 表单引用
  const formRef = ref<FormInstance>()
  const submitting = ref(false)

  // 表单数据
  const formData = reactive({
    newPassword: '',
    confirmPassword: ''
  })

  // 自定义验证器
  const validateConfirmPassword = (rule: any, value: string, callback: Function) => {
    if (!value) {
      callback(new Error('请再次输入新密码'))
    } else if (value !== formData.newPassword) {
      callback(new Error('两次输入的密码不一致'))
    } else {
      callback()
    }
  }

  // 表单验证规则
  const formRules: FormRules = {
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度在6-20个字符', trigger: 'blur' },
      {
        pattern: /^(?=.*[a-zA-Z])(?=.*\d).+$/,
        message: '密码必须包含字母和数字',
        trigger: 'blur'
      }
    ],
    confirmPassword: [
      { required: true, message: '请再次输入新密码', trigger: 'blur' },
      { validator: validateConfirmPassword, trigger: 'blur' }
    ]
  }

  /**
   * 生成随机密码
   */
  const generateRandomPassword = (): string => {
    const letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    const symbols = '!@#$%^&*'

    let password = ''

    // 至少包含一个字母
    password += letters[Math.floor(Math.random() * letters.length)]

    // 至少包含一个数字
    password += numbers[Math.floor(Math.random() * numbers.length)]

    // 填充剩余位数
    const allChars = letters + numbers + symbols
    for (let i = 2; i < 8; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)]
    }

    // 打乱顺序
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('')
  }

  /**
   * 使用随机密码
   */
  const useRandomPassword = (): void => {
    const randomPassword = generateRandomPassword()
    formData.newPassword = randomPassword
    formData.confirmPassword = randomPassword
  }

  /**
   * 处理提交
   */
  const handleSubmit = async (): Promise<void> => {
    if (!formRef.value || !props.userData) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      submitting.value = true

      const success = await resetUserPassword(props.userData.id, formData.newPassword)

      if (success) {
        ElMessage.success('密码重置成功，请通知用户使用新密码登录')
        emit('success')
        handleClose()
      }
    } catch (error) {
      console.error('Reset password error:', error)
    } finally {
      submitting.value = false
    }
  }

  /**
   * 处理关闭
   */
  const handleClose = (): void => {
    visible.value = false

    // 延迟重置表单，避免动画闪烁
    setTimeout(() => {
      formRef.value?.resetFields()
      formData.newPassword = ''
      formData.confirmPassword = ''
    }, 300)
  }

  // 监听对话框打开
  watch(
    () => props.modelValue,
    newValue => {
      if (newValue) {
        // 对话框打开时重置表单
        formData.newPassword = ''
        formData.confirmPassword = ''
      }
    }
  )
</script>

<style lang="scss" scoped>
  .user-info {
    margin-bottom: 24px;
  }

  .reset-form {
    margin: 24px 0;
  }

  .password-tips {
    margin-top: 16px;
    padding: 16px;
    background: var(--color-bg-light);
    border-radius: 6px;
    border-left: 3px solid var(--color-warning);

    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: var(--color-text-primary);
    }

    ul {
      margin: 0;
      padding-left: 16px;

      li {
        font-size: 12px;
        color: var(--color-text-regular);
        line-height: 1.5;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .generate-actions {
    margin: 16px 0;
    text-align: center;

    .el-button {
      margin: 0 8px;
    }
  }

  :deep(.el-alert) {
    border-radius: 6px;
  }

  :deep(.el-dialog) {
    border-radius: 8px;
  }

  :deep(.el-dialog__header) {
    background: var(--color-bg-light);
    border-bottom: 1px solid var(--color-border-lighter);
    border-radius: 8px 8px 0 0;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--color-text-primary);
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 10vh auto;
    }

    :deep(.el-dialog__body) {
      padding: 16px;
    }
  }
</style>
