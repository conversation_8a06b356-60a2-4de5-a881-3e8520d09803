/**
 * IC封测CIM系统 - Pinia Store 统一导出
 * Centralized Store Exports for IC Packaging & Testing CIM System
 */

// 导出所有Store
export { useAuthStore } from './auth'
export { useConfigStore } from './config'
export { useAppStore } from './app'
export { useRealtimeStore } from './realtime'

// 导出现有业务Store
export { useCustomerStore } from './customer'
export { useOrderStore } from './order'
export { useEquipmentStore } from './equipment'

// 导出类型定义
export type { UserInfo, LoginRequest, LoginResponse, PermissionConfig, RoleConfig } from './auth'

export type { SystemConfig, UserPreference, SystemStatus } from './config'

export type {
  LoadingState,
  BreadcrumbItem,
  TabItem,
  NotificationItem,
  SystemEvent,
  QuickAction,
  AppState
} from './app'

export type {
  RealtimeEquipmentStatus,
  RealtimeProductionData,
  RealtimeQualityData,
  RealtimeEnvironmentData,
  RealtimeInventoryData,
  RealtimeAlarmData,
  WebSocketMessage,
  DataSubscription,
  ConnectionStatus
} from './realtime'

/**
 * Store初始化函数
 */
export function initializeStores() {
  // 可以在这里进行Store的初始化工作
  console.log('Pinia stores initialized')
}

/**
 * 导出默认对象
 */
export default {
  useAuthStore,
  useConfigStore,
  useAppStore,
  useRealtimeStore,
  useCustomerStore,
  useOrderStore,
  useEquipmentStore,
  initializeStores
}
