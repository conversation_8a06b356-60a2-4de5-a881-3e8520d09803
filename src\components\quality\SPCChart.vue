<template>
  <div class="spc-chart">
    <div class="spc-chart__header">
      <div class="spc-chart__title">
        <h3>{{ title }}</h3>
        <div class="spc-chart__info">
          <span class="parameter">参数: {{ parameter }}</span>
          <span class="process">工序: {{ processName }}</span>
          <span class="last-update">更新: {{ formatTime(lastUpdate) }}</span>
        </div>
      </div>
      <div class="spc-chart__controls">
        <el-select v-model="chartType" @change="updateChartType" size="small">
          <el-option label="X̄-R图" value="xbar-r" />
          <el-option label="X̄-S图" value="xbar-s" />
          <el-option label="I-MR图" value="i-mr" />
          <el-option label="p图" value="p" />
          <el-option label="np图" value="np" />
          <el-option label="c图" value="c" />
          <el-option label="u图" value="u" />
        </el-select>
        <el-button
size="small" @click="refreshData"
:loading="loading"
>
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button
size="small" @click="exportChart"
>
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <div class="spc-chart__statistics">
      <div class="stat-card">
        <div class="stat-label">Cpk</div>
        <div class="stat-value" :class="getCpkClass(statistics.cpk)">
          {{ statistics.cpk.toFixed(3) }}
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-label">Ppk</div>
        <div class="stat-value" :class="getPpkClass(statistics.ppk)">
          {{ statistics.ppk.toFixed(3) }}
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-label">良率</div>
        <div class="stat-value" :class="getYieldClass(statistics.yield)">
          {{ statistics.yield.toFixed(2) }}%
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-label">σ水平</div>
        <div class="stat-value">
{{ calculateSigmaLevel().toFixed(1) }}σ
</div>
      </div>
      <div class="stat-card">
        <div class="stat-label">DPMO</div>
        <div class="stat-value">
          {{ calculateDPMO().toFixed(0) }}
        </div>
      </div>
    </div>

    <div
v-if="violationRules.length > 0" class="spc-chart__violations"
>
      <el-alert
        v-for="violation in activeViolations"
        :key="violation"
        :title="violation"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <div class="spc-chart__container">
      <div
ref="chartContainer" class="chart-container"
/>
    </div>

    <div
v-if="showDataTable" class="spc-chart__data-table"
>
      <el-table :data="recentPoints" size="small" height="200">
        <el-table-column prop="sampleNumber" label="样本号" width="80" />
        <el-table-column prop="mean" label="均值" width="100">
          <template #default="{ row }">
            {{ row.mean.toFixed(4) }}
          </template>
        </el-table-column>
        <el-table-column prop="range" label="极差" width="100">
          <template #default="{ row }">
            {{ row.range.toFixed(4) }}
          </template>
        </el-table-column>
        <el-table-column prop="result" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
:type="getResultTagType(row.result)" size="small"
>
              {{ getResultText(row.result) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="timestamp" label="时间">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import { Refresh, Download } from '@element-plus/icons-vue'
  import * as echarts from 'echarts'
  import type { ECharts } from 'echarts'
  import type { SPCData, SPCPoint, ControlLimits, SPCStatistics } from '@/types/quality'
  import { SPCCalculator } from '@/utils/spcCalculator'

  interface Props {
    spcData: SPCData
    showDataTable?: boolean
    autoRefresh?: boolean
    refreshInterval?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    showDataTable: false,
    autoRefresh: true,
    refreshInterval: 30000 // 30秒
  })

  const emit = defineEmits<{
    refresh: []
    export: [chartType: string]
    violationDetected: [violations: string[]]
  }>()

  const chartContainer = ref<HTMLDivElement>()
  const chartType = ref<string>('xbar-r')
  const loading = ref(false)
  let chart: ECharts | null = null
  let refreshTimer: number | null = null

  // 计算属性
  const title = computed(() => props.spcData.processName)
  const parameter = computed(() => props.spcData.parameter)
  const processName = computed(() => props.spcData.processName)
  const lastUpdate = computed(() => props.spcData.lastUpdate)
  const statistics = computed(() => props.spcData.statistics)
  const violationRules = computed(() => props.spcData.violationRules)
  const recentPoints = computed(() => props.spcData.sampleData.slice(-20)) // 显示最近20个点

  const activeViolations = computed(() => {
    return props.spcData.sampleData
      .flatMap(point => point.violatedRules)
      .filter((rule, index, self) => self.indexOf(rule) === index)
  })

  // 方法
  const getCpkClass = (cpk: number): string => {
    if (cpk >= 1.67) return 'excellent'
    if (cpk >= 1.33) return 'good'
    if (cpk >= 1.0) return 'marginal'
    return 'poor'
  }

  const getPpkClass = (ppk: number): string => {
    if (ppk >= 1.67) return 'excellent'
    if (ppk >= 1.33) return 'good'
    if (ppk >= 1.0) return 'marginal'
    return 'poor'
  }

  const getYieldClass = (yieldValue: number): string => {
    if (yieldValue >= 99.5) return 'excellent'
    if (yieldValue >= 98.0) return 'good'
    if (yieldValue >= 95.0) return 'marginal'
    return 'poor'
  }

  const calculateSigmaLevel = (): number => {
    const dpmo = calculateDPMO()
    return SPCCalculator.calculateSigmaLevel(dpmo)
  }

  const calculateDPMO = (): number => {
    const defects = props.spcData.sampleData.filter(point => point.result !== 'NORMAL').length
    const opportunities = 1
    const units = props.spcData.sampleData.length
    return SPCCalculator.calculateDPMO(defects, opportunities, units)
  }

  const getResultTagType = (result: string): string => {
    switch (result) {
      case 'NORMAL':
        return 'success'
      case 'WARNING':
        return 'warning'
      case 'OUT_OF_CONTROL':
        return 'danger'
      default:
        return 'info'
    }
  }

  const getResultText = (result: string): string => {
    switch (result) {
      case 'NORMAL':
        return '正常'
      case 'WARNING':
        return '警告'
      case 'OUT_OF_CONTROL':
        return '失控'
      default:
        return '未知'
    }
  }

  const formatTime = (date: Date): string => {
    return new Intl.DateTimeFormat('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const initChart = () => {
    if (!chartContainer.value) return

    chart = echarts.init(chartContainer.value)
    updateChart()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }

  const updateChart = () => {
    if (!chart) return

    const option = generateChartOption()
    chart.setOption(option, true)
  }

  const generateChartOption = () => {
    const points = props.spcData.sampleData
    const controlLimits = props.spcData.controlLimits

    if (chartType.value === 'xbar-r') {
      return generateXbarRChartOption(points, controlLimits)
    } else if (chartType.value === 'xbar-s') {
      return generateXbarSChartOption(points, controlLimits)
    } else {
      return generateXbarRChartOption(points, controlLimits) // 默认
    }
  }

  const generateXbarRChartOption = (points: SPCPoint[], controlLimits: ControlLimits) => {
    const xbarData = points.map((point, index) => [index + 1, point.mean])
    const rangeData = points.map((point, index) => [index + 1, point.range])

    const violationPoints = points
      .map((point, index) => {
        if (point.result === 'OUT_OF_CONTROL') {
          return [index + 1, point.mean]
        }
        return null
      })
      .filter(Boolean)

    return {
      title: {
        text: `${title.value} - X̄-R控制图`,
        left: 'center',
        textStyle: {
          fontSize: 14,
          fontWeight: 'bold'
        }
      },
      grid: [
        {
          left: 60,
          right: 60,
          top: 80,
          height: '35%'
        },
        {
          left: 60,
          right: 60,
          top: '60%',
          height: '35%'
        }
      ],
      xAxis: [
        {
          type: 'value',
          name: '样本号',
          gridIndex: 0
        },
        {
          type: 'value',
          name: '样本号',
          gridIndex: 1
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: 'X̄ (均值)',
          gridIndex: 0
        },
        {
          type: 'value',
          name: 'R (极差)',
          gridIndex: 1,
          min: 0
        }
      ],
      series: [
        // X̄图数据点
        {
          name: 'X̄',
          type: 'line',
          data: xbarData,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          itemStyle: {
            color: '#1890ff'
          },
          xAxisIndex: 0,
          yAxisIndex: 0
        },
        // X̄图控制限
        {
          name: 'UCL',
          type: 'line',
          data: xbarData.map(item => [item[0], controlLimits.xbar.ucl]),
          lineStyle: {
            color: '#ff4d4f',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          xAxisIndex: 0,
          yAxisIndex: 0
        },
        {
          name: 'CL',
          type: 'line',
          data: xbarData.map(item => [item[0], controlLimits.xbar.cl]),
          lineStyle: {
            color: '#52c41a',
            type: 'solid',
            width: 2
          },
          symbol: 'none',
          xAxisIndex: 0,
          yAxisIndex: 0
        },
        {
          name: 'LCL',
          type: 'line',
          data: xbarData.map(item => [item[0], controlLimits.xbar.lcl]),
          lineStyle: {
            color: '#ff4d4f',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          xAxisIndex: 0,
          yAxisIndex: 0
        },
        // 违规点标记
        {
          name: '违规点',
          type: 'scatter',
          data: violationPoints,
          symbol: 'diamond',
          symbolSize: 10,
          itemStyle: {
            color: '#ff4d4f'
          },
          xAxisIndex: 0,
          yAxisIndex: 0
        },
        // R图数据点
        {
          name: 'R',
          type: 'line',
          data: rangeData,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#722ed1',
            width: 2
          },
          itemStyle: {
            color: '#722ed1'
          },
          xAxisIndex: 1,
          yAxisIndex: 1
        },
        // R图控制限
        {
          name: 'R-UCL',
          type: 'line',
          data: rangeData.map(item => [item[0], controlLimits.r.ucl]),
          lineStyle: {
            color: '#ff4d4f',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          xAxisIndex: 1,
          yAxisIndex: 1
        },
        {
          name: 'R-CL',
          type: 'line',
          data: rangeData.map(item => [item[0], controlLimits.r.cl]),
          lineStyle: {
            color: '#52c41a',
            type: 'solid',
            width: 2
          },
          symbol: 'none',
          xAxisIndex: 1,
          yAxisIndex: 1
        },
        {
          name: 'R-LCL',
          type: 'line',
          data: rangeData.map(item => [item[0], controlLimits.r.lcl]),
          lineStyle: {
            color: '#ff4d4f',
            type: 'dashed',
            width: 2
          },
          symbol: 'none',
          xAxisIndex: 1,
          yAxisIndex: 1
        }
      ],
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          const sampleNum = params[0].data[0]
          const point = points[sampleNum - 1]
          if (!point) return ''

          return `
          <div>
            <strong>样本 ${sampleNum}</strong><br/>
            时间: ${formatTime(point.timestamp)}<br/>
            均值: ${point.mean.toFixed(4)}<br/>
            极差: ${point.range.toFixed(4)}<br/>
            状态: ${getResultText(point.result)}<br/>
            ${point.violatedRules.length > 0 ? '违规: ' + point.violatedRules.join(', ') : ''}
          </div>
        `
        }
      },
      legend: {
        data: ['X̄', 'UCL', 'CL', 'LCL', '违规点', 'R', 'R-UCL', 'R-CL', 'R-LCL'],
        top: 40
      },
      animation: true
    }
  }

  const generateXbarSChartOption = (points: SPCPoint[], controlLimits: ControlLimits) => {
    // 类似于X̄-R图，但使用标准偏差代替极差
    const xbarData = points.map((point, index) => [index + 1, point.mean])
    const sigmaData = points.map((point, index) => [index + 1, point.standardDeviation])

    // 简化实现，实际应该有完整的控制限计算
    return generateXbarRChartOption(points, controlLimits)
  }

  const updateChartType = () => {
    updateChart()
  }

  const refreshData = async () => {
    loading.value = true
    try {
      emit('refresh')
      await new Promise(resolve => setTimeout(resolve, 500)) // 模拟加载
      updateChart()
      ElMessage.success('数据已刷新')
    } catch (error) {
      ElMessage.error('刷新失败')
    } finally {
      loading.value = false
    }
  }

  const exportChart = () => {
    if (chart) {
      const dataURL = chart.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
      const link = document.createElement('a')
      link.download = `${title.value}_SPC图_${new Date().toISOString().split('T')[0]}.png`
      link.href = dataURL
      link.click()
    }
    emit('export', chartType.value)
  }

  const handleResize = () => {
    if (chart) {
      chart.resize()
    }
  }

  const startAutoRefresh = () => {
    if (props.autoRefresh && props.refreshInterval > 0) {
      refreshTimer = window.setInterval(() => {
        refreshData()
      }, props.refreshInterval)
    }
  }

  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  // 监听数据变化
  watch(
    () => props.spcData,
    () => {
      updateChart()

      // 检查违规并发送事件
      const violations = activeViolations.value
      if (violations.length > 0) {
        emit('violationDetected', violations)
      }
    },
    { deep: true }
  )

  // 生命周期
  onMounted(() => {
    initChart()
    startAutoRefresh()
  })

  onUnmounted(() => {
    stopAutoRefresh()
    window.removeEventListener('resize', handleResize)
    if (chart) {
      chart.dispose()
    }
  })
</script>

<style lang="scss" scoped>
  .spc-chart {
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);

    &__header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: var(--spacing-4);
    }

    &__title {
      h3 {
        margin: 0 0 var(--spacing-2) 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }

    &__info {
      display: flex;
      gap: var(--spacing-4);
      font-size: 14px;
      color: var(--color-text-secondary);

      .parameter {
        font-weight: 500;
        color: var(--color-primary);
      }
    }

    &__controls {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;
    }

    &__statistics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);

      .stat-card {
        padding: var(--spacing-3);
        text-align: center;
        background: var(--color-bg-secondary);
        border-radius: var(--radius-sm);

        .stat-label {
          margin-bottom: var(--spacing-1);
          font-size: 12px;
          color: var(--color-text-secondary);
        }

        .stat-value {
          font-size: 20px;
          font-weight: 600;

          &.excellent {
            color: var(--color-success);
          }

          &.good {
            color: var(--color-primary);
          }

          &.marginal {
            color: var(--color-warning);
          }

          &.poor {
            color: var(--color-danger);
          }
        }
      }
    }

    &__violations {
      margin-bottom: var(--spacing-4);

      .el-alert {
        margin-bottom: var(--spacing-2);

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    &__container {
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      background: var(--color-bg-secondary);
      border-radius: var(--radius-sm);

      .chart-container {
        width: 100%;
        height: 500px;
      }
    }

    &__data-table {
      padding: var(--spacing-4);
      background: var(--color-bg-secondary);
      border-radius: var(--radius-sm);
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .spc-chart {
      &__header {
        flex-direction: column;
        gap: var(--spacing-3);
      }

      &__info {
        flex-direction: column;
        gap: var(--spacing-2);
      }

      &__controls {
        align-self: stretch;
      }

      &__statistics {
        grid-template-columns: repeat(2, 1fr);
      }

      &__container .chart-container {
        height: 400px;
      }
    }
  }
</style>
