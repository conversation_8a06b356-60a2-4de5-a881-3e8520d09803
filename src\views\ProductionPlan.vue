<template>
  <div class="production-plan">
    <!-- 专业级页面头部 -->
    <div class="page-header">
      <div class="page-header__content">
        <div class="page-header__main">
          <h1 class="page-title">主生产计划 (MPS)</h1>
          <p class="page-subtitle">OSAT优化的主生产调度与产能规划系统</p>
        </div>
        <div class="page-header__actions">
          <el-button
:loading="loading" @click="handleRefresh"
>
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button type="primary" @click="showCapacityAnalysis = true">
            <el-icon><TrendCharts /></el-icon>
            产能分析
          </el-button>
          <el-button type="primary" @click="showPlanModal = true">
            <el-icon><Plus /></el-icon>
            新建计划
          </el-button>
        </div>
      </div>
    </div>

    <!-- OSAT专业统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <!-- 产能统计 -->
        <el-card class="stat-card stat-card--capacity">
          <div class="stat-header">
            <div class="stat-icon stat-icon--primary">
              <el-icon><Factory /></el-icon>
            </div>
            <div class="stat-trend stat-trend--up">
              <el-icon><ArrowUp /></el-icon>
              <span>+5.2%</span>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ formatCapacity(totalCapacity.cpTesting) }}
            </div>
            <div class="stat-label">CP测试日产能</div>
            <div class="stat-detail">{{ bottleneckInfo.current }}工序为瓶颈</div>
          </div>
        </el-card>

        <!-- 设备利用率 -->
        <el-card class="stat-card stat-card--utilization">
          <div class="stat-header">
            <div class="stat-icon stat-icon--warning">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="stat-trend stat-trend--stable">
              <el-icon><Minus /></el-icon>
              <span>稳定</span>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ bottleneckInfo.utilization }}%</div>
            <div class="stat-label">设备综合利用率</div>
            <div class="stat-detail">{{ activeEquipmentCount }}台设备运行中</div>
          </div>
        </el-card>

        <!-- 在制品数量 -->
        <el-card class="stat-card stat-card--wip">
          <div class="stat-header">
            <div class="stat-icon stat-icon--info">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stat-trend stat-trend--down">
              <el-icon><ArrowDown /></el-icon>
              <span>-2.1%</span>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ formatQuantity(totalWipQuantity) }}
            </div>
            <div class="stat-label">在制品数量</div>
            <div class="stat-detail">{{ wipDistribution.length }}个批次在线</div>
          </div>
        </el-card>

        <!-- 计划执行率 -->
        <el-card class="stat-card stat-card--execution">
          <div class="stat-header">
            <div class="stat-icon stat-icon--success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-trend stat-trend--up">
              <el-icon><ArrowUp /></el-icon>
              <span>+3.8%</span>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ planExecutionRate }}%</div>
            <div class="stat-label">计划执行达成率</div>
            <div class="stat-detail">{{ completedPlansCount }}/{{ totalPlansCount }}个计划完成</div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 筛选条件 -->
    <c-card class="filter-card">
      <div class="filter-form">
        <div class="filter-item">
          <label>时间范围</label>
          <c-select v-model="filterForm.timeRange" :options="timeRangeOptions" />
        </div>
        <div class="filter-item">
          <label>生产线</label>
          <c-select v-model="filterForm.productionLine" :options="productionLineOptions" />
        </div>
        <div class="filter-item">
          <label>状态</label>
          <c-select v-model="filterForm.status" :options="planStatusOptions" />
        </div>
        <div class="filter-actions">
          <c-button type="primary"
@click="loadPlans"
>
查询
</c-button>
        </div>
      </div>
    </c-card>

    <!-- 甘特图视图 -->
    <c-card class="gantt-card">
      <div class="gantt-header">
        <h3>生产计划甘特图</h3>
        <div class="view-controls">
          <c-button
            v-for="view in viewOptions"
            :key="view.value"
            :type="currentView === view.value ? 'primary' : 'secondary'"
            size="small"
            @click="currentView = view.value"
          >
            {{ view.label }}
          </c-button>
        </div>
      </div>
      <div
ref="ganttContainer" class="gantt-container"
>
        <div class="gantt-timeline">
          <div class="timeline-header">
            <div class="timeline-scale">
              <div
                v-for="date in timelineData"
                :key="date.date"
                class="scale-item"
                :style="{ width: scaleItemWidth + 'px' }"
              >
                {{ formatDate(date.date) }}
              </div>
            </div>
          </div>
          <div class="timeline-body">
            <div
v-for="plan in filteredPlans" :key="plan.id"
class="timeline-row"
>
              <div class="row-label">
                <div class="plan-info">
                  <div class="plan-name">
                    {{ plan.orderNo }}
                  </div>
                  <div class="plan-product">
                    {{ plan.productName }}
                  </div>
                </div>
              </div>
              <div class="row-content" :style="{ width: timelineWidth + 'px' }">
                <div
                  v-for="stage in plan.stages"
                  :key="stage.id"
                  :class="getStageClass(stage)"
                  :style="getStageStyle(stage)"
                  @click="handleStageClick(plan, stage)"
                >
                  <div class="stage-content">
                    <span class="stage-name">{{ stage.name }}</span>
                    <span class="stage-progress">{{ stage.progress }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </c-card>

    <!-- 计划列表 -->
    <c-card class="list-card">
      <c-table
        v-model:current="pagination.current"
        v-model:page-size="pagination.pageSize"
        :data-source="plans"
        :columns="planColumns"
        :loading="tableLoading"
        :show-pagination="true"
        :total="pagination.total"
        class="plan-table"
        @refresh="loadPlans"
        @row-click="handleRowClick"
      >
        <!-- 订单号列 -->
        <template #orderNo="{ record }">
          <span class="order-no">{{ record.orderNo }}</span>
        </template>

        <!-- 进度列 -->
        <template #progress="{ record }">
          <div class="progress-bar">
            <div class="progress-track">
              <div class="progress-fill"
:style="{ width: record.progress + '%' }"
/>
            </div>
            <span class="progress-text">{{ record.progress }}%</span>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <span :class="getPlanStatusClass(record.status)">
            {{ getPlanStatusText(record.status) }}
          </span>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <div class="table-actions">
            <c-button type="text"
size="small" @click="handleViewDetails(record)"
>
详情
</c-button>
            <c-button type="text"
size="small" @click="handleEditPlan(record)"
>
编辑
</c-button>
          </div>
        </template>
      </c-table>
    </c-card>

    <!-- 新建计划模态框 -->
    <c-modal
      v-model:visible="showPlanModal"
      title="新建生产计划"
      width="700px"
      :confirm-loading="submitLoading"
      @confirm="handleSubmitPlan"
      @cancel="handleCancelPlan"
    >
      <div class="plan-form">
        <div class="form-grid">
          <div class="form-item">
            <label class="required">关联订单</label>
            <c-select
              v-model="planForm.orderId"
              :options="orderOptions"
              placeholder="请选择订单"
              filterable
            />
          </div>
          <div class="form-item">
            <label class="required">生产线</label>
            <c-select
              v-model="planForm.productionLine"
              :options="productionLineOptions"
              placeholder="请选择生产线"
            />
          </div>
          <div class="form-item">
            <label class="required">计划开始时间</label>
            <el-date-picker
              v-model="planForm.startDate"
              type="datetime"
              placeholder="请选择开始时间"
            />
          </div>
          <div class="form-item">
            <label class="required">计划结束时间</label>
            <el-date-picker
              v-model="planForm.endDate"
              type="datetime"
              placeholder="请选择结束时间"
            />
          </div>
          <div class="form-item full-width">
            <label>备注</label>
            <c-input
              v-model="planForm.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </div>
        </div>
      </div>
    </c-modal>

    <!-- 阶段详情模态框 -->
    <c-modal
      v-model:visible="showStageModal"
      :title="currentStage ? `${currentStage.name} - 详情` : '阶段详情'"
      width="600px"
      :show-footer="false"
    >
      <div v-if="currentStage" class="stage-detail">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">阶段名称</span>
            <span class="value">{{ currentStage.name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">进度</span>
            <span class="value">{{ currentStage.progress }}%</span>
          </div>
          <div class="detail-item">
            <span class="label">开始时间</span>
            <span class="value">{{ currentStage.startDate }}</span>
          </div>
          <div class="detail-item">
            <span class="label">结束时间</span>
            <span class="value">{{ currentStage.endDate }}</span>
          </div>
          <div class="detail-item">
            <span class="label">负责人</span>
            <span class="value">{{ currentStage.assignee }}</span>
          </div>
          <div class="detail-item">
            <span class="label">状态</span>
            <span class="value" :class="getStageStatusClass(currentStage.status)">
              {{ currentStage.status }}
            </span>
          </div>
        </div>

        <div
v-if="currentStage.equipment" class="stage-equipment"
>
          <h4>使用设备</h4>
          <div class="equipment-list">
            <div
v-for="equip in currentStage.equipment" :key="equip.id"
class="equipment-item"
>
              <span class="equipment-name">{{ equip.name }}</span>
              <span class="equipment-status" :class="equip.status">{{ equip.statusText }}</span>
            </div>
          </div>
        </div>
      </div>
    </c-modal>

    <!-- 产能分析模态框 -->
    <c-modal
      v-model:visible="showCapacityAnalysis"
      title="OSAT产能分析报告"
      width="900px"
      :show-footer="false"
    >
      <div class="capacity-analysis">
        <!-- 设备产能矩阵 -->
        <div class="analysis-section">
          <h4>设备产能矩阵</h4>
          <div class="equipment-matrix">
            <el-table :data="getEquipmentCapacity()" style="width: 100%">
              <el-table-column prop="equipmentId" label="设备ID" width="120" />
              <el-table-column prop="type" label="设备类型" width="120" />
              <el-table-column prop="processType" label="工艺类型" width="100" />
              <el-table-column prop="dailyCapacity" label="日产能(K pcs)" width="120" align="right">
                <template #default="scope">
                  {{ formatCapacity(scope.row.dailyCapacity) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="availabilityHours"
                label="可用时间(h)"
                width="100"
                align="right"
              />
              <el-table-column prop="supportedPackages" label="支持封装" min-width="150">
                <template #default="scope">
                  <el-tag
                    v-for="pkg in scope.row.supportedPackages"
                    :key="pkg"
                    size="small"
                    style="margin-right: 4px"
                  >
                    {{ pkg }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 瓶颈分析 -->
        <div class="analysis-section">
          <h4>瓶颈分析</h4>
          <div class="bottleneck-analysis">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card>
                  <div class="bottleneck-info">
                    <div class="bottleneck-title">当前瓶颈工序</div>
                    <div class="bottleneck-value">
                      {{ bottleneckInfo.current }}
                    </div>
                    <div class="bottleneck-detail">
                      利用率: {{ bottleneckInfo.utilization }}%
                      <br />
                      约束产能: {{ formatCapacity(bottleneckInfo.capacity) }}/日
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card>
                  <div class="improvement-suggestions">
                    <div class="suggestions-title">改善建议</div>
                    <ul class="suggestions-list">
                      <li
                        v-for="action in getBottleneckAnalysis().recommendedActions"
                        :key="action"
                      >
                        {{ action }}
                      </li>
                    </ul>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 产能预警 -->
        <div
v-if="capacityWarnings.length > 0" class="analysis-section"
>
          <h4>产能预警</h4>
          <div class="capacity-warnings">
            <el-alert
              v-for="warning in capacityWarnings"
              :key="`${warning.process}-${warning.shortfall}`"
              :title="`${warning.process}产能不足`"
              :description="`缺口: ${formatQuantity(warning.shortfall)} pcs/日，建议: ${warning.recommendation}`"
              type="warning"
              show-icon
              style="margin-bottom: 12px"
            />
          </div>
        </div>
      </div>
    </c-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, nextTick } from 'vue'
  import { formatNumber } from '@/utils/common'
  import type { CTableColumn } from '@/components/base'
  import { useProductionPlan, useCapacityPlanning } from '@/composables/useProductionPlan'
  import type { ProductionPlan, WorkOrderType, PackageType } from '@/types/order'

  // 接口定义 - 保留用于甘特图展示
  interface ProductionStage {
    id: string
    name: string
    type: 'cp' | 'assembly' | 'ft' | 'delivery'
    startDate: string
    endDate: string
    progress: number
    status: string
    assignee: string
    equipment?: Array<{ id: string; name: string; status: string; statusText: string }>
  }

  // OSAT专业组合函数
  const {
    loading,
    plans: osatPlans,
    loadProductionPlans,
    createProductionPlan,
    analyzeCapacityRequirements
  } = useProductionPlan()

  const {
    capacityAnalysis,
    getEquipmentCapacity,
    getBottleneckAnalysis,
    calculateAvailableCapacity,
    analyzeCapacityWarnings
  } = useCapacityPlanning()

  // 响应式数据
  const plans = ref<ProductionPlan[]>([])
  const tableLoading = ref(false)
  const submitLoading = ref(false)
  const showPlanModal = ref(false)
  const showStageModal = ref(false)
  const showCapacityAnalysis = ref(false)
  const currentView = ref('week')
  const currentStage = ref<ProductionStage | null>(null)
  const ganttContainer = ref<HTMLElement>()

  // OSAT专业统计数据计算
  const totalCapacity = computed(() => {
    const equipment = getEquipmentCapacity()
    return {
      cpTesting: equipment
        .filter(eq => eq.processType === 'cp_testing')
        .reduce((sum, eq) => sum + eq.dailyCapacity, 0),
      assembly: equipment
        .filter(eq => eq.processType === 'assembly')
        .reduce((sum, eq) => sum + eq.dailyCapacity, 0),
      ftTesting: equipment
        .filter(eq => eq.processType === 'ft_testing')
        .reduce((sum, eq) => sum + eq.dailyCapacity, 0)
    }
  })

  const bottleneckInfo = computed(() => {
    const bottleneck = getBottleneckAnalysis()
    return {
      current:
        bottleneck.currentBottleneck === 'assembly'
          ? '封装工艺'
          : bottleneck.currentBottleneck === 'cp_testing'
            ? 'CP测试'
            : 'FT测试',
      utilization: bottleneck.bottleneckUtilization,
      capacity: bottleneck.constraintCapacity
    }
  })

  const equipmentUtilization = computed(() => {
    const bottleneck = getBottleneckAnalysis()
    return Math.round(bottleneck.bottleneckUtilization)
  })

  // 格式化产能显示
  const formatCapacity = (capacity: number) => {
    if (capacity >= 1000000) {
      return (capacity / 1000000).toFixed(1) + 'M'
    }
    return (capacity / 1000).toFixed(0) + 'K'
  }

  // 格式化数量显示
  const formatQuantity = (quantity: number) => {
    if (quantity >= 1000000) {
      return (quantity / 1000000).toFixed(1) + 'M'
    }
    return (quantity / 1000).toFixed(0) + 'K'
  }

  // 活跃设备数量
  const activeEquipmentCount = computed(() => {
    const equipment = getEquipmentCapacity()
    return equipment.length
  })

  // 在制品数量和分布
  const totalWipQuantity = computed(() => {
    // 模拟在制品数据
    return (
      osatPlans.value.reduce((sum, plan) => {
        if (plan.status === 'executing') {
          return (
            sum + plan.orderGroups.reduce((groupSum, group) => groupSum + group.totalQuantity, 0)
          )
        }
        return sum
      }, 0) || 850000
    ) // 默认850K
  })

  const wipDistribution = computed(() => {
    // 模拟批次分布
    return osatPlans.value.filter(plan => plan.status === 'executing') || [1, 2, 3]
  })

  // 计划执行统计
  const planExecutionRate = computed(() => {
    const total = osatPlans.value.length || 8
    const completed = osatPlans.value.filter(plan => plan.status === 'completed').length || 6
    return Math.round((completed / total) * 100) || 88
  })

  const completedPlansCount = computed(() => {
    return osatPlans.value.filter(plan => plan.status === 'completed').length || 6
  })

  const totalPlansCount = computed(() => {
    return osatPlans.value.length || 8
  })

  // 产能预警分析
  const capacityWarnings = computed(() => {
    if (osatPlans.value.length === 0) return []

    // 获取当前计划的订单组合并分析预警
    const currentOrders = osatPlans.value
      .filter(plan => plan.status === 'executing' || plan.status === 'approved')
      .flatMap(plan => plan.orderGroups)

    return analyzeCapacityWarnings(currentOrders)
  })

  // 筛选表单
  const filterForm = reactive({
    timeRange: 'week',
    productionLine: '',
    status: ''
  })

  // 分页
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0
  })

  // 计划表单
  const planForm = reactive({
    orderId: '',
    productionLine: '',
    startDate: '',
    endDate: '',
    remarks: ''
  })

  // 选项数据
  const timeRangeOptions = [
    { label: '本周', value: 'week' },
    { label: '本月', value: 'month' },
    { label: '本季度', value: 'quarter' }
  ]

  const productionLineOptions = [
    { label: 'CP测试线A', value: 'cp-a' },
    { label: 'CP测试线B', value: 'cp-b' },
    { label: '封装线1', value: 'assembly-1' },
    { label: '封装线2', value: 'assembly-2' },
    { label: 'FT测试线A', value: 'ft-a' },
    { label: 'FT测试线B', value: 'ft-b' }
  ]

  const planStatusOptions = [
    { label: '计划中', value: 'planned' },
    { label: '进行中', value: 'running' },
    { label: '暂停', value: 'paused' },
    { label: '完成', value: 'completed' },
    { label: '延期', value: 'delayed' }
  ]

  const viewOptions = [
    { label: '周视图', value: 'week' },
    { label: '月视图', value: 'month' }
  ]

  const orderOptions = [
    { label: 'ORD-2024-001 - 华为技术', value: '1' },
    { label: 'ORD-2024-002 - 小米科技', value: '2' },
    { label: 'ORD-2024-003 - OPPO', value: '3' }
  ]

  // 表格列配置
  const planColumns: CTableColumn[] = [
    {
      key: 'orderNo',
      title: '订单号',
      dataIndex: 'orderNo',
      width: 150
    },
    {
      key: 'productName',
      title: '产品名称',
      dataIndex: 'productName',
      width: 180
    },
    {
      key: 'customerName',
      title: '客户',
      dataIndex: 'customerName',
      width: 150
    },
    {
      key: 'quantity',
      title: '数量',
      dataIndex: 'quantity',
      width: 100,
      align: 'right'
    },
    {
      key: 'progress',
      title: '进度',
      width: 150
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      width: 100
    },
    {
      key: 'startDate',
      title: '开始时间',
      dataIndex: 'startDate',
      width: 120
    },
    {
      key: 'endDate',
      title: '结束时间',
      dataIndex: 'endDate',
      width: 120
    },
    {
      key: 'actions',
      title: '操作',
      width: 120,
      fixed: 'right'
    }
  ]

  // 计算属性
  const filteredPlans = computed(() => {
    return plans.value.filter(plan => {
      if (filterForm.status && plan.status !== filterForm.status) return false
      return true
    })
  })

  const timelineData = computed(() => {
    const dates = []
    const today = new Date()
    const daysToShow = currentView.value === 'week' ? 7 : 30

    for (let i = 0; i < daysToShow; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      dates.push({ date: date.toISOString().split('T')[0] })
    }

    return dates
  })

  const scaleItemWidth = computed(() => {
    return currentView.value === 'week' ? 120 : 40
  })

  const timelineWidth = computed(() => {
    return timelineData.value.length * scaleItemWidth.value
  })

  // 方法
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    if (currentView.value === 'week') {
      return `${date.getMonth() + 1}/${date.getDate()}`
    } else {
      return `${date.getDate()}`
    }
  }

  const getStageClass = (stage: ProductionStage) => [
    'timeline-stage',
    `timeline-stage--${stage.type}`,
    {
      'timeline-stage--completed': stage.progress === 100,
      'timeline-stage--running': stage.progress > 0 && stage.progress < 100,
      'timeline-stage--pending': stage.progress === 0
    }
  ]

  const getStageStyle = (stage: ProductionStage) => {
    const startDate = new Date(stage.startDate)
    const endDate = new Date(stage.endDate)
    const today = new Date(timelineData.value[0].date)

    const startOffset = Math.floor((startDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    const duration = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

    return {
      left: Math.max(0, startOffset * scaleItemWidth.value) + 'px',
      width: Math.max(scaleItemWidth.value, duration * scaleItemWidth.value) + 'px'
    }
  }

  const getPlanStatusClass = (status: string) => {
    const classMap: Record<string, string> = {
      planned: 'status-planned',
      running: 'status-running',
      paused: 'status-paused',
      completed: 'status-completed',
      delayed: 'status-delayed'
    }
    return classMap[status] || ''
  }

  const getPlanStatusText = (status: string) => {
    const option = planStatusOptions.find(opt => opt.value === status)
    return option?.label || status
  }

  const getStageStatusClass = (status: string) => {
    return `stage-status--${status.toLowerCase()}`
  }

  const loadPlans = async () => {
    tableLoading.value = true
    try {
      // 加载OSAT专业生产计划数据
      await loadProductionPlans()

      // 将OSAT计划转换为显示格式（保留甘特图功能）
      const displayPlans = osatPlans.value.map(plan => ({
        id: plan.id,
        orderNo: plan.planNumber,
        productName: plan.planName,
        customerName: '客户订单组',
        quantity: plan.orderGroups.reduce((sum, group) => sum + group.totalQuantity, 0),
        progress:
          plan.status === 'completed'
            ? 100
            : plan.status === 'executing'
              ? 65
              : plan.status === 'approved'
                ? 30
                : 10,
        status: plan.status,
        startDate: plan.planPeriod.startDate,
        endDate: plan.planPeriod.endDate,
        stages: [] // 简化甘特图，仅显示基本信息
      }))

      plans.value = displayPlans
      pagination.total = displayPlans.length
    } catch (error) {
      console.error('加载生产计划失败:', error)
    } finally {
      tableLoading.value = false
    }
  }

  const handleRefresh = () => {
    loadPlans()
  }

  const handleRowClick = (record: ProductionPlan) => {
    console.log('点击计划:', record)
  }

  const handleViewDetails = (record: ProductionPlan) => {
    console.log('查看详情:', record)
  }

  const handleEditPlan = (record: ProductionPlan) => {
    console.log('编辑计划:', record)
  }

  const handleStageClick = (plan: ProductionPlan, stage: ProductionStage) => {
    currentStage.value = stage
    showStageModal.value = true
  }

  const handleSubmitPlan = async () => {
    submitLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log('提交计划:', planForm)
      showPlanModal.value = false
      loadPlans()
    } finally {
      submitLoading.value = false
    }
  }

  const handleCancelPlan = () => {
    Object.keys(planForm).forEach(key => {
      planForm[key] = ''
    })
  }

  // 生命周期
  onMounted(() => {
    loadPlans()
  })
</script>

<style lang="scss">
  .production-plan {
    padding: var(--spacing-6);
  }

  .page-header {
    @include flex-between;
    margin-bottom: var(--spacing-6);
  }

  .page-title {
    margin: 0;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
  }

  .header-actions {
    display: flex;
    gap: var(--spacing-3);
  }

  // 统计卡片
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
  }

  .stat-card {
    .stat-content {
      @include flex-center;
      gap: var(--spacing-4);
      padding: var(--spacing-2);
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      font-size: 20px;
      border-radius: var(--radius-lg);

      @include flex-center;

      &--primary {
        background: var(--color-primary-light);
      }

      &--success {
        background: var(--color-success);
        opacity: 0.1;
      }

      &--warning {
        background: var(--color-warning);
        opacity: 0.1;
      }

      &--error {
        background: var(--color-error);
        opacity: 0.1;
      }
    }

    .stat-info {
      flex: 1;

      .stat-value {
        margin-bottom: var(--spacing-1);
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
      }

      .stat-label {
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }
  }

  // 筛选卡片
  .filter-card {
    margin-bottom: var(--spacing-6);

    .filter-form {
      @include flex-between;
      gap: var(--spacing-4);

      .filter-item {
        label {
          display: block;
          margin-bottom: var(--spacing-1);
          font-size: var(--font-size-sm);
          color: var(--color-text-secondary);
        }
      }

      .filter-actions {
        margin-top: 20px;
      }
    }
  }

  // 甘特图
  .gantt-card {
    margin-bottom: var(--spacing-6);

    .gantt-header {
      @include flex-between;
      padding-bottom: var(--spacing-3);
      margin-bottom: var(--spacing-4);
      border-bottom: 1px solid var(--color-border-light);

      h3 {
        margin: 0;
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
      }

      .view-controls {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    .gantt-container {
      overflow-x: auto;

      @include scrollbar;
    }

    .gantt-timeline {
      min-width: 800px;

      .timeline-header {
        border-bottom: 1px solid var(--color-border-base);

        .timeline-scale {
          display: flex;
          height: 40px;
          margin-left: 200px;

          .scale-item {
            @include flex-center;
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
            border-right: 1px solid var(--color-border-light);
          }
        }
      }

      .timeline-body {
        .timeline-row {
          display: flex;
          min-height: 60px;
          border-bottom: 1px solid var(--color-border-light);

          &:hover {
            background: var(--color-bg-hover);
          }

          .row-label {
            width: 200px;
            padding: var(--spacing-3);
            border-right: 1px solid var(--color-border-light);

            .plan-info {
              .plan-name {
                margin-bottom: var(--spacing-1);
                font-weight: var(--font-weight-medium);
                color: var(--color-text-primary);
              }

              .plan-product {
                font-size: var(--font-size-xs);
                color: var(--color-text-secondary);
              }
            }
          }

          .row-content {
            position: relative;
            padding: var(--spacing-2) 0;
          }
        }
      }
    }

    .timeline-stage {
      position: absolute;
      height: 36px;
      cursor: pointer;
      border-radius: var(--radius-base);
      transition: all var(--transition-fast);

      &--cp {
        background: var(--color-cp-test);
        border-left: 4px solid var(--color-info);
      }

      &--assembly {
        background: var(--color-assembly);
        border-left: 4px solid #8b5cf6;
      }

      &--ft {
        background: var(--color-ft-test);
        border-left: 4px solid var(--color-success);
      }

      &--completed {
        opacity: 0.8;
      }

      &--running {
        box-shadow: 0 0 0 2px var(--color-primary);
      }

      &:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
      }

      .stage-content {
        @include flex-between;
        align-items: center;
        height: 100%;
        padding: 0 var(--spacing-2);

        .stage-name {
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
        }

        .stage-progress {
          font-size: var(--font-size-xs);
          opacity: 0.8;
        }
      }
    }
  }

  // 进度条
  .progress-bar {
    @include flex-center;
    gap: var(--spacing-2);

    .progress-track {
      flex: 1;
      height: 8px;
      overflow: hidden;
      background: var(--color-bg-tertiary);
      border-radius: var(--radius-full);

      .progress-fill {
        height: 100%;
        background: var(--color-success);
        transition: width var(--transition-normal);
      }
    }

    .progress-text {
      min-width: 35px;
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
    }
  }

  // 状态样式
  .status-planned {
    color: var(--color-info);
  }

  .status-running {
    color: var(--color-primary);
  }

  .status-paused {
    color: var(--color-warning);
  }

  .status-completed {
    color: var(--color-success);
  }

  .status-delayed {
    color: var(--color-error);
  }

  // 表单样式
  .plan-form {
    .form-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-4);

      .full-width {
        grid-column: 1 / -1;
      }
    }

    .form-item {
      label {
        display: block;
        margin-bottom: var(--spacing-1);
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);

        &.required::after {
          color: var(--color-error);
          content: ' *';
        }
      }
    }
  }

  // 阶段详情
  .stage-detail {
    .detail-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-6);
    }

    .detail-item {
      .label {
        display: block;
        margin-bottom: var(--spacing-1);
        font-size: var(--font-size-xs);
        color: var(--color-text-secondary);
      }

      .value {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
      }
    }

    .stage-equipment {
      h4 {
        margin-bottom: var(--spacing-3);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
      }

      .equipment-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .equipment-item {
        @include flex-between;
        padding: var(--spacing-2) var(--spacing-3);
        background: var(--color-bg-secondary);
        border-radius: var(--radius-base);

        .equipment-name {
          font-size: var(--font-size-sm);
          color: var(--color-text-primary);
        }

        .equipment-status {
          padding: 2px 6px;
          font-size: var(--font-size-xs);
          border-radius: var(--radius-sm);

          &.idle {
            color: white;
            background: var(--color-success);
            opacity: 0.8;
          }

          &.busy {
            color: white;
            background: var(--color-warning);
            opacity: 0.8;
          }

          &.maintenance {
            color: white;
            background: var(--color-error);
            opacity: 0.8;
          }
        }
      }
    }
  }

  // 响应式
  @media (width <= 768px) {
    .production-plan {
      padding: var(--spacing-4);
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .filter-form {
      flex-direction: column;
      gap: var(--spacing-3) !important;
    }

    .gantt-container {
      font-size: var(--font-size-xs);

      .timeline-row .row-label {
        width: 150px;
      }
    }

    .form-grid {
      grid-template-columns: 1fr !important;
    }

    .detail-grid {
      grid-template-columns: 1fr !important;
    }
  }

  // 产能分析模态框样式
  .capacity-analysis {
    .analysis-section {
      margin-bottom: var(--spacing-6);

      h4 {
        padding-bottom: var(--spacing-2);
        margin: 0 0 var(--spacing-4) 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
        border-bottom: 2px solid var(--color-primary);
      }
    }

    .equipment-matrix {
      .el-table {
        font-size: var(--font-size-sm);
      }
    }

    .bottleneck-analysis {
      .bottleneck-info,
      .improvement-suggestions {
        padding: var(--spacing-4);
        text-align: center;
      }

      .bottleneck-title,
      .suggestions-title {
        margin-bottom: var(--spacing-3);
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }

      .bottleneck-value {
        margin-bottom: var(--spacing-2);
        font-size: var(--font-size-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--color-warning);
      }

      .bottleneck-detail {
        font-size: var(--font-size-sm);
        line-height: 1.6;
        color: var(--color-text-secondary);
      }

      .suggestions-list {
        padding-left: var(--spacing-4);
        margin: 0;
        text-align: left;

        li {
          margin-bottom: var(--spacing-2);
          font-size: var(--font-size-sm);
          line-height: 1.5;
          color: var(--color-text-primary);
        }
      }
    }

    .capacity-warnings {
      .el-alert {
        border-radius: var(--radius-base);
      }
    }
  }

  // OSAT专业统计卡片增强样式
  .stat-card {
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);

    &:hover {
      border-color: var(--color-primary);
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .stat-header {
      @include flex-between;
      margin-bottom: var(--spacing-3);
    }

    .stat-content {
      text-align: center;
    }

    .stat-value {
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
    }

    .stat-label {
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .stat-detail {
      font-size: var(--font-size-xs);
      color: var(--color-text-tertiary);
    }

    .stat-trend {
      @include flex-center;
      gap: 4px;
      font-size: var(--font-size-xs);

      &--up {
        color: var(--color-success);
      }

      &--down {
        color: var(--color-error);
      }

      &--stable {
        color: var(--color-text-secondary);
      }
    }
  }
</style>
