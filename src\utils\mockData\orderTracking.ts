/**
 * IC封测CIM系统 - 订单跟踪模拟数据
 * Order Tracking Mock Data for IC Packaging & Testing CIM System
 */

import type {
  OrderTrackingDetail,
  ProductionStageInfo,
  ProductionEvent,
  QualityCheckpoint,
  CustomerNotification,
  GanttChartData,
  ProductionStatistics
} from '@/types/order'
import { ProductionStage, StageStatus, EventType, EventSeverity } from '@/types/order'
import { allMockOrders } from './orders'

// OSAT专业流程阶段配置
const PRODUCTION_STAGES = [
  {
    stage: ProductionStage.MATERIAL_READY,
    stageName: '物料准备',
    standardDuration: 120, // 2小时
    description: '晶圆到货检验，物料准备'
  },
  {
    stage: ProductionStage.CP_TESTING,
    stageName: 'CP测试',
    standardDuration: 480, // 8小时
    description: '晶圆级电参数测试，良率筛选'
  },
  {
    stage: ProductionStage.DICING,
    stageName: '晶圆切割',
    standardDuration: 240, // 4小时
    description: '晶圆切割成单个die'
  },
  {
    stage: ProductionStage.DIE_ATTACH,
    stageName: '贴片',
    standardDuration: 360, // 6小时
    description: 'Die贴装到基板/引脚框架'
  },
  {
    stage: ProductionStage.WIRE_BOND,
    stageName: '线键合',
    standardDuration: 720, // 12小时
    description: '金丝/铜丝连接die和引脚'
  },
  {
    stage: ProductionStage.MOLDING,
    stageName: '塑封',
    standardDuration: 480, // 8小时
    description: '环氧树脂包封保护die'
  },
  {
    stage: ProductionStage.MARKING,
    stageName: '打标',
    standardDuration: 120, // 2小时
    description: '激光打标产品信息'
  },
  {
    stage: ProductionStage.TRIM_FORM,
    stageName: '切筋成型',
    standardDuration: 180, // 3小时
    description: '切断多余引脚并成型'
  },
  {
    stage: ProductionStage.FT_TESTING,
    stageName: '最终测试',
    standardDuration: 360, // 6小时
    description: '成品功能和参数测试'
  },
  {
    stage: ProductionStage.BURN_IN,
    stageName: '老化测试',
    standardDuration: 2880, // 48小时
    description: '高温老化筛选早期失效'
  },
  {
    stage: ProductionStage.FINAL_QC,
    stageName: '最终质检',
    standardDuration: 180, // 3小时
    description: '外观和尺寸最终检验'
  },
  {
    stage: ProductionStage.PACKAGING,
    stageName: '包装',
    standardDuration: 240, // 4小时
    description: '防静电包装入库'
  },
  {
    stage: ProductionStage.SHIPPING,
    stageName: '发货',
    standardDuration: 120, // 2小时
    description: '物流发货'
  }
]

// 生成阶段信息
const generateStageInfo = (
  stage: (typeof PRODUCTION_STAGES)[0],
  orderStatus: string,
  stageIndex: number,
  totalStages: number
): ProductionStageInfo => {
  let status: StageStatus
  let progress: number
  let actualStartTime: string | undefined
  let actualEndTime: string | undefined

  const baseTime = Date.now() - (totalStages - stageIndex) * 24 * 60 * 60 * 1000

  if (orderStatus === 'completed') {
    status = StageStatus.COMPLETED
    progress = 100
    actualStartTime = new Date(baseTime).toISOString()
    actualEndTime = new Date(baseTime + stage.standardDuration * 60 * 1000).toISOString()
  } else if (orderStatus === 'processing') {
    if (stageIndex < totalStages / 2) {
      status = StageStatus.COMPLETED
      progress = 100
      actualStartTime = new Date(baseTime).toISOString()
      actualEndTime = new Date(baseTime + stage.standardDuration * 60 * 1000).toISOString()
    } else if (stageIndex === Math.floor(totalStages / 2)) {
      status = StageStatus.IN_PROGRESS
      progress = Math.floor(Math.random() * 60 + 20) // 20-80%
      actualStartTime = new Date(baseTime).toISOString()
    } else {
      status = StageStatus.PENDING
      progress = 0
    }
  } else {
    status = StageStatus.PENDING
    progress = 0
  }

  return {
    stage: stage.stage,
    stageName: stage.stageName,
    status,
    progress,
    plannedStartTime: new Date(baseTime - 60 * 60 * 1000).toISOString(),
    plannedEndTime: new Date(baseTime + stage.standardDuration * 60 * 1000).toISOString(),
    actualStartTime,
    actualEndTime,
    duration: actualEndTime
      ? stage.standardDuration + Math.floor(Math.random() * 60 - 30)
      : undefined,
    standardDuration: stage.standardDuration,
    assignedEquipment:
      stageIndex % 2 === 0 ? `EQ-${String(stageIndex + 1).padStart(3, '0')}` : undefined,
    assignedOperator:
      stageIndex % 3 === 0 ? `OP-${String(stageIndex + 1).padStart(3, '0')}` : undefined,
    yield: status === StageStatus.COMPLETED ? Math.floor(Math.random() * 5 + 96) : undefined,
    throughput:
      status === StageStatus.COMPLETED ? Math.floor(Math.random() * 1000 + 9000) : undefined
  }
}

// 生成异常事件
const generateEvents = (orderId: string): ProductionEvent[] => {
  const events: ProductionEvent[] = []
  const eventCount = Math.floor(Math.random() * 5 + 2) // 2-6个事件

  const eventTemplates = [
    {
      title: '设备故障',
      description: 'CP测试设备EQ-001出现通信故障，影响生产进度',
      type: EventType.ERROR,
      severity: EventSeverity.HIGH,
      stage: ProductionStage.CP_TESTING
    },
    {
      title: '良率异常',
      description: '线键合工序良率下降至92%，低于标准95%',
      type: EventType.WARNING,
      severity: EventSeverity.MEDIUM,
      stage: ProductionStage.WIRE_BOND
    },
    {
      title: '物料短缺',
      description: '金丝库存不足，需要紧急补料',
      type: EventType.ALERT,
      severity: EventSeverity.HIGH,
      stage: ProductionStage.WIRE_BOND
    },
    {
      title: '质量异常',
      description: '塑封工序发现气泡缺陷，需要调整参数',
      type: EventType.WARNING,
      severity: EventSeverity.MEDIUM,
      stage: ProductionStage.MOLDING
    },
    {
      title: '工艺优化',
      description: '优化FT测试程序，提高测试效率15%',
      type: EventType.INFO,
      severity: EventSeverity.LOW,
      stage: ProductionStage.FT_TESTING
    },
    {
      title: '里程碑达成',
      description: '首批产品完成CP测试，良率达98%',
      type: EventType.MILESTONE,
      severity: EventSeverity.LOW,
      stage: ProductionStage.CP_TESTING
    }
  ]

  for (let i = 0; i < eventCount; i++) {
    const template = eventTemplates[Math.floor(Math.random() * eventTemplates.length)]
    const occurredAt = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
    const resolved = Math.random() > 0.3

    events.push({
      id: `event_${orderId}_${i + 1}`,
      orderId,
      stage: template.stage,
      eventType: template.type,
      severity: template.severity,
      title: template.title,
      description: template.description,
      occurredAt: occurredAt.toISOString(),
      resolvedAt: resolved
        ? new Date(occurredAt.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString()
        : undefined,
      duration: resolved ? Math.floor(Math.random() * 1440 + 60) : undefined, // 1-24小时
      responsiblePerson: ['张工程师', '李技术员', '王主管'][Math.floor(Math.random() * 3)],
      impact: resolved ? '已解决，无重大影响' : '影响生产进度30分钟',
      solution: resolved ? '更换备件，调整工艺参数' : undefined,
      preventiveAction: resolved ? '加强设备预防性维护' : undefined,
      status: resolved ? 'resolved' : Math.random() > 0.5 ? 'investigating' : 'open',
      tags: ['生产', '质量', '设备', '工艺'].slice(0, Math.floor(Math.random() * 3 + 1))
    })
  }

  return events.sort((a, b) => new Date(b.occurredAt).getTime() - new Date(a.occurredAt).getTime())
}

// 生成质量检测节点
const generateQualityCheckpoints = (stages: ProductionStageInfo[]): QualityCheckpoint[] => {
  const checkpoints: QualityCheckpoint[] = []

  const qualityStages = [
    ProductionStage.MATERIAL_READY,
    ProductionStage.CP_TESTING,
    ProductionStage.WIRE_BOND,
    ProductionStage.MOLDING,
    ProductionStage.FT_TESTING,
    ProductionStage.FINAL_QC
  ]

  qualityStages.forEach((stage, index) => {
    const stageInfo = stages.find(s => s.stage === stage)
    if (!stageInfo) return

    const checkType =
      stage === ProductionStage.MATERIAL_READY
        ? 'IQC'
        : stage === ProductionStage.FINAL_QC
          ? 'FQC'
          : stage === ProductionStage.FT_TESTING
            ? 'OQC'
            : 'IPQC'

    const status =
      stageInfo.status === StageStatus.COMPLETED
        ? 'passed'
        : stageInfo.status === StageStatus.IN_PROGRESS
          ? 'in_progress'
          : 'pending'

    checkpoints.push({
      id: `qc_${index + 1}`,
      checkpointName: `${stageInfo.stageName}质量检查`,
      stage,
      checkType: checkType as any,
      status: status as any,
      checkParameters: [
        {
          parameter: '外观检查',
          specification: '无明显缺陷',
          actualValue: status === 'passed' ? '合格' : undefined,
          result: status === 'passed' ? 'pass' : 'na'
        },
        {
          parameter: '尺寸测量',
          specification: '±0.1mm',
          actualValue: status === 'passed' ? '0.05mm' : undefined,
          result: status === 'passed' ? 'pass' : 'na'
        },
        {
          parameter: '电性测试',
          specification: '符合规格书',
          actualValue: status === 'passed' ? '合格' : undefined,
          result: status === 'passed' ? 'pass' : 'na'
        }
      ],
      inspector:
        status !== 'pending'
          ? ['质检员A', '质检员B', '质检员C'][Math.floor(Math.random() * 3)]
          : undefined,
      checkTime: status !== 'pending' ? stageInfo.actualEndTime : undefined,
      notes: status === 'passed' ? '检验合格' : status === 'in_progress' ? '检验中' : undefined
    })
  })

  return checkpoints
}

// 生成客户通知记录
const generateNotifications = (orderId: string): CustomerNotification[] => {
  const notifications: CustomerNotification[] = []
  const notificationCount = Math.floor(Math.random() * 3 + 2) // 2-4个通知

  const notificationTemplates = [
    {
      type: 'email' as const,
      subject: '订单生产开始通知',
      content: '您的订单已开始生产，预计完成时间为：{date}'
    },
    {
      type: 'portal' as const,
      subject: '生产进度更新',
      content: '订单当前进度：CP测试已完成，正在进行封装工艺'
    },
    {
      type: 'email' as const,
      subject: '质量检验报告',
      content: '订单质量检验完成，良率达到98%，符合质量要求'
    },
    {
      type: 'sms' as const,
      subject: '订单发货通知',
      content: '您的订单已发货，物流单号：{trackingNumber}'
    }
  ]

  for (let i = 0; i < notificationCount; i++) {
    const template = notificationTemplates[i % notificationTemplates.length]
    const sentAt = new Date(Date.now() - (notificationCount - i) * 24 * 60 * 60 * 1000)

    notifications.push({
      id: `notification_${orderId}_${i + 1}`,
      orderId,
      notificationType: template.type,
      recipientName: '客户联系人',
      recipientContact: template.type === 'email' ? '<EMAIL>' : '+86 138****1234',
      subject: template.subject,
      content: template.content
        .replace('{date}', new Date().toLocaleDateString())
        .replace('{trackingNumber}', 'SF123456789'),
      sentAt: sentAt.toISOString(),
      status: Math.random() > 0.1 ? 'delivered' : 'sent',
      response: Math.random() > 0.7 ? '客户已确认收到' : undefined,
      responseAt:
        Math.random() > 0.7
          ? new Date(sentAt.getTime() + Math.random() * 60 * 60 * 1000).toISOString()
          : undefined
    })
  }

  return notifications.sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())
}

// 生成订单跟踪详细信息
export const generateOrderTrackingDetail = (order: any): OrderTrackingDetail => {
  const stages = PRODUCTION_STAGES.map((stage, index) =>
    generateStageInfo(stage, order.status, index, PRODUCTION_STAGES.length)
  )

  const completedStages = stages.filter(s => s.status === StageStatus.COMPLETED).length
  const currentStageIndex = stages.findIndex(s => s.status === StageStatus.IN_PROGRESS)
  const currentStage =
    currentStageIndex >= 0
      ? stages[currentStageIndex].stage
      : completedStages < stages.length
        ? stages[completedStages].stage
        : ProductionStage.SHIPPING

  const overallProgress = Math.floor((completedStages / stages.length) * 100)

  const qualityCheckpoints = generateQualityCheckpoints(stages)
  const events = generateEvents(order.id)
  const notifications = generateNotifications(order.id)

  // 计算预计完成时间
  const remainingStages = stages.filter(
    s => s.status === StageStatus.PENDING || s.status === StageStatus.IN_PROGRESS
  )
  const remainingDuration = remainingStages.reduce((sum, stage) => sum + stage.standardDuration, 0)
  const estimatedCompletionTime = new Date(Date.now() + remainingDuration * 60 * 1000)

  // 判断是否按时
  const deliveryDate = new Date(order.schedule.deliveryDate)
  const isOnSchedule = estimatedCompletionTime.getTime() <= deliveryDate.getTime()
  const delayDays = isOnSchedule
    ? undefined
    : Math.ceil(
        (estimatedCompletionTime.getTime() - deliveryDate.getTime()) / (24 * 60 * 60 * 1000)
      )

  return {
    id: `tracking_${order.id}`,
    orderNumber: order.orderNumber,
    order,
    productionProgress: {
      currentStage,
      overallProgress,
      stages,
      estimatedCompletionTime: estimatedCompletionTime.toISOString(),
      actualCompletionTime:
        order.status === 'completed'
          ? new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
          : undefined,
      isOnSchedule,
      delayDays
    },
    realTimeData: {
      currentQuantity: Math.floor(order.productInfo.quantity * (overallProgress / 100)),
      remainingQuantity: Math.ceil(order.productInfo.quantity * ((100 - overallProgress) / 100)),
      actualYield: order.qualityInfo.currentYield || Math.floor(Math.random() * 5 + 95),
      targetYield: order.qualityInfo.yieldRequirement,
      defectRate: Math.floor(Math.random() * 50 + 10), // 10-60 ppm
      reworkQuantity: Math.floor(Math.random() * 100 + 10),
      scrapQuantity: Math.floor(Math.random() * 50 + 5)
    },
    equipmentStatus: [
      {
        equipmentId: 'EQ-CP-001',
        equipmentName: 'CP测试机1号',
        status: 'running',
        utilization: Math.floor(Math.random() * 20 + 75),
        currentRecipe: 'CP_STD_V1.2',
        assignedStage: ProductionStage.CP_TESTING
      },
      {
        equipmentId: 'EQ-WB-002',
        equipmentName: '线键合机2号',
        status: currentStage === ProductionStage.WIRE_BOND ? 'running' : 'idle',
        utilization: Math.floor(Math.random() * 30 + 60),
        currentRecipe: 'WB_AU_25um',
        assignedStage: ProductionStage.WIRE_BOND
      },
      {
        equipmentId: 'EQ-MD-003',
        equipmentName: '塑封机3号',
        status: 'maintenance',
        utilization: 0,
        assignedStage: ProductionStage.MOLDING
      }
    ],
    qualityCheckpoints,
    events,
    notifications,
    logistics:
      order.status === 'completed' || overallProgress > 90
        ? {
            packaging: {
              packagingType: '防静电托盘',
              packageQuantity: Math.ceil(order.productInfo.quantity / 1000),
              palletQuantity: Math.ceil(order.productInfo.quantity / 10000),
              packagingDate:
                order.status === 'completed'
                  ? new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
                  : undefined
            },
            shipping: {
              shippingMethod: '陆运快递',
              trackingNumber:
                order.status === 'completed'
                  ? `SF${Math.floor(Math.random() * 1000000000)}`
                  : undefined,
              shippedDate:
                order.status === 'completed'
                  ? new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
                  : undefined,
              estimatedArrival:
                order.status === 'completed'
                  ? new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()
                  : undefined,
              actualArrival: undefined,
              carrier: '顺丰速运'
            }
          }
        : undefined,
    updatedAt: new Date().toISOString()
  }
}

// 生成甘特图数据
export const generateGanttData = (trackingDetail: OrderTrackingDetail): GanttChartData => {
  return {
    orderId: trackingDetail.order.id,
    stages: trackingDetail.productionProgress.stages.map(stage => ({
      stage: stage.stage,
      stageName: stage.stageName,
      plannedStart: stage.plannedStartTime!,
      plannedEnd: stage.plannedEndTime!,
      actualStart: stage.actualStartTime,
      actualEnd: stage.actualEndTime,
      progress: stage.progress,
      status: stage.status,
      dependencies:
        stage.stage === ProductionStage.CP_TESTING
          ? [ProductionStage.MATERIAL_READY]
          : stage.stage === ProductionStage.DICING
            ? [ProductionStage.CP_TESTING]
            : stage.stage === ProductionStage.DIE_ATTACH
              ? [ProductionStage.DICING]
              : undefined
    }))
  }
}

// 生成生产统计数据
export const generateProductionStats = (orderId: string): ProductionStatistics => {
  const baseQuantity = Math.floor(Math.random() * 5000 + 1000)

  return {
    orderId,
    period: 'daily',
    data: Array.from({ length: 7 }, (_, index) => ({
      date: new Date(Date.now() - (6 - index) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      plannedQuantity: baseQuantity,
      actualQuantity: Math.floor(baseQuantity * (0.9 + Math.random() * 0.2)), // 90%-110%
      yieldRate: Math.floor(Math.random() * 5 + 95), // 95%-100%
      defectCount: Math.floor(Math.random() * 50 + 10), // 10-60个
      reworkCount: Math.floor(Math.random() * 20 + 5), // 5-25个
      equipmentUtilization: Math.floor(Math.random() * 20 + 75) // 75%-95%
    }))
  }
}

// 为所有订单生成跟踪数据
export const allOrderTrackingDetails: OrderTrackingDetail[] = allMockOrders
  .filter(order => order.status !== 'cancelled' && order.status !== 'pending')
  .map(order => generateOrderTrackingDetail(order))

// 根据订单ID获取跟踪详情
export const getOrderTrackingDetail = (orderId: string): OrderTrackingDetail | undefined => {
  return allOrderTrackingDetails.find(detail => detail.order.id === orderId)
}

// 根据订单编号获取跟踪详情
export const getOrderTrackingByNumber = (orderNumber: string): OrderTrackingDetail | undefined => {
  return allOrderTrackingDetails.find(detail => detail.orderNumber === orderNumber)
}

// 获取延迟订单
export const getDelayedOrders = (): OrderTrackingDetail[] => {
  return allOrderTrackingDetails.filter(detail => !detail.productionProgress.isOnSchedule)
}

// 获取有异常事件的订单
export const getOrdersWithEvents = (): OrderTrackingDetail[] => {
  return allOrderTrackingDetails.filter(detail =>
    detail.events.some(
      event => event.status === 'open' || event.severity === EventSeverity.CRITICAL
    )
  )
}

// 统计数据
export const trackingStats = {
  totalTracking: allOrderTrackingDetails.length,
  onSchedule: allOrderTrackingDetails.filter(detail => detail.productionProgress.isOnSchedule)
    .length,
  delayed: allOrderTrackingDetails.filter(detail => !detail.productionProgress.isOnSchedule).length,
  withEvents: allOrderTrackingDetails.filter(detail => detail.events.length > 0).length,
  averageProgress: Math.round(
    allOrderTrackingDetails.reduce(
      (sum, detail) => sum + detail.productionProgress.overallProgress,
      0
    ) / allOrderTrackingDetails.length
  )
}
