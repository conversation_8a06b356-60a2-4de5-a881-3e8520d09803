/**
 * IC封测CIM系统 - 用户管理Mock数据
 * User Management Mock Data for IC Packaging & Testing CIM System
 */

import type {
  UserInfo,
  Department,
  RoleConfig,
  PermissionConfig,
  UserStatistics,
  OnlineUser,
  UserOperationLog,
  ICRoles,
  ICPermissions
} from '@/types/user'
import { <PERSON>ck<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/api/config'

/**
 * Mock用户数据
 */
export const mockUsers: UserInfo[] = [
  {
    id: 'user_001',
    username: 'admin',
    email: '<EMAIL>',
    phone: '13800138000',
    realName: '王系统管理员',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    department: 'IT信息部',
    position: '系统管理员',
    roles: [ICRoles.SYSTEM_ADMIN],
    permissions: [
      ICPermissions.SYSTEM_READ,
      ICPermissions.SYSTEM_WRITE,
      ICPermissions.SYSTEM_DELETE,
      ICPermissions.USER_READ,
      ICPermissions.USER_WRITE,
      ICPermissions.USER_DELETE
    ],
    lastLoginTime: new Date().toISOString(),
    lastLoginIp: '*************',
    status: 'active',
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'user_002',
    username: 'process_eng01',
    email: '<EMAIL>',
    phone: '13800138001',
    realName: '张工艺工程师',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    department: '工艺工程部',
    position: '高级工艺工程师',
    roles: [ICRoles.PROCESS_ENGINEER],
    permissions: [
      ICPermissions.PRODUCTION_READ,
      ICPermissions.PRODUCTION_WRITE,
      ICPermissions.CP_READ,
      ICPermissions.CP_CONFIG,
      ICPermissions.ASSEMBLY_READ,
      ICPermissions.ASSEMBLY_CONFIG,
      ICPermissions.FT_READ,
      ICPermissions.FT_CONFIG
    ],
    lastLoginTime: new Date(Date.now() - 3600000).toISOString(),
    lastLoginIp: '*************',
    status: 'active',
    createTime: '2024-01-02T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'user_003',
    username: 'prod_op01',
    email: '<EMAIL>',
    phone: '13800138002',
    realName: '李生产操作员',
    avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    department: '生产制造部',
    position: 'CP测试操作员',
    roles: [ICRoles.PRODUCTION_OPERATOR],
    permissions: [
      ICPermissions.PRODUCTION_READ,
      ICPermissions.CP_READ,
      ICPermissions.CP_OPERATE,
      ICPermissions.EQUIPMENT_READ
    ],
    lastLoginTime: new Date(Date.now() - 7200000).toISOString(),
    lastLoginIp: '*************',
    status: 'active',
    createTime: '2024-01-03T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'user_004',
    username: 'test_eng01',
    email: '<EMAIL>',
    phone: '13800138003',
    realName: '赵测试工程师',
    avatar: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    department: '测试工程部',
    position: '测试工程师',
    roles: [ICRoles.TEST_ENGINEER],
    permissions: [
      ICPermissions.CP_READ,
      ICPermissions.CP_CONFIG,
      ICPermissions.FT_READ,
      ICPermissions.FT_CONFIG,
      ICPermissions.EQUIPMENT_READ,
      ICPermissions.QUALITY_READ
    ],
    lastLoginTime: new Date(Date.now() - 86400000).toISOString(),
    lastLoginIp: '*************',
    status: 'active',
    createTime: '2024-01-04T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'user_005',
    username: 'quality_eng01',
    email: '<EMAIL>',
    phone: '13800138004',
    realName: '钱质量工程师',
    avatar: '',
    department: '质量管理部',
    position: '质量工程师',
    roles: [ICRoles.QUALITY_ENGINEER],
    permissions: [
      ICPermissions.QUALITY_READ,
      ICPermissions.QUALITY_WRITE,
      ICPermissions.SPC_CONFIG,
      ICPermissions.QUALITY_AUDIT
    ],
    lastLoginTime: new Date(Date.now() - 172800000).toISOString(),
    lastLoginIp: '*************',
    status: 'active',
    createTime: '2024-01-05T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'user_006',
    username: 'qc_inspector01',
    email: '<EMAIL>',
    phone: '13800138005',
    realName: '孙质检员',
    avatar: '',
    department: '质量管理部',
    position: 'QC检验员',
    roles: [ICRoles.QC_INSPECTOR],
    permissions: [ICPermissions.QUALITY_READ, ICPermissions.QUALITY_WRITE],
    lastLoginTime: null,
    lastLoginIp: null,
    status: 'inactive',
    createTime: '2024-01-06T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'user_007',
    username: 'equipment_eng01',
    email: '<EMAIL>',
    phone: '13800138006',
    realName: '周设备工程师',
    avatar: '',
    department: '设备工程部',
    position: '设备工程师',
    roles: [ICRoles.EQUIPMENT_ENGINEER],
    permissions: [
      ICPermissions.EQUIPMENT_READ,
      ICPermissions.EQUIPMENT_WRITE,
      ICPermissions.EQUIPMENT_CONTROL,
      ICPermissions.SECS_GEM_CONFIG
    ],
    lastLoginTime: new Date(Date.now() - 3600000).toISOString(),
    lastLoginIp: '*************',
    status: 'active',
    createTime: '2024-01-07T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'user_008',
    username: 'warehouse_mgr01',
    email: '<EMAIL>',
    phone: '13800138007',
    realName: '吴仓库经理',
    avatar: '',
    department: '物料管理部',
    position: '仓库经理',
    roles: [ICRoles.WAREHOUSE_MANAGER],
    permissions: [
      ICPermissions.MATERIAL_READ,
      ICPermissions.MATERIAL_WRITE,
      ICPermissions.INVENTORY_CONTROL,
      ICPermissions.WAREHOUSE_OPERATE
    ],
    lastLoginTime: new Date(Date.now() - 10800000).toISOString(),
    lastLoginIp: '*************',
    status: 'active',
    createTime: '2024-01-08T00:00:00.000Z',
    updateTime: new Date().toISOString()
  }
]

/**
 * Mock部门数据
 */
export const mockDepartments: Department[] = [
  {
    id: 'dept_001',
    code: 'IT',
    name: 'IT信息部',
    level: 1,
    sort: 1,
    description: '负责信息化建设、系统维护和数据管理',
    status: 'active',
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'dept_002',
    code: 'PROCESS_ENG',
    name: '工艺工程部',
    level: 1,
    sort: 2,
    description: '负责封装工艺开发、优化和技术支持',
    status: 'active',
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'dept_003',
    code: 'PRODUCTION',
    name: '生产制造部',
    level: 1,
    sort: 3,
    description: '负责IC封装生产执行和产线管理',
    status: 'active',
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'dept_004',
    code: 'TEST_ENG',
    name: '测试工程部',
    level: 1,
    sort: 4,
    description: '负责CP测试、FT测试工艺和设备管理',
    status: 'active',
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'dept_005',
    code: 'QUALITY',
    name: '质量管理部',
    level: 1,
    sort: 5,
    description: '负责质量控制、SPC管理和客户质量要求',
    status: 'active',
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'dept_006',
    code: 'EQUIPMENT',
    name: '设备工程部',
    level: 1,
    sort: 6,
    description: '负责设备维护、SECS/GEM集成和预防保养',
    status: 'active',
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'dept_007',
    code: 'MATERIAL',
    name: '物料管理部',
    level: 1,
    sort: 7,
    description: '负责原料采购、库存管理和供应链协调',
    status: 'active',
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    id: 'dept_008',
    code: 'CUSTOMER_SERVICE',
    name: '客户服务部',
    level: 1,
    sort: 8,
    description: '负责客户关系管理、订单跟踪和售后服务',
    status: 'active',
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  }
]

/**
 * Mock角色数据
 */
export const mockRoles: RoleConfig[] = [
  {
    code: ICRoles.SYSTEM_ADMIN,
    name: '系统管理员',
    permissions: [
      ICPermissions.SYSTEM_READ,
      ICPermissions.SYSTEM_WRITE,
      ICPermissions.SYSTEM_DELETE,
      ICPermissions.USER_READ,
      ICPermissions.USER_WRITE,
      ICPermissions.USER_DELETE,
      ICPermissions.USER_ROLE_ASSIGN
    ],
    description: '系统管理员，拥有系统管理和用户管理的所有权限',
    level: 1,
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    code: ICRoles.PROCESS_ENGINEER,
    name: '工艺工程师',
    permissions: [
      ICPermissions.PRODUCTION_READ,
      ICPermissions.PRODUCTION_WRITE,
      ICPermissions.CP_READ,
      ICPermissions.CP_CONFIG,
      ICPermissions.ASSEMBLY_READ,
      ICPermissions.ASSEMBLY_CONFIG,
      ICPermissions.FT_READ,
      ICPermissions.FT_CONFIG,
      ICPermissions.QUALITY_READ
    ],
    description: '工艺工程师，负责封装和测试工艺的开发与优化',
    level: 2,
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    code: ICRoles.TEST_ENGINEER,
    name: '测试工程师',
    permissions: [
      ICPermissions.CP_READ,
      ICPermissions.CP_CONFIG,
      ICPermissions.FT_READ,
      ICPermissions.FT_CONFIG,
      ICPermissions.EQUIPMENT_READ,
      ICPermissions.QUALITY_READ
    ],
    description: '测试工程师，专注于CP和FT测试技术和设备管理',
    level: 2,
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    code: ICRoles.PRODUCTION_OPERATOR,
    name: '生产操作员',
    permissions: [
      ICPermissions.PRODUCTION_READ,
      ICPermissions.CP_READ,
      ICPermissions.CP_OPERATE,
      ICPermissions.ASSEMBLY_READ,
      ICPermissions.ASSEMBLY_OPERATE,
      ICPermissions.FT_READ,
      ICPermissions.FT_OPERATE,
      ICPermissions.EQUIPMENT_READ
    ],
    description: '生产操作员，负责生产设备的日常操作',
    level: 3,
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    code: ICRoles.QUALITY_ENGINEER,
    name: '质量工程师',
    permissions: [
      ICPermissions.QUALITY_READ,
      ICPermissions.QUALITY_WRITE,
      ICPermissions.SPC_CONFIG,
      ICPermissions.QUALITY_AUDIT,
      ICPermissions.PRODUCTION_READ
    ],
    description: '质量工程师，负责质量体系建设和过程控制',
    level: 2,
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    code: ICRoles.QC_INSPECTOR,
    name: '质量检验员',
    permissions: [ICPermissions.QUALITY_READ, ICPermissions.QUALITY_WRITE],
    description: '质量检验员，负责产品质量检验和记录',
    level: 3,
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    code: ICRoles.EQUIPMENT_ENGINEER,
    name: '设备工程师',
    permissions: [
      ICPermissions.EQUIPMENT_READ,
      ICPermissions.EQUIPMENT_WRITE,
      ICPermissions.EQUIPMENT_CONTROL,
      ICPermissions.EQUIPMENT_MAINTENANCE,
      ICPermissions.SECS_GEM_CONFIG
    ],
    description: '设备工程师，负责设备维护和SECS/GEM集成',
    level: 2,
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  },
  {
    code: ICRoles.WAREHOUSE_MANAGER,
    name: '仓库经理',
    permissions: [
      ICPermissions.MATERIAL_READ,
      ICPermissions.MATERIAL_WRITE,
      ICPermissions.INVENTORY_CONTROL,
      ICPermissions.WAREHOUSE_OPERATE
    ],
    description: '仓库经理，负责物料和库存管理',
    level: 2,
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: new Date().toISOString()
  }
]

/**
 * Mock权限数据
 */
export const mockPermissions: PermissionConfig[] = [
  // 系统管理权限
  {
    code: ICPermissions.SYSTEM_READ,
    name: '系统查看',
    type: 'menu',
    description: '查看系统管理菜单和基本信息',
    level: 1,
    sort: 1
  },
  {
    code: ICPermissions.SYSTEM_WRITE,
    name: '系统配置',
    type: 'button',
    parentCode: ICPermissions.SYSTEM_READ,
    description: '修改系统配置和参数',
    level: 2,
    sort: 2
  },
  {
    code: ICPermissions.SYSTEM_DELETE,
    name: '系统数据删除',
    type: 'button',
    parentCode: ICPermissions.SYSTEM_READ,
    description: '删除系统数据（危险操作）',
    level: 2,
    sort: 3
  },

  // 用户管理权限
  {
    code: ICPermissions.USER_READ,
    name: '用户查看',
    type: 'menu',
    description: '查看用户管理菜单和用户列表',
    level: 1,
    sort: 10
  },
  {
    code: ICPermissions.USER_WRITE,
    name: '用户编辑',
    type: 'button',
    parentCode: ICPermissions.USER_READ,
    description: '新增、编辑用户信息',
    level: 2,
    sort: 11
  },
  {
    code: ICPermissions.USER_DELETE,
    name: '用户删除',
    type: 'button',
    parentCode: ICPermissions.USER_READ,
    description: '删除用户账号',
    level: 2,
    sort: 12
  },
  {
    code: ICPermissions.USER_ROLE_ASSIGN,
    name: '用户角色分配',
    type: 'button',
    parentCode: ICPermissions.USER_READ,
    description: '为用户分配和撤销角色',
    level: 2,
    sort: 13
  },

  // 生产管理权限
  {
    code: ICPermissions.PRODUCTION_READ,
    name: '生产查看',
    type: 'menu',
    description: '查看生产管理相关信息',
    level: 1,
    sort: 20
  },
  {
    code: ICPermissions.PRODUCTION_WRITE,
    name: '生产操作',
    type: 'button',
    parentCode: ICPermissions.PRODUCTION_READ,
    description: '执行生产相关操作',
    level: 2,
    sort: 21
  },

  // CP测试权限
  {
    code: ICPermissions.CP_READ,
    name: 'CP测试查看',
    type: 'menu',
    description: '查看CP测试相关信息',
    level: 1,
    sort: 30
  },
  {
    code: ICPermissions.CP_OPERATE,
    name: 'CP测试操作',
    type: 'button',
    parentCode: ICPermissions.CP_READ,
    description: '执行CP测试操作',
    level: 2,
    sort: 31
  },
  {
    code: ICPermissions.CP_CONFIG,
    name: 'CP测试配置',
    type: 'button',
    parentCode: ICPermissions.CP_READ,
    description: '配置CP测试参数和工艺',
    level: 2,
    sort: 32
  },

  // 封装工艺权限
  {
    code: ICPermissions.ASSEMBLY_READ,
    name: '封装查看',
    type: 'menu',
    description: '查看封装工艺相关信息',
    level: 1,
    sort: 40
  },
  {
    code: ICPermissions.ASSEMBLY_OPERATE,
    name: '封装操作',
    type: 'button',
    parentCode: ICPermissions.ASSEMBLY_READ,
    description: '执行封装工艺操作',
    level: 2,
    sort: 41
  },
  {
    code: ICPermissions.ASSEMBLY_CONFIG,
    name: '封装配置',
    type: 'button',
    parentCode: ICPermissions.ASSEMBLY_READ,
    description: '配置封装工艺参数',
    level: 2,
    sort: 42
  },

  // 最终测试权限
  {
    code: ICPermissions.FT_READ,
    name: '最终测试查看',
    type: 'menu',
    description: '查看最终测试相关信息',
    level: 1,
    sort: 50
  },
  {
    code: ICPermissions.FT_OPERATE,
    name: '最终测试操作',
    type: 'button',
    parentCode: ICPermissions.FT_READ,
    description: '执行最终测试操作',
    level: 2,
    sort: 51
  },
  {
    code: ICPermissions.FT_CONFIG,
    name: '最终测试配置',
    type: 'button',
    parentCode: ICPermissions.FT_READ,
    description: '配置最终测试参数',
    level: 2,
    sort: 52
  },

  // 设备管理权限
  {
    code: ICPermissions.EQUIPMENT_READ,
    name: '设备查看',
    type: 'menu',
    description: '查看设备管理相关信息',
    level: 1,
    sort: 60
  },
  {
    code: ICPermissions.EQUIPMENT_WRITE,
    name: '设备编辑',
    type: 'button',
    parentCode: ICPermissions.EQUIPMENT_READ,
    description: '编辑设备信息',
    level: 2,
    sort: 61
  },
  {
    code: ICPermissions.EQUIPMENT_CONTROL,
    name: '设备控制',
    type: 'button',
    parentCode: ICPermissions.EQUIPMENT_READ,
    description: '控制设备运行状态',
    level: 2,
    sort: 62
  },
  {
    code: ICPermissions.EQUIPMENT_MAINTENANCE,
    name: '设备维护',
    type: 'button',
    parentCode: ICPermissions.EQUIPMENT_READ,
    description: '执行设备维护操作',
    level: 2,
    sort: 63
  },
  {
    code: ICPermissions.SECS_GEM_CONFIG,
    name: 'SECS/GEM配置',
    type: 'button',
    parentCode: ICPermissions.EQUIPMENT_READ,
    description: '配置SECS/GEM通信',
    level: 2,
    sort: 64
  },

  // 质量管理权限
  {
    code: ICPermissions.QUALITY_READ,
    name: '质量查看',
    type: 'menu',
    description: '查看质量管理相关信息',
    level: 1,
    sort: 70
  },
  {
    code: ICPermissions.QUALITY_WRITE,
    name: '质量记录',
    type: 'button',
    parentCode: ICPermissions.QUALITY_READ,
    description: '记录质量数据',
    level: 2,
    sort: 71
  },
  {
    code: ICPermissions.SPC_CONFIG,
    name: 'SPC配置',
    type: 'button',
    parentCode: ICPermissions.QUALITY_READ,
    description: '配置SPC控制参数',
    level: 2,
    sort: 72
  },
  {
    code: ICPermissions.QUALITY_AUDIT,
    name: '质量审核',
    type: 'button',
    parentCode: ICPermissions.QUALITY_READ,
    description: '执行质量审核操作',
    level: 2,
    sort: 73
  },

  // 物料管理权限
  {
    code: ICPermissions.MATERIAL_READ,
    name: '物料查看',
    type: 'menu',
    description: '查看物料管理相关信息',
    level: 1,
    sort: 80
  },
  {
    code: ICPermissions.MATERIAL_WRITE,
    name: '物料编辑',
    type: 'button',
    parentCode: ICPermissions.MATERIAL_READ,
    description: '编辑物料信息',
    level: 2,
    sort: 81
  },
  {
    code: ICPermissions.INVENTORY_CONTROL,
    name: '库存控制',
    type: 'button',
    parentCode: ICPermissions.MATERIAL_READ,
    description: '控制库存出入库',
    level: 2,
    sort: 82
  },
  {
    code: ICPermissions.WAREHOUSE_OPERATE,
    name: '仓库操作',
    type: 'button',
    parentCode: ICPermissions.MATERIAL_READ,
    description: '执行仓库相关操作',
    level: 2,
    sort: 83
  },

  // 报表权限
  {
    code: ICPermissions.REPORT_READ,
    name: '报表查看',
    type: 'menu',
    description: '查看各类报表',
    level: 1,
    sort: 90
  },
  {
    code: ICPermissions.REPORT_EXPORT,
    name: '报表导出',
    type: 'button',
    parentCode: ICPermissions.REPORT_READ,
    description: '导出报表数据',
    level: 2,
    sort: 91
  },

  // 监控权限
  {
    code: ICPermissions.MONITORING_READ,
    name: '监控查看',
    type: 'menu',
    description: '查看监控大屏',
    level: 1,
    sort: 100
  }
]

/**
 * Mock用户统计数据
 */
export const mockUserStatistics: UserStatistics = {
  totalUsers: mockUsers.length,
  activeUsers: mockUsers.filter(u => u.status === 'active').length,
  inactiveUsers: mockUsers.filter(u => u.status === 'inactive').length,
  lockedUsers: mockUsers.filter(u => u.status === 'locked').length,
  onlineUsers: 5,
  newUsersToday: 2,
  newUsersThisMonth: 8,
  departmentStats: [
    { department: 'IT信息部', count: 1 },
    { department: '工艺工程部', count: 1 },
    { department: '生产制造部', count: 1 },
    { department: '测试工程部', count: 1 },
    { department: '质量管理部', count: 2 },
    { department: '设备工程部', count: 1 },
    { department: '物料管理部', count: 1 }
  ],
  roleStats: [
    { role: '系统管理员', count: 1 },
    { role: '工艺工程师', count: 1 },
    { role: '测试工程师', count: 1 },
    { role: '生产操作员', count: 1 },
    { role: '质量工程师', count: 1 },
    { role: '质量检验员', count: 1 },
    { role: '设备工程师', count: 1 },
    { role: '仓库经理', count: 1 }
  ]
}

/**
 * Mock在线用户数据
 */
export const mockOnlineUsers: OnlineUser[] = [
  {
    userId: 'user_001',
    username: 'admin',
    realName: '王系统管理员',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    department: 'IT信息部',
    position: '系统管理员',
    loginTime: new Date(Date.now() - 3600000).toISOString(),
    lastActiveTime: new Date().toISOString(),
    ip: '*************',
    location: '工厂内网',
    browser: 'Chrome',
    os: 'Windows 11'
  },
  {
    userId: 'user_002',
    username: 'process_eng01',
    realName: '张工艺工程师',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    department: '工艺工程部',
    position: '高级工艺工程师',
    loginTime: new Date(Date.now() - 1800000).toISOString(),
    lastActiveTime: new Date(Date.now() - 300000).toISOString(),
    ip: '*************',
    location: '工厂内网',
    browser: 'Firefox',
    os: 'Windows 10'
  }
]

/**
 * Mock用户操作日志数据
 */
export const mockUserOperationLogs: UserOperationLog[] = [
  {
    id: 'log_001',
    userId: 'user_001',
    username: 'admin',
    operation: '用户登录',
    module: '认证系统',
    description: '管理员账号登录系统',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    status: 'success',
    createTime: new Date().toISOString()
  },
  {
    id: 'log_002',
    userId: 'user_001',
    username: 'admin',
    operation: '创建用户',
    module: '用户管理',
    description: '创建新用户账号：test_user',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    status: 'success',
    createTime: new Date(Date.now() - 300000).toISOString()
  },
  {
    id: 'log_003',
    userId: 'user_002',
    username: 'process_eng01',
    operation: '查看生产数据',
    module: '生产管理',
    description: '查看CP测试生产数据',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    status: 'success',
    createTime: new Date(Date.now() - 600000).toISOString()
  }
]
