// IC封测CIM系统 - 极简主义样式入口文件
// 按照依赖顺序导入所有样式模块

// 1. 主题变量（最高优先级）
@use './themes/variables';

// 2. 基础样式
@use './base/reset';
@use './base/mixins';
@use './base/utilities';

// 3. 组件样式（按需导入）
// @use './components/button.scss';
// @use './components/form.scss';
// @use './components/card.scss';
// @use './components/table.scss';
// @use './components/modal.scss';

// 4. 布局样式
// @use './layout/header.scss';
// @use './layout/sidebar.scss';
// @use './layout/main.scss';
// @use './layout/footer.scss';

// 5. 页面特定样式
// @use './pages/dashboard.scss';
// @use './pages/orders.scss';
// @use './pages/manufacturing.scss';

// 6. 第三方组件库主题覆盖
// Element Plus 主题将通过 Vite 插件自动处理
// Vant 主题通过 CSS 变量覆盖

// 7. 响应式布局
@media (width <= 768px) {
  .desktop-only {
    display: none !important;
  }
}

@media (width >= 769px) {
  .mobile-only {
    display: none !important;
  }
}

// 8. 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    color: black !important;
    background: white !important;
  }
}

// 9. 高对比度模式支持
@media (prefers-contrast: high) {
  :root {
    --color-border-base: #000;
    --color-text-primary: #000;
  }
  
  .dark-theme {
    --color-border-base: #fff;
    --color-text-primary: #fff;
  }
}

// 10. 减少动画偏好设置
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
  }
}

// 11. 全局辅助类
.theme-transition {
  transition: background-color var(--transition-normal), 
              color var(--transition-normal),
              border-color var(--transition-normal);
}

.page-container {
  min-height: 100vh;
  color: var(--color-text-primary);
  background: var(--color-bg-primary);
}

.content-wrapper {
  max-width: 1200px;
  padding: 0 var(--spacing-4);
  margin: 0 auto;
}

// 12. IC封测专业样式类
.wafer-map {
  background: var(--color-wafer);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-base);
}

.die-status-pass {
  color: var(--color-success);
  background: var(--color-die-pass);
}

.die-status-fail {
  color: var(--color-error);
  background: var(--color-die-fail);
}

.process-cp {
  border-left: 4px solid var(--color-cp-test);
}

.process-assembly {
  border-left: 4px solid var(--color-assembly);
}

.process-ft {
  border-left: 4px solid var(--color-ft-test);
}