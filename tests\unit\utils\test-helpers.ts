import { mount, shallowMount, VueWrapper } from '@vue/test-utils'
import { createPinia, type Pinia } from 'pinia'
import { createRouter, createWebHistory, type Router } from 'vue-router'
import { nextTick } from 'vue'
import { vi, type MockedFunction } from 'vitest'
import type { ComponentPublicInstance } from 'vue'

// 测试路由配置
const testRoutes = [
  { path: '/', name: 'Home', component: { template: '<div>Home</div>' } },
  { path: '/orders', name: 'Orders', component: { template: '<div>Orders</div>' } },
  { path: '/production', name: 'Production', component: { template: '<div>Production</div>' } },
  { path: '/quality', name: 'Quality', component: { template: '<div>Quality</div>' } },
  { path: '/equipment', name: 'Equipment', component: { template: '<div>Equipment</div>' } }
]

// 创建测试路由
export function createTestRouter(): Router {
  return createRouter({
    history: createWebHistory(),
    routes: testRoutes
  })
}

// 创建测试Pinia实例
export function createTestPinia(): Pinia {
  return createPinia()
}

// 测试挂载选项
export interface TestMountOptions {
  shallow?: boolean
  router?: Router
  pinia?: Pinia
  global?: {
    plugins?: any[]
    provide?: Record<string, any>
    stubs?: Record<string, any>
    mocks?: Record<string, any>
  }
  props?: Record<string, any>
  slots?: Record<string, any>
  attachTo?: Element
}

// 增强的挂载函数
export function mountComponent(
  component: any,
  options: TestMountOptions = {}
): VueWrapper<ComponentPublicInstance> {
  const {
    shallow = false,
    router = createTestRouter(),
    pinia = createTestPinia(),
    global = {},
    ...restOptions
  } = options

  const mountFn = shallow ? shallowMount : mount

  const defaultGlobal = {
    plugins: [router, pinia],
    provide: {},
    stubs: {},
    mocks: {},
    ...global
  }

  if (global.plugins) {
    defaultGlobal.plugins = [...defaultGlobal.plugins, ...global.plugins]
  }

  return mountFn(component, {
    global: defaultGlobal,
    ...restOptions
  })
}

// 异步等待DOM更新
export async function flushPromises(): Promise<void> {
  await nextTick()
  await new Promise(resolve => setTimeout(resolve, 0))
}

// 等待组件更新
export async function waitForUpdate(wrapper: VueWrapper): Promise<void> {
  await wrapper.vm.$nextTick()
  await flushPromises()
}

// 触发组件事件并等待更新
export async function triggerAndWait(
  wrapper: VueWrapper,
  selector: string,
  event: string,
  payload?: any
): Promise<void> {
  const element = wrapper.find(selector)
  await element.trigger(event, payload)
  await waitForUpdate(wrapper)
}

// Mock API响应
export interface MockApiResponse<T = any> {
  data?: T
  code?: number
  message?: string
  success?: boolean
}

export function createMockApiResponse<T = any>(
  data: T,
  options: Partial<MockApiResponse> = {}
): MockApiResponse<T> {
  return {
    data,
    code: 200,
    message: 'success',
    success: true,
    ...options
  }
}

// 错误API响应
export function createMockApiError(
  message: string = 'API Error',
  code: number = 500
): MockApiResponse {
  return {
    data: null,
    code,
    message,
    success: false
  }
}

// Mock WebSocket连接
export class MockWebSocket {
  url: string
  readyState: number = WebSocket.CONNECTING
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null

  constructor(url: string) {
    this.url = url
    // 模拟连接建立
    setTimeout(() => {
      this.readyState = WebSocket.OPEN
      if (this.onopen) {
        this.onopen(new Event('open'))
      }
    }, 100)
  }

  send(data: string | ArrayBufferLike | Blob | ArrayBufferView): void {
    // 模拟发送数据
  }

  close(): void {
    this.readyState = WebSocket.CLOSED
    if (this.onclose) {
      this.onclose(new CloseEvent('close'))
    }
  }

  // 模拟接收消息
  simulateMessage(data: any): void {
    if (this.onmessage) {
      this.onmessage(new MessageEvent('message', { data }))
    }
  }
}

// 测试数据工厂
export class TestDataFactory {
  // 生成订单数据
  static createOrder(overrides: Partial<any> = {}) {
    return {
      id: Math.random().toString(36).substr(2, 9),
      orderNumber: `ORD-${Date.now()}`,
      customerName: 'Test Customer',
      productName: 'IC-TEST-001',
      quantity: 10000,
      status: 'pending',
      priority: 'normal',
      createTime: new Date().toISOString(),
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      ...overrides
    }
  }

  // 生成设备数据
  static createEquipment(overrides: Partial<any> = {}) {
    return {
      id: Math.random().toString(36).substr(2, 9),
      name: 'Test Equipment',
      type: 'TESTER',
      status: 'running',
      utilization: 85.5,
      location: 'Line-01',
      lastMaintenance: new Date().toISOString(),
      ...overrides
    }
  }

  // 生成用户数据
  static createUser(overrides: Partial<any> = {}) {
    return {
      id: Math.random().toString(36).substr(2, 9),
      username: 'testuser',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'operator',
      department: 'production',
      ...overrides
    }
  }

  // 生成图表数据
  static createChartData(length: number = 10) {
    return Array.from({ length }, (_, i) => ({
      time: new Date(Date.now() - (length - i) * 60000).toISOString(),
      value: Math.floor(Math.random() * 100)
    }))
  }
}

// 等待特定条件
export async function waitForCondition(
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> {
  const startTime = Date.now()

  while (Date.now() - startTime < timeout) {
    if (await condition()) {
      return
    }
    await new Promise(resolve => setTimeout(resolve, interval))
  }

  throw new Error(`Condition not met within ${timeout}ms`)
}

// 创建Mock函数的类型断言
export function asMockedFunction<T extends (...args: any[]) => any>(fn: T): MockedFunction<T> {
  return fn as MockedFunction<T>
}
