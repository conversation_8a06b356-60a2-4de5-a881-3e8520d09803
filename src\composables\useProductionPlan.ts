/**
 * 生产计划管理组合函数 - 第一阶段严格规划
 * 严格按照CLAUDE.md第一阶段要求实现：
 * - Master production scheduling optimized for OSAT operations
 * - Work order management for CP/Assembly/FT processes
 * - Capacity planning based on test and assembly equipment availability
 */

import { ref, reactive, computed } from 'vue'
import type { ProductionPlan, WorkOrder, OrderLifecycle, PackageType } from '@/types/order'
import { WorkOrderType, WorkOrderStatus, OrderPriority } from '@/types/order'
import {
  productionPlans,
  workOrders,
  orderLifecycles,
  capacityData
} from '@/utils/mockData/productionPlan'

// ===== 主生产计划管理 (Master Production Scheduling) =====

export function useProductionPlan() {
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 生产计划数据
  const plans = ref<ProductionPlan[]>([])
  const currentPlan = ref<ProductionPlan | null>(null)

  // 获取所有生产计划
  const loadProductionPlans = async () => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      plans.value = productionPlans
    } catch (err) {
      error.value = '加载生产计划失败'
      console.error('生产计划加载错误:', err)
    } finally {
      loading.value = false
    }
  }

  // 创建新的生产计划 - OSAT优化
  const createProductionPlan = async (planData: Partial<ProductionPlan>) => {
    loading.value = true

    try {
      // 自动分析产能和设备分配
      const capacityAnalysis = analyzeCapacityRequirements(planData.orderGroups || [])
      const equipmentAllocation = allocateEquipment(planData.orderGroups || [])

      const newPlan: ProductionPlan = {
        id: `PP${Date.now()}`,
        planNumber: generatePlanNumber(),
        planName: planData.planName || '',
        planPeriod: planData.planPeriod || {
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          planType: 'weekly'
        },
        orderGroups: planData.orderGroups || [],
        capacityPlan: capacityAnalysis,
        equipmentAllocation: equipmentAllocation,
        status: 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      plans.value.push(newPlan)
      return newPlan
    } catch (err) {
      error.value = '创建生产计划失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 产能需求分析 - 基于测试和封装设备可用性
  const analyzeCapacityRequirements = (orderGroups: ProductionPlan['orderGroups']) => {
    const totalQuantity = orderGroups.reduce((sum, group) => sum + group.totalQuantity, 0)

    // 基于历史数据和设备能力计算产能需求
    const cpTestingCapacity = Math.ceil(totalQuantity * 1.1) // CP测试需要110%产能（考虑重测）
    const assemblyCapacity = Math.ceil(totalQuantity * 1.02) // 封装需要102%产能（考虑良率）
    const ftTestingCapacity = Math.ceil(totalQuantity * 1.05) // FT测试需要105%产能

    // 识别瓶颈工序
    const processes = [
      { name: 'cp_testing', capacity: cpTestingCapacity },
      { name: 'assembly', capacity: assemblyCapacity },
      { name: 'ft_testing', capacity: ftTestingCapacity }
    ]

    const bottleneck = processes.reduce((max, process) =>
      process.capacity > max.capacity ? process : max
    ).name

    return {
      cpTestingCapacity,
      assemblyCapacity,
      ftTestingCapacity,
      bottleneckProcess: bottleneck
    }
  }

  // 设备分配优化 - OSAT专业设备调度
  const allocateEquipment = (orderGroups: ProductionPlan['orderGroups']) => {
    const allocation: ProductionPlan['equipmentAllocation'] = []

    // 基于订单组的封装类型选择合适设备
    const packageTypes = [...new Set(orderGroups.flatMap(group => group.packageTypes))]

    // CP测试设备分配
    const cpEquipment = capacityData.equipmentCapacity
      .filter(eq => eq.processType === 'cp_testing')
      .filter(eq => packageTypes.some(pkg => eq.supportedPackages.includes(pkg)))

    if (cpEquipment.length > 0) {
      allocation.push({
        processType: WorkOrderType.CP_TESTING,
        equipmentIds: cpEquipment.map(eq => eq.equipmentId),
        allocatedHours: 168, // 7天 * 24小时
        utilization: calculateUtilization(cpEquipment, orderGroups)
      })
    }

    // 封装设备分配
    const asmEquipment = capacityData.equipmentCapacity
      .filter(eq => eq.processType === 'assembly')
      .filter(eq => packageTypes.some(pkg => eq.supportedPackages.includes(pkg)))

    if (asmEquipment.length > 0) {
      allocation.push({
        processType: WorkOrderType.ASSEMBLY,
        equipmentIds: asmEquipment.map(eq => eq.equipmentId),
        allocatedHours: 168,
        utilization: calculateUtilization(asmEquipment, orderGroups)
      })
    }

    // FT测试设备分配
    const ftEquipment = capacityData.equipmentCapacity
      .filter(eq => eq.processType === 'ft_testing')
      .filter(eq => packageTypes.some(pkg => eq.supportedPackages.includes(pkg)))

    if (ftEquipment.length > 0) {
      allocation.push({
        processType: WorkOrderType.FT_TESTING,
        equipmentIds: ftEquipment.map(eq => eq.equipmentId),
        allocatedHours: 168,
        utilization: calculateUtilization(ftEquipment, orderGroups)
      })
    }

    return allocation
  }

  // 计算设备利用率
  const calculateUtilization = (equipment: any[], orderGroups: ProductionPlan['orderGroups']) => {
    const totalCapacity = equipment.reduce((sum, eq) => sum + eq.dailyCapacity, 0) * 7 // 周产能
    const totalDemand = orderGroups.reduce((sum, group) => sum + group.totalQuantity, 0)
    return Math.min(95, Math.ceil((totalDemand / totalCapacity) * 100)) // 最大95%利用率
  }

  // 生成计划编号
  const generatePlanNumber = () => {
    const now = new Date()
    const year = now.getFullYear()
    const week = Math.ceil(now.getDate() / 7)
    return `MPS-${year}-W${week.toString().padStart(2, '0')}`
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    plans: readonly(plans),
    currentPlan: readonly(currentPlan),
    loadProductionPlans,
    createProductionPlan,
    analyzeCapacityRequirements,
    allocateEquipment
  }
}

// ===== 工作订单管理 (Work Order Management) =====

export function useWorkOrder() {
  const loading = ref(false)
  const workOrderList = ref<WorkOrder[]>([])
  const currentWorkOrder = ref<WorkOrder | null>(null)

  // 加载工作订单
  const loadWorkOrders = async (filters?: {
    parentOrderId?: string
    type?: WorkOrderType
    status?: WorkOrderStatus
  }) => {
    loading.value = true

    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      let filteredOrders = [...workOrders]

      if (filters?.parentOrderId) {
        filteredOrders = filteredOrders.filter(wo => wo.parentOrderId === filters.parentOrderId)
      }
      if (filters?.type) {
        filteredOrders = filteredOrders.filter(wo => wo.type === filters.type)
      }
      if (filters?.status) {
        filteredOrders = filteredOrders.filter(wo => wo.status === filters.status)
      }

      workOrderList.value = filteredOrders
    } catch (err) {
      console.error('工作订单加载错误:', err)
    } finally {
      loading.value = false
    }
  }

  // 创建工作订单 - CP/Assembly/FT专业流程
  const createWorkOrder = async (orderData: Partial<WorkOrder>) => {
    loading.value = true

    try {
      const newWorkOrder: WorkOrder = {
        id: `WO-${orderData.type?.toUpperCase()}-${Date.now()}`,
        workOrderNumber: generateWorkOrderNumber(orderData.type!),
        parentOrderId: orderData.parentOrderId!,
        type: orderData.type!,
        status: WorkOrderStatus.PENDING,
        priority: orderData.priority || OrderPriority.MEDIUM,
        productInfo: orderData.productInfo!,
        processInfo: orderData.processInfo!,
        schedule: orderData.schedule!,
        assignedOperator: orderData.assignedOperator,
        notes: orderData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      workOrderList.value.push(newWorkOrder)
      return newWorkOrder
    } catch (err) {
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新工作订单状态
  const updateWorkOrderStatus = async (
    workOrderId: string,
    status: WorkOrderStatus,
    qualityResult?: WorkOrder['qualityResult']
  ) => {
    const workOrder = workOrderList.value.find(wo => wo.id === workOrderId)
    if (!workOrder) throw new Error('工作订单不存在')

    workOrder.status = status
    workOrder.updatedAt = new Date().toISOString()

    if (status === WorkOrderStatus.IN_PROGRESS && !workOrder.schedule.actualStartTime) {
      workOrder.schedule.actualStartTime = new Date().toISOString()
    }

    if (status === WorkOrderStatus.COMPLETED) {
      workOrder.schedule.actualEndTime = new Date().toISOString()
      if (qualityResult) {
        workOrder.qualityResult = qualityResult
      }
    }
  }

  // 生成工作订单编号
  const generateWorkOrderNumber = (type: WorkOrderType) => {
    const prefix = type.toUpperCase().replace('_', '-')
    const year = new Date().getFullYear()
    const seq = workOrderList.value.filter(wo => wo.type === type).length + 1
    return `${prefix}-${year}-${seq.toString().padStart(4, '0')}`
  }

  return {
    loading: readonly(loading),
    workOrderList: readonly(workOrderList),
    currentWorkOrder: readonly(currentWorkOrder),
    loadWorkOrders,
    createWorkOrder,
    updateWorkOrderStatus
  }
}

// ===== 订单生命周期管理 (Order Lifecycle Management) =====

export function useOrderLifecycle() {
  const lifecycles = ref<OrderLifecycle[]>([])

  // 加载订单生命周期
  const loadOrderLifecycles = async (orderId?: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 200))
      lifecycles.value = orderId
        ? orderLifecycles.filter(lc => lc.orderId === orderId)
        : orderLifecycles
    } catch (err) {
      console.error('订单生命周期加载错误:', err)
    }
  }

  // 获取订单当前阶段
  const getCurrentStage = (orderId: string) => {
    const lifecycle = lifecycles.value.find(lc => lc.orderId === orderId)
    return lifecycle?.currentStage
  }

  // 获取订单关键里程碑
  const getKeyMilestones = (orderId: string) => {
    const lifecycle = lifecycles.value.find(lc => lc.orderId === orderId)
    return lifecycle?.keyMilestones
  }

  // 获取封装规格 - OSAT专业要求
  const getPackagingSpecifications = (orderId: string) => {
    const lifecycle = lifecycles.value.find(lc => lc.orderId === orderId)
    return lifecycle?.packagingSpecifications
  }

  return {
    lifecycles: readonly(lifecycles),
    loadOrderLifecycles,
    getCurrentStage,
    getKeyMilestones,
    getPackagingSpecifications
  }
}

// ===== 产能规划分析 (Capacity Planning) =====

export function useCapacityPlanning() {
  const capacityAnalysis = ref(capacityData)

  // 获取设备产能矩阵
  const getEquipmentCapacity = () => {
    return capacityAnalysis.value.equipmentCapacity
  }

  // 获取瓶颈分析
  const getBottleneckAnalysis = () => {
    return capacityAnalysis.value.bottleneckAnalysis
  }

  // 计算可用产能 - 基于设备可用性
  const calculateAvailableCapacity = (processType: WorkOrderType, packageTypes: PackageType[]) => {
    const relevantEquipment = capacityAnalysis.value.equipmentCapacity
      .filter(eq => eq.processType === processType)
      .filter(eq => packageTypes.some(pkg => eq.supportedPackages.includes(pkg)))

    return relevantEquipment.reduce((sum, eq) => sum + eq.dailyCapacity, 0)
  }

  // 产能预警分析
  const analyzeCapacityWarnings = (plannedOrders: ProductionPlan['orderGroups']) => {
    const warnings = []

    for (const group of plannedOrders) {
      const cpCapacity = calculateAvailableCapacity(WorkOrderType.CP_TESTING, group.packageTypes)
      const asmCapacity = calculateAvailableCapacity(WorkOrderType.ASSEMBLY, group.packageTypes)
      const ftCapacity = calculateAvailableCapacity(WorkOrderType.FT_TESTING, group.packageTypes)

      if (group.totalQuantity > cpCapacity) {
        warnings.push({
          type: 'capacity_shortage',
          process: 'CP测试',
          shortfall: group.totalQuantity - cpCapacity,
          recommendation: '增加CP测试设备或延长测试时间'
        })
      }

      if (group.totalQuantity > asmCapacity) {
        warnings.push({
          type: 'capacity_shortage',
          process: '封装工艺',
          shortfall: group.totalQuantity - asmCapacity,
          recommendation: '增加封装设备或考虑外包'
        })
      }

      if (group.totalQuantity > ftCapacity) {
        warnings.push({
          type: 'capacity_shortage',
          process: 'FT测试',
          shortfall: group.totalQuantity - ftCapacity,
          recommendation: '增加FT测试设备'
        })
      }
    }

    return warnings
  }

  return {
    capacityAnalysis: readonly(capacityAnalysis),
    getEquipmentCapacity,
    getBottleneckAnalysis,
    calculateAvailableCapacity,
    analyzeCapacityWarnings
  }
}
