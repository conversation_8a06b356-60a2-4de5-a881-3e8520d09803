/**
 * 监控中心数据类型定义
 * IC封装测试工厂实时监控系统
 */

// ============ 基础类型 ============

/** 监控数据源状态 */
export type DataSourceStatus = 'connected' | 'disconnected' | 'error' | 'warning'

/** 时间范围选项 */
export type TimeRange = '1h' | '4h' | '12h' | '24h' | '7d' | '30d'

/** 刷新间隔（秒） */
export type RefreshInterval = 5 | 10 | 30 | 60 | 300

/** 大屏显示模式 */
export type DisplayMode = 'normal' | 'fullscreen' | 'presentation'

// ============ 生产监控相关 ============

/** 生产KPI指标 */
export interface ProductionKPI {
  /** 当日产量 */
  dailyOutput: number
  /** 产量目标 */
  outputTarget: number
  /** 产量完成率 */
  outputRate: number
  /** 整体良率 */
  overallYield: number
  /** 良率目标 */
  yieldTarget: number
  /** 设备稼动率 */
  oeeRate: number
  /** 在制品数量 */
  wipCount: number
  /** 计划达成率 */
  planAchievementRate: number
  /** 数据更新时间 */
  updateTime: string
}

/** 产线状态信息 */
export interface ProductionLineStatus {
  lineId: string
  lineName: string
  status: 'running' | 'idle' | 'maintenance' | 'alarm' | 'offline'
  currentLot: string
  processStep: 'CP' | 'Assembly' | 'FT' | 'Packaging'
  progress: number
  operatorCount: number
  lastUpdateTime: string
}

/** 生产趋势数据点 */
export interface ProductionTrendPoint {
  timestamp: string
  output: number
  yield: number
  oee: number
  defectRate: number
}

/** 生产预测数据 */
export interface ProductionForecast {
  timeRange: TimeRange
  predictedOutput: number
  confidenceLevel: number
  influenceFactors: string[]
  recommendedActions: string[]
}

// ============ 设备监控相关 ============

/** 设备健康状态 */
export type EquipmentHealth = 'excellent' | 'good' | 'fair' | 'poor' | 'critical'

/** 设备类型 */
export type EquipmentType =
  | 'prober'
  | 'bonder'
  | 'molding'
  | 'tester'
  | 'handler'
  | 'oven'
  | 'inspection'

/** 设备状态详情 */
export interface EquipmentStatus {
  equipmentId: string
  equipmentName: string
  equipmentType: EquipmentType
  status: 'running' | 'idle' | 'maintenance' | 'alarm' | 'offline'
  health: EquipmentHealth
  temperature?: number
  pressure?: number
  vibration?: number
  utilization: number
  oee: {
    availability: number
    performance: number
    quality: number
    overall: number
  }
  currentRecipe: string
  alarmCount: number
  lastMaintenance: string
  nextMaintenance: string
  location: {
    area: string
    position: string
  }
  operator?: string
  updateTime: string
}

/** 设备告警信息 */
export interface EquipmentAlarm {
  alarmId: string
  equipmentId: string
  equipmentName: string
  alarmType: 'critical' | 'warning' | 'info'
  alarmCode: string
  alarmMessage: string
  alarmTime: string
  acknowledgedBy?: string
  acknowledgedTime?: string
  resolvedTime?: string
  isActive: boolean
}

/** 维护计划项 */
export interface MaintenanceSchedule {
  scheduleId: string
  equipmentId: string
  equipmentName: string
  maintenanceType: 'preventive' | 'predictive' | 'emergency'
  plannedDate: string
  estimatedDuration: number
  assignedTechnician: string
  description: string
  status: 'scheduled' | 'in_progress' | 'completed' | 'overdue'
  priority: 'high' | 'medium' | 'low'
}

// ============ 质量监控相关 ============

/** 质量KPI指标 */
export interface QualityKPI {
  /** 整体良率 */
  overallYield: number
  /** 首次通过率 */
  firstPassYield: number
  /** 缺陷密度 */
  defectDensity: number
  /** 客户投诉数 */
  customerComplaints: number
  /** 质量成本占比 */
  qualityCostRatio: number
  /** 过程能力指数 */
  cpkIndex: number
  /** 供应商质量指数 */
  supplierQualityIndex: number
  /** 数据更新时间 */
  updateTime: string
}

/** SPC控制状态 */
export interface SPCStatus {
  processId: string
  processName: string
  parameter: string
  status: 'in_control' | 'out_of_control' | 'warning'
  cpk: number
  ucl: number
  lcl: number
  target: number
  lastValue: number
  trendDirection: 'up' | 'down' | 'stable'
  updateTime: string
}

/** 缺陷分析数据 */
export interface DefectAnalysis {
  defectType: string
  count: number
  percentage: number
  trend: 'increasing' | 'decreasing' | 'stable'
  rootCause?: string
  correctionAction?: string
}

/** 客户满意度指标 */
export interface CustomerSatisfaction {
  customerId: string
  customerName: string
  satisfactionScore: number
  qualityRating: number
  deliveryRating: number
  serviceRating: number
  feedbackCount: number
  lastFeedbackTime: string
}

// ============ 综合监控中心 ============

/** 监控面板类型 */
export type MonitoringPanel = 'production' | 'equipment' | 'quality' | 'overview'

/** 监控配置 */
export interface MonitoringConfig {
  /** 当前面板 */
  currentPanel: MonitoringPanel
  /** 显示模式 */
  displayMode: DisplayMode
  /** 自动刷新间隔 */
  refreshInterval: RefreshInterval
  /** 是否启用声音告警 */
  soundAlertEnabled: boolean
  /** 数据时间范围 */
  timeRange: TimeRange
  /** 自定义布局配置 */
  layoutConfig?: {
    [key: string]: {
      x: number
      y: number
      width: number
      height: number
      visible: boolean
    }
  }
}

/** 系统状态摘要 */
export interface SystemStatusSummary {
  /** 总体健康状态 */
  overallHealth: 'healthy' | 'warning' | 'critical'
  /** 生产状态 */
  productionStatus: 'normal' | 'behind_schedule' | 'ahead_schedule' | 'stopped'
  /** 设备状态统计 */
  equipmentSummary: {
    total: number
    running: number
    idle: number
    maintenance: number
    alarm: number
  }
  /** 质量状态 */
  qualityStatus: 'excellent' | 'good' | 'needs_attention' | 'critical'
  /** 活跃告警数量 */
  activeAlarms: {
    critical: number
    warning: number
    info: number
  }
  /** 数据源连接状态 */
  dataSourceStatus: {
    mes: DataSourceStatus
    equipment: DataSourceStatus
    quality: DataSourceStatus
    erp: DataSourceStatus
  }
}

/** 实时消息 */
export interface RealtimeMessage {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  source: 'production' | 'equipment' | 'quality' | 'system'
  title: string
  message: string
  timestamp: string
  acknowledged: boolean
  priority: 'high' | 'medium' | 'low'
}

// ============ API响应类型 ============

/** API响应基础类型 */
export interface MonitoringApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

/** 监控数据响应 */
export interface MonitoringDataResponse {
  productionKPI: ProductionKPI
  equipmentStatus: EquipmentStatus[]
  qualityKPI: QualityKPI
  systemStatus: SystemStatusSummary
  realtimeMessages: RealtimeMessage[]
}

/** 图表数据点 */
export interface ChartDataPoint {
  timestamp: string
  value: number
  label?: string
}

/** 图表数据序列 */
export interface ChartSeries {
  name: string
  data: ChartDataPoint[]
  type?: 'line' | 'bar' | 'area' | 'scatter'
  color?: string
}
