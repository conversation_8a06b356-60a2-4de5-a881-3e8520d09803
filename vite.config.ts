import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver, VantResolver } from 'unplugin-vue-components/resolvers'
import { resolve } from 'path'

export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [
      vue(),
      // 自动导入Vue API和组合式函数
      AutoImport({
        imports: [
          'vue',
          'vue-router',
          'pinia',
          '@vueuse/core',
          {
            'lodash-es': ['debounce', 'throttle', 'cloneDeep', 'merge'],
            'dayjs': [['default', 'dayjs']],
            'axios': [['default', 'axios']]
          }
        ],
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/auto-imports.d.ts',
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json'
        }
      }),
      // 自动导入组件
      Components({
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass',
            directives: true,
            version: '2.5.6'
          }),
          VantResolver({
            importStyle: false // Vant 4.x 使用CSS变量，不需要样式导入
          })
        ],
        dts: 'src/types/components.d.ts'
      })
    ],
    
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@views': resolve(__dirname, 'src/views'),
        '@assets': resolve(__dirname, 'src/assets'),
        '@utils': resolve(__dirname, 'src/utils'),
        '@stores': resolve(__dirname, 'src/stores'),
        '@composables': resolve(__dirname, 'src/composables'),
        '@api': resolve(__dirname, 'src/api'),
        '@types': resolve(__dirname, 'src/types'),
        // 修复Vue模板编译器问题
        'vue': 'vue/dist/vue.esm-bundler.js'
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          // 自动导入主题变量和混入
          additionalData: `
            @use "@/assets/styles/themes/variables.scss" as *;
            @use "@/assets/styles/base/mixins.scss" as *;
          `,
          api: 'modern-compiler'
        }
      }
    },

    server: {
      host: '0.0.0.0',
      port: parseInt(env.VITE_PORT || '3000'),
      open: true,
      cors: true,
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL || 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '/api/v1')
        },
        '/ws': {
          target: env.VITE_WS_BASE_URL || 'ws://localhost:8080',
          ws: true,
          changeOrigin: true
        }
      }
    },

    build: {
      target: 'es2022',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: mode !== 'production',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'production',
          drop_debugger: mode === 'production'
        }
      },
      rollupOptions: {
        output: {
          manualChunks: {
            // 将Vue相关库打包到vendor chunk
            vendor: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
            // Element Plus单独打包
            'element-plus': ['element-plus'],
            // 图表库单独打包
            charts: ['echarts', 'd3'],
            // 工具库单独打包
            utils: ['lodash-es', 'dayjs', 'axios']
          },
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.')
            const ext = info[info.length - 1]
            if (/\.(png|jpe?g|gif|svg|webp)$/i.test(assetInfo.name)) {
              return `images/[name]-[hash].${ext}`
            }
            if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
              return `fonts/[name]-[hash].${ext}`
            }
            if (/\.css$/i.test(assetInfo.name)) {
              return `css/[name]-[hash].${ext}`
            }
            return `assets/[name]-[hash].${ext}`
          }
        }
      },
      // 优化包体积
      chunkSizeWarningLimit: 1000
    },

    esbuild: {
      target: 'es2022',
      drop: mode === 'production' ? ['console', 'debugger'] : []
    },

    // 预构建优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
        'element-plus',
        'axios',
        'dayjs',
        'lodash-es'
      ]
    },

    define: {
      __VUE_OPTIONS_API__: 'true', // 启用选项式API支持，确保兼容性
      __VUE_PROD_DEVTOOLS__: 'false'
    }
  }
})