/**
 * IC封测CIM系统 - 客户模拟数据
 * Customer Mock Data for IC Packaging & Testing CIM System
 */

import type {
  Customer,
  CustomerContact,
  CommunicationRecord,
  CustomerFinancialInfo,
  CustomerTechnicalInfo,
  CooperationHistory,
  CustomerType,
  CustomerLevel,
  ApplicationField,
  ContactType,
  CreditLevel,
  CustomerStatus
} from '@/types/customer'

// 真实的IC行业客户数据模拟
export const mockCustomers: Customer[] = [
  // 头部IC设计公司
  {
    id: 'customer_001',
    code: 'C001',
    name: '华为海思半导体有限公司',
    englishName: 'HiSilicon Technologies Co., Ltd.',
    type: 'fabless',
    level: 'strategic',
    industryType: 'communication',
    applicationFields: ['communication', 'ai', 'iot'],
    unifiedSocialCreditCode: '91440300279374817X',
    registeredAddress: '深圳市南山区高新技术产业园区',
    officeAddress: '深圳市龙岗区坂田华为基地',
    contacts: [
      {
        id: 'contact_001_01',
        name: '张伟',
        position: '采购总监',
        department: '供应链管理部',
        mobile: '***********',
        email: '<EMAIL>',
        wechat: 'zw_hisilicon',
        isPrimary: true,
        contactType: ['Procurement', 'Business'],
        communicationRecords: []
      },
      {
        id: 'contact_001_02',
        name: '李明',
        position: '技术经理',
        department: '封装技术部',
        mobile: '***********',
        email: '<EMAIL>',
        isPrimary: false,
        contactType: ['Technical'],
        communicationRecords: []
      }
    ],
    financialInfo: {
      creditLevel: 'A+',
      creditLimit: ********,
      paymentTerms: '月结90天',
      paymentCycle: 90,
      currency: 'CNY',
      taxRate: 0.13,
      invoiceInfo: {
        invoiceTitle: '华为海思半导体有限公司',
        taxNumber: '91440300279374817X',
        bankName: '招商银行深圳分行',
        bankAccount: '755900********9',
        registeredAddress: '深圳市南山区高新技术产业园区科技南路',
        registeredPhone: '0755-********'
      }
    },
    technicalInfo: {
      mainProductTypes: ['processor', 'mixed_signal', 'rf'],
      packageTypePreferences: ['BGA', 'FC', 'CSP'],
      testRequirements: ['cp_test', 'ft_test', 'reliability_test'],
      qualityStandards: ['JEDEC', 'ISO9001']
    },
    cooperationHistory: {
      firstCooperationTime: '2018-03-15T08:30:00Z',
      cooperationYears: 6,
      totalOrders: 156,
      totalSalesAmount: ********,
      ordersLast12Months: 28,
      salesLast12Months: ********,
      averageOrderAmount: 664285,
      satisfactionScore: 4.8,
      mainCooperationProducts: ['Kirin处理器', '5G基带芯片', 'AI芯片']
    },
    status: 'active',
    createdAt: '2018-03-15T08:30:00Z',
    updatedAt: '2024-01-15T10:20:00Z',
    createdBy: 'admin',
    remarks: '战略合作伙伴，重点客户，优先保障产能'
  },

  {
    id: 'customer_002',
    code: 'C002',
    name: '紫光展锐科技有限公司',
    englishName: 'UNISOC Technologies Co., Ltd.',
    type: 'fabless',
    level: 'strategic',
    industryType: 'communication',
    applicationFields: ['communication', 'iot', 'consumer'],
    unifiedSocialCreditCode: '913301007963940751',
    registeredAddress: '浙江省杭州市滨江区网商路599号',
    officeAddress: '上海市浦东新区张江高科技园区',
    contacts: [
      {
        id: 'contact_002_01',
        name: '王强',
        position: '供应链总监',
        department: '供应链管理中心',
        mobile: '***********',
        email: '<EMAIL>',
        isPrimary: true,
        contactType: ['Procurement', 'Business'],
        communicationRecords: []
      }
    ],
    financialInfo: {
      creditLevel: 'A+',
      creditLimit: ********,
      paymentTerms: '月结60天',
      paymentCycle: 60,
      currency: 'CNY',
      taxRate: 0.13,
      invoiceInfo: {
        invoiceTitle: '紫光展锐科技有限公司',
        taxNumber: '913301007963940751',
        bankName: '中国银行杭州分行',
        bankAccount: '***************',
        registeredAddress: '浙江省杭州市滨江区网商路599号',
        registeredPhone: '0571-********'
      }
    },
    technicalInfo: {
      mainProductTypes: ['processor', 'rf', 'mixed_signal'],
      packageTypePreferences: ['BGA', 'QFN', 'CSP'],
      testRequirements: ['cp_test', 'ft_test'],
      qualityStandards: ['JEDEC', 'ISO9001']
    },
    cooperationHistory: {
      firstCooperationTime: '2019-06-20T09:15:00Z',
      cooperationYears: 5,
      totalOrders: 98,
      totalSalesAmount: ********,
      ordersLast12Months: 18,
      salesLast12Months: 9800000,
      averageOrderAmount: 544444,
      satisfactionScore: 4.6,
      mainCooperationProducts: ['Tiger处理器', '5G调制解调器', '物联网芯片']
    },
    status: 'active',
    createdAt: '2019-06-20T09:15:00Z',
    updatedAt: '2024-01-12T14:45:00Z',
    createdBy: 'admin',
    remarks: '主要合作伙伴，在5G和IoT领域有深度合作'
  },

  {
    id: 'customer_003',
    code: 'C003',
    name: '比亚迪半导体股份有限公司',
    englishName: 'BYD Semiconductor Co., Ltd.',
    type: 'fabless',
    level: 'important',
    industryType: 'automotive',
    applicationFields: ['automotive', 'automotive', 'power'],
    unifiedSocialCreditCode: '91440300MA5G2KU83Y',
    registeredAddress: '深圳市坪山区坪山大道2002号比亚迪创新大厦',
    officeAddress: '深圳市坪山区比亚迪路3009号',
    contacts: [
      {
        id: 'contact_003_01',
        name: '陈志华',
        position: '采购部经理',
        department: '供应链采购部',
        mobile: '***********',
        email: '<EMAIL>',
        isPrimary: true,
        contactType: ['Procurement', 'Business'],
        communicationRecords: []
      }
    ],
    financialInfo: {
      creditLevel: 'A',
      creditLimit: ********,
      paymentTerms: '月结45天',
      paymentCycle: 45,
      currency: 'CNY',
      taxRate: 0.13,
      invoiceInfo: {
        invoiceTitle: '比亚迪半导体股份有限公司',
        taxNumber: '91440300MA5G2KU83Y',
        bankName: '平安银行深圳分行',
        bankAccount: '***************',
        registeredAddress: '深圳市坪山区坪山大道2002号',
        registeredPhone: '0755-********'
      }
    },
    technicalInfo: {
      mainProductTypes: ['power', 'analog', 'mcu'],
      packageTypePreferences: ['QFN', 'DFN', 'SOP'],
      testRequirements: ['cp_test', 'ft_test', 'reliability_test', 'burn_in_test'],
      qualityStandards: ['IATF16949', 'AEC-Q100', 'ISO9001']
    },
    cooperationHistory: {
      firstCooperationTime: '2020-08-10T10:30:00Z',
      cooperationYears: 4,
      totalOrders: 67,
      totalSalesAmount: ********,
      ordersLast12Months: 22,
      salesLast12Months: ********,
      averageOrderAmount: 568181,
      satisfactionScore: 4.7,
      mainCooperationProducts: ['IGBT驱动IC', 'DC-DC电源IC', '电池管理IC']
    },
    status: 'active',
    createdAt: '2020-08-10T10:30:00Z',
    updatedAt: '2024-01-10T16:20:00Z',
    createdBy: 'admin',
    remarks: '新能源汽车行业重要客户，质量要求极高'
  },

  {
    id: 'customer_004',
    code: 'C004',
    name: '兆易创新科技集团股份有限公司',
    englishName: 'GigaDevice Semiconductor Inc.',
    type: 'fabless',
    level: 'important',
    industryType: 'industrial',
    applicationFields: ['iot', 'consumer', 'industrial'],
    unifiedSocialCreditCode: '91110108102819705Q',
    registeredAddress: '北京市海淀区学院路30号科大天工大厦A座',
    officeAddress: '北京市海淀区中关村东路1号院清华科技园',
    contacts: [
      {
        id: 'contact_004_01',
        name: '刘建华',
        position: '供应链总监',
        department: '运营管理中心',
        mobile: '***********',
        email: '<EMAIL>',
        isPrimary: true,
        contactType: ['Procurement', 'Business'],
        communicationRecords: []
      }
    ],
    financialInfo: {
      creditLevel: 'A',
      creditLimit: ********,
      paymentTerms: '月结60天',
      paymentCycle: 60,
      currency: 'CNY',
      taxRate: 0.13,
      invoiceInfo: {
        invoiceTitle: '兆易创新科技集团股份有限公司',
        taxNumber: '91110108102819705Q',
        bankName: '中信银行北京分行',
        bankAccount: '288********9012',
        registeredAddress: '北京市海淀区学院路30号',
        registeredPhone: '010-********'
      }
    },
    technicalInfo: {
      mainProductTypes: ['memory', 'mcu', 'analog'],
      packageTypePreferences: ['QFN', 'SOP', 'TSOP'],
      testRequirements: ['cp_test', 'ft_test'],
      qualityStandards: ['JEDEC', 'ISO9001']
    },
    cooperationHistory: {
      firstCooperationTime: '2019-11-25T11:00:00Z',
      cooperationYears: 5,
      totalOrders: 89,
      totalSalesAmount: ********,
      ordersLast12Months: 19,
      salesLast12Months: 8900000,
      averageOrderAmount: 468421,
      satisfactionScore: 4.5,
      mainCooperationProducts: ['SPI NOR Flash', 'MCU芯片', 'GD32系列']
    },
    status: 'active',
    createdAt: '2019-11-25T11:00:00Z',
    updatedAt: '2024-01-08T13:10:00Z',
    createdBy: 'admin',
    remarks: '存储和MCU领域的重要合作伙伴'
  },

  {
    id: 'customer_005',
    code: 'C005',
    name: '汇顶科技股份有限公司',
    englishName: 'Goodix Technology Inc.',
    type: 'fabless',
    level: 'standard',
    industryType: 'consumer',
    applicationFields: ['consumer', 'iot', 'ai'],
    unifiedSocialCreditCode: '91440300279506043P',
    registeredAddress: '深圳市南山区粤海街道学府路63号高新区联合总部大厦',
    officeAddress: '深圳市南山区高新区科技中一路汇顶科技大厦',
    contacts: [
      {
        id: 'contact_005_01',
        name: '胡晓峰',
        position: '采购经理',
        department: '供应链管理部',
        mobile: '***********',
        email: '<EMAIL>',
        isPrimary: true,
        contactType: ['Procurement'],
        communicationRecords: []
      }
    ],
    financialInfo: {
      creditLevel: 'B+',
      creditLimit: ********,
      paymentTerms: '月结45天',
      paymentCycle: 45,
      currency: 'CNY',
      taxRate: 0.13,
      invoiceInfo: {
        invoiceTitle: '汇顶科技股份有限公司',
        taxNumber: '91440300279506043P',
        bankName: '工商银行深圳分行',
        bankAccount: '***************',
        registeredAddress: '深圳市南山区粤海街道学府路63号',
        registeredPhone: '0755-********'
      }
    },
    technicalInfo: {
      mainProductTypes: ['mixed_signal', 'analog'],
      packageTypePreferences: ['QFN', 'CSP', 'BGA'],
      testRequirements: ['cp_test', 'ft_test'],
      qualityStandards: ['JEDEC', 'ISO9001']
    },
    cooperationHistory: {
      firstCooperationTime: '2020-02-15T14:20:00Z',
      cooperationYears: 4,
      totalOrders: 45,
      totalSalesAmount: ********,
      ordersLast12Months: 8,
      salesLast12Months: 3200000,
      averageOrderAmount: 400000,
      satisfactionScore: 4.3,
      mainCooperationProducts: ['指纹识别芯片', '触控芯片', '音频解码芯片']
    },
    status: 'active',
    createdAt: '2020-02-15T14:20:00Z',
    updatedAt: '2024-01-05T09:30:00Z',
    createdBy: 'admin',
    remarks: '人机交互芯片领域合作伙伴'
  }
]

// 生成更多模拟客户数据的工具函数
export function generateMockCustomers(count: number): Customer[] {
  const companies = [
    {
      name: '芯海科技',
      nameEn: 'Chipsea Technologies',
      industryType: 'consumer'
    },
    {
      name: '瑞芯微电子',
      nameEn: 'Rockchip Electronics',
      industryType: 'consumer'
    },
    {
      name: '全志科技',
      nameEn: 'Allwinner Technology',
      industryType: 'consumer'
    },
    { name: '思瑞浦微电子', nameEn: 'SIPREC Electronics', industryType: 'industrial' },
    { name: '博通集成', nameEn: 'Beken Corporation', industryType: 'communication' },
    { name: '上海贝岭', nameEn: 'Shanghai Belling', industryType: 'industrial' },
    { name: '华虹半导体', nameEn: 'Hua Hong Semiconductor', industryType: 'communication' },
    { name: '中芯国际', nameEn: 'SMIC', industryType: 'communication' },
    { name: '长电科技', nameEn: 'JCET Group', industryType: 'industrial' },
    { name: '通富微电', nameEn: 'TFME', industryType: 'industrial' }
  ]

  const generatedCustomers: Customer[] = []
  const baseDate = new Date('2020-01-01')

  for (let i = 0; i < count && i < companies.length; i++) {
    const company = companies[i]
    const customerId = `customer_${String(mockCustomers.length + i + 1).padStart(3, '0')}`
    const customerCode = `C${String(mockCustomers.length + i + 1).padStart(3, '0')}`

    generatedCustomers.push({
      id: customerId,
      code: customerCode,
      name: company.name,
      englishName: company.nameEn,
      type: getRandomCustomerType(),
      level: getRandomCustomerLevel(),
      industryType: company.industryType,
      applicationFields: getRandomApplicationFields(),
      registeredAddress: getRandomAddress(),
      officeAddress: getRandomAddress(),
      contacts: generateRandomContacts(customerId),
      financialInfo: generateRandomFinancialInfo(),
      technicalInfo: generateRandomTechnicalInfo(),
      cooperationHistory: generateRandomCooperationHistory(baseDate, i),
      status: 'active',
      createdAt: new Date(baseDate.getTime() + i * 86400000 * 30).toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'admin',
      remarks: '系统生成的模拟客户数据'
    })
  }

  return generatedCustomers
}

// 辅助函数
function getRandomCustomerType(): CustomerType {
  const types = ['fabless', 'idm', 'foundry', 'distributor', 'broker', 'ems'] as const
  return types[Math.floor(Math.random() * types.length)]
}

function getRandomCustomerLevel(): CustomerLevel {
  const levels = ['strategic', 'important', 'standard', 'potential'] as const
  return levels[Math.floor(Math.random() * levels.length)]
}

function getRandomApplicationFields(): ApplicationField[] {
  const fields = [
    'automotive',
    'consumer',
    'communication',
    'industrial',
    'ai',
    'iot',
    'medical',
    'aerospace',
    'power',
    'security'
  ] as const
  const count = Math.floor(Math.random() * 3) + 1
  const selected: ApplicationField[] = []

  for (let i = 0; i < count; i++) {
    const field = fields[Math.floor(Math.random() * fields.length)]
    if (!selected.includes(field)) {
      selected.push(field)
    }
  }

  return selected
}

function getRandomAddress(): string {
  const cities = ['北京', '上海', '深圳', '杭州', '成都', '西安', '苏州', '无锡']
  const city = cities[Math.floor(Math.random() * cities.length)]
  return `${city}市高新技术开发区科技园${Math.floor(Math.random() * 100) + 1}号`
}

function generateRandomContacts(customerId: string): CustomerContact[] {
  const names = ['张三', '李四', '王五', '赵六', '孙七']
  const positions = ['采购经理', '技术总监', '供应链经理', '质量经理', '项目经理']
  const departments = ['采购部', '技术部', '供应链部', '质量部', '项目部']

  return [
    {
      id: `contact_${customerId}_01`,
      name: names[Math.floor(Math.random() * names.length)],
      position: positions[Math.floor(Math.random() * positions.length)],
      department: departments[Math.floor(Math.random() * departments.length)],
      mobile: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
      email: `<EMAIL>`,
      isPrimary: true,
      contactType: ['Procurement', 'Business'],
      communicationRecords: []
    }
  ]
}

function generateRandomFinancialInfo(): CustomerFinancialInfo {
  const creditLevels = ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D'] as const
  const creditLevel = creditLevels[Math.floor(Math.random() * creditLevels.length)]

  return {
    creditLevel,
    creditLimit: Math.floor(Math.random() * ********) + 5000000,
    paymentTerms: `月结${[30, 45, 60, 90][Math.floor(Math.random() * 4)]}天`,
    paymentCycle: [30, 45, 60, 90][Math.floor(Math.random() * 4)],
    currency: 'CNY',
    taxRate: 0.13,
    invoiceInfo: {
      invoiceTitle: '模拟公司名称',
      taxNumber: `${Math.floor(Math.random() * *********) + *********}`,
      bankName: '中国银行',
      bankAccount: `${Math.floor(Math.random() * *********0) + **********}`,
      registeredAddress: '注册地址',
      registeredPhone: '010-********'
    }
  }
}

function generateRandomTechnicalInfo(): CustomerTechnicalInfo {
  const productTypes = [
    'processor',
    'memory',
    'analog',
    'mixed_signal',
    'rf',
    'power',
    'mcu'
  ] as const
  const packageTypes = [
    'QFP',
    'BGA',
    'CSP',
    'QFN',
    'SOP',
    'TSOP',
    'TQFP',
    'LQFP',
    'FC',
    'WLCSP',
    'DIP'
  ] as const
  const testRequirements = ['cp_test', 'ft_test', 'reliability_test', 'burn_in_test'] as const
  const qualityStandards = ['JEDEC', 'ISO9001', 'IATF16949', 'AEC-Q100'] as const

  return {
    mainProductTypes: productTypes.slice(0, Math.floor(Math.random() * 3) + 1) as any,
    packageTypePreferences: packageTypes.slice(0, Math.floor(Math.random() * 3) + 1) as any,
    testRequirements: testRequirements.slice(0, Math.floor(Math.random() * 3) + 1) as any,
    qualityStandards: qualityStandards.slice(0, Math.floor(Math.random() * 2) + 1) as any
  }
}

function generateRandomCooperationHistory(baseDate: Date, index: number): CooperationHistory {
  const cooperationYears = Math.floor(Math.random() * 5) + 1
  const totalOrders = Math.floor(Math.random() * 100) + 10
  const totalSalesAmount = Math.floor(Math.random() * ********) + 5000000

  return {
    firstCooperationTime: new Date(baseDate.getTime() + index * 86400000 * 30).toISOString(),
    cooperationYears,
    totalOrders,
    totalSalesAmount,
    ordersLast12Months: Math.floor(Math.random() * 20) + 5,
    salesLast12Months: Math.floor(Math.random() * 8000000) + 2000000,
    averageOrderAmount: Math.floor(totalSalesAmount / totalOrders),
    satisfactionScore: Math.round((Math.random() * 1.5 + 3.5) * 10) / 10,
    mainCooperationProducts: ['产品A', '产品B', '产品C']
  }
}

// 导出所有客户数据（包含真实数据 + 生成的数据）
export const allMockCustomers = [...mockCustomers, ...generateMockCustomers(25)]

// 客户统计数据
export const customerStats = {
  totalCustomers: allMockCustomers.length,
  activeCustomers: allMockCustomers.filter(c => c.status === 'active').length,
  strategicCustomers: allMockCustomers.filter(c => c.customerLevel === 'strategic').length,
  newCustomersThisMonth: allMockCustomers.filter(c => {
    const createDate = new Date(c.createdAt)
    const now = new Date()
    return (
      createDate.getMonth() === now.getMonth() && createDate.getFullYear() === now.getFullYear()
    )
  }).length,
  industryDistribution: {
    ['automotive']: allMockCustomers.filter(c => c.industry === 'automotive').length,
    ['consumer']: allMockCustomers.filter(c => c.industry === 'consumer').length,
    ['communication']: allMockCustomers.filter(c => c.industry === 'communication').length,
    ['industrial']: allMockCustomers.filter(c => c.industry === 'industrial').length,
    ['medical']: allMockCustomers.filter(c => c.industry === 'medical').length
  }
}
