@echo off
echo ==========================================
echo       D盘软件安装验证检查
echo ==========================================

echo.
echo [1/4] 检查环境变量...
echo JAVA_HOME: %JAVA_HOME%
echo MAVEN_HOME: %MAVEN_HOME%
echo REDIS_HOME: %REDIS_HOME%

echo.
echo [2/4] 检查Java安装...
java -version
if %errorlevel% == 0 (
    echo ✓ Java安装成功
) else (
    echo ✗ Java未正确安装或环境变量未生效
)

echo.
echo [3/4] 检查Maven安装...
mvn --version
if %errorlevel% == 0 (
    echo ✓ Maven安装成功
) else (
    echo ✗ Maven未正确安装或环境变量未生效
)

echo.
echo [4/4] 检查Redis安装...
redis-cli --version
if %errorlevel% == 0 (
    echo ✓ Redis安装成功
) else (
    echo ✗ Redis未正确安装或环境变量未生效
)

echo.
echo [5/5] 检查Docker...
docker --version
if %errorlevel% == 0 (
    echo ✓ Docker可用
) else (
    echo ⚠ Docker未安装或未启动
)

echo.
echo ==========================================
echo           验证检查完成
echo ==========================================
echo.
echo 如果有✗标记，请：
echo 1. 重启命令行窗口
echo 2. 或注销重新登录Windows
echo 3. 再次运行此验证脚本
echo.
pause