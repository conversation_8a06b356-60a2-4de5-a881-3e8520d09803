-- ========================================
-- BOM物料清单管理模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- BOM主表 (多级BOM支持)
CREATE TABLE bom_headers (
    bom_id VARCHAR(32) PRIMARY KEY COMMENT 'BOM ID',
    bom_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'BOM编码',
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    
    -- BOM基本信息
    bom_name VARCHAR(200) NOT NULL COMMENT 'BOM名称',
    bom_version VARCHAR(20) NOT NULL COMMENT 'BOM版本',
    bom_type VARCHAR(20) NOT NULL COMMENT 'BOM类型(EBOM/MBOM/PBOM)',
    bom_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT 'BOM状态',
    
    -- 产品信息
    package_type VARCHAR(50) COMMENT '封装类型(QFP/BGA/CSP/FC)',
    die_size_x DECIMAL(8,3) COMMENT 'Die尺寸X(mm)',
    die_size_y DECIMAL(8,3) COMMENT 'Die尺寸Y(mm)',
    package_size_x DECIMAL(8,3) COMMENT '封装尺寸X(mm)',
    package_size_y DECIMAL(8,3) COMMENT '封装尺寸Y(mm)',
    package_height DECIMAL(8,3) COMMENT '封装高度(mm)',
    pin_count INT COMMENT '引脚数量',
    
    -- 成本信息
    material_cost DECIMAL(10,4) COMMENT '材料成本',
    processing_cost DECIMAL(10,4) COMMENT '加工成本',
    total_cost DECIMAL(10,4) COMMENT '总成本',
    target_cost DECIMAL(10,4) COMMENT '目标成本',
    
    -- BOM层次
    bom_level INT DEFAULT 1 COMMENT 'BOM层级',
    parent_bom_id VARCHAR(32) COMMENT '父级BOM ID',
    
    -- 生效信息
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 业务信息
    customer_id VARCHAR(32) COMMENT '客户ID',
    npi_project_id VARCHAR(32) COMMENT 'NPI项目ID',
    process_flow_id VARCHAR(32) COMMENT '工艺流程ID',
    
    -- 审批信息
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version_no INT DEFAULT 1 COMMENT '版本号',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_bom_product (product_id),
    INDEX idx_bom_code (bom_code),
    INDEX idx_bom_type (bom_type),
    INDEX idx_bom_status (bom_status),
    INDEX idx_bom_version (product_id, bom_version),
    INDEX idx_bom_parent (parent_bom_id),
    INDEX idx_bom_customer (customer_id),
    INDEX idx_bom_npi (npi_project_id),
    INDEX idx_bom_effective (effective_date, expiry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM主表';

-- BOM组件明细表
CREATE TABLE bom_components (
    component_id VARCHAR(32) PRIMARY KEY COMMENT '组件ID',
    bom_id VARCHAR(32) NOT NULL COMMENT 'BOM ID',
    item_sequence INT NOT NULL COMMENT '项目序号',
    
    -- 物料信息
    material_id VARCHAR(32) NOT NULL COMMENT '物料ID',
    material_code VARCHAR(50) NOT NULL COMMENT '物料编码',
    material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
    material_type VARCHAR(50) NOT NULL COMMENT '物料类型',
    material_category VARCHAR(50) COMMENT '物料分类',
    
    -- 规格信息
    specification TEXT COMMENT '规格说明',
    model_no VARCHAR(100) COMMENT '型号',
    brand VARCHAR(100) COMMENT '品牌',
    supplier_id VARCHAR(32) COMMENT '供应商ID',
    supplier_part_no VARCHAR(100) COMMENT '供应商物料号',
    
    -- 用量信息
    quantity DECIMAL(12,6) NOT NULL COMMENT '用量',
    unit VARCHAR(20) NOT NULL COMMENT '单位',
    loss_rate DECIMAL(5,4) DEFAULT 0 COMMENT '损耗率',
    actual_quantity DECIMAL(12,6) COMMENT '实际用量(含损耗)',
    
    -- 成本信息
    unit_cost DECIMAL(10,4) COMMENT '单价',
    total_cost DECIMAL(15,2) COMMENT '总成本',
    
    -- 工艺信息
    process_step VARCHAR(50) COMMENT '工艺步骤',
    station_code VARCHAR(50) COMMENT '工位代码',
    operation_sequence INT COMMENT '作业顺序',
    
    -- 替换信息
    is_critical TINYINT(1) DEFAULT 0 COMMENT '是否关键物料',
    substitute_group VARCHAR(50) COMMENT '替换组',
    is_optional TINYINT(1) DEFAULT 0 COMMENT '是否可选',
    
    -- 质量要求
    quality_grade VARCHAR(20) COMMENT '质量等级',
    inspection_required TINYINT(1) DEFAULT 0 COMMENT '是否需要检验',
    cert_required TINYINT(1) DEFAULT 0 COMMENT '是否需要证书',
    
    -- 生效信息
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 备注
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (bom_id) REFERENCES bom_headers(bom_id) ON DELETE CASCADE,
    INDEX idx_bom_comp_bom (bom_id),
    INDEX idx_bom_comp_material (material_id),
    INDEX idx_bom_comp_sequence (bom_id, item_sequence),
    INDEX idx_bom_comp_type (material_type),
    INDEX idx_bom_comp_supplier (supplier_id),
    INDEX idx_bom_comp_critical (is_critical),
    UNIQUE KEY uk_bom_comp_item (bom_id, item_sequence)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM组件明细表';

-- BOM替换物料表
CREATE TABLE bom_substitutes (
    substitute_id VARCHAR(32) PRIMARY KEY COMMENT '替换ID',
    bom_id VARCHAR(32) NOT NULL COMMENT 'BOM ID',
    primary_component_id VARCHAR(32) NOT NULL COMMENT '主物料组件ID',
    substitute_material_id VARCHAR(32) NOT NULL COMMENT '替换物料ID',
    substitute_material_code VARCHAR(50) NOT NULL COMMENT '替换物料编码',
    substitute_material_name VARCHAR(200) NOT NULL COMMENT '替换物料名称',
    
    -- 替换信息
    substitute_type VARCHAR(20) NOT NULL COMMENT '替换类型(PREFERRED/ALTERNATE/EMERGENCY)',
    substitute_priority INT DEFAULT 1 COMMENT '替换优先级(1-9)',
    substitute_ratio DECIMAL(8,4) DEFAULT 1 COMMENT '替换比例',
    
    -- 条件信息
    condition_description TEXT COMMENT '使用条件',
    min_quantity DECIMAL(12,6) COMMENT '最小替换数量',
    max_quantity DECIMAL(12,6) COMMENT '最大替换数量',
    
    -- 成本信息
    cost_difference DECIMAL(10,4) COMMENT '成本差异',
    cost_impact_percentage DECIMAL(5,2) COMMENT '成本影响百分比',
    
    -- 质量信息
    quality_impact VARCHAR(100) COMMENT '质量影响',
    qualification_status VARCHAR(20) COMMENT '认证状态',
    qualification_date DATE COMMENT '认证日期',
    
    -- 生效信息
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 审批信息
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (bom_id) REFERENCES bom_headers(bom_id) ON DELETE CASCADE,
    FOREIGN KEY (primary_component_id) REFERENCES bom_components(component_id) ON DELETE CASCADE,
    INDEX idx_bom_sub_bom (bom_id),
    INDEX idx_bom_sub_primary (primary_component_id),
    INDEX idx_bom_sub_material (substitute_material_id),
    INDEX idx_bom_sub_type (substitute_type),
    INDEX idx_bom_sub_priority (substitute_priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM替换物料表';

-- BOM变更历史表
CREATE TABLE bom_change_history (
    change_id VARCHAR(32) PRIMARY KEY COMMENT '变更ID',
    bom_id VARCHAR(32) NOT NULL COMMENT 'BOM ID',
    change_type VARCHAR(20) NOT NULL COMMENT '变更类型(ADD/MODIFY/DELETE)',
    change_category VARCHAR(50) COMMENT '变更分类',
    
    -- 变更内容
    component_id VARCHAR(32) COMMENT '组件ID',
    field_name VARCHAR(50) COMMENT '字段名称',
    old_value TEXT COMMENT '原值',
    new_value TEXT COMMENT '新值',
    
    -- 变更原因
    change_reason TEXT COMMENT '变更原因',
    change_description TEXT COMMENT '变更描述',
    
    -- ECO信息
    eco_no VARCHAR(50) COMMENT 'ECO编号',
    eco_date DATE COMMENT 'ECO日期',
    
    -- 影响分析
    cost_impact DECIMAL(10,4) COMMENT '成本影响',
    lead_time_impact INT COMMENT '交期影响(天)',
    quality_impact TEXT COMMENT '质量影响',
    
    -- 审批信息
    change_requester_id VARCHAR(32) NOT NULL COMMENT '变更申请人',
    approved_by VARCHAR(32) COMMENT '批准人',
    approved_date DATE COMMENT '批准日期',
    
    -- 生效信息
    effective_date DATE COMMENT '生效日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (bom_id) REFERENCES bom_headers(bom_id) ON DELETE CASCADE,
    INDEX idx_bom_change_bom (bom_id),
    INDEX idx_bom_change_type (change_type),
    INDEX idx_bom_change_component (component_id),
    INDEX idx_bom_change_eco (eco_no),
    INDEX idx_bom_change_requester (change_requester_id),
    INDEX idx_bom_change_date (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM变更历史表';

-- BOM成本分析表
CREATE TABLE bom_cost_analysis (
    analysis_id VARCHAR(32) PRIMARY KEY COMMENT '分析ID',
    bom_id VARCHAR(32) NOT NULL COMMENT 'BOM ID',
    analysis_date DATE NOT NULL COMMENT '分析日期',
    analysis_version VARCHAR(20) COMMENT '分析版本',
    
    -- 成本分解
    raw_material_cost DECIMAL(15,2) COMMENT '原材料成本',
    packaging_material_cost DECIMAL(15,2) COMMENT '封装材料成本',
    die_cost DECIMAL(15,2) COMMENT 'Die成本',
    leadframe_cost DECIMAL(15,2) COMMENT '引脚框架成本',
    bonding_wire_cost DECIMAL(15,2) COMMENT '键合线成本',
    molding_compound_cost DECIMAL(15,2) COMMENT '封装胶成本',
    other_material_cost DECIMAL(15,2) COMMENT '其他材料成本',
    
    -- 加工成本
    die_attach_cost DECIMAL(15,2) COMMENT 'Die贴装成本',
    wire_bonding_cost DECIMAL(15,2) COMMENT '键合成本',
    molding_cost DECIMAL(15,2) COMMENT '封装成本',
    trim_form_cost DECIMAL(15,2) COMMENT '切筋成型成本',
    marking_cost DECIMAL(15,2) COMMENT '打标成本',
    testing_cost DECIMAL(15,2) COMMENT '测试成本',
    
    -- 总成本
    total_material_cost DECIMAL(15,2) COMMENT '材料总成本',
    total_processing_cost DECIMAL(15,2) COMMENT '加工总成本',
    overhead_cost DECIMAL(15,2) COMMENT '制造费用',
    total_cost DECIMAL(15,2) COMMENT '总成本',
    
    -- 成本占比
    material_cost_percentage DECIMAL(5,2) COMMENT '材料成本占比',
    processing_cost_percentage DECIMAL(5,2) COMMENT '加工成本占比',
    overhead_cost_percentage DECIMAL(5,2) COMMENT '制造费用占比',
    
    -- 目标对比
    target_cost DECIMAL(15,2) COMMENT '目标成本',
    cost_variance DECIMAL(15,2) COMMENT '成本差异',
    cost_variance_percentage DECIMAL(5,2) COMMENT '成本差异百分比',
    
    -- 分析人员
    analyst_id VARCHAR(32) NOT NULL COMMENT '分析人员ID',
    reviewed_by VARCHAR(32) COMMENT '审核人ID',
    approved_by VARCHAR(32) COMMENT '批准人ID',
    
    -- 备注
    analysis_notes TEXT COMMENT '分析说明',
    improvement_suggestions TEXT COMMENT '改进建议',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (bom_id) REFERENCES bom_headers(bom_id) ON DELETE CASCADE,
    INDEX idx_bom_cost_bom (bom_id),
    INDEX idx_bom_cost_date (analysis_date),
    INDEX idx_bom_cost_analyst (analyst_id),
    INDEX idx_bom_cost_version (bom_id, analysis_version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM成本分析表';

-- BOM版本管理表
CREATE TABLE bom_versions (
    version_id VARCHAR(32) PRIMARY KEY COMMENT '版本ID',
    bom_id VARCHAR(32) NOT NULL COMMENT 'BOM ID',
    version_no VARCHAR(20) NOT NULL COMMENT '版本号',
    version_type VARCHAR(20) NOT NULL COMMENT '版本类型(MAJOR/MINOR/PATCH)',
    
    -- 版本信息
    version_description TEXT COMMENT '版本说明',
    change_summary TEXT COMMENT '变更摘要',
    release_notes TEXT COMMENT '发布说明',
    
    -- 版本状态
    version_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '版本状态',
    is_current_version TINYINT(1) DEFAULT 0 COMMENT '是否当前版本',
    
    -- 时间信息
    created_date DATE NOT NULL COMMENT '创建日期',
    released_date DATE COMMENT '发布日期',
    effective_date DATE COMMENT '生效日期',
    end_of_life_date DATE COMMENT '生命周期结束日期',
    
    -- 基线信息
    baseline_snapshot LONGTEXT COMMENT 'BOM基线快照(JSON)',
    checksum VARCHAR(64) COMMENT '校验和',
    
    -- 审批信息
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    approval_comments TEXT COMMENT '审批意见',
    
    -- 发布信息
    released_by VARCHAR(32) COMMENT '发布人',
    release_type VARCHAR(20) COMMENT '发布类型(PRODUCTION/PILOT/ENGINEERING)',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (bom_id) REFERENCES bom_headers(bom_id) ON DELETE CASCADE,
    INDEX idx_bom_ver_bom (bom_id),
    INDEX idx_bom_ver_no (version_no),
    INDEX idx_bom_ver_status (version_status),
    INDEX idx_bom_ver_current (is_current_version),
    INDEX idx_bom_ver_date (created_date),
    UNIQUE KEY uk_bom_version (bom_id, version_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM版本管理表';

-- BOM用料清单表 (生产用料)
CREATE TABLE bom_material_requirements (
    requirement_id VARCHAR(32) PRIMARY KEY COMMENT '需求ID',
    bom_id VARCHAR(32) NOT NULL COMMENT 'BOM ID',
    production_order_id VARCHAR(32) COMMENT '生产订单ID',
    
    -- 需求信息
    required_quantity BIGINT NOT NULL COMMENT '需求数量',
    unit_quantity DECIMAL(12,6) NOT NULL COMMENT '单套用量',
    total_required_quantity DECIMAL(15,6) COMMENT '总需求量',
    
    -- 物料信息
    material_id VARCHAR(32) NOT NULL COMMENT '物料ID',
    material_code VARCHAR(50) NOT NULL COMMENT '物料编码',
    material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
    
    -- 库存信息
    available_quantity DECIMAL(15,6) COMMENT '可用库存',
    reserved_quantity DECIMAL(15,6) COMMENT '预留数量',
    shortage_quantity DECIMAL(15,6) COMMENT '短缺数量',
    
    -- 采购信息
    purchase_required TINYINT(1) DEFAULT 0 COMMENT '是否需要采购',
    purchase_quantity DECIMAL(15,6) COMMENT '采购数量',
    supplier_id VARCHAR(32) COMMENT '供应商ID',
    expected_delivery_date DATE COMMENT '预期到货日期',
    
    -- 生产信息
    required_date DATE NOT NULL COMMENT '需求日期',
    priority_level VARCHAR(20) DEFAULT 'NORMAL' COMMENT '优先级',
    
    -- 状态信息
    requirement_status VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '需求状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (bom_id) REFERENCES bom_headers(bom_id) ON DELETE CASCADE,
    INDEX idx_bom_req_bom (bom_id),
    INDEX idx_bom_req_order (production_order_id),
    INDEX idx_bom_req_material (material_id),
    INDEX idx_bom_req_date (required_date),
    INDEX idx_bom_req_status (requirement_status),
    INDEX idx_bom_req_shortage (shortage_quantity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM用料清单表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. bom_headers: BOM主表，支持多级BOM结构
2. bom_components: BOM组件明细表，记录每个BOM的物料组成
3. bom_substitutes: 替换物料表，支持物料替换管理
4. bom_change_history: BOM变更历史，记录所有变更轨迹
5. bom_cost_analysis: 成本分析表，详细的成本分解和分析
6. bom_versions: 版本管理表，支持BOM版本控制
7. bom_material_requirements: 用料清单表，生产需求计算

核心特性:
- 多级BOM支持 (parent_bom_id)
- 版本控制和变更管理
- 替换物料管理
- 详细成本分解分析
- 生产用料需求计算
- 完整的审计跟踪
*/