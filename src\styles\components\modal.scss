// IC封测CIM系统 - 模态框组件样式

.modal-mask {
  position: fixed;
  inset: 0;
  z-index: var(--z-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  background-color: var(--color-bg-mask);
}

.modal {
  z-index: var(--z-modal);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-5);
    border-bottom: 1px solid var(--color-border-light);
    
    &-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
    
    &-close {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      color: var(--color-text-tertiary);
      cursor: pointer;
      background: transparent;
      border: none;
      border-radius: var(--radius-base);
      transition: all var(--transition-fast);
      
      &:hover {
        color: var(--color-text-primary);
        background-color: var(--color-bg-hover);
      }
    }
  }
  
  &__body {
    padding: var(--spacing-5);
    overflow-y: auto;
  }
  
  &__footer {
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
    padding: var(--spacing-5);
    background-color: var(--color-bg-secondary);
    border-top: 1px solid var(--color-border-light);
  }
}