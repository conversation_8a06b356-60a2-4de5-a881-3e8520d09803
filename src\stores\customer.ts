/**
 * IC封测CIM系统 - 客户管理Store
 * Customer Management Store
 */

import { defineStore } from 'pinia'
import type {
  Customer,
  CustomerQueryParams,
  CustomerListResponse,
  CreateCustomerData
} from '@/types/customer'
import * as customerApi from '@/api/customer'

interface CustomerState {
  /** 客户列表 */
  customers: Customer[]
  /** 当前选中的客户 */
  currentCustomer: Customer | null
  /** 客户总数 */
  total: number
  /** 当前页码 */
  currentPage: number
  /** 每页数量 */
  pageSize: number
  /** 总页数 */
  totalPages: number
  /** 加载状态 */
  loading: boolean
  /** 搜索参数 */
  searchParams: CustomerQueryParams
  /** 客户统计信息 */
  stats: any
}

export const useCustomerStore = defineStore('customer', {
  state: (): CustomerState => ({
    customers: [],
    currentCustomer: null,
    total: 0,
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    loading: false,
    searchParams: {},
    stats: {}
  }),

  getters: {
    /**
     * 获取战略客户列表
     */
    strategicCustomers: state => {
      return state.customers.filter(customer => customer.customerLevel === 'STRATEGIC')
    },

    /**
     * 获取活跃客户列表
     */
    activeCustomers: state => {
      return state.customers.filter(customer => customer.status === 'ACTIVE')
    },

    /**
     * 获取客户类型分布
     */
    customerTypeDistribution: state => {
      const distribution: Record<string, number> = {}
      state.customers.forEach(customer => {
        distribution[customer.customerType] = (distribution[customer.customerType] || 0) + 1
      })
      return distribution
    },

    /**
     * 获取客户等级分布
     */
    customerLevelDistribution: state => {
      const distribution: Record<string, number> = {}
      state.customers.forEach(customer => {
        distribution[customer.customerLevel] = (distribution[customer.customerLevel] || 0) + 1
      })
      return distribution
    },

    /**
     * 是否有更多数据
     */
    hasMore: state => {
      return state.currentPage < state.totalPages
    }
  },

  actions: {
    /**
     * 获取客户列表
     */
    async fetchCustomers(params?: CustomerQueryParams) {
      this.loading = true
      try {
        // 合并搜索参数
        const queryParams = { ...this.searchParams, ...params }

        const response: CustomerListResponse = await customerApi.getCustomers(queryParams)

        // 如果是第一页，替换数据；否则追加数据
        if (!params?.page || params.page === 1) {
          this.customers = response.data
        } else {
          this.customers.push(...response.data)
        }

        this.total = response.total
        this.currentPage = response.page
        this.pageSize = response.pageSize
        this.totalPages = response.totalPages
        this.searchParams = queryParams

        return response
      } catch (error) {
        console.error('获取客户列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 搜索客户
     */
    async searchCustomers(searchParams: CustomerQueryParams) {
      this.searchParams = { ...searchParams, page: 1 }
      return this.fetchCustomers(this.searchParams)
    },

    /**
     * 加载更多客户
     */
    async loadMoreCustomers() {
      if (!this.hasMore || this.loading) return

      const nextPage = this.currentPage + 1
      return this.fetchCustomers({ ...this.searchParams, page: nextPage })
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchParams = {}
      return this.fetchCustomers({ page: 1 })
    },

    /**
     * 根据ID获取客户详情
     */
    async fetchCustomerById(id: string) {
      this.loading = true
      try {
        const customer = await customerApi.getCustomerById(id)
        if (customer) {
          this.currentCustomer = customer

          // 如果客户不在列表中，添加到列表开头
          const existingIndex = this.customers.findIndex(c => c.id === id)
          if (existingIndex === -1) {
            this.customers.unshift(customer)
          } else {
            // 更新列表中的客户信息
            this.customers[existingIndex] = customer
          }
        }
        return customer
      } catch (error) {
        console.error('获取客户详情失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 创建新客户
     */
    async createCustomer(data: CreateCustomerData) {
      this.loading = true
      try {
        const newCustomer = await customerApi.createCustomer(data)

        // 添加到列表开头
        this.customers.unshift(newCustomer)
        this.total++

        // 设置为当前客户
        this.currentCustomer = newCustomer

        return newCustomer
      } catch (error) {
        console.error('创建客户失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 更新客户信息
     */
    async updateCustomer(id: string, data: Partial<CreateCustomerData>) {
      this.loading = true
      try {
        const updatedCustomer = await customerApi.updateCustomer(id, data)

        // 更新列表中的客户信息
        const index = this.customers.findIndex(c => c.id === id)
        if (index !== -1) {
          this.customers[index] = updatedCustomer
        }

        // 更新当前客户
        if (this.currentCustomer?.id === id) {
          this.currentCustomer = updatedCustomer
        }

        return updatedCustomer
      } catch (error) {
        console.error('更新客户失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 删除客户
     */
    async deleteCustomer(id: string) {
      this.loading = true
      try {
        const success = await customerApi.deleteCustomer(id)

        if (success) {
          // 从列表中移除
          const index = this.customers.findIndex(c => c.id === id)
          if (index !== -1) {
            this.customers.splice(index, 1)
            this.total--
          }

          // 清除当前客户
          if (this.currentCustomer?.id === id) {
            this.currentCustomer = null
          }
        }

        return success
      } catch (error) {
        console.error('删除客户失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 批量删除客户
     */
    async batchDeleteCustomers(ids: string[]) {
      this.loading = true
      try {
        const result = await customerApi.batchDeleteCustomers(ids)

        // 从列表中移除已删除的客户
        this.customers = this.customers.filter(customer => !ids.includes(customer.id))
        this.total -= result.success

        // 清除当前客户（如果被删除）
        if (this.currentCustomer && ids.includes(this.currentCustomer.id)) {
          this.currentCustomer = null
        }

        return result
      } catch (error) {
        console.error('批量删除客户失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取客户统计信息
     */
    async fetchCustomerStats() {
      try {
        this.stats = await customerApi.getCustomerStats()
        return this.stats
      } catch (error) {
        console.error('获取客户统计失败:', error)
        throw error
      }
    },

    /**
     * 导出客户数据
     */
    async exportCustomers(params?: CustomerQueryParams) {
      this.loading = true
      try {
        const blob = await customerApi.exportCustomers(params || this.searchParams)

        // 创建下载链接
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `客户数据_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        return blob
      } catch (error) {
        console.error('导出客户数据失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 搜索客户建议
     */
    async searchCustomerSuggestions(keyword: string) {
      try {
        return await customerApi.searchCustomerSuggestions(keyword)
      } catch (error) {
        console.error('搜索客户建议失败:', error)
        return []
      }
    },

    /**
     * 设置当前客户
     */
    setCurrentCustomer(customer: Customer | null) {
      this.currentCustomer = customer
    },

    /**
     * 清空客户列表
     */
    clearCustomers() {
      this.customers = []
      this.currentCustomer = null
      this.total = 0
      this.currentPage = 1
      this.totalPages = 0
      this.searchParams = {}
    }
  }
})
