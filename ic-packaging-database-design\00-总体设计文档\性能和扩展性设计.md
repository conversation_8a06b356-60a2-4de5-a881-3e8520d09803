# IC封测CIM系统性能和扩展性设计

## 1. 性能设计目标

### 1.1 核心性能指标
基于IC封测行业24小时连续生产的特点，制定以下性能目标：

| 性能指标 | 目标值 | 说明 |
|---------|--------|------|
| **数据库响应时间** | <100ms | 95%的查询在100ms内完成 |
| **并发用户数** | 1000+ | 支持1000个并发用户同时操作 |
| **数据写入性能** | 100万TPS | 支持100万次/秒的测试数据写入 |
| **数据查询性能** | 50万QPS | 支持50万次/秒的数据查询 |
| **存储容量** | 100TB+ | 支持100TB以上数据存储 |
| **系统可用性** | 99.99% | 年度停机时间<52.56分钟 |

### 1.2 IC封测行业特殊性能需求

#### 实时数据采集性能
```sql
-- 设备数据采集表设计 - 优化插入性能
CREATE TABLE ic_equipment_realtime_data (
    data_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    equipment_id SMALLINT NOT NULL COMMENT '设备ID(编号化)',
    parameter_id SMALLINT NOT NULL COMMENT '参数ID(编号化)',
    data_value DECIMAL(15,6) NOT NULL COMMENT '参数值',
    collection_time TIMESTAMP(3) NOT NULL COMMENT '采集时间(毫秒精度)',
    data_quality TINYINT DEFAULT 1 COMMENT '数据质量(1-优秀,2-良好,3-可疑)',
    
    -- 分区键 - 按时间分区提升插入性能
    PARTITION BY RANGE (UNIX_TIMESTAMP(collection_time)) (
        PARTITION p202501 VALUES LESS THAN (UNIX_TIMESTAMP('2025-02-01')),
        PARTITION p202502 VALUES LESS THAN (UNIX_TIMESTAMP('2025-03-01')),
        -- ... 按月分区
        PARTITION p_future VALUES LESS THAN MAXVALUE
    ),
    
    -- 复合索引优化查询性能
    INDEX idx_equipment_time (equipment_id, collection_time),
    INDEX idx_parameter_time (parameter_id, collection_time)
) ENGINE=InnoDB 
COMMENT='设备实时数据表-按月分区';
```

#### STDF测试数据大批量插入优化
```sql
-- STDF测试数据批量插入优化
CREATE TABLE ic_stdf_test_data_bulk (
    test_data_id BIGINT PRIMARY KEY,
    lot_id VARCHAR(50) NOT NULL,
    wafer_id VARCHAR(50),
    die_x SMALLINT,
    die_y SMALLINT,
    test_results JSON NOT NULL COMMENT '批量测试结果JSON格式',
    test_summary JSON NOT NULL COMMENT '测试汇总数据',
    test_time TIMESTAMP(3) NOT NULL,
    
    -- 使用压缩存储减少IO
    ROW_FORMAT=COMPRESSED,
    
    -- 按Lot分区提升查询性能
    PARTITION BY KEY(lot_id) PARTITIONS 32
) ENGINE=InnoDB 
COMMENT='STDF测试数据批量存储表-哈希分区';
```

## 2. 分库分表策略

### 2.1 垂直分库设计
基于IC封测业务特点进行垂直分库：

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  核心业务库      │  实时数据库      │  历史数据库      │  配置库  │
│  (Core Business) │  (Real-time)    │  (Historical)   │ (Config) │
├─────────────────────────────────────────────────────────────┤
│ • 产品/客户数据  │ • 设备实时数据   │ • 历史测试数据   │ • 系统参数│
│ • 订单/BOM      │ • SECS/GEM数据  │ • 归档质量数据   │ • 用户权限│
│ • NPI项目       │ • 测试数据流    │ • 统计分析数据   │ • 数据字典│
│ • 质量管理      │ • 设备状态      │ • 报表数据      │ • 工作流  │
└─────────────────────────────────────────────────────────────┘
```

#### 核心业务数据库 (core_business_db)
```sql
-- 核心业务库连接配置
spring.datasource.core.url=jdbc:mysql://core-db-cluster:3306/ic_core_business
spring.datasource.core.username=${DB_CORE_USER}
spring.datasource.core.password=${DB_CORE_PASS}
spring.datasource.core.hikari.maximum-pool-size=50
spring.datasource.core.hikari.minimum-idle=10
```

#### 实时数据数据库 (realtime_data_db)
```sql
-- 实时数据库连接配置
spring.datasource.realtime.url=jdbc:mysql://realtime-db-cluster:3306/ic_realtime_data
spring.datasource.realtime.hikari.maximum-pool-size=100
spring.datasource.realtime.hikari.minimum-idle=20
```

### 2.2 水平分表策略

#### 基于时间的分表策略
```sql
-- 测试数据按月分表
CREATE TABLE ic_test_data_202501 (
    test_data_id BIGINT PRIMARY KEY,
    -- ... 其他字段
    test_time TIMESTAMP(3) NOT NULL
) ENGINE=InnoDB;

-- 自动分表触发器
DELIMITER //
CREATE TRIGGER tr_auto_partition_test_data
BEFORE INSERT ON ic_test_data_main
FOR EACH ROW
BEGIN
    SET @table_suffix = DATE_FORMAT(NEW.test_time, '%Y%m');
    SET @sql = CONCAT('INSERT INTO ic_test_data_', @table_suffix, ' VALUES (...)');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END//
DELIMITER ;
```

#### 基于产品线的分表策略
```sql
-- 客户数据按客户分表(数据隔离)
CREATE TABLE ic_customer_001_data LIKE ic_customer_template;
CREATE TABLE ic_customer_002_data LIKE ic_customer_template;
-- ... 每个客户独立数据表

-- 基于客户ID的路由逻辑
@Service
public class CustomerDataRouter {
    public String getTableName(String customerId) {
        return "ic_customer_" + customerId.substring(0,3) + "_data";
    }
}
```

### 2.3 读写分离架构

#### 主从复制配置
```sql
-- 主库配置 (my.cnf)
[mysqld]
server-id=1
log-bin=mysql-bin
binlog-format=ROW
binlog-do-db=ic_core_business,ic_realtime_data
sync_binlog=1
innodb_flush_log_at_trx_commit=1

-- 从库配置 (my.cnf) 
[mysqld]
server-id=2
read-only=1
relay-log=mysql-relay
replicate-do-db=ic_core_business,ic_realtime_data
```

#### 应用层读写分离
```java
// Spring Boot多数据源配置
@Configuration
public class DataSourceConfig {
    
    @Bean
    @Primary
    public DataSource masterDataSource() {
        return DataSourceBuilder.create()
            .url("********************************************")
            .build();
    }
    
    @Bean
    public DataSource slaveDataSource() {
        return DataSourceBuilder.create()
            .url("*******************************************")
            .build();
    }
}

@Service
public class TestDataService {
    
    @Autowired
    @Qualifier("masterDataSource")
    private JdbcTemplate masterJdbcTemplate;
    
    @Autowired 
    @Qualifier("slaveDataSource")
    private JdbcTemplate slaveJdbcTemplate;
    
    // 写操作使用主库
    public void insertTestData(TestData testData) {
        masterJdbcTemplate.update("INSERT INTO ic_test_data ...", testData);
    }
    
    // 读操作使用从库
    public List<TestData> queryTestData(String lotId) {
        return slaveJdbcTemplate.query("SELECT * FROM ic_test_data WHERE lot_id = ?", 
                                     lotId, new TestDataRowMapper());
    }
}
```

## 3. 索引优化策略

### 3.1 索引设计原则
基于IC封测行业查询特点设计索引：

#### 高频查询索引
```sql
-- 产品测试数据查询优化
CREATE INDEX idx_test_product_time ON ic_test_data 
(product_id, test_time DESC, temperature, test_result);

-- Wafer Map查询优化  
CREATE INDEX idx_wafer_spatial ON ic_wafer_map 
(wafer_id, die_x, die_y, test_result);

-- 设备状态查询优化
CREATE INDEX idx_equipment_status ON ic_equipment_realtime 
(equipment_id, collection_time DESC, parameter_type);
```

#### 覆盖索引设计
```sql
-- 测试汇总查询覆盖索引
CREATE INDEX idx_test_summary_covering ON ic_test_data 
(lot_id, test_time, test_result, pass_count, fail_count, total_count);

-- 避免回表查询，提升性能30-50%
SELECT test_result, pass_count, fail_count, total_count 
FROM ic_test_data 
WHERE lot_id = 'LOT123' 
ORDER BY test_time DESC 
LIMIT 100;
```

#### 函数索引优化
```sql
-- 日期范围查询优化
CREATE INDEX idx_test_date_func ON ic_test_data 
((DATE(test_time)), product_id);

-- 温度区间查询优化
CREATE INDEX idx_temperature_range ON ic_test_data 
((CASE WHEN temperature < 0 THEN 'LOW' 
       WHEN temperature < 100 THEN 'NORMAL' 
       ELSE 'HIGH' END));
```

### 3.2 索引维护策略

#### 自动索引分析和优化
```sql
-- 索引使用情况分析
SELECT 
    table_name,
    index_name,
    seq_in_index,
    column_name,
    cardinality,
    sub_part,
    packed,
    nullable,
    index_type
FROM information_schema.statistics 
WHERE table_schema = 'ic_core_business'
ORDER BY table_name, index_name, seq_in_index;

-- 未使用索引识别
SELECT 
    object_schema,
    object_name,
    index_name
FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE index_name IS NOT NULL
  AND count_star = 0
  AND object_schema = 'ic_core_business';
```

#### 在线索引重建
```sql
-- 在线重建大表索引（MySQL 8.0）
ALTER TABLE ic_test_data 
DROP INDEX idx_old_index,
ADD INDEX idx_new_optimized (product_id, test_time DESC, test_result),
ALGORITHM=INPLACE, LOCK=NONE;
```

## 4. 缓存策略设计

### 4.1 多级缓存架构
```
┌─────────────────────────────────────────────────────────┐
│                   应用服务层                             │
├─────────────────────────────────────────────────────────┤
│  L1: 本地缓存 (Caffeine)    │  L2: 分布式缓存 (Redis)   │
│  - 配置数据 (5分钟TTL)      │  - 热点数据 (30分钟TTL)   │ 
│  - 字典数据 (永久缓存)       │  - 用户会话 (2小时TTL)    │
│  - 计算结果 (10分钟TTL)     │  - 查询结果 (15分钟TTL)   │
├─────────────────────────────────────────────────────────┤
│                   数据库层                               │
│  - InnoDB Buffer Pool      │  - Query Cache           │
│  - 数据页缓存               │  - 查询结果缓存           │
└─────────────────────────────────────────────────────────┘
```

#### 本地缓存配置
```java
@Configuration
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(caffeineCacheBuilder());
        return cacheManager;
    }
    
    Caffeine<Object, Object> caffeineCacheBuilder() {
        return Caffeine.newBuilder()
            .initialCapacity(100)
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .recordStats();
    }
}

@Service
public class ProductService {
    
    @Cacheable(value = "products", key = "#productId")
    public Product getProduct(String productId) {
        return productRepository.findById(productId);
    }
    
    @CacheEvict(value = "products", key = "#product.productId")
    public void updateProduct(Product product) {
        productRepository.save(product);
    }
}
```

#### Redis分布式缓存
```yaml
# Redis配置
spring:
  redis:
    cluster:
      nodes:
        - redis-node1:6379
        - redis-node2:6379  
        - redis-node3:6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 200
        max-idle: 20
        min-idle: 5
```

```java
@Component
public class TestDataCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 缓存测试数据汇总
    public TestSummary getTestSummaryCache(String lotId) {
        String key = "test_summary:" + lotId;
        TestSummary summary = (TestSummary) redisTemplate.opsForValue().get(key);
        
        if (summary == null) {
            summary = calculateTestSummary(lotId);
            // 缓存15分钟
            redisTemplate.opsForValue().set(key, summary, Duration.ofMinutes(15));
        }
        
        return summary;
    }
}
```

### 4.2 缓存更新策略

#### 基于事件的缓存更新
```java
@EventListener
public class CacheUpdateEventListener {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @EventListener
    public void handleTestDataUpdate(TestDataUpdateEvent event) {
        // 清除相关缓存
        String pattern = "test_summary:" + event.getLotId() + "*";
        Set<String> keys = redisTemplate.keys(pattern);
        if (!keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
        
        // 预热新缓存
        preloadTestSummaryCache(event.getLotId());
    }
}
```

## 5. 连接池优化

### 5.1 HikariCP连接池配置
```yaml
# 数据库连接池配置
spring:
  datasource:
    hikari:
      # 核心业务库连接池
      core:
        maximum-pool-size: 50      # 最大连接数
        minimum-idle: 10           # 最小空闲连接数
        idle-timeout: 300000       # 空闲连接超时(5分钟)
        max-lifetime: 1800000      # 连接最大生命周期(30分钟)
        connection-timeout: 30000   # 连接超时(30秒)
        validation-timeout: 5000    # 验证超时(5秒)
        leak-detection-threshold: 60000  # 连接泄露检测(1分钟)
        
      # 实时数据库连接池
      realtime:
        maximum-pool-size: 100     # 实时数据需要更多连接
        minimum-idle: 20
        idle-timeout: 180000       # 更短的空闲超时(3分钟)
        max-lifetime: 900000       # 更短的生命周期(15分钟)
```

### 5.2 连接池监控
```java
@Component
public class ConnectionPoolMonitor {
    
    @Autowired
    private DataSource dataSource;
    
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void monitorConnectionPool() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDS = (HikariDataSource) dataSource;
            HikariPoolMXBean poolMXBean = hikariDS.getHikariPoolMXBean();
            
            int activeConnections = poolMXBean.getActiveConnections();
            int idleConnections = poolMXBean.getIdleConnections();
            int totalConnections = poolMXBean.getTotalConnections();
            
            logger.info("连接池状态 - 活跃: {}, 空闲: {}, 总计: {}", 
                       activeConnections, idleConnections, totalConnections);
            
            // 连接池使用率告警
            double utilization = (double) activeConnections / totalConnections;
            if (utilization > 0.8) {
                alertService.sendAlert("连接池使用率过高: " + (utilization * 100) + "%");
            }
        }
    }
}
```

## 6. 查询优化策略

### 6.1 慢查询优化

#### 慢查询日志分析
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = ON;
SET GLOBAL slow_query_log_file = '/var/log/mysql/mysql-slow.log';
SET GLOBAL long_query_time = 1; -- 超过1秒的查询记录

-- 分析慢查询
mysqldumpslow -s c -t 10 /var/log/mysql/mysql-slow.log
```

#### 查询优化示例
```sql
-- 优化前：全表扫描
SELECT * FROM ic_test_data 
WHERE test_time BETWEEN '2025-01-01' AND '2025-01-31'
  AND product_id LIKE 'PROD%';

-- 优化后：使用复合索引
ALTER TABLE ic_test_data 
ADD INDEX idx_product_time (product_id, test_time);

SELECT product_id, test_time, test_result, pass_count
FROM ic_test_data  
WHERE product_id >= 'PROD' AND product_id < 'PROE'
  AND test_time BETWEEN '2025-01-01' AND '2025-01-31';
```

### 6.2 批量操作优化

#### 批量插入优化
```java
@Service
public class BulkInsertService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Transactional
    public void bulkInsertTestData(List<TestData> testDataList) {
        String sql = "INSERT INTO ic_test_data (lot_id, wafer_id, test_result, test_time) VALUES (?, ?, ?, ?)";
        
        // 使用批量插入，每批1000条
        jdbcTemplate.batchUpdate(sql, testDataList, 1000, 
            (PreparedStatement ps, TestData testData) -> {
                ps.setString(1, testData.getLotId());
                ps.setString(2, testData.getWaferId());
                ps.setBigDecimal(3, testData.getTestResult());
                ps.setTimestamp(4, Timestamp.valueOf(testData.getTestTime()));
            });
    }
}
```

## 7. 扩展性设计

### 7.1 水平扩展策略

#### Sharding策略
```java
@Component
public class DatabaseShardingStrategy {
    
    // 基于客户ID分片
    public String getShardingDatabase(String customerId) {
        int hash = customerId.hashCode();
        int shardIndex = Math.abs(hash) % 4; // 4个分片
        return "ic_business_shard_" + shardIndex;
    }
    
    // 基于时间分片
    public String getTimeBasedShard(LocalDateTime dateTime) {
        String yearMonth = dateTime.format(DateTimeFormatter.ofPattern("yyyyMM"));
        return "ic_data_" + yearMonth;
    }
}
```

#### 分片路由配置
```yaml
# ShardingSphere分片配置
spring:
  shardingsphere:
    datasource:
      names: shard0,shard1,shard2,shard3
      shard0:
        type: com.zaxxer.hikari.HikariDataSource
        jdbc-url: ************************************
      shard1:
        type: com.zaxxer.hikari.HikariDataSource  
        jdbc-url: ************************************
        
    rules:
      sharding:
        tables:
          ic_test_data:
            actual-data-nodes: shard$->{0..3}.ic_test_data_$->{2025..2030}$->{01..12}
            database-strategy:
              standard:
                sharding-column: customer_id
                sharding-algorithm-name: customer_hash
            table-strategy:
              standard:
                sharding-column: test_time
                sharding-algorithm-name: time_range
```

### 7.2 垂直扩展策略

#### 硬件资源优化
```bash
# MySQL配置优化
[mysqld]
# 内存配置
innodb_buffer_pool_size = 32G          # 设置为内存的70-80%
innodb_log_file_size = 2G              # 事务日志文件大小
innodb_flush_log_at_trx_commit = 2     # 异步刷新提升性能

# IO配置
innodb_io_capacity = 4000              # SSD IO能力
innodb_io_capacity_max = 8000          # 最大IO能力
innodb_read_io_threads = 8             # 读IO线程数
innodb_write_io_threads = 8            # 写IO线程数

# 并发配置
innodb_thread_concurrency = 32         # 并发线程数
max_connections = 2000                  # 最大连接数
```

### 7.3 弹性扩容设计

#### 基于Kubernetes的自动扩容
```yaml
# Kubernetes HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: mysql-slave-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mysql-slave
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory  
      target:
        type: Utilization
        averageUtilization: 80
```

## 8. 性能监控和调优

### 8.1 关键性能指标监控
```java
@Component
public class PerformanceMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter queryCounter;
    private final Timer queryTimer;
    private final Gauge connectionGauge;
    
    public PerformanceMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.queryCounter = Counter.builder("database.queries.total")
                .description("Total database queries")
                .register(meterRegistry);
        this.queryTimer = Timer.builder("database.query.duration")
                .description("Database query duration")
                .register(meterRegistry);
    }
    
    @EventListener
    public void onQueryExecuted(QueryExecutedEvent event) {
        queryCounter.increment();
        queryTimer.record(event.getDuration(), TimeUnit.MILLISECONDS);
    }
}
```

### 8.2 自动性能调优
```sql
-- 自动收集统计信息
SET GLOBAL innodb_stats_auto_recalc = ON;
SET GLOBAL innodb_stats_persistent = ON;

-- 自动分析表统计信息
CREATE EVENT evt_analyze_tables
ON SCHEDULE EVERY 1 DAY
STARTS '2025-01-01 02:00:00'
DO
  CALL sp_analyze_all_tables();
  
-- 存储过程：分析所有表
DELIMITER //
CREATE PROCEDURE sp_analyze_all_tables()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE tbl_name VARCHAR(128);
    DECLARE cur CURSOR FOR 
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'ic_core_business' 
          AND table_type = 'BASE TABLE';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO tbl_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        SET @sql = CONCAT('ANALYZE TABLE ', tbl_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END LOOP;
    
    CLOSE cur;
END//
DELIMITER ;
```

---

*本文档定义了IC封测CIM系统的性能和扩展性设计策略，确保系统能够支撑大规模生产环境的高性能需求。*
*版本: V1.0 | 更新时间: 2025年*