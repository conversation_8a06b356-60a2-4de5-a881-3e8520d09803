/**
 * IC封测CIM系统 - 报表分析类型定义
 * Analytics and Reporting Type Definitions for IC Packaging & Testing CIM System
 */

// ===== 报表基础类型 =====

// 报表类型
export enum ReportType {
  DASHBOARD = 'dashboard', // 仪表板
  PRODUCTION = 'production', // 生产报表
  QUALITY = 'quality', // 质量报表
  EQUIPMENT = 'equipment', // 设备报表
  CUSTOMER = 'customer', // 客户报表
  FINANCIAL = 'financial', // 财务报表
  INVENTORY = 'inventory', // 库存报表
  PERFORMANCE = 'performance', // 绩效报表
  CUSTOM = 'custom' // 自定义报表
}

// 报表状态
export enum ReportStatus {
  DRAFT = 'draft', // 草稿
  PUBLISHED = 'published', // 已发布
  ARCHIVED = 'archived', // 已归档
  DISABLED = 'disabled' // 已禁用
}

// 图表类型
export enum ChartType {
  LINE = 'line', // 折线图
  BAR = 'bar', // 柱状图
  PIE = 'pie', // 饼图
  DOUGHNUT = 'doughnut', // 环形图
  AREA = 'area', // 面积图
  SCATTER = 'scatter', // 散点图
  RADAR = 'radar', // 雷达图
  GAUGE = 'gauge', // 仪表盘
  HEATMAP = 'heatmap', // 热力图
  WATERFALL = 'waterfall', // 瀑布图
  FUNNEL = 'funnel', // 漏斗图
  SANKEY = 'sankey', // 桑基图
  TREEMAP = 'treemap', // 树图
  TABLE = 'table', // 表格
  KPI = 'kpi' // KPI指标卡
}

// 数据聚合类型
export enum AggregationType {
  SUM = 'sum', // 求和
  AVG = 'avg', // 平均值
  COUNT = 'count', // 计数
  MAX = 'max', // 最大值
  MIN = 'min', // 最小值
  MEDIAN = 'median', // 中位数
  STDDEV = 'stddev', // 标准差
  VARIANCE = 'variance', // 方差
  DISTINCT_COUNT = 'distinct_count', // 去重计数
  FIRST = 'first', // 第一个值
  LAST = 'last' // 最后一个值
}

// 时间粒度
export enum TimeGranularity {
  MINUTE = 'minute', // 分钟
  HOUR = 'hour', // 小时
  DAY = 'day', // 天
  WEEK = 'week', // 周
  MONTH = 'month', // 月
  QUARTER = 'quarter', // 季度
  YEAR = 'year' // 年
}

// ===== 报表定义 =====

// 报表定义
export interface ReportDefinition {
  id: string
  name: string
  description: string
  type: ReportType
  status: ReportStatus
  category: string
  tags: string[]

  // 数据源配置
  dataSource: DataSourceConfig

  // 图表配置
  charts: ChartConfig[]

  // 过滤器配置
  filters: FilterConfig[]

  // 布局配置
  layout: LayoutConfig

  // 权限配置
  permissions: PermissionConfig

  // 调度配置
  schedule?: ScheduleConfig

  // 元数据
  metadata: {
    createdBy: string
    createdAt: string
    updatedBy: string
    updatedAt: string
    version: string
    lastExecuted?: string
    executionCount: number
  }
}

// 数据源配置
export interface DataSourceConfig {
  type: 'database' | 'api' | 'file' | 'custom'
  connection: {
    url?: string
    database?: string
    table?: string
    query?: string
    apiEndpoint?: string
    filePath?: string
    customFunction?: string
  }
  refreshInterval?: number // 刷新间隔（分钟）
  cacheEnabled?: boolean // 是否启用缓存
  cacheDuration?: number // 缓存持续时间（分钟）
}

// 图表配置
export interface ChartConfig {
  id: string
  name: string
  type: ChartType
  position: {
    x: number
    y: number
    width: number
    height: number
  }

  // 数据配置
  data: {
    dimensions: DimensionConfig[] // 维度
    measures: MeasureConfig[] // 度量
    filters?: FilterCondition[] // 局部过滤器
    sort?: SortConfig[] // 排序
    limit?: number // 数据限制
  }

  // 样式配置
  style: ChartStyleConfig

  // 交互配置
  interactions?: InteractionConfig
}

// 维度配置
export interface DimensionConfig {
  field: string
  alias?: string
  type: 'string' | 'number' | 'date' | 'boolean'
  format?: string // 格式化规则
  groupBy?: boolean // 是否分组
  sort?: 'asc' | 'desc' | 'none' // 排序方式
}

// 度量配置
export interface MeasureConfig {
  field: string
  alias?: string
  aggregation: AggregationType
  format?: string // 格式化规则
  unit?: string // 单位
  precision?: number // 精度
  showPercentage?: boolean // 是否显示百分比
}

// 过滤器配置
export interface FilterConfig {
  id: string
  name: string
  field: string
  type: 'text' | 'number' | 'date' | 'select' | 'multiselect' | 'range'
  operator: '=' | '!=' | '>' | '>=' | '<' | '<=' | 'in' | 'not_in' | 'like' | 'between'
  defaultValue?: any
  options?: Array<{ label: string; value: any }> // 选项列表
  required?: boolean
  visible?: boolean
  position?: {
    x: number
    y: number
  }
}

// 过滤条件
export interface FilterCondition {
  field: string
  operator: string
  value: any
  logic?: 'AND' | 'OR'
}

// 排序配置
export interface SortConfig {
  field: string
  order: 'asc' | 'desc'
  priority: number
}

// 图表样式配置
export interface ChartStyleConfig {
  colors?: string[] // 颜色配置
  theme?: 'light' | 'dark' // 主题
  title?: {
    show: boolean
    text: string
    position: 'top' | 'bottom' | 'left' | 'right'
    style?: any
  }
  legend?: {
    show: boolean
    position: 'top' | 'bottom' | 'left' | 'right'
    style?: any
  }
  axis?: {
    x?: AxisConfig
    y?: AxisConfig
  }
  grid?: {
    show: boolean
    style?: any
  }
  tooltip?: {
    show: boolean
    trigger?: 'item' | 'axis'
    formatter?: string
  }
  animation?: {
    enabled: boolean
    duration?: number
  }
  customOptions?: any // 自定义选项
}

// 坐标轴配置
export interface AxisConfig {
  show: boolean
  name?: string
  type?: 'value' | 'category' | 'time' | 'log'
  min?: number | string
  max?: number | string
  interval?: number | string
  format?: string
  rotate?: number
}

// 布局配置
export interface LayoutConfig {
  type: 'grid' | 'flex' | 'absolute'
  columns?: number // 网格列数
  gap?: number // 间隔
  padding?: number // 内边距
  background?: string // 背景色
  responsive?: boolean // 是否响应式
}

// 权限配置
export interface PermissionConfig {
  viewUsers: string[] // 查看用户
  viewRoles: string[] // 查看角色
  editUsers: string[] // 编辑用户
  editRoles: string[] // 编辑角色
  isPublic?: boolean // 是否公开
}

// 调度配置
export interface ScheduleConfig {
  enabled: boolean
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly'
  cronExpression?: string // Cron表达式
  timezone?: string // 时区
  emailRecipients?: string[] // 邮件接收人
  fileFormat?: 'pdf' | 'excel' | 'csv' // 文件格式
}

// 交互配置
export interface InteractionConfig {
  drillDown?: boolean // 是否支持下钻
  crossFilter?: boolean // 是否支持交叉筛选
  zoom?: boolean // 是否支持缩放
  brush?: boolean // 是否支持刷选
  tooltip?: boolean // 是否显示提示
  legend?: boolean // 是否可点击图例
}

// ===== 报表实例和执行 =====

// 报表实例
export interface ReportInstance {
  id: string
  reportDefinitionId: string
  name: string
  parameters: Record<string, any> // 报表参数
  data: ReportData[] // 报表数据
  metadata: {
    executedBy: string
    executedAt: string
    executionTime: number // 执行耗时（毫秒）
    dataRows: number // 数据行数
    status: 'success' | 'error' | 'timeout'
    error?: string
  }
}

// 报表数据
export interface ReportData {
  chartId: string
  data: any[] // 图表数据
  summary?: {
    totalRecords: number
    aggregations: Record<string, any>
  }
}

// ===== IC封测专用报表类型 =====

// IC封测关键指标
export interface ICPackagingKPI {
  // 生产指标
  production: {
    totalVolume: number // 总产量（K pcs）
    dailyOutput: number // 日产量
    utilizationRate: number // 设备利用率
    throughputTime: number // 吞吐时间
    cycleTime: number // 周期时间
  }

  // 质量指标
  quality: {
    overallYield: number // 总体良率
    cpYield: number // CP良率
    ftYield: number // FT良率
    firstPassYield: number // 一次通过率
    defectRate: number // 缺陷率（ppm）
    reworkRate: number // 返工率
  }

  // 交付指标
  delivery: {
    onTimeDelivery: number // 准时交付率
    averageLeadTime: number // 平均前置时间
    orderFulfillment: number // 订单完成率
  }

  // 成本指标
  cost: {
    unitCost: number // 单位成本
    materialCost: number // 材料成本
    laborCost: number // 人工成本
    equipmentCost: number // 设备成本
  }

  // 设备指标
  equipment: {
    oee: number // 整体设备效率
    availability: number // 可用率
    performance: number // 性能率
    qualityRate: number // 质量率
    mtbf: number // 平均故障间隔时间
    mttr: number // 平均修复时间
  }
}

// 良率分析数据
export interface YieldAnalysis {
  waferLevel: {
    waferLot: string
    totalDies: number
    goodDies: number
    yield: number
    defectTypes: Array<{
      type: string
      count: number
      percentage: number
    }>
  }[]

  packageLevel: {
    lotNumber: string
    inputUnits: number
    outputUnits: number
    yield: number
    stage: 'assembly' | 'test' | 'final'
  }[]

  trendData: Array<{
    date: string
    cpYield: number
    assemblyYield: number
    ftYield: number
    overallYield: number
  }>
}

// 设备效率分析
export interface EquipmentEfficiency {
  equipmentId: string
  equipmentName: string
  type: string

  // OEE组成
  availability: {
    plannedTime: number // 计划时间
    operatingTime: number // 运行时间
    downtime: number // 停机时间
    availabilityRate: number // 可用率
  }

  performance: {
    idealCycleTime: number // 理想周期时间
    actualCycleTime: number // 实际周期时间
    performanceRate: number // 性能率
  }

  quality: {
    totalProduced: number // 总产量
    goodProduced: number // 良品产量
    qualityRate: number // 质量率
  }

  oee: number // 整体设备效率

  // 停机分析
  downtimeReasons: Array<{
    reason: string
    duration: number
    frequency: number
    impact: number
  }>

  // 维护记录
  maintenanceHistory: Array<{
    date: string
    type: 'preventive' | 'corrective'
    duration: number
    cost: number
    description: string
  }>
}

// 客户分析数据
export interface CustomerAnalysis {
  customerId: string
  customerName: string

  // 业务指标
  businessMetrics: {
    totalOrders: number
    totalRevenue: number
    averageOrderValue: number
    orderFrequency: number
    customerLifetimeValue: number
  }

  // 产品偏好
  productPreferences: Array<{
    productCategory: string
    packageType: string
    volume: number
    revenue: number
    growthRate: number
  }>

  // 质量表现
  qualityMetrics: {
    averageYield: number
    defectRate: number
    customerComplaints: number
    qualityScore: number
  }

  // 交付表现
  deliveryMetrics: {
    onTimeDeliveryRate: number
    averageLeadTime: number
    orderChangeFrequency: number
  }

  // 趋势数据
  trendData: Array<{
    month: string
    orderCount: number
    revenue: number
    yieldRate: number
    onTimeDelivery: number
  }>
}

// ===== 报表查询和管理 =====

// 报表查询参数
export interface ReportQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  type?: ReportType
  status?: ReportStatus
  category?: string
  tags?: string[]
  createdBy?: string
  createdDateRange?: [string, string]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 报表执行参数
export interface ReportExecutionParams {
  reportId: string
  parameters?: Record<string, any>
  format?: 'json' | 'pdf' | 'excel' | 'csv'
  async?: boolean // 是否异步执行
  cacheEnabled?: boolean // 是否启用缓存
}

// 报表统计信息
export interface ReportStats {
  totalReports: number
  publishedReports: number
  executionsToday: number
  executionsThisWeek: number
  averageExecutionTime: number
  popularReports: Array<{
    reportId: string
    reportName: string
    executionCount: number
  }>
  errorRate: number
  cacheHitRate: number
}

export default {
  ReportType,
  ReportStatus,
  ChartType,
  AggregationType,
  TimeGranularity
}
