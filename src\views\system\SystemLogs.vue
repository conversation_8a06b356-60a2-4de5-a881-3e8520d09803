<template>
  <div class="system-logs">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Document /></el-icon>
        系统日志
      </h1>
      <p class="page-description">系统操作日志和审计跟踪</p>
    </div>

    <div class="page-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>操作日志</span>
            <div class="header-actions">
              <el-button size="small">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button size="small">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </div>
        </template>

        <div class="coming-soon">
          <el-empty description="系统日志功能开发中">
            <template #image>
              <el-icon size="64"><Document /></el-icon>
            </template>
            <el-button type="primary">返回用户管理</el-button>
          </el-empty>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  // 系统日志页面 - 开发中
  console.log('系统日志页面已加载')
</script>

<style lang="scss" scoped>
  .system-logs {
    padding: var(--spacing-4);
  }

  .page-header {
    margin-bottom: var(--spacing-6);

    .page-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
    }

    .page-description {
      margin: 0;
      color: var(--color-text-secondary);
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      gap: var(--spacing-2);
    }
  }

  .coming-soon {
    padding: var(--spacing-8);
    text-align: center;
  }
</style>