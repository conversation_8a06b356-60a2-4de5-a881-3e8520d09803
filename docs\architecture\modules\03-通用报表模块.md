# 通用报表模块设计

## 1. 模块概述

### 1.1 模块定位
通用报表模块为CIM系统提供统一的报表生成、管理和发布能力，支持各业务模块的标准报表和自定义报表需求，实现报表的可视化设计、自动生成、权限控制和数据分析。

### 1.2 复用价值
- **统一标准**：所有报表采用统一的设计和生成标准
- **快速开发**：可视化设计器大幅减少报表开发时间
- **高度复用**：模板化设计支持快速复制和修改
- **集中管理**：统一的报表管理和权限控制

### 1.3 应用场景覆盖
```
通用报表模块应用场景
├── 生产管理报表
│   ├── 生产日报/周报/月报
│   ├── 工单完成情况报表
│   ├── 产能利用率报表
│   └── 生产效率分析报表
├── 质量管理报表
│   ├── 良率统计报表
│   ├── 缺陷分析报表
│   ├── SPC控制图报表
│   └── 质量成本分析报表
├── 设备管理报表
│   ├── 设备OEE报表
│   ├── 维护成本分析报表
│   ├── 故障统计报表
│   └── 设备效率对比报表
├── 物料管理报表
│   ├── 库存周转报表
│   ├── 物料消耗分析报表
│   ├── 采购成本报表
│   └── 呆滞库存报表
└── 人员管理报表
    ├── 考勤统计报表
    ├── 绩效分析报表
    ├── 培训效果报表
    └── 人员结构分析报表
```

## 2. 技术架构

### 2.1 架构设计
```
通用报表模块架构
├── 报表设计器              # 可视化报表设计
├── 报表引擎核心            # 报表生成引擎
├── 数据源管理              # 多数据源连接管理
├── 模板管理中心            # 报表模板管理
├── 权限控制中心            # 报表权限管理
├── 调度执行中心            # 定时报表生成
├── 缓存优化层              # 报表缓存管理
└── 输出格式转换            # 多格式输出支持
```

### 2.2 核心数据模型

#### 2.2.1 报表定义管理
```sql
-- 报表定义表
CREATE TABLE report_definitions (
    report_id VARCHAR(30) PRIMARY KEY,
    report_code VARCHAR(100) UNIQUE,        -- 报表编码
    report_name VARCHAR(200),               -- 报表名称
    report_category VARCHAR(50),            -- 报表分类
    description TEXT,                       -- 报表描述
    template_type ENUM('table','chart','dashboard','mixed'), -- 模板类型
    data_source_id VARCHAR(30),             -- 数据源ID
    query_config JSON,                      -- 查询配置
    layout_config JSON,                     -- 布局配置
    style_config JSON,                      -- 样式配置
    parameter_config JSON,                  -- 参数配置
    version VARCHAR(20),                    -- 版本号
    status ENUM('draft','published','archived'), -- 状态
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_by VARCHAR(20),
    updated_at TIMESTAMP,
    
    INDEX idx_category_status (report_category, status),
    INDEX idx_code (report_code)
);

-- 数据源配置表
CREATE TABLE data_sources (
    source_id VARCHAR(30) PRIMARY KEY,
    source_name VARCHAR(100),               -- 数据源名称
    source_type ENUM('mysql','postgresql','oracle','api','file'), -- 类型
    connection_config JSON,                 -- 连接配置
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 报表参数定义表
CREATE TABLE report_parameters (
    param_id VARCHAR(30) PRIMARY KEY,
    report_id VARCHAR(30),
    param_name VARCHAR(100),                -- 参数名称
    param_label VARCHAR(200),               -- 参数标签
    param_type ENUM('string','number','date','datetime','boolean','list'), -- 参数类型
    default_value VARCHAR(500),             -- 默认值
    is_required BOOLEAN DEFAULT FALSE,      -- 是否必填
    param_options JSON,                     -- 选项配置（下拉框等）
    validation_rules JSON,                  -- 验证规则
    sort_order INT,                         -- 排序
    
    INDEX idx_report_param (report_id, sort_order)
);
```

#### 2.2.2 报表执行管理
```sql
-- 报表执行记录表
CREATE TABLE report_executions (
    execution_id VARCHAR(30) PRIMARY KEY,
    report_id VARCHAR(30),
    executed_by VARCHAR(20),                -- 执行人
    execution_type ENUM('manual','scheduled','api'), -- 执行类型
    parameters JSON,                        -- 执行参数
    start_time TIMESTAMP,                   -- 开始时间
    end_time TIMESTAMP,                     -- 结束时间
    duration_seconds INT,                   -- 执行时长
    status ENUM('running','success','failed','cancelled'), -- 状态
    result_size_kb INT,                     -- 结果大小
    error_message TEXT,                     -- 错误信息
    output_format VARCHAR(20),              -- 输出格式
    output_path VARCHAR(500),               -- 输出路径
    
    INDEX idx_report_time (report_id, start_time),
    INDEX idx_executed_by (executed_by, start_time)
);

-- 报表调度配置表
CREATE TABLE report_schedules (
    schedule_id VARCHAR(30) PRIMARY KEY,
    report_id VARCHAR(30),
    schedule_name VARCHAR(100),             -- 调度名称
    cron_expression VARCHAR(100),           -- Cron表达式
    parameters JSON,                        -- 默认参数
    output_format VARCHAR(20),              -- 输出格式
    recipients JSON,                        -- 接收人配置
    is_active BOOLEAN DEFAULT TRUE,
    last_execution_time TIMESTAMP,         -- 最后执行时间
    next_execution_time TIMESTAMP,         -- 下次执行时间
    created_at TIMESTAMP
);
```

## 3. 可视化报表设计器

### 3.1 前端设计器组件
```vue
<template>
  <div class="report-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <el-button-group>
        <el-button @click="newReport" icon="el-icon-plus">新建</el-button>
        <el-button @click="saveReport" icon="el-icon-check">保存</el-button>
        <el-button @click="previewReport" icon="el-icon-view">预览</el-button>
        <el-button @click="publishReport" icon="el-icon-upload">发布</el-button>
      </el-button-group>
    </div>
    
    <!-- 主设计区域 -->
    <div class="designer-main">
      <!-- 左侧组件面板 -->
      <div class="component-panel">
        <el-collapse>
          <el-collapse-item title="数据组件" name="data">
            <div class="component-group">
              <div class="component-item" draggable @dragstart="onDragStart($event, 'table')">
                <i class="el-icon-s-grid"></i>
                <span>数据表格</span>
              </div>
              <div class="component-item" draggable @dragstart="onDragStart($event, 'chart')">
                <i class="el-icon-pie-chart"></i>
                <span>统计图表</span>
              </div>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="布局组件" name="layout">
            <div class="component-group">
              <div class="component-item" draggable @dragstart="onDragStart($event, 'container')">
                <i class="el-icon-menu"></i>
                <span>容器</span>
              </div>
              <div class="component-item" draggable @dragstart="onDragStart($event, 'text')">
                <i class="el-icon-document"></i>
                <span>文本</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <!-- 中间设计画布 -->
      <div class="design-canvas" 
           @drop="onDrop" 
           @dragover.prevent
           @dragenter.prevent>
        <div class="canvas-container" :style="canvasStyle">
          <component 
            v-for="component in reportComponents" 
            :key="component.id"
            :is="getComponentType(component.type)"
            :config="component"
            :selected="selectedComponent && selectedComponent.id === component.id"
            @click="selectComponent(component)"
            @update="updateComponent"/>
        </div>
      </div>
      
      <!-- 右侧属性面板 -->
      <div class="property-panel">
        <el-tabs v-model="activePropertyTab">
          <el-tab-pane label="属性" name="properties">
            <component-properties 
              v-if="selectedComponent"
              :component="selectedComponent"
              @change="onPropertyChange"/>
          </el-tab-pane>
          
          <el-tab-pane label="数据" name="data">
            <data-source-config 
              v-if="selectedComponent"
              :component="selectedComponent"
              @change="onDataSourceChange"/>
          </el-tab-pane>
          
          <el-tab-pane label="样式" name="style">
            <style-config 
              v-if="selectedComponent"
              :component="selectedComponent"
              @change="onStyleChange"/>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReportDesigner',
  data() {
    return {
      reportComponents: [],           // 报表组件列表
      selectedComponent: null,        // 当前选中组件
      activePropertyTab: 'properties', // 属性面板激活tab
      canvasStyle: {
        width: '21cm',
        minHeight: '29.7cm',
        backgroundColor: '#ffffff'
      }
    };
  },
  methods: {
    onDragStart(event, componentType) {
      event.dataTransfer.setData('componentType', componentType);
    },
    
    onDrop(event) {
      const componentType = event.dataTransfer.getData('componentType');
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      this.addComponent(componentType, x, y);
    },
    
    addComponent(type, x, y) {
      const component = {
        id: this.generateId(),
        type: type,
        x: x,
        y: y,
        width: this.getDefaultWidth(type),
        height: this.getDefaultHeight(type),
        properties: this.getDefaultProperties(type),
        dataConfig: {},
        styleConfig: {}
      };
      
      this.reportComponents.push(component);
      this.selectComponent(component);
    },
    
    selectComponent(component) {
      this.selectedComponent = component;
    },
    
    async saveReport() {
      const reportDefinition = {
        reportCode: this.reportCode,
        reportName: this.reportName,
        reportCategory: this.reportCategory,
        layoutConfig: {
          components: this.reportComponents,
          canvasStyle: this.canvasStyle
        },
        parameterConfig: this.reportParameters
      };
      
      try {
        await this.$api.report.saveDefinition(reportDefinition);
        this.$message.success('报表保存成功');
      } catch (error) {
        this.$message.error('报表保存失败：' + error.message);
      }
    },
    
    async previewReport() {
      const reportData = await this.generateReportData();
      this.$refs.previewDialog.show(reportData);
    }
  }
};
</script>
```

### 3.2 报表组件库
```javascript
// 数据表格组件
const TableComponent = {
  name: 'TableComponent',
  props: ['config', 'selected'],
  template: `
    <div class="table-component" 
         :class="{ selected: selected }"
         :style="componentStyle">
      <el-table :data="tableData" v-loading="loading">
        <el-table-column 
          v-for="column in columns" 
          :key="column.field"
          :prop="column.field"
          :label="column.label"
          :width="column.width"
          :formatter="column.formatter">
        </el-table-column>
      </el-table>
    </div>
  `,
  computed: {
    componentStyle() {
      return {
        position: 'absolute',
        left: this.config.x + 'px',
        top: this.config.y + 'px',
        width: this.config.width + 'px',
        height: this.config.height + 'px'
      };
    },
    
    columns() {
      return this.config.dataConfig.columns || [];
    }
  },
  data() {
    return {
      tableData: [],
      loading: false
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      if (!this.config.dataConfig.query) return;
      
      this.loading = true;
      try {
        const response = await this.$api.report.executeQuery({
          query: this.config.dataConfig.query,
          parameters: this.config.dataConfig.parameters
        });
        this.tableData = response.data;
      } catch (error) {
        console.error('Failed to load table data:', error);
      } finally {
        this.loading = false;
      }
    }
  }
};

// 图表组件
const ChartComponent = {
  name: 'ChartComponent',
  props: ['config', 'selected'],
  template: `
    <div class="chart-component"
         :class="{ selected: selected }"
         :style="componentStyle">
      <div :id="chartId" style="width: 100%; height: 100%;"></div>
    </div>
  `,
  computed: {
    componentStyle() {
      return {
        position: 'absolute',
        left: this.config.x + 'px',
        top: this.config.y + 'px',
        width: this.config.width + 'px',
        height: this.config.height + 'px'
      };
    },
    
    chartId() {
      return 'chart_' + this.config.id;
    }
  },
  async mounted() {
    await this.initChart();
  },
  methods: {
    async initChart() {
      const chartDom = document.getElementById(this.chartId);
      this.chart = echarts.init(chartDom);
      
      await this.loadChartData();
    },
    
    async loadChartData() {
      if (!this.config.dataConfig.query) return;
      
      try {
        const response = await this.$api.report.executeQuery({
          query: this.config.dataConfig.query,
          parameters: this.config.dataConfig.parameters
        });
        
        const chartOption = this.buildChartOption(response.data);
        this.chart.setOption(chartOption);
      } catch (error) {
        console.error('Failed to load chart data:', error);
      }
    },
    
    buildChartOption(data) {
      const config = this.config.properties;
      
      return {
        title: {
          text: config.title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item[config.xField])
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: config.chartType || 'bar',
          data: data.map(item => item[config.yField])
        }]
      };
    }
  }
};
```

## 4. 报表生成引擎

### 4.1 报表引擎核心
```java
@Service
public class ReportGenerationEngine {
    
    @Autowired
    private DataSourceManager dataSourceManager;
    
    @Autowired
    private QueryExecutor queryExecutor;
    
    @Autowired
    private TemplateRenderer templateRenderer;
    
    @Autowired
    private OutputFormatConverter outputFormatConverter;
    
    public ReportResult generateReport(String reportId, Map<String, Object> parameters, 
                                     String outputFormat, String userId) {
        ReportExecution execution = createExecution(reportId, parameters, outputFormat, userId);
        
        try {
            // 1. 获取报表定义
            ReportDefinition definition = getReportDefinition(reportId);
            
            // 2. 参数验证和预处理
            Map<String, Object> processedParams = processParameters(definition, parameters);
            
            // 3. 执行数据查询
            List<ReportDataSet> dataSets = executeDataQueries(definition, processedParams);
            
            // 4. 渲染报表内容
            ReportContent content = renderReportContent(definition, dataSets, processedParams);
            
            // 5. 格式转换和输出
            byte[] outputData = convertToOutputFormat(content, outputFormat);
            
            // 6. 保存执行结果
            ReportResult result = saveReportResult(execution, outputData, outputFormat);
            
            updateExecutionStatus(execution, ExecutionStatus.SUCCESS);
            
            return result;
            
        } catch (Exception e) {
            updateExecutionStatus(execution, ExecutionStatus.FAILED, e.getMessage());
            throw new ReportGenerationException("报表生成失败", e);
        }
    }
    
    private List<ReportDataSet> executeDataQueries(ReportDefinition definition, 
                                                  Map<String, Object> parameters) {
        List<ReportDataSet> dataSets = new ArrayList<>();
        JSONObject queryConfig = definition.getQueryConfig();
        
        // 主查询
        if (queryConfig.containsKey("mainQuery")) {
            JSONObject mainQuery = queryConfig.getJSONObject("mainQuery");
            ReportDataSet mainDataSet = executeQuery(mainQuery, parameters, "main");
            dataSets.add(mainDataSet);
        }
        
        // 子查询
        if (queryConfig.containsKey("subQueries")) {
            JSONArray subQueries = queryConfig.getJSONArray("subQueries");
            for (int i = 0; i < subQueries.size(); i++) {
                JSONObject subQuery = subQueries.getJSONObject(i);
                String dataSetName = subQuery.getString("name");
                ReportDataSet subDataSet = executeQuery(subQuery, parameters, dataSetName);
                dataSets.add(subDataSet);
            }
        }
        
        return dataSets;
    }
    
    private ReportDataSet executeQuery(JSONObject queryConfig, Map<String, Object> parameters, 
                                     String dataSetName) {
        String dataSourceId = queryConfig.getString("dataSourceId");
        String queryText = queryConfig.getString("query");
        
        // 参数替换
        String processedQuery = processQueryParameters(queryText, parameters);
        
        // 执行查询
        DataSource dataSource = dataSourceManager.getDataSource(dataSourceId);
        List<Map<String, Object>> resultData = queryExecutor.executeQuery(dataSource, processedQuery);
        
        return new ReportDataSet(dataSetName, resultData);
    }
    
    private String processQueryParameters(String queryText, Map<String, Object> parameters) {
        // 简单的参数替换实现，实际项目中可使用更强大的模板引擎
        String processedQuery = queryText;
        
        for (Map.Entry<String, Object> param : parameters.entrySet()) {
            String placeholder = "${" + param.getKey() + "}";
            String value = String.valueOf(param.getValue());
            
            // 根据参数类型进行适当的引用
            if (param.getValue() instanceof String) {
                value = "'" + value + "'";
            } else if (param.getValue() instanceof Date) {
                value = "'" + new SimpleDateFormat("yyyy-MM-dd").format(param.getValue()) + "'";
            }
            
            processedQuery = processedQuery.replace(placeholder, value);
        }
        
        return processedQuery;
    }
}
```

### 4.2 多数据源管理
```java
@Component
public class DataSourceManager {
    
    private Map<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();
    
    @Autowired
    private DataSourceRepository dataSourceRepository;
    
    public DataSource getDataSource(String sourceId) {
        return dataSourceCache.computeIfAbsent(sourceId, this::createDataSource);
    }
    
    private DataSource createDataSource(String sourceId) {
        com.example.entity.DataSource sourceConfig = dataSourceRepository.findById(sourceId)
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在: " + sourceId));
        
        JSONObject config = sourceConfig.getConnectionConfig();
        
        switch (sourceConfig.getSourceType()) {
            case MYSQL:
                return createMySQLDataSource(config);
            case POSTGRESQL:
                return createPostgreSQLDataSource(config);
            case ORACLE:
                return createOracleDataSource(config);
            case API:
                return createAPIDataSource(config);
            default:
                throw new UnsupportedOperationException("不支持的数据源类型: " + sourceConfig.getSourceType());
        }
    }
    
    private DataSource createMySQLDataSource(JSONObject config) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(config.getString("url"));
        hikariConfig.setUsername(config.getString("username"));
        hikariConfig.setPassword(config.getString("password"));
        hikariConfig.setMaximumPoolSize(config.getIntValue("maxPoolSize"));
        hikariConfig.setMinimumIdle(config.getIntValue("minIdle"));
        
        return new HikariDataSource(hikariConfig);
    }
    
    // API数据源适配器
    private DataSource createAPIDataSource(JSONObject config) {
        return new APIDataSourceAdapter(
            config.getString("baseUrl"),
            config.getString("authToken"),
            config.getIntValue("timeout")
        );
    }
}
```

## 5. 报表模板系统

### 5.1 模板引擎集成
```java
@Service
public class ReportTemplateService {
    
    @Autowired
    private TemplateEngine templateEngine; // 可以是Freemarker、Thymeleaf等
    
    public String renderTemplate(String templateContent, Map<String, Object> data) {
        try {
            Template template = templateEngine.createTemplate(templateContent);
            return template.process(data);
        } catch (Exception e) {
            throw new TemplateRenderException("模板渲染失败", e);
        }
    }
    
    public ReportTemplate createTemplate(ReportDefinition definition) {
        JSONObject layoutConfig = definition.getLayoutConfig();
        StringBuilder templateBuilder = new StringBuilder();
        
        // 生成HTML模板
        templateBuilder.append("<!DOCTYPE html><html><head>");
        templateBuilder.append("<style>").append(generateCSS(definition)).append("</style>");
        templateBuilder.append("</head><body>");
        
        // 渲染组件
        JSONArray components = layoutConfig.getJSONArray("components");
        for (int i = 0; i < components.size(); i++) {
            JSONObject component = components.getJSONObject(i);
            templateBuilder.append(renderComponent(component));
        }
        
        templateBuilder.append("</body></html>");
        
        return new ReportTemplate(definition.getReportId(), templateBuilder.toString());
    }
    
    private String renderComponent(JSONObject component) {
        String type = component.getString("type");
        
        switch (type) {
            case "table":
                return renderTableComponent(component);
            case "chart":
                return renderChartComponent(component);
            case "text":
                return renderTextComponent(component);
            default:
                return "";
        }
    }
    
    private String renderTableComponent(JSONObject component) {
        StringBuilder html = new StringBuilder();
        JSONObject dataConfig = component.getJSONObject("dataConfig");
        JSONArray columns = dataConfig.getJSONArray("columns");
        
        html.append("<table class='report-table'>");
        
        // 表头
        html.append("<thead><tr>");
        for (int i = 0; i < columns.size(); i++) {
            JSONObject column = columns.getJSONObject(i);
            html.append("<th>").append(column.getString("label")).append("</th>");
        }
        html.append("</tr></thead>");
        
        // 表体 - 使用模板变量
        html.append("<tbody>");
        html.append("<#list ").append(component.getString("dataSet")).append(" as item>");
        html.append("<tr>");
        for (int i = 0; i < columns.size(); i++) {
            JSONObject column = columns.getJSONObject(i);
            String field = column.getString("field");
            html.append("<td>${item.").append(field).append("!''}</td>");
        }
        html.append("</tr>");
        html.append("</#list>");
        html.append("</tbody>");
        
        html.append("</table>");
        
        return html.toString();
    }
}
```

### 5.2 样式管理
```java
@Service
public class ReportStyleService {
    
    public String generateCSS(ReportDefinition definition) {
        StringBuilder css = new StringBuilder();
        
        // 全局样式
        css.append("body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; }");
        css.append(".report-title { text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 20px; }");
        
        // 表格样式
        css.append(".report-table { width: 100%; border-collapse: collapse; margin: 10px 0; }");
        css.append(".report-table th, .report-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        css.append(".report-table th { background-color: #f5f5f5; font-weight: bold; }");
        css.append(".report-table tr:nth-child(even) { background-color: #f9f9f9; }");
        
        // 图表样式
        css.append(".report-chart { margin: 20px 0; text-align: center; }");
        
        // 组件特定样式
        JSONObject styleConfig = definition.getStyleConfig();
        if (styleConfig != null) {
            css.append(styleConfig.getString("customCSS"));
        }
        
        return css.toString();
    }
}
```

## 6. 输出格式转换

### 6.1 多格式输出支持
```java
@Service
public class OutputFormatConverter {
    
    @Autowired
    private PDFConverter pdfConverter;
    
    @Autowired
    private ExcelConverter excelConverter;
    
    @Autowired
    private WordConverter wordConverter;
    
    public byte[] convert(ReportContent content, String outputFormat) {
        switch (outputFormat.toLowerCase()) {
            case "pdf":
                return pdfConverter.convert(content);
            case "excel":
                return excelConverter.convert(content);
            case "word":
                return wordConverter.convert(content);
            case "html":
                return content.getHtmlContent().getBytes(StandardCharsets.UTF_8);
            default:
                throw new UnsupportedOperationException("不支持的输出格式: " + outputFormat);
        }
    }
}

@Component
public class PDFConverter {
    
    public byte[] convert(ReportContent content) {
        try {
            // 使用iText或其他PDF库生成PDF
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            // HTML转PDF
            HtmlConverter.convertToPdf(content.getHtmlContent(), baos);
            
            return baos.toByteArray();
            
        } catch (Exception e) {
            throw new ConversionException("PDF转换失败", e);
        }
    }
}

@Component  
public class ExcelConverter {
    
    public byte[] convert(ReportContent content) {
        try (Workbook workbook = new XSSFWorkbook(); 
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet("报表");
            
            // 解析HTML表格转换为Excel
            List<ReportTable> tables = content.getTables();
            int rowIndex = 0;
            
            for (ReportTable table : tables) {
                // 创建表头
                Row headerRow = sheet.createRow(rowIndex++);
                List<String> headers = table.getHeaders();
                for (int i = 0; i < headers.size(); i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers.get(i));
                }
                
                // 创建数据行
                List<List<Object>> rows = table.getRows();
                for (List<Object> row : rows) {
                    Row dataRow = sheet.createRow(rowIndex++);
                    for (int i = 0; i < row.size(); i++) {
                        Cell cell = dataRow.createCell(i);
                        Object value = row.get(i);
                        if (value instanceof Number) {
                            cell.setCellValue(((Number) value).doubleValue());
                        } else {
                            cell.setCellValue(String.valueOf(value));
                        }
                    }
                }
                
                rowIndex++; // 表格间留空行
            }
            
            workbook.write(baos);
            return baos.toByteArray();
            
        } catch (Exception e) {
            throw new ConversionException("Excel转换失败", e);
        }
    }
}
```

## 7. 定时调度系统

### 7.1 报表调度服务
```java
@Service
public class ReportScheduleService {
    
    @Autowired
    private TaskScheduler taskScheduler;
    
    @Autowired
    private ReportGenerationEngine reportEngine;
    
    @Autowired
    private NotificationService notificationService;
    
    private Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    
    public void scheduleReport(ReportSchedule schedule) {
        String scheduleId = schedule.getScheduleId();
        
        // 取消已存在的任务
        cancelScheduledReport(scheduleId);
        
        // 创建新任务
        ScheduledFuture<?> future = taskScheduler.schedule(
            () -> executeScheduledReport(schedule),
            new CronTrigger(schedule.getCronExpression())
        );
        
        scheduledTasks.put(scheduleId, future);
    }
    
    private void executeScheduledReport(ReportSchedule schedule) {
        try {
            // 生成报表
            ReportResult result = reportEngine.generateReport(
                schedule.getReportId(),
                schedule.getParameters(),
                schedule.getOutputFormat(),
                "SYSTEM"
            );
            
            // 发送给接收人
            List<String> recipients = parseRecipients(schedule.getRecipients());
            for (String recipient : recipients) {
                notificationService.sendReportNotification(recipient, result);
            }
            
            // 更新调度记录
            updateScheduleExecution(schedule, true, null);
            
        } catch (Exception e) {
            log.error("定时报表执行失败: {}", schedule.getScheduleId(), e);
            updateScheduleExecution(schedule, false, e.getMessage());
        }
    }
    
    public void cancelScheduledReport(String scheduleId) {
        ScheduledFuture<?> future = scheduledTasks.remove(scheduleId);
        if (future != null) {
            future.cancel(false);
        }
    }
    
    @PostConstruct
    public void initScheduledReports() {
        // 系统启动时加载所有激活的调度任务
        List<ReportSchedule> activeSchedules = reportScheduleRepository.findByIsActiveTrue();
        for (ReportSchedule schedule : activeSchedules) {
            scheduleReport(schedule);
        }
    }
}
```

## 8. 权限控制

### 8.1 报表权限管理
```java
@Service
public class ReportPermissionService {
    
    @PreAuthorize("hasPermission(#reportId, 'REPORT', 'VIEW')")
    public ReportResult viewReport(String reportId, String userId) {
        // 权限检查通过后执行查看逻辑
        return reportService.getReport(reportId);
    }
    
    @PreAuthorize("hasPermission(#reportId, 'REPORT', 'EXECUTE')")
    public ReportResult executeReport(String reportId, Map<String, Object> parameters, String userId) {
        // 检查参数权限
        validateParameterPermissions(reportId, parameters, userId);
        
        // 执行报表
        return reportEngine.generateReport(reportId, parameters, "html", userId);
    }
    
    private void validateParameterPermissions(String reportId, Map<String, Object> parameters, String userId) {
        ReportDefinition definition = reportService.getReportDefinition(reportId);
        List<ReportParameter> paramDefs = definition.getParameters();
        
        for (ReportParameter paramDef : paramDefs) {
            if (paramDef.isRestrictedParameter()) {
                Object paramValue = parameters.get(paramDef.getParamName());
                if (!hasParameterPermission(userId, paramDef, paramValue)) {
                    throw new SecurityException("无权限设置参数: " + paramDef.getParamName());
                }
            }
        }
    }
    
    public List<ReportDefinition> getAccessibleReports(String userId) {
        List<ReportDefinition> allReports = reportService.getAllReports();
        
        return allReports.stream()
            .filter(report -> hasReportPermission(userId, report.getReportId(), "VIEW"))
            .collect(Collectors.toList());
    }
}
```

## 9. 缓存优化

### 9.1 多级缓存策略
```java
@Service
public class ReportCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private final Cache<String, ReportResult> localCache = Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(30, TimeUnit.MINUTES)
        .build();
    
    public ReportResult getCachedReport(String cacheKey) {
        // 1. 尝试本地缓存
        ReportResult result = localCache.getIfPresent(cacheKey);
        if (result != null) {
            return result;
        }
        
        // 2. 尝试Redis缓存
        result = (ReportResult) redisTemplate.opsForValue().get(cacheKey);
        if (result != null) {
            // 放入本地缓存
            localCache.put(cacheKey, result);
            return result;
        }
        
        return null;
    }
    
    public void cacheReport(String cacheKey, ReportResult result, Duration expiration) {
        // 缓存到本地
        localCache.put(cacheKey, result);
        
        // 缓存到Redis
        redisTemplate.opsForValue().set(cacheKey, result, expiration);
    }
    
    public String generateCacheKey(String reportId, Map<String, Object> parameters) {
        // 根据报表ID和参数生成缓存键
        StringBuilder keyBuilder = new StringBuilder("report:");
        keyBuilder.append(reportId);
        
        // 参数排序后拼接
        TreeMap<String, Object> sortedParams = new TreeMap<>(parameters);
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            keyBuilder.append(":").append(entry.getKey()).append("=").append(entry.getValue());
        }
        
        return DigestUtils.md5DigestAsHex(keyBuilder.toString().getBytes());
    }
    
    public boolean shouldCache(ReportDefinition definition, Map<String, Object> parameters) {
        // 根据报表配置和参数决定是否缓存
        if (definition.isCacheEnabled()) {
            // 检查参数中是否包含实时性要求高的参数（如当前时间）
            return !parameters.containsKey("currentTime") && !parameters.containsKey("realtime");
        }
        return false;
    }
}
```

---

*通用报表模块为CIM系统提供了强大、灵活、易用的报表解决方案，大幅提升了报表开发效率和用户体验*