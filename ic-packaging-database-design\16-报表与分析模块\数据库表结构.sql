-- ========================================
-- 报表与分析模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- 报表定义主表
CREATE TABLE report_definitions (
    report_id VARCHAR(32) PRIMARY KEY COMMENT '报表ID',
    report_code VARCHAR(50) NOT NULL UNIQUE COMMENT '报表编码',
    report_name VARCHAR(200) NOT NULL COMMENT '报表名称',
    report_type VARCHAR(30) NOT NULL COMMENT '报表类型',
    
    -- 报表分类
    business_category VARCHAR(50) NOT NULL COMMENT '业务分类',
    functional_category VARCHAR(50) COMMENT '功能分类',
    report_level VARCHAR(20) DEFAULT 'OPERATIONAL' COMMENT '报表层级',
    
    -- 数据源配置
    data_source_type VARCHAR(30) NOT NULL COMMENT '数据源类型',
    data_source_config JSON COMMENT '数据源配置',
    main_query TEXT COMMENT '主查询SQL',
    
    -- 报表模板
    template_type VARCHAR(30) COMMENT '模板类型',
    template_file VARCHAR(500) COMMENT '模板文件路径',
    layout_config JSON COMMENT '布局配置',
    
    -- 参数配置
    parameters_config JSON COMMENT '参数配置',
    default_parameters JSON COMMENT '默认参数值',
    
    -- 权限控制
    access_level VARCHAR(20) DEFAULT 'PUBLIC' COMMENT '访问级别',
    authorized_roles JSON COMMENT '授权角色',
    authorized_users JSON COMMENT '授权用户',
    data_filter_rules JSON COMMENT '数据过滤规则',
    
    -- 调度配置
    schedule_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用调度',
    schedule_config JSON COMMENT '调度配置',
    
    -- 输出配置
    output_formats JSON COMMENT '支持的输出格式',
    default_format VARCHAR(20) DEFAULT 'PDF' COMMENT '默认输出格式',
    
    -- 缓存配置
    cache_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用缓存',
    cache_duration INT COMMENT '缓存时长(分钟)',
    
    -- 报表描述
    report_description TEXT COMMENT '报表描述',
    usage_notes TEXT COMMENT '使用说明',
    
    -- 状态管理
    report_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '报表状态',
    
    -- 设计信息
    designer_id VARCHAR(32) NOT NULL COMMENT '设计人ID',
    design_date DATE NOT NULL COMMENT '设计日期',
    
    -- 审批信息
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    
    -- 版本信息
    report_version VARCHAR(20) DEFAULT '1.0' COMMENT '报表版本',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_report_code (report_code),
    INDEX idx_report_type (report_type),
    INDEX idx_report_business (business_category),
    INDEX idx_report_status (report_status),
    INDEX idx_report_designer (designer_id),
    INDEX idx_report_level (report_level),
    INDEX idx_report_version (report_code, report_version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表定义主表';

-- 报表执行历史表
CREATE TABLE report_execution_history (
    execution_id VARCHAR(32) PRIMARY KEY COMMENT '执行ID',
    report_id VARCHAR(32) NOT NULL COMMENT '报表ID',
    execution_code VARCHAR(50) NOT NULL UNIQUE COMMENT '执行编码',
    
    -- 执行信息
    execution_type VARCHAR(20) NOT NULL COMMENT '执行类型',
    execution_trigger VARCHAR(30) COMMENT '执行触发方式',
    
    -- 参数信息
    execution_parameters JSON COMMENT '执行参数',
    data_range JSON COMMENT '数据范围',
    
    -- 执行结果
    execution_status VARCHAR(20) NOT NULL COMMENT '执行状态',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    execution_duration INT COMMENT '执行时长(秒)',
    
    -- 数据统计
    record_count BIGINT COMMENT '记录数量',
    data_size BIGINT COMMENT '数据大小(字节)',
    
    -- 输出信息
    output_format VARCHAR(20) COMMENT '输出格式',
    output_file_path VARCHAR(500) COMMENT '输出文件路径',
    output_file_size BIGINT COMMENT '输出文件大小(字节)',
    
    -- 错误信息
    error_message TEXT COMMENT '错误信息',
    error_stack_trace TEXT COMMENT '错误堆栈',
    
    -- 执行人信息
    executed_by VARCHAR(32) NOT NULL COMMENT '执行人',
    execution_client VARCHAR(100) COMMENT '执行客户端',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    
    -- 性能指标
    query_time INT COMMENT '查询时间(毫秒)',
    render_time INT COMMENT '渲染时间(毫秒)',
    export_time INT COMMENT '导出时间(毫秒)',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (report_id) REFERENCES report_definitions(report_id) ON DELETE CASCADE,
    INDEX idx_report_exec_report (report_id),
    INDEX idx_report_exec_status (execution_status),
    INDEX idx_report_exec_time (start_time),
    INDEX idx_report_exec_user (executed_by),
    INDEX idx_report_exec_type (execution_type),
    INDEX idx_report_exec_duration (execution_duration)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表执行历史表';

-- 仪表盘定义表
CREATE TABLE dashboard_definitions (
    dashboard_id VARCHAR(32) PRIMARY KEY COMMENT '仪表盘ID',
    dashboard_code VARCHAR(50) NOT NULL UNIQUE COMMENT '仪表盘编码',
    dashboard_name VARCHAR(200) NOT NULL COMMENT '仪表盘名称',
    dashboard_type VARCHAR(30) NOT NULL COMMENT '仪表盘类型',
    
    -- 仪表盘分类
    business_category VARCHAR(50) NOT NULL COMMENT '业务分类',
    target_audience VARCHAR(50) COMMENT '目标用户群',
    
    -- 布局配置
    layout_type VARCHAR(30) DEFAULT 'GRID' COMMENT '布局类型',
    layout_config JSON COMMENT '布局配置',
    grid_columns INT DEFAULT 12 COMMENT '网格列数',
    
    -- 样式配置
    theme_config JSON COMMENT '主题配置',
    css_config JSON COMMENT 'CSS配置',
    
    -- 刷新配置
    auto_refresh_enabled TINYINT(1) DEFAULT 1 COMMENT '是否自动刷新',
    refresh_interval INT DEFAULT 300 COMMENT '刷新间隔(秒)',
    
    -- 权限控制
    access_level VARCHAR(20) DEFAULT 'PUBLIC' COMMENT '访问级别',
    authorized_roles JSON COMMENT '授权角色',
    authorized_users JSON COMMENT '授权用户',
    
    -- 个性化配置
    personalization_enabled TINYINT(1) DEFAULT 1 COMMENT '是否允许个性化',
    default_filters JSON COMMENT '默认过滤器',
    
    -- 仪表盘描述
    dashboard_description TEXT COMMENT '仪表盘描述',
    
    -- 状态管理
    dashboard_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '仪表盘状态',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认仪表盘',
    
    -- 设计信息
    designer_id VARCHAR(32) NOT NULL COMMENT '设计人ID',
    design_date DATE NOT NULL COMMENT '设计日期',
    
    -- 版本信息
    dashboard_version VARCHAR(20) DEFAULT '1.0' COMMENT '版本',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_dashboard_code (dashboard_code),
    INDEX idx_dashboard_type (dashboard_type),
    INDEX idx_dashboard_business (business_category),
    INDEX idx_dashboard_status (dashboard_status),
    INDEX idx_dashboard_designer (designer_id),
    INDEX idx_dashboard_default (is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仪表盘定义表';

-- 仪表盘组件表
CREATE TABLE dashboard_components (
    component_id VARCHAR(32) PRIMARY KEY COMMENT '组件ID',
    dashboard_id VARCHAR(32) NOT NULL COMMENT '仪表盘ID',
    component_code VARCHAR(50) NOT NULL COMMENT '组件编码',
    component_name VARCHAR(200) NOT NULL COMMENT '组件名称',
    component_type VARCHAR(30) NOT NULL COMMENT '组件类型',
    
    -- 位置和尺寸
    position_x INT NOT NULL COMMENT 'X坐标',
    position_y INT NOT NULL COMMENT 'Y坐标',
    width INT NOT NULL COMMENT '宽度',
    height INT NOT NULL COMMENT '高度',
    z_index INT DEFAULT 1 COMMENT 'Z轴层级',
    
    -- 数据配置
    data_source_type VARCHAR(30) COMMENT '数据源类型',
    data_source_config JSON COMMENT '数据源配置',
    query_config JSON COMMENT '查询配置',
    
    -- 图表配置
    chart_type VARCHAR(30) COMMENT '图表类型',
    chart_config JSON COMMENT '图表配置',
    
    -- 显示配置
    title_config JSON COMMENT '标题配置',
    legend_config JSON COMMENT '图例配置',
    axis_config JSON COMMENT '坐标轴配置',
    
    -- 交互配置
    drill_down_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用下钻',
    drill_down_config JSON COMMENT '下钻配置',
    click_action_config JSON COMMENT '点击动作配置',
    
    -- 过滤配置
    filter_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用过滤',
    filter_config JSON COMMENT '过滤配置',
    
    -- 刷新配置
    refresh_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用刷新',
    refresh_interval INT COMMENT '刷新间隔(秒)',
    
    -- 缓存配置
    cache_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用缓存',
    cache_duration INT COMMENT '缓存时长(分钟)',
    
    -- 告警配置
    alert_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用告警',
    alert_config JSON COMMENT '告警配置',
    
    -- 组件状态
    component_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '组件状态',
    is_visible TINYINT(1) DEFAULT 1 COMMENT '是否可见',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (dashboard_id) REFERENCES dashboard_definitions(dashboard_id) ON DELETE CASCADE,
    INDEX idx_component_dashboard (dashboard_id),
    INDEX idx_component_code (component_code),
    INDEX idx_component_type (component_type),
    INDEX idx_component_position (position_x, position_y),
    INDEX idx_component_status (component_status),
    UNIQUE KEY uk_dashboard_component_code (dashboard_id, component_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仪表盘组件表';

-- KPI指标定义表
CREATE TABLE kpi_definitions (
    kpi_id VARCHAR(32) PRIMARY KEY COMMENT 'KPI ID',
    kpi_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'KPI编码',
    kpi_name VARCHAR(200) NOT NULL COMMENT 'KPI名称',
    kpi_category VARCHAR(50) NOT NULL COMMENT 'KPI分类',
    
    -- 指标属性
    measurement_unit VARCHAR(20) COMMENT '度量单位',
    kpi_type VARCHAR(30) NOT NULL COMMENT 'KPI类型',
    calculation_method VARCHAR(30) NOT NULL COMMENT '计算方法',
    
    -- 计算公式
    calculation_formula TEXT COMMENT '计算公式',
    data_source_config JSON COMMENT '数据源配置',
    calculation_sql TEXT COMMENT '计算SQL',
    
    -- 目标值设置
    target_value DECIMAL(15,4) COMMENT '目标值',
    warning_threshold DECIMAL(15,4) COMMENT '预警阈值',
    critical_threshold DECIMAL(15,4) COMMENT '临界阈值',
    
    -- 阈值方向
    threshold_direction VARCHAR(20) DEFAULT 'HIGHER_BETTER' COMMENT '阈值方向',
    
    -- 计算频率
    calculation_frequency VARCHAR(20) NOT NULL COMMENT '计算频率',
    aggregation_period VARCHAR(20) COMMENT '聚合周期',
    
    -- 责任归属
    owner_department VARCHAR(100) COMMENT '责任部门',
    owner_person VARCHAR(32) COMMENT '责任人',
    
    -- 业务含义
    business_definition TEXT COMMENT '业务定义',
    calculation_logic TEXT COMMENT '计算逻辑说明',
    usage_notes TEXT COMMENT '使用说明',
    
    -- 状态管理
    kpi_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT 'KPI状态',
    is_key_indicator TINYINT(1) DEFAULT 0 COMMENT '是否关键指标',
    
    -- 显示配置
    display_format VARCHAR(50) COMMENT '显示格式',
    decimal_places INT DEFAULT 2 COMMENT '小数位数',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_kpi_code (kpi_code),
    INDEX idx_kpi_category (kpi_category),
    INDEX idx_kpi_type (kpi_type),
    INDEX idx_kpi_status (kpi_status),
    INDEX idx_kpi_owner (owner_person),
    INDEX idx_kpi_key (is_key_indicator)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='KPI指标定义表';

-- KPI实际值表
CREATE TABLE kpi_actual_values (
    value_id VARCHAR(32) PRIMARY KEY COMMENT '值ID',
    kpi_id VARCHAR(32) NOT NULL COMMENT 'KPI ID',
    calculation_date DATE NOT NULL COMMENT '计算日期',
    calculation_time TIMESTAMP NOT NULL COMMENT '计算时间',
    
    -- 维度信息
    dimension_values JSON COMMENT '维度值',
    
    -- 实际值
    actual_value DECIMAL(15,4) NOT NULL COMMENT '实际值',
    formatted_value VARCHAR(100) COMMENT '格式化值',
    
    -- 目标对比
    target_value DECIMAL(15,4) COMMENT '目标值',
    variance_value DECIMAL(15,4) COMMENT '差异值',
    variance_percentage DECIMAL(8,2) COMMENT '差异百分比',
    achievement_rate DECIMAL(8,2) COMMENT '完成率',
    
    -- 状态评价
    performance_level VARCHAR(20) COMMENT '绩效水平',
    alert_level VARCHAR(20) COMMENT '告警级别',
    
    -- 趋势信息
    previous_value DECIMAL(15,4) COMMENT '上期值',
    trend_direction VARCHAR(20) COMMENT '趋势方向',
    change_rate DECIMAL(8,2) COMMENT '变化率',
    
    -- 数据质量
    data_quality_score DECIMAL(3,2) DEFAULT 1.0 COMMENT '数据质量评分',
    confidence_level DECIMAL(3,2) DEFAULT 1.0 COMMENT '置信水平',
    
    -- 计算信息
    calculation_method_used VARCHAR(50) COMMENT '使用的计算方法',
    data_sample_size BIGINT COMMENT '数据样本量',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (kpi_id) REFERENCES kpi_definitions(kpi_id) ON DELETE CASCADE,
    INDEX idx_kpi_value_kpi (kpi_id),
    INDEX idx_kpi_value_date (calculation_date),
    INDEX idx_kpi_value_time (calculation_time),
    INDEX idx_kpi_value_level (performance_level),
    INDEX idx_kpi_value_alert (alert_level),
    UNIQUE KEY uk_kpi_date_dimension (kpi_id, calculation_date, MD5(IFNULL(dimension_values, '')))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='KPI实际值表';

-- 数据分析任务表
CREATE TABLE data_analysis_tasks (
    task_id VARCHAR(32) PRIMARY KEY COMMENT '任务ID',
    task_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    task_type VARCHAR(30) NOT NULL COMMENT '任务类型',
    
    -- 分析目标
    analysis_objective TEXT NOT NULL COMMENT '分析目标',
    business_question TEXT COMMENT '业务问题',
    expected_outcome TEXT COMMENT '预期成果',
    
    -- 数据配置
    data_source_config JSON NOT NULL COMMENT '数据源配置',
    data_range_config JSON COMMENT '数据范围配置',
    data_quality_requirements JSON COMMENT '数据质量要求',
    
    -- 分析方法
    analysis_method VARCHAR(50) NOT NULL COMMENT '分析方法',
    analysis_algorithm VARCHAR(100) COMMENT '分析算法',
    algorithm_parameters JSON COMMENT '算法参数',
    
    -- 模型配置
    model_type VARCHAR(50) COMMENT '模型类型',
    model_config JSON COMMENT '模型配置',
    training_config JSON COMMENT '训练配置',
    
    -- 输出配置
    output_format VARCHAR(30) DEFAULT 'REPORT' COMMENT '输出格式',
    output_template VARCHAR(500) COMMENT '输出模板',
    visualization_config JSON COMMENT '可视化配置',
    
    -- 调度配置
    schedule_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用调度',
    schedule_config JSON COMMENT '调度配置',
    
    -- 任务状态
    task_status VARCHAR(20) NOT NULL DEFAULT 'CREATED' COMMENT '任务状态',
    
    -- 任务优先级
    priority_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '优先级',
    
    -- 负责人
    analyst_id VARCHAR(32) NOT NULL COMMENT '分析师ID',
    requester_id VARCHAR(32) COMMENT '需求方ID',
    
    -- 时间信息
    request_date DATE NOT NULL COMMENT '需求日期',
    target_completion_date DATE COMMENT '目标完成日期',
    actual_completion_date DATE COMMENT '实际完成日期',
    
    -- 任务描述
    task_description TEXT COMMENT '任务描述',
    business_impact TEXT COMMENT '业务影响',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_analysis_task_code (task_code),
    INDEX idx_analysis_task_type (task_type),
    INDEX idx_analysis_task_status (task_status),
    INDEX idx_analysis_task_analyst (analyst_id),
    INDEX idx_analysis_task_requester (requester_id),
    INDEX idx_analysis_task_priority (priority_level),
    INDEX idx_analysis_task_dates (request_date, target_completion_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据分析任务表';

-- 数据分析结果表
CREATE TABLE data_analysis_results (
    result_id VARCHAR(32) PRIMARY KEY COMMENT '结果ID',
    task_id VARCHAR(32) NOT NULL COMMENT '任务ID',
    result_code VARCHAR(50) NOT NULL COMMENT '结果编码',
    result_name VARCHAR(200) NOT NULL COMMENT '结果名称',
    
    -- 分析结果
    analysis_summary TEXT COMMENT '分析摘要',
    key_findings JSON COMMENT '关键发现',
    statistical_results JSON COMMENT '统计结果',
    model_performance JSON COMMENT '模型性能',
    
    -- 业务洞察
    business_insights TEXT COMMENT '业务洞察',
    actionable_recommendations TEXT COMMENT '可行性建议',
    risk_assessment TEXT COMMENT '风险评估',
    
    -- 数据质量
    data_quality_assessment JSON COMMENT '数据质量评估',
    analysis_confidence DECIMAL(3,2) COMMENT '分析置信度',
    reliability_score DECIMAL(3,2) COMMENT '可靠性评分',
    
    -- 输出文件
    result_files JSON COMMENT '结果文件列表',
    visualization_files JSON COMMENT '可视化文件列表',
    
    -- 验证信息
    validation_status VARCHAR(20) COMMENT '验证状态',
    validation_results JSON COMMENT '验证结果',
    validated_by VARCHAR(32) COMMENT '验证人',
    validation_date DATE COMMENT '验证日期',
    
    -- 发布信息
    publication_status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '发布状态',
    published_by VARCHAR(32) COMMENT '发布人',
    publication_date DATE COMMENT '发布日期',
    target_audience JSON COMMENT '目标受众',
    
    -- 使用情况
    access_count INT DEFAULT 0 COMMENT '访问次数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    feedback_rating DECIMAL(3,2) COMMENT '反馈评分',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (task_id) REFERENCES data_analysis_tasks(task_id) ON DELETE CASCADE,
    INDEX idx_analysis_result_task (task_id),
    INDEX idx_analysis_result_code (result_code),
    INDEX idx_analysis_result_validation (validation_status),
    INDEX idx_analysis_result_publication (publication_status),
    INDEX idx_analysis_result_validated_by (validated_by),
    INDEX idx_analysis_result_published_by (published_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据分析结果表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. report_definitions: 报表定义主表，定义各种业务报表
2. report_execution_history: 报表执行历史表，跟踪报表运行情况
3. dashboard_definitions: 仪表盘定义表，管理可视化仪表盘
4. dashboard_components: 仪表盘组件表，仪表盘的具体组件
5. kpi_definitions: KPI指标定义表，关键绩效指标定义
6. kpi_actual_values: KPI实际值表，指标的实际计算结果
7. data_analysis_tasks: 数据分析任务表，分析任务管理
8. data_analysis_results: 数据分析结果表，分析结果存储

核心特性:
- 完整的报表生命周期管理
- 灵活的仪表盘设计和展示
- 科学的KPI指标体系
- 强大的数据分析能力
- 全面的执行跟踪和性能监控
- 多样化的数据可视化支持
- 智能的告警和预警机制
*/