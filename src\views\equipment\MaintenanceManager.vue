<template>
  <div class="maintenance-manager">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">预测性维护管理</h1>
        <p class="page-description">设备维护计划制定、任务执行跟踪和备件库存管理</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreatePlanDialog = true">
          <el-icon><Plus /></el-icon>
          新建维护计划
        </el-button>
        <el-button type="success" @click="showCreateTaskDialog = true">
          <el-icon><Calendar /></el-icon>
          新建维护任务
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="24">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon scheduled">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ taskStats.scheduledTasks }}
                </div>
                <div class="stat-label">计划任务</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon in-progress">
                <el-icon><Tools /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ taskStats.inProgressTasks }}
                </div>
                <div class="stat-label">进行中</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon overdue">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ overdueTasks.length }}
                </div>
                <div class="stat-label">逾期任务</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ taskStats.completedTasks }}
                </div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 维护统计图表 -->
    <div
v-if="statistics" class="maintenance-charts"
>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span class="card-title">月度维护趋势</span>
            </template>
            <div class="chart-container">
              <!-- 这里可以集成ECharts图表 -->
              <div class="chart-placeholder">
                <el-statistic title="平均完成时间" :value="statistics.avgCompletionTime">
                  <template #suffix>分钟</template>
                </el-statistic>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card>
            <template #header>
              <span class="card-title">维护成本分析</span>
            </template>
            <div class="chart-container">
              <div class="chart-placeholder">
                <el-statistic title="总维护成本" :value="statistics.totalCost">
                  <template #suffix>元</template>
                </el-statistic>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Tab页签 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 维护任务 -->
        <el-tab-pane label="维护任务" name="tasks">
          <div class="tasks-management">
            <!-- 筛选器 -->
            <div class="filters">
              <el-row :gutter="16">
                <el-col :span="5">
                  <el-input
v-model="taskSearchKeyword" placeholder="搜索任务或设备"
clearable
>
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
                    <el-option label="已计划" value="SCHEDULED" />
                    <el-option label="进行中" value="IN_PROGRESS" />
                    <el-option label="已完成" value="COMPLETED" />
                    <el-option label="已取消" value="CANCELLED" />
                    <el-option label="逾期" value="OVERDUE" />
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="priorityFilter" placeholder="优先级筛选" clearable>
                    <el-option label="紧急" value="CRITICAL" />
                    <el-option label="高" value="HIGH" />
                    <el-option label="中" value="MEDIUM" />
                    <el-option label="低" value="LOW" />
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="equipmentFilter" placeholder="设备筛选" clearable filterable>
                    <el-option
                      v-for="eq in equipmentList"
                      :key="eq.id"
                      :label="eq.name"
                      :value="eq.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-switch
v-model="overdueOnly" active-text="仅逾期"
/>
                </el-col>
                <el-col :span="4">
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="default"
                  />
                </el-col>
              </el-row>
            </div>

            <!-- 任务列表 -->
            <div class="tasks-table">
              <el-table :data="paginatedTasks" stripe @row-click="handleViewTaskDetail">
                <el-table-column prop="title" label="任务标题" width="200" show-overflow-tooltip />

                <el-table-column label="设备" width="150">
                  <template #default="{ row }">
                    {{ row.equipmentName }}
                  </template>
                </el-table-column>

                <el-table-column prop="type" label="类型" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getTypeTagType(row.type)" size="small">
                      {{ getTypeText(row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="priority" label="优先级" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getPriorityTagType(row.priority)" size="small">
                      {{ getPriorityText(row.priority) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)" size="small">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="scheduledDate" label="计划时间" width="160">
                  <template #default="{ row }">
                    {{ formatDateTime(row.scheduledDate) }}
                  </template>
                </el-table-column>

                <el-table-column label="进度" width="120">
                  <template #default="{ row }">
                    <el-progress
                      :percentage="getTaskProgress(row)"
                      :color="getProgressColor(row)"
                      :show-text="false"
                      :stroke-width="6"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="分配给" width="120">
                  <template #default="{ row }">
                    <el-tag
                      v-for="user in row.assignedTo.slice(0, 2)"
                      :key="user"
                      size="small"
                      style="margin-right: 4px; margin-bottom: 2px"
                    >
                      {{ user }}
                    </el-tag>
                    <span v-if="row.assignedTo.length > 2">...</span>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button
                      v-if="row.status === 'SCHEDULED'"
                      type="primary"
                      size="small"
                      @click="handleStartTask(row)"
                    >
                      开始
                    </el-button>
                    <el-button
                      v-if="row.status === 'IN_PROGRESS'"
                      type="success"
                      size="small"
                      @click="handleCompleteTask(row)"
                    >
                      完成
                    </el-button>
                    <el-button size="small"
@click="handleEditTask(row)"
>
编辑
</el-button>
                    <el-button
                      v-if="['SCHEDULED', 'IN_PROGRESS'].includes(row.status)"
                      type="danger"
                      size="small"
                      @click="handleCancelTask(row)"
                    >
                      取消
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <div class="pagination">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="filteredTasks.length"
                  layout="total, sizes, prev, pager, next, jumper"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 维护计划 -->
        <el-tab-pane label="维护计划" name="plans">
          <div class="plans-management">
            <div class="plans-toolbar">
              <el-input
                v-model="planSearchKeyword"
                placeholder="搜索计划"
                style="width: 300px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>

            <div class="plans-table">
              <el-table :data="maintenancePlans" stripe>
                <el-table-column prop="title" label="计划标题" width="200" show-overflow-tooltip />

                <el-table-column label="设备" width="150">
                  <template #default="{ row }">
                    {{ row.equipmentName }}
                  </template>
                </el-table-column>

                <el-table-column prop="type" label="类型" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getTypeTagType(row.type)" size="small">
                      {{ getTypeText(row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="frequency" label="频率" width="120" />

                <el-table-column
prop="estimatedDuration" label="预计时长"
width="100"
>
                  <template #default="{ row }">
{{ row.estimatedDuration }}分钟
</template>
                </el-table-column>

                <el-table-column prop="nextDue" label="下次执行" width="160">
                  <template #default="{ row }">
                    {{ formatDateTime(row.nextDue) }}
                  </template>
                </el-table-column>

                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.isActive ? 'success' : 'info'" size="small">
                      {{ row.isActive ? '激活' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button
type="primary" size="small"
@click="handleExecutePlan(row)"
>
                      立即执行
                    </el-button>
                    <el-button size="small"
@click="handleEditPlan(row)"
>
编辑
</el-button>
                    <el-button
                      :type="row.isActive ? 'warning' : 'success'"
                      size="small"
                      @click="handleTogglePlan(row)"
                    >
                      {{ row.isActive ? '停用' : '激活' }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 维护记录 -->
        <el-tab-pane label="维护记录" name="records">
          <div class="records-management">
            <div class="records-toolbar">
              <el-row :gutter="16">
                <el-col :span="6">
                  <el-select v-model="recordEquipmentFilter" placeholder="设备筛选" clearable>
                    <el-option
                      v-for="eq in equipmentList"
                      :key="eq.id"
                      :label="eq.name"
                      :value="eq.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="8">
                  <el-date-picker
                    v-model="recordDateRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                  />
                </el-col>
                <el-col :span="4">
                  <el-button @click="exportRecords">
                    <el-icon><Download /></el-icon>
                    导出记录
                  </el-button>
                </el-col>
              </el-row>
            </div>

            <div class="records-table">
              <el-table :data="maintenanceRecords" stripe>
                <el-table-column prop="title" label="维护项目" width="200" />
                <el-table-column prop="equipmentName" label="设备" width="150" />
                <el-table-column prop="maintenanceType" label="类型" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getTypeTagType(row.maintenanceType)" size="small">
                      {{ getTypeText(row.maintenanceType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="startTime" label="开始时间" width="160">
                  <template #default="{ row }">
                    {{ formatDateTime(row.startTime) }}
                  </template>
                </el-table-column>
                <el-table-column
prop="duration" label="耗时"
width="100"
>
                  <template #default="{ row }">
{{ row.duration }}分钟
</template>
                </el-table-column>
                <el-table-column prop="completedBy" label="执行人" width="120" />
                <el-table-column prop="cost" label="成本" width="100">
                  <template #default="{ row }">
                    {{ row.cost ? `¥${row.cost}` : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button
type="text" size="small"
@click="handleViewRecord(row)"
>
                      查看详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 备件管理 -->
        <el-tab-pane label="备件管理" name="parts">
          <div class="parts-management">
            <div class="parts-toolbar">
              <el-input
                v-model="partSearchKeyword"
                placeholder="搜索备件"
                style="width: 300px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-button type="primary" @click="showAddPartDialog = true">
                <el-icon><Plus /></el-icon>
                添加备件
              </el-button>
            </div>

            <div class="parts-table">
              <el-table :data="sparePartsList" stripe>
                <el-table-column prop="partNumber" label="备件编号" width="150" />
                <el-table-column prop="partName" label="备件名称" width="200" />
                <el-table-column prop="stockQuantity" label="库存数量" width="120">
                  <template #default="{ row }">
                    <span :class="{ 'low-stock': row.stockQuantity <= row.minStock }">
                      {{ row.stockQuantity }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="minStock" label="最小库存" width="120" />
                <el-table-column prop="unit" label="单位" width="80" />
                <el-table-column label="库存状态" width="120">
                  <template #default="{ row }">
                    <el-tag
                      :type="
                        row.stockQuantity > row.minStock
                          ? 'success'
                          : row.stockQuantity > 0
                            ? 'warning'
                            : 'danger'
                      "
                      size="small"
                    >
                      {{
                        row.stockQuantity > row.minStock
                          ? '充足'
                          : row.stockQuantity > 0
                            ? '不足'
                            : '缺货'
                      }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button size="small"
@click="handleUpdateStock(row)"
>
更新库存
</el-button>
                    <el-button
type="primary" size="small"
@click="handleOrderPart(row)"
>
                      订购
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog
v-model="showTaskDialog" :title="taskDialogTitle"
width="80%"
>
      <div v-if="selectedTask" class="task-detail">
        <!-- 任务详情内容 -->
        <el-descriptions :column="3" border>
          <el-descriptions-item label="任务标题">
            {{ selectedTask.title }}
          </el-descriptions-item>
          <el-descriptions-item label="设备">
            {{ selectedTask.equipmentName }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="getTypeTagType(selectedTask.type)">
              {{ getTypeText(selectedTask.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityTagType(selectedTask.priority)">
              {{ getPriorityText(selectedTask.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="计划时间">
            {{ formatDateTime(selectedTask.scheduledDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="预计时长">
            {{ selectedTask.estimatedDuration }}分钟
          </el-descriptions-item>
          <el-descriptions-item
v-if="selectedTask.actualDuration" label="实际时长"
>
            {{ selectedTask.actualDuration }}分钟
          </el-descriptions-item>
          <el-descriptions-item label="分配给">
            <el-tag
              v-for="user in selectedTask.assignedTo"
              :key="user"
              size="small"
              style="margin-right: 4px"
            >
              {{ user }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div
v-if="selectedTask.description" class="task-description"
>
          <h4>任务描述</h4>
          <p>{{ selectedTask.description }}</p>
        </div>

        <!-- 检查清单 -->
        <div
v-if="selectedTask.checklist.length > 0" class="checklist-section"
>
          <h4>检查清单</h4>
          <el-table :data="selectedTask.checklist" stripe>
            <el-table-column label="完成" width="80">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.isCompleted"
                  :disabled="selectedTask.status !== 'IN_PROGRESS'"
                />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="检查项" width="200" />
            <el-table-column prop="description" label="描述" />
            <el-table-column prop="notes" label="备注" width="200">
              <template #default="{ row }">
                <el-input
                  v-model="row.notes"
                  placeholder="添加备注"
                  :disabled="selectedTask.status !== 'IN_PROGRESS'"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 所需备件 -->
        <div
v-if="selectedTask.requiredParts.length > 0" class="required-parts"
>
          <h4>所需备件</h4>
          <el-table :data="selectedTask.requiredParts" stripe>
            <el-table-column prop="partNumber" label="备件编号" width="150" />
            <el-table-column prop="partName" label="备件名称" width="200" />
            <el-table-column prop="quantity" label="需要数量" width="100" />
            <el-table-column prop="stockQuantity" label="库存数量" width="100" />
            <el-table-column label="库存状态" width="120">
              <template #default="{ row }">
                <el-tag
:type="row.isAvailable ? 'success' : 'danger'" size="small"
>
                  {{ row.isAvailable ? '充足' : '不足' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="showTaskDialog = false">关闭</el-button>
        <el-button
          v-if="selectedTask?.status === 'IN_PROGRESS'"
          type="success"
          @click="handleCompleteTaskFromDialog"
        >
          完成任务
        </el-button>
      </template>
    </el-dialog>

    <!-- 其他弹窗省略，保持代码简洁 -->
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    Plus,
    Calendar,
    Refresh,
    Tools,
    Warning,
    CircleCheck,
    Search,
    Download
  } from '@element-plus/icons-vue'
  import { useMaintenanceManagement } from '@/composables/useEquipment'
  import { useEquipmentStore } from '@/stores/equipment'
  import type { MaintenanceTask, MaintenancePlan, MaintenanceRecord } from '@/types/equipment'

  // 状态管理
  const equipmentStore = useEquipmentStore()

  // 页面状态
  const activeTab = ref('tasks')
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 筛选器状态
  const taskSearchKeyword = ref('')
  const statusFilter = ref('')
  const priorityFilter = ref('')
  const equipmentFilter = ref('')
  const overdueOnly = ref(false)
  const dateRange = ref<[Date, Date] | null>(null)

  const planSearchKeyword = ref('')
  const recordEquipmentFilter = ref('')
  const recordDateRange = ref<[Date, Date] | null>(null)
  const partSearchKeyword = ref('')

  // 弹窗状态
  const showTaskDialog = ref(false)
  const showCreatePlanDialog = ref(false)
  const showCreateTaskDialog = ref(false)
  const showAddPartDialog = ref(false)
  const selectedTask = ref<MaintenanceTask | null>(null)
  const isEditingTask = ref(false)

  // 使用组合式函数
  const {
    tasks: maintenanceTasks,
    overdueTasks,
    statistics,
    statusFilter: maintenanceStatusFilter,
    priorityFilter: maintenancePriorityFilter,
    equipmentFilter: maintenanceEquipmentFilter,
    overdueOnly: maintenanceOverdueOnly,
    getPriorityColor,
    getPriorityText,
    getStatusText,
    handleStartTask: startTask,
    handleCompleteTask: completeTask,
    refresh: refreshMaintenanceData
  } = useMaintenanceManagement()

  // 计算属性
  const equipmentList = computed(() => equipmentStore.equipment)
  const maintenancePlans = computed(() => equipmentStore.maintenancePlans)
  const maintenanceRecords = computed(() => []) // 暂时为空，可以添加实际数据
  const sparePartsList = computed(() => []) // 暂时为空，可以添加实际数据

  const taskStats = computed(() => ({
    scheduledTasks: maintenanceTasks.value.filter(t => t.status === 'SCHEDULED').length,
    inProgressTasks: maintenanceTasks.value.filter(t => t.status === 'IN_PROGRESS').length,
    completedTasks: maintenanceTasks.value.filter(t => t.status === 'COMPLETED').length,
    cancelledTasks: maintenanceTasks.value.filter(t => t.status === 'CANCELLED').length
  }))

  const filteredTasks = computed(() => {
    let result = maintenanceTasks.value

    if (taskSearchKeyword.value) {
      const keyword = taskSearchKeyword.value.toLowerCase()
      result = result.filter(
        task =>
          task.title.toLowerCase().includes(keyword) ||
          task.equipmentName.toLowerCase().includes(keyword)
      )
    }

    if (statusFilter.value) {
      result = result.filter(task => task.status === statusFilter.value)
    }

    if (priorityFilter.value) {
      result = result.filter(task => task.priority === priorityFilter.value)
    }

    if (equipmentFilter.value) {
      result = result.filter(task => task.equipmentId === equipmentFilter.value)
    }

    if (overdueOnly.value) {
      const now = new Date()
      result = result.filter(
        task => task.status === 'SCHEDULED' && new Date(task.scheduledDate) < now
      )
    }

    return result.sort(
      (a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime()
    )
  })

  const paginatedTasks = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return filteredTasks.value.slice(start, end)
  })

  const taskDialogTitle = computed(() => {
    return isEditingTask.value ? '编辑维护任务' : '维护任务详情'
  })

  // 工具方法
  const getTypeText = (type: string): string => {
    const textMap: Record<string, string> = {
      PREVENTIVE: '预防性',
      CORRECTIVE: '纠正性',
      PREDICTIVE: '预测性'
    }
    return textMap[type] || type
  }

  const getTypeTagType = (type: string): string => {
    const typeMap: Record<string, string> = {
      PREVENTIVE: 'success',
      CORRECTIVE: 'warning',
      PREDICTIVE: 'primary'
    }
    return typeMap[type] || 'info'
  }

  const getPriorityTagType = (priority: string): string => {
    const typeMap: Record<string, string> = {
      CRITICAL: 'danger',
      HIGH: 'warning',
      MEDIUM: 'primary',
      LOW: 'success'
    }
    return typeMap[priority] || 'info'
  }

  const getStatusTagType = (status: string): string => {
    const typeMap: Record<string, string> = {
      SCHEDULED: 'info',
      IN_PROGRESS: 'warning',
      COMPLETED: 'success',
      CANCELLED: 'danger',
      OVERDUE: 'danger'
    }
    return typeMap[status] || 'info'
  }

  const getTaskProgress = (task: MaintenanceTask): number => {
    if (task.status === 'COMPLETED') return 100
    if (task.status === 'CANCELLED') return 0
    if (task.status === 'IN_PROGRESS') {
      const completedItems = task.checklist.filter(item => item.isCompleted).length
      const totalItems = task.checklist.length
      return totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 50
    }
    return 0
  }

  const getProgressColor = (task: MaintenanceTask): string => {
    const progress = getTaskProgress(task)
    if (progress >= 80) return '#67C23A'
    if (progress >= 50) return '#E6A23C'
    return '#F56C6C'
  }

  const formatDateTime = (dateTime: string): string => {
    return new Date(dateTime).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 事件处理
  const refreshData = () => {
    refreshMaintenanceData()
    equipmentStore.fetchEquipmentList()
    equipmentStore.fetchMaintenancePlans()
    equipmentStore.fetchMaintenanceStatistics()
  }

  const handleTabChange = (tabName: string) => {
    switch (tabName) {
      case 'tasks':
        refreshMaintenanceData()
        break
      case 'plans':
        equipmentStore.fetchMaintenancePlans()
        break
      case 'records':
        // 刷新维护记录
        break
      case 'parts':
        // 刷新备件数据
        break
    }
  }

  const handleViewTaskDetail = (task: MaintenanceTask) => {
    selectedTask.value = task
    isEditingTask.value = false
    showTaskDialog.value = true
  }

  const handleStartTask = (task: MaintenanceTask) => {
    startTask(task.id, 'current_user') // TODO: 获取当前用户
  }

  const handleCompleteTask = (task: MaintenanceTask) => {
    completeTask(task.id, {
      notes: '',
      usedParts: []
    })
  }

  const handleCompleteTaskFromDialog = () => {
    if (selectedTask.value) {
      handleCompleteTask(selectedTask.value)
      showTaskDialog.value = false
    }
  }

  const handleEditTask = (task: MaintenanceTask) => {
    selectedTask.value = task
    isEditingTask.value = true
    showTaskDialog.value = true
  }

  const handleCancelTask = async (task: MaintenanceTask) => {
    try {
      await ElMessageBox.confirm('确定要取消此维护任务吗？', '确认取消', {
        confirmButtonText: '取消任务',
        cancelButtonText: '返回',
        type: 'warning'
      })
      ElMessage.success('维护任务已取消')
      refreshMaintenanceData()
    } catch (error) {
      // 用户取消操作
    }
  }

  const handleExecutePlan = (plan: MaintenancePlan) => {
    // 立即执行维护计划
    ElMessage.info('正在创建维护任务...')
  }

  const handleEditPlan = (plan: MaintenancePlan) => {
    // 编辑维护计划
    ElMessage.info('编辑维护计划功能待实现')
  }

  const handleTogglePlan = (plan: MaintenancePlan) => {
    // 切换计划状态
    plan.isActive = !plan.isActive
    ElMessage.success(`维护计划已${plan.isActive ? '激活' : '停用'}`)
  }

  const handleViewRecord = (record: MaintenanceRecord) => {
    // 查看维护记录详情
    ElMessage.info('维护记录详情功能待实现')
  }

  const handleUpdateStock = (part: any) => {
    // 更新备件库存
    ElMessage.info('更新库存功能待实现')
  }

  const handleOrderPart = (part: any) => {
    // 订购备件
    ElMessage.info('备件订购功能待实现')
  }

  const exportRecords = () => {
    // 导出维护记录
    ElMessage.info('导出功能待实现')
  }

  // 生命周期
  onMounted(() => {
    refreshData()
  })
</script>

<style lang="scss" scoped>
  .maintenance-manager {
    padding: var(--spacing-4);

    .page-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: var(--spacing-6);

      .header-left {
        .page-title {
          margin: 0 0 var(--spacing-2);
          font-size: 1.75rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .page-description {
          margin: 0;
          color: var(--color-text-secondary);
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    .stats-overview {
      margin-bottom: var(--spacing-6);

      .stat-card {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--shadow-large);
          transform: translateY(-2px);
        }

        .stat-content {
          display: flex;
          gap: var(--spacing-4);
          align-items: center;
        }

        .stat-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 60px;
          font-size: 24px;
          color: white;
          border-radius: 50%;

          &.scheduled {
            background-color: var(--color-primary);
          }

          &.in-progress {
            background-color: var(--color-warning);
          }

          &.overdue {
            background-color: var(--color-danger);
          }

          &.completed {
            background-color: var(--color-success);
          }
        }

        .stat-info {
          .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--color-text-primary);
          }

          .stat-label {
            font-size: 0.875rem;
            color: var(--color-text-secondary);
          }
        }
      }
    }

    .maintenance-charts {
      margin-bottom: var(--spacing-6);

      .card-title {
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .chart-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;

        .chart-placeholder {
          text-align: center;
        }
      }
    }

    .main-content {
      .tasks-management,
      .plans-management,
      .records-management,
      .parts-management {
        .filters,
        .plans-toolbar,
        .records-toolbar,
        .parts-toolbar {
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          background-color: var(--color-bg-soft);
          border-radius: var(--radius-base);
        }

        .tasks-table,
        .plans-table,
        .records-table,
        .parts-table {
          padding: var(--spacing-2);
          background-color: var(--color-bg-primary);
          border-radius: var(--radius-base);

          .pagination {
            display: flex;
            justify-content: center;
            margin-top: var(--spacing-4);
          }

          .low-stock {
            font-weight: 600;
            color: var(--color-danger);
          }
        }
      }

      .parts-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }

    .task-detail {
      .task-description {
        margin: var(--spacing-4) 0;

        h4 {
          margin-bottom: var(--spacing-2);
          font-size: 1rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        p {
          line-height: 1.5;
          color: var(--color-text-secondary);
        }
      }

      .checklist-section,
      .required-parts {
        margin: var(--spacing-4) 0;

        h4 {
          margin-bottom: var(--spacing-3);
          font-size: 1rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }
      }
    }
  }
</style>
