<template>
  <el-dialog
    v-model="dialogVisible"
    title="物料详情"
    width="80%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-if="material" class="material-detail">
      <!-- 基本信息 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><Box /></el-icon>
            <span>基本信息</span>
          </div>
        </template>

        <el-descriptions :column="3" border>
          <el-descriptions-item label="物料编码">
            <span class="material-code">{{ material.basicInfo.materialCode }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="物料名称">
            {{ material.basicInfo.materialName }}
          </el-descriptions-item>
          <el-descriptions-item label="英文名称">
            {{ material.basicInfo.englishName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="规格型号">
            {{ material.basicInfo.specification }}
          </el-descriptions-item>
          <el-descriptions-item label="分类">
            <el-tag
:color="getCategoryColor(material.basicInfo.category)" style="color: white"
>
              {{ getCategoryLabel(material.basicInfo.category) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(material.basicInfo.status)">
              {{ getStatusLabel(material.basicInfo.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="制造商">
            {{ material.basicInfo.manufacturer }}
          </el-descriptions-item>
          <el-descriptions-item label="品牌">
            {{ material.basicInfo.brand || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="型号">
            {{ material.basicInfo.model || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="3">
            {{ material.basicInfo.description || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="3">
            {{ material.basicInfo.remarks || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 技术参数 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><SetUp /></el-icon>
            <span>技术参数</span>
          </div>
        </template>

        <!-- 尺寸参数 -->
        <div v-if="material.technicalParams.dimensions" class="param-section">
          <h4>尺寸参数</h4>
          <el-descriptions :column="4" size="small">
            <el-descriptions-item v-if="material.technicalParams.dimensions.length" label="长度">
              {{ material.technicalParams.dimensions.length }}mm
            </el-descriptions-item>
            <el-descriptions-item v-if="material.technicalParams.dimensions.width" label="宽度">
              {{ material.technicalParams.dimensions.width }}mm
            </el-descriptions-item>
            <el-descriptions-item v-if="material.technicalParams.dimensions.height" label="高度">
              {{ material.technicalParams.dimensions.height }}mm
            </el-descriptions-item>
            <el-descriptions-item v-if="material.technicalParams.dimensions.thickness" label="厚度">
              {{ material.technicalParams.dimensions.thickness }}μm
            </el-descriptions-item>
            <el-descriptions-item v-if="material.technicalParams.dimensions.diameter" label="直径">
              {{ material.technicalParams.dimensions.diameter }}μm
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 晶圆参数 -->
        <div v-if="material.technicalParams.waferInfo" class="param-section">
          <h4>晶圆参数</h4>
          <el-descriptions :column="3" size="small">
            <el-descriptions-item label="晶圆尺寸">
              {{ material.technicalParams.waferInfo.waferSize }}寸
            </el-descriptions-item>
            <el-descriptions-item label="厚度">
              {{ material.technicalParams.waferInfo.thickness }}μm
            </el-descriptions-item>
            <el-descriptions-item label="晶向">
              {{ material.technicalParams.waferInfo.orientation || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="电阻率">
              {{ material.technicalParams.waferInfo.resistivity || '-' }}Ω·cm
            </el-descriptions-item>
            <el-descriptions-item label="芯片尺寸">
              {{ material.technicalParams.waferInfo.dieSize || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 封装参数 -->
        <div v-if="material.technicalParams.packageInfo" class="param-section">
          <h4>封装参数</h4>
          <el-descriptions :column="3" size="small">
            <el-descriptions-item label="封装类型">
              {{ material.technicalParams.packageInfo.packageType?.toUpperCase() }}
            </el-descriptions-item>
            <el-descriptions-item label="引脚数">
              {{ material.technicalParams.packageInfo.pinCount }}
            </el-descriptions-item>
            <el-descriptions-item label="引脚间距">
              {{ material.technicalParams.packageInfo.pitchSize }}mm
            </el-descriptions-item>
            <el-descriptions-item label="封装体尺寸">
              {{ material.technicalParams.packageInfo.bodySize || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 线材参数 -->
        <div v-if="material.technicalParams.wirebondInfo" class="param-section">
          <h4>线材参数</h4>
          <el-descriptions :column="3" size="small">
            <el-descriptions-item label="线材类型">
              <div class="wire-type">
                <span
                  class="wire-indicator"
                  :style="{
                    backgroundColor: getWireColor(material.technicalParams.wirebondInfo.wireType)
                  }"
                />
                {{ getWireTypeLabel(material.technicalParams.wirebondInfo.wireType) }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="直径">
              {{ material.technicalParams.wirebondInfo.diameter }}μm
            </el-descriptions-item>
            <el-descriptions-item label="拉伸强度">
              {{ material.technicalParams.wirebondInfo.tensileStrength }}MPa
            </el-descriptions-item>
            <el-descriptions-item label="延伸率">
              {{ material.technicalParams.wirebondInfo.elongation }}%
            </el-descriptions-item>
            <el-descriptions-item label="纯度">
              {{ material.technicalParams.wirebondInfo.purity }}%
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 电气特性 -->
        <div v-if="material.technicalParams.electricalProperties" class="param-section">
          <h4>电气特性</h4>
          <el-descriptions :column="3" size="small">
            <el-descriptions-item
              v-if="material.technicalParams.electricalProperties.resistance"
              label="电阻"
            >
              {{ material.technicalParams.electricalProperties.resistance }}Ω
            </el-descriptions-item>
            <el-descriptions-item
              v-if="material.technicalParams.electricalProperties.conductivity"
              label="导电率"
            >
              {{ material.technicalParams.electricalProperties.conductivity }}S/m
            </el-descriptions-item>
            <el-descriptions-item
              v-if="material.technicalParams.electricalProperties.voltage"
              label="电压"
            >
              {{ material.technicalParams.electricalProperties.voltage }}V
            </el-descriptions-item>
            <el-descriptions-item
              v-if="material.technicalParams.electricalProperties.current"
              label="电流"
            >
              {{ material.technicalParams.electricalProperties.current }}A
            </el-descriptions-item>
            <el-descriptions-item
              v-if="material.technicalParams.electricalProperties.power"
              label="功率"
            >
              {{ material.technicalParams.electricalProperties.power }}W
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 环境要求 -->
        <div v-if="material.technicalParams.environmentalRequirements" class="param-section">
          <h4>环境要求</h4>
          <el-descriptions :column="3" size="small">
            <el-descriptions-item
              v-if="material.technicalParams.environmentalRequirements.temperatureMin"
              label="最低温度"
            >
              {{ material.technicalParams.environmentalRequirements.temperatureMin }}°C
            </el-descriptions-item>
            <el-descriptions-item
              v-if="material.technicalParams.environmentalRequirements.temperatureMax"
              label="最高温度"
            >
              {{ material.technicalParams.environmentalRequirements.temperatureMax }}°C
            </el-descriptions-item>
            <el-descriptions-item
              v-if="material.technicalParams.environmentalRequirements.humidityMax"
              label="最大湿度"
            >
              {{ material.technicalParams.environmentalRequirements.humidityMax }}%
            </el-descriptions-item>
            <el-descriptions-item
              v-if="material.technicalParams.environmentalRequirements.storageConditions"
              label="存储条件"
              :span="3"
            >
              <el-tag
                v-for="condition in material.technicalParams.environmentalRequirements
                  .storageConditions"
                :key="condition"
                size="small"
                style="margin-right: 8px"
              >
                {{ condition }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>

      <!-- 供应商信息 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><Shop /></el-icon>
            <span>供应商信息</span>
          </div>
        </template>

        <el-table :data="material.suppliers" border>
          <el-table-column label="供应商名称" prop="supplierName" />
          <el-table-column label="类型" width="80">
            <template #default="{ row }">
              <el-tag
v-if="row.isPrimary" type="success" size="small">主供</el-tag>
              <el-tag
v-else type="info" size="small">备选</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="联系人" prop="contactPerson" />
          <el-table-column label="联系电话" prop="contactPhone" />
          <el-table-column label="交期" width="80">
            <template #default="{ row }">{{ row.leadTime }}天</template>
          </el-table-column>
          <el-table-column label="最小订货量" width="120">
            <template #default="{ row }">
              {{ row.minOrderQty.toLocaleString() }}
            </template>
          </el-table-column>
          <el-table-column label="质量评级" width="100">
            <template #default="{ row }">
              <el-progress
                :percentage="row.qualityRating"
                :stroke-width="6"
                :show-text="false"
                :status="
                  row.qualityRating >= 95
                    ? 'success'
                    : row.qualityRating >= 85
                      ? 'warning'
                      : 'exception'
                "
              />
              <span style="margin-left: 4px; font-size: 12px">{{ row.qualityRating }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="交期评级" width="100">
            <template #default="{ row }">
              <el-progress
                :percentage="row.deliveryRating"
                :stroke-width="6"
                :show-text="false"
                :status="
                  row.deliveryRating >= 95
                    ? 'success'
                    : row.deliveryRating >= 85
                      ? 'warning'
                      : 'exception'
                "
              />
              <span style="margin-left: 4px; font-size: 12px">{{ row.deliveryRating }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 成本和库存信息 -->
      <el-row :gutter="20">
        <!-- 成本信息 -->
        <el-col :span="12">
          <el-card class="detail-card">
            <template #header>
              <div class="card-header">
                <el-icon><Money /></el-icon>
                <span>成本信息</span>
              </div>
            </template>

            <el-descriptions :column="2" size="small">
              <el-descriptions-item label="标准成本">
                {{ material.costInfo.currency
                }}{{ material.costInfo.standardCost.toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item label="当前价格">
                {{ material.costInfo.currency
                }}{{ material.costInfo.currentPrice.toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item label="价格单位">
                {{ material.costInfo.priceUnit }}
              </el-descriptions-item>
              <el-descriptions-item label="最后更新">
                {{ formatDate(material.costInfo.lastPriceUpdate) }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- 成本构成 -->
            <div v-if="material.costInfo.costAnalysis" class="cost-analysis">
              <h5>成本构成分析</h5>
              <div class="cost-breakdown">
                <div
v-if="material.costInfo.costAnalysis.materialCost" class="cost-item"
>
                  <span class="cost-label">材料成本:</span>
                  <span class="cost-value">
                    ${{ material.costInfo.costAnalysis.materialCost.toLocaleString() }}
                  </span>
                </div>
                <div
v-if="material.costInfo.costAnalysis.laborCost" class="cost-item"
>
                  <span class="cost-label">人工成本:</span>
                  <span class="cost-value">
                    ${{ material.costInfo.costAnalysis.laborCost.toLocaleString() }}
                  </span>
                </div>
                <div
v-if="material.costInfo.costAnalysis.overheadCost" class="cost-item"
>
                  <span class="cost-label">制造费用:</span>
                  <span class="cost-value">
                    ${{ material.costInfo.costAnalysis.overheadCost.toLocaleString() }}
                  </span>
                </div>
                <div
v-if="material.costInfo.costAnalysis.transportCost" class="cost-item"
>
                  <span class="cost-label">运输费用:</span>
                  <span class="cost-value">
                    ${{ material.costInfo.costAnalysis.transportCost.toLocaleString() }}
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 库存信息 -->
        <el-col :span="12">
          <el-card class="detail-card">
            <template #header>
              <div class="card-header">
                <el-icon><Goods /></el-icon>
                <span>库存信息</span>
              </div>
            </template>

            <el-descriptions :column="2" size="small">
              <el-descriptions-item label="计量单位">
                {{ material.stockInfo.unitOfMeasure }}
              </el-descriptions-item>
              <el-descriptions-item label="安全库存">
                {{ material.stockInfo.safetyStock.toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item label="最大库存">
                {{ material.stockInfo.maxStock.toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item label="再订货点">
                {{ material.stockInfo.reorderPoint.toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item label="采购周期">
                {{ material.stockInfo.leadTime }}天
              </el-descriptions-item>
              <el-descriptions-item label="存储位置">
                {{ material.stockInfo.storageLocation || '-' }}
              </el-descriptions-item>
              <el-descriptions-item
                v-if="material.stockInfo.shelfLife && material.stockInfo.shelfLife < 9999"
                label="保质期"
              >
                {{ material.stockInfo.shelfLife }}天
              </el-descriptions-item>
              <el-descriptions-item label="存储要求" :span="2">
                <el-tag
                  v-for="requirement in material.stockInfo.storageRequirements"
                  :key="requirement"
                  size="small"
                  style="margin-right: 8px"
                >
                  {{ requirement }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>

      <!-- 质量信息 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><Medal /></el-icon>
            <span>质量信息</span>
          </div>
        </template>

        <el-descriptions :column="2">
          <el-descriptions-item label="执行标准">
            {{ material.qualityInfo.standard }}
          </el-descriptions-item>
          <el-descriptions-item label="技术规范">
            {{ material.qualityInfo.specification }}
          </el-descriptions-item>
          <el-descriptions-item label="检验方法" :span="2">
            {{ material.qualityInfo.inspectionMethod }}
          </el-descriptions-item>
          <el-descriptions-item label="接收标准" :span="2">
            {{ material.qualityInfo.acceptanceCriteria }}
          </el-descriptions-item>
          <el-descriptions-item label="认证信息" :span="2">
            <el-tag
              v-for="cert in material.qualityInfo.certifications"
              :key="cert"
              type="success"
              size="small"
              style="margin-right: 8px"
            >
              {{ cert }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 可靠性信息 -->
        <div v-if="material.qualityInfo.reliability" class="reliability-section">
          <h5>可靠性信息</h5>
          <el-descriptions :column="3" size="small">
            <el-descriptions-item v-if="material.qualityInfo.reliability.mtbf" label="MTBF">
              {{ material.qualityInfo.reliability.mtbf.toLocaleString() }}小时
            </el-descriptions-item>
            <el-descriptions-item v-if="material.qualityInfo.reliability.lifeTest" label="寿命测试">
              {{ material.qualityInfo.reliability.lifeTest }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="material.qualityInfo.reliability.qualificationStatus"
              label="认证状态"
            >
              <el-tag type="success" size="small">
                {{ material.qualityInfo.reliability.qualificationStatus }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>

      <!-- 替代料信息 -->
      <el-card v-if="material.alternatives.length > 0" class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><Connection /></el-icon>
            <span>替代料信息</span>
          </div>
        </template>

        <el-table :data="material.alternatives" border>
          <el-table-column label="替代料编码" prop="alternativeCode" width="150" />
          <el-table-column label="替代料名称" prop="alternativeName" />
          <el-table-column label="替代类型" width="100">
            <template #default="{ row }">
              <el-tag
                :type="
                  row.relationType === 'FULL'
                    ? 'success'
                    : row.relationType === 'PARTIAL'
                      ? 'warning'
                      : 'info'
                "
                size="small"
              >
                {{ getRelationTypeLabel(row.relationType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="替代比例" width="100">
            <template #default="{ row }">{{ (row.substitutionRatio * 100).toFixed(0) }}%</template>
          </el-table-column>
          <el-table-column label="优先级" width="80" prop="priority" />
          <el-table-column label="有效期" width="180">
            <template #default="{ row }">
              {{ formatDate(row.validFrom) }} -
              {{ row.validTo ? formatDate(row.validTo) : '长期有效' }}
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remarks" />
        </el-table>
      </el-card>

      <!-- 审核信息 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>审核信息</span>
          </div>
        </template>

        <el-descriptions :column="3">
          <el-descriptions-item label="创建人">
            {{ material.auditInfo.createdBy }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(material.auditInfo.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="版本号">
            v{{ material.auditInfo.version }}
          </el-descriptions-item>
          <el-descriptions-item label="更新人">
            {{ material.auditInfo.updatedBy }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(material.auditInfo.updatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="审批人">
            {{ material.auditInfo.approvedBy || '-' }}
          </el-descriptions-item>
          <el-descriptions-item v-if="material.auditInfo.approvedAt" label="审批时间" :span="3">
            {{ formatDateTime(material.auditInfo.approvedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 附件信息 -->
      <el-card v-if="material.attachments && material.attachments.length > 0" class="detail-card">
        <template #header>
          <div class="card-header">
            <el-icon><Paperclip /></el-icon>
            <span>附件信息</span>
          </div>
        </template>

        <el-table :data="material.attachments" border>
          <el-table-column label="文件名" prop="fileName" />
          <el-table-column label="文件类型" width="100">
            <template #default="{ row }">
              <el-tag size="small">
                {{ row.fileType.toUpperCase() }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="文件大小" width="100">
            <template #default="{ row }">
              {{ formatFileSize(row.fileSize) }}
            </template>
          </el-table-column>
          <el-table-column label="附件分类" width="100">
            <template #default="{ row }">
              <el-tag :type="getAttachmentTagType(row.category)" size="small">
                {{ getAttachmentLabel(row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="上传人" prop="uploadBy" />
          <el-table-column label="上传时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.uploadTime) }}
            </template>
          </el-table-column>
          <el-table-column label="描述" prop="description" />
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button size="small" type="primary" @click="downloadAttachment(row)">
                <el-icon><Download /></el-icon>
                下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="$emit('edit', material)">编辑</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import type {
    MaterialMaster,
    MaterialMasterCategory,
    MaterialStatus,
    WireType
  } from '@/types/materialMaster'
  import {
    MATERIAL_CATEGORY_OPTIONS,
    MATERIAL_STATUS_OPTIONS,
    WIRE_TYPE_OPTIONS
  } from '@/utils/mockData/materialMaster'

  interface Props {
    visible: boolean
    material: MaterialMaster | null
  }

  interface Emits {
    (e: 'update:visible', visible: boolean): void
    (e: 'edit', material: MaterialMaster): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 对话框显示状态
  const dialogVisible = computed({
    get: () => props.visible,
    set: value => emit('update:visible', value)
  })

  // 获取分类颜色
  const getCategoryColor = (category: MaterialMasterCategory): string => {
    const option = MATERIAL_CATEGORY_OPTIONS.find(opt => opt.value === category)
    return option?.color || '#909399'
  }

  // 获取分类标签
  const getCategoryLabel = (category: MaterialMasterCategory): string => {
    const option = MATERIAL_CATEGORY_OPTIONS.find(opt => opt.value === category)
    return option?.label || category
  }

  // 获取状态标签类型
  const getStatusTagType = (status: MaterialStatus): string => {
    const option = MATERIAL_STATUS_OPTIONS.find(opt => opt.value === status)
    return option?.tagType || ''
  }

  // 获取状态标签文本
  const getStatusLabel = (status: MaterialStatus): string => {
    const option = MATERIAL_STATUS_OPTIONS.find(opt => opt.value === status)
    return option?.label || status
  }

  // 获取线材颜色
  const getWireColor = (wireType?: WireType): string => {
    if (!wireType) return '#909399'
    const option = WIRE_TYPE_OPTIONS.find(opt => opt.value === wireType)
    return option?.color || '#909399'
  }

  // 获取线材类型标签
  const getWireTypeLabel = (wireType?: WireType): string => {
    if (!wireType) return '-'
    const option = WIRE_TYPE_OPTIONS.find(opt => opt.value === wireType)
    return option?.label || wireType
  }

  // 获取替代关系类型标签
  const getRelationTypeLabel = (relationType: string): string => {
    const typeMap: Record<string, string> = {
      FULL: '完全替代',
      PARTIAL: '部分替代',
      EMERGENCY: '紧急替代'
    }
    return typeMap[relationType] || relationType
  }

  // 获取附件类型标签
  const getAttachmentTagType = (category: string): string => {
    const typeMap: Record<string, string> = {
      SPEC: 'primary',
      DRAWING: 'success',
      CERTIFICATE: 'warning',
      PHOTO: 'info',
      OTHER: ''
    }
    return typeMap[category] || ''
  }

  // 获取附件类型标签文本
  const getAttachmentLabel = (category: string): string => {
    const labelMap: Record<string, string> = {
      SPEC: '规格书',
      DRAWING: '图纸',
      CERTIFICATE: '证书',
      PHOTO: '照片',
      OTHER: '其他'
    }
    return labelMap[category] || category
  }

  // 格式化日期
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  // 格式化日期时间
  const formatDateTime = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 格式化文件大小
  const formatFileSize = (size: number): string => {
    if (size < 1024) return `${size}B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
    if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)}MB`
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)}GB`
  }

  // 下载附件
  const downloadAttachment = (attachment: any) => {
    // 模拟下载
    console.log('下载附件:', attachment.fileName)
    // 实际项目中这里会调用下载API
  }
</script>

<style lang="scss" scoped>
  .material-detail {
    .detail-card {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        gap: 8px;
        align-items: center;
        font-weight: 500;
      }

      .material-code {
        font-family: Monaco, Consolas, monospace;
        font-weight: 600;
        color: var(--color-primary);
      }

      .param-section {
        margin-bottom: 24px;

        h4,
        h5 {
          padding-left: 8px;
          margin: 0 0 12px;
          font-size: 14px;
          font-weight: 500;
          color: var(--color-text-primary);
          border-left: 3px solid var(--color-primary);
        }

        h5 {
          margin-top: 16px;
          font-size: 13px;
        }
      }

      .wire-type {
        display: flex;
        gap: 6px;
        align-items: center;

        .wire-indicator {
          width: 12px;
          height: 12px;
          border: 1px solid var(--color-border-light);
          border-radius: 50%;
        }
      }

      .cost-analysis {
        margin-top: 16px;

        .cost-breakdown {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          .cost-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            background: var(--color-bg-light);
            border-radius: 4px;

            .cost-label {
              font-size: 13px;
              color: var(--color-text-secondary);
            }

            .cost-value {
              font-size: 13px;
              font-weight: 500;
              color: var(--color-text-primary);
            }
          }
        }
      }

      .reliability-section {
        padding-top: 16px;
        margin-top: 16px;
        border-top: 1px solid var(--color-border-lighter);
      }
    }

    :deep(.el-descriptions__label) {
      font-weight: 500;
      color: var(--color-text-regular);
    }

    :deep(.el-descriptions__content) {
      color: var(--color-text-primary);
    }

    :deep(.el-table) {
      font-size: 13px;
    }

    :deep(.el-progress) {
      display: inline-block;
      width: 60px;
    }
  }

  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 10px;
    }
  }

  // 响应式设计
  @media (width <= 1200px) {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto !important;
    }

    .material-detail {
      .cost-breakdown {
        grid-template-columns: 1fr !important;
      }

      :deep(.el-descriptions) {
        --el-descriptions-item-bordered-label-width: 120px;
      }
    }
  }
</style>
