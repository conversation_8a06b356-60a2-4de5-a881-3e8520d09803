// IC封测CIM系统 - 质量控制专业组件样式

.quality-dashboard {
  overflow: hidden;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  
  &__header {
    padding: var(--spacing-5);
    background-color: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-border-light);
    
    &-title {
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
    }
    
    &-subtitle {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
  }
  
  &__metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    border-bottom: 1px solid var(--color-border-light);
  }
  
  &__chart {
    height: 300px;
    padding: var(--spacing-5);
  }
}

.quality-metric {
  padding: var(--spacing-4);
  text-align: center;
  border-right: 1px solid var(--color-border-light);
  
  &:last-child {
    border-right: none;
  }
  
  &__value {
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    
    &--excellent {
      color: var(--color-success);
    }
    
    &--good {
      color: var(--color-primary);
    }
    
    &--warning {
      color: var(--color-warning);
    }
    
    &--poor {
      color: var(--color-error);
    }
  }
  
  &__label {
    margin-bottom: var(--spacing-1);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }
  
  &__trend {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    
    &--up {
      color: var(--color-success);
    }
    
    &--down {
      color: var(--color-error);
    }
    
    &--stable {
      color: var(--color-text-tertiary);
    }
  }
}

.spc-chart {
  padding: var(--spacing-5);
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);
    
    &-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
    
    &-controls {
      display: flex;
      gap: var(--spacing-3);
      align-items: center;
    }
  }
  
  &__canvas {
    width: 100%;
    height: 300px;
    background-color: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);
  }
  
  &__legend {
    display: flex;
    gap: var(--spacing-6);
    align-items: center;
    justify-content: center;
    padding-top: var(--spacing-4);
    margin-top: var(--spacing-4);
    border-top: 1px solid var(--color-border-light);
  }
  
  &__legend-item {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    
    &-line {
      width: 20px;
      height: 2px;
      
      &--data {
        background-color: var(--color-primary);
      }
      
      &--ucl {
        background-color: var(--color-error);
      }
      
      &--lcl {
        background-color: var(--color-error);
      }
      
      &--center {
        background-color: var(--color-success);
      }
    }
  }
}

.quality-alert {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-4);
  border-radius: var(--radius-base);
  
  &__icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
  }
  
  &__content {
    flex: 1;
    
    &-title {
      margin-bottom: 2px;
      font-weight: var(--font-weight-medium);
    }
    
    &-message {
      font-size: var(--font-size-sm);
    }
  }
  
  &--warning {
    background-color: color-mix(in srgb, var(--color-warning) 10%, transparent);
    border: 1px solid var(--color-warning);
    
    .quality-alert__icon,
    .quality-alert__content-title {
      color: var(--color-warning);
    }
  }
  
  &--error {
    background-color: color-mix(in srgb, var(--color-error) 10%, transparent);
    border: 1px solid var(--color-error);
    
    .quality-alert__icon,
    .quality-alert__content-title {
      color: var(--color-error);
    }
  }
  
  &--info {
    background-color: color-mix(in srgb, var(--color-info) 10%, transparent);
    border: 1px solid var(--color-info);
    
    .quality-alert__icon,
    .quality-alert__content-title {
      color: var(--color-info);
    }
  }
}