<template>
  <div class="role-management">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><UserFilled /></el-icon>
        角色管理
      </h1>
      <p class="page-description">系统角色定义和权限配置管理</p>
    </div>

    <div class="page-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>角色列表</span>
            <el-button type="primary" size="small">
              <el-icon><Plus /></el-icon>
              新增角色
            </el-button>
          </div>
        </template>

        <div class="coming-soon">
          <el-empty description="角色管理功能开发中">
            <template #image>
              <el-icon size="64"><Setting /></el-icon>
            </template>
            <el-button type="primary">返回用户管理</el-button>
          </el-empty>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  // 角色管理页面 - 开发中
  console.log('角色管理页面已加载')
</script>

<style lang="scss" scoped>
  .role-management {
    padding: var(--spacing-4);
  }

  .page-header {
    margin-bottom: var(--spacing-6);

    .page-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
    }

    .page-description {
      margin: 0;
      color: var(--color-text-secondary);
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .coming-soon {
    padding: var(--spacing-8);
    text-align: center;
  }
</style>