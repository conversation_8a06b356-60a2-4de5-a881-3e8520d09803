/**
 * 组织架构管理模块模拟数据
 * IC封测工厂组织架构数据
 */

import type {
  Department,
  Position,
  Employee,
  Permission,
  RolePermission,
  CostCenter,
  OrganizationChange,
  DepartmentType,
  DepartmentStatus,
  PositionLevel,
  EmployeeStatus,
  PermissionType
} from '@/types/organization'

// 模拟部门数据 - IC封测工厂典型组织架构
export const mockDepartments: Department[] = [
  {
    id: 'dept-001',
    code: 'CEO',
    name: '总经理办公室',
    type: DepartmentType.MANAGEMENT,
    level: 1,
    managerId: 'emp-001',
    description: '公司最高管理层，负责整体战略规划和重大决策',
    responsibilities: ['公司战略制定', '重大决策审批', '对外合作', '企业文化建设'],
    status: DepartmentStatus.ACTIVE,
    costCenterCode: 'CC-001',
    location: '办公楼3楼',
    phoneNumber: '021-12345001',
    email: '<EMAIL>',
    establishedDate: new Date('2020-01-01'),
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin',
    children: []
  },

  // 生产部门
  {
    id: 'dept-010',
    code: 'PROD',
    name: '生产制造部',
    type: DepartmentType.PRODUCTION,
    level: 1,
    managerId: 'emp-010',
    description: 'IC封装测试生产制造管理',
    responsibilities: ['生产计划执行', '产能管理', '现场管理', '安全生产'],
    status: DepartmentStatus.ACTIVE,
    costCenterCode: 'CC-010',
    location: '生产厂房',
    phoneNumber: '021-12345010',
    email: '<EMAIL>',
    establishedDate: new Date('2020-02-01'),
    createdAt: new Date('2020-02-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin',
    children: [
      {
        id: 'dept-011',
        code: 'CP',
        name: 'CP测试部',
        type: DepartmentType.PRODUCTION,
        level: 2,
        parentId: 'dept-010',
        managerId: 'emp-011',
        description: '晶圆探针测试部门',
        responsibilities: ['CP测试执行', '探针卡管理', '测试数据分析', '良率提升'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-011',
        location: '生产厂房A区',
        phoneNumber: '021-12345011',
        email: '<EMAIL>',
        establishedDate: new Date('2020-02-15'),
        createdAt: new Date('2020-02-15'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-012',
        code: 'ASM',
        name: '封装部',
        type: DepartmentType.PRODUCTION,
        level: 2,
        parentId: 'dept-010',
        managerId: 'emp-012',
        description: 'IC封装工艺部门',
        responsibilities: ['贴片封装', '引线键合', '塑封成型', '外观检测'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-012',
        location: '生产厂房B区',
        phoneNumber: '021-12345012',
        email: '<EMAIL>',
        establishedDate: new Date('2020-03-01'),
        createdAt: new Date('2020-03-01'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-013',
        code: 'FT',
        name: 'FT测试部',
        type: DepartmentType.PRODUCTION,
        level: 2,
        parentId: 'dept-010',
        managerId: 'emp-013',
        description: '最终成品测试部门',
        responsibilities: ['成品测试', '老化测试', '出货检验', '测试设备维护'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-013',
        location: '生产厂房C区',
        phoneNumber: '021-12345013',
        email: '<EMAIL>',
        establishedDate: new Date('2020-03-15'),
        createdAt: new Date('2020-03-15'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-014',
        code: 'PKG',
        name: '包装发货部',
        type: DepartmentType.PRODUCTION,
        level: 2,
        parentId: 'dept-010',
        managerId: 'emp-014',
        description: '产品包装和发货管理',
        responsibilities: ['产品包装', '标签打印', '发货准备', '物流协调'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-014',
        location: '生产厂房D区',
        phoneNumber: '021-12345014',
        email: '<EMAIL>',
        establishedDate: new Date('2020-04-01'),
        createdAt: new Date('2020-04-01'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      }
    ]
  },

  // 技术部门
  {
    id: 'dept-020',
    code: 'ENG',
    name: '工程技术部',
    type: DepartmentType.TECHNICAL,
    level: 1,
    managerId: 'emp-020',
    description: '技术工程和研发管理',
    responsibilities: ['技术创新', '工艺开发', '设备管理', '技术支持'],
    status: DepartmentStatus.ACTIVE,
    costCenterCode: 'CC-020',
    location: '技术楼',
    phoneNumber: '021-12345020',
    email: '<EMAIL>',
    establishedDate: new Date('2020-02-01'),
    createdAt: new Date('2020-02-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin',
    children: [
      {
        id: 'dept-021',
        code: 'PE',
        name: '工艺工程部',
        type: DepartmentType.TECHNICAL,
        level: 2,
        parentId: 'dept-020',
        managerId: 'emp-021',
        description: '生产工艺开发和优化',
        responsibilities: ['工艺开发', '参数优化', 'DOE实验', '工艺标准化'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-021',
        location: '技术楼2楼',
        phoneNumber: '021-12345021',
        email: '<EMAIL>',
        establishedDate: new Date('2020-02-15'),
        createdAt: new Date('2020-02-15'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-022',
        code: 'QE',
        name: '质量工程部',
        type: DepartmentType.TECHNICAL,
        level: 2,
        parentId: 'dept-020',
        managerId: 'emp-022',
        description: '质量体系和持续改进',
        responsibilities: ['质量体系管理', 'SPC控制', '客诉处理', '质量改善'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-022',
        location: '技术楼3楼',
        phoneNumber: '021-12345022',
        email: '<EMAIL>',
        establishedDate: new Date('2020-03-01'),
        createdAt: new Date('2020-03-01'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-023',
        code: 'EE',
        name: '设备工程部',
        type: DepartmentType.TECHNICAL,
        level: 2,
        parentId: 'dept-020',
        managerId: 'emp-023',
        description: '设备维护和改善管理',
        responsibilities: ['设备维护', '预防保养', '设备改善', '备件管理'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-023',
        location: '技术楼1楼',
        phoneNumber: '021-12345023',
        email: '<EMAIL>',
        establishedDate: new Date('2020-03-15'),
        createdAt: new Date('2020-03-15'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-024',
        code: 'NPI',
        name: 'NPI开发部',
        type: DepartmentType.TECHNICAL,
        level: 2,
        parentId: 'dept-020',
        managerId: 'emp-024',
        description: '新产品导入和开发',
        responsibilities: ['新产品导入', '客户技术对接', '试产管理', '技术文件'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-024',
        location: '技术楼4楼',
        phoneNumber: '021-12345024',
        email: '<EMAIL>',
        establishedDate: new Date('2020-04-01'),
        createdAt: new Date('2020-04-01'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      }
    ]
  },

  // 支持部门
  {
    id: 'dept-030',
    code: 'SUPPLY',
    name: '供应链管理部',
    type: DepartmentType.SUPPORT,
    level: 1,
    managerId: 'emp-030',
    description: '供应链和物流管理',
    responsibilities: ['采购管理', '供应商管理', '库存控制', '物流协调'],
    status: DepartmentStatus.ACTIVE,
    costCenterCode: 'CC-030',
    location: '办公楼1楼',
    phoneNumber: '021-12345030',
    email: '<EMAIL>',
    establishedDate: new Date('2020-02-01'),
    createdAt: new Date('2020-02-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin',
    children: [
      {
        id: 'dept-031',
        code: 'PUR',
        name: '采购部',
        type: DepartmentType.SUPPORT,
        level: 2,
        parentId: 'dept-030',
        managerId: 'emp-031',
        description: '物料采购和供应商管理',
        responsibilities: ['物料采购', '供应商开发', '合同管理', '成本控制'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-031',
        location: '办公楼1楼A区',
        phoneNumber: '021-12345031',
        email: '<EMAIL>',
        establishedDate: new Date('2020-02-15'),
        createdAt: new Date('2020-02-15'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-032',
        code: 'PLAN',
        name: '计划部',
        type: DepartmentType.SUPPORT,
        level: 2,
        parentId: 'dept-030',
        managerId: 'emp-032',
        description: '生产计划和物料计划',
        responsibilities: ['生产计划', '物料计划', '产能分析', '交期管控'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-032',
        location: '办公楼1楼B区',
        phoneNumber: '021-12345032',
        email: '<EMAIL>',
        establishedDate: new Date('2020-03-01'),
        createdAt: new Date('2020-03-01'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-033',
        code: 'WH',
        name: '仓储部',
        type: DepartmentType.SUPPORT,
        level: 2,
        parentId: 'dept-030',
        managerId: 'emp-033',
        description: '仓储管理和库存控制',
        responsibilities: ['库存管理', '物料收发', '盘点管理', 'ESD管理'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-033',
        location: '仓库',
        phoneNumber: '021-12345033',
        email: '<EMAIL>',
        establishedDate: new Date('2020-03-15'),
        createdAt: new Date('2020-03-15'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      }
    ]
  },

  // 管理部门
  {
    id: 'dept-040',
    code: 'ADMIN',
    name: '行政管理部',
    type: DepartmentType.MANAGEMENT,
    level: 1,
    managerId: 'emp-040',
    description: '行政管理和人力资源',
    responsibilities: ['人事管理', '行政事务', '法务合规', '企业文化'],
    status: DepartmentStatus.ACTIVE,
    costCenterCode: 'CC-040',
    location: '办公楼2楼',
    phoneNumber: '021-12345040',
    email: '<EMAIL>',
    establishedDate: new Date('2020-02-01'),
    createdAt: new Date('2020-02-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin',
    children: [
      {
        id: 'dept-041',
        code: 'HR',
        name: '人力资源部',
        type: DepartmentType.MANAGEMENT,
        level: 2,
        parentId: 'dept-040',
        managerId: 'emp-041',
        description: '人力资源管理',
        responsibilities: ['招聘管理', '培训发展', '绩效管理', '薪酬福利'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-041',
        location: '办公楼2楼A区',
        phoneNumber: '021-12345041',
        email: '<EMAIL>',
        establishedDate: new Date('2020-02-15'),
        createdAt: new Date('2020-02-15'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-042',
        code: 'FIN',
        name: '财务部',
        type: DepartmentType.MANAGEMENT,
        level: 2,
        parentId: 'dept-040',
        managerId: 'emp-042',
        description: '财务管理和成本核算',
        responsibilities: ['财务核算', '成本管理', '预算控制', '税务管理'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-042',
        location: '办公楼2楼B区',
        phoneNumber: '021-12345042',
        email: '<EMAIL>',
        establishedDate: new Date('2020-03-01'),
        createdAt: new Date('2020-03-01'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-043',
        code: 'IT',
        name: 'IT信息部',
        type: DepartmentType.MANAGEMENT,
        level: 2,
        parentId: 'dept-040',
        managerId: 'emp-043',
        description: '信息系统和数字化管理',
        responsibilities: ['系统开发', '网络维护', '数据安全', '数字化推进'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-043',
        location: '办公楼2楼C区',
        phoneNumber: '021-12345043',
        email: '<EMAIL>',
        establishedDate: new Date('2020-03-15'),
        createdAt: new Date('2020-03-15'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      },
      {
        id: 'dept-044',
        code: 'CS',
        name: '客服部',
        type: DepartmentType.MANAGEMENT,
        level: 2,
        parentId: 'dept-040',
        managerId: 'emp-044',
        description: '客户服务和销售支持',
        responsibilities: ['客户服务', '订单管理', '客诉处理', '销售支持'],
        status: DepartmentStatus.ACTIVE,
        costCenterCode: 'CC-044',
        location: '办公楼2楼D区',
        phoneNumber: '021-12345044',
        email: '<EMAIL>',
        establishedDate: new Date('2020-04-01'),
        createdAt: new Date('2020-04-01'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'system',
        updatedBy: 'admin',
        children: []
      }
    ]
  }
]

// 模拟岗位数据
export const mockPositions: Position[] = [
  // 管理岗位
  {
    id: 'pos-001',
    code: 'CEO',
    name: '总经理',
    departmentId: 'dept-001',
    level: PositionLevel.DIRECTOR,
    description: '公司最高管理者',
    responsibilities: ['制定公司战略', '重大决策', '对外代表公司'],
    requirements: ['MBA学历', '10年以上管理经验', '半导体行业背景'],
    skills: ['战略规划', '团队管理', '商务谈判'],
    headcount: 1,
    occupiedCount: 1,
    isKeyPosition: true,
    status: 'active',
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin'
  },

  // 生产部门岗位
  {
    id: 'pos-010',
    code: 'PROD_MGR',
    name: '生产部经理',
    departmentId: 'dept-010',
    level: PositionLevel.MANAGER,
    description: '负责生产制造管理',
    responsibilities: ['生产计划制定', '产能管理', '现场管理', '安全生产'],
    requirements: ['本科以上学历', '5年以上生产管理经验', 'IC封测行业经验'],
    skills: ['生产管理', '质量控制', '团队领导'],
    headcount: 1,
    occupiedCount: 1,
    isKeyPosition: true,
    status: 'active',
    createdAt: new Date('2020-02-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin'
  },
  {
    id: 'pos-011',
    code: 'CP_MGR',
    name: 'CP测试主管',
    departmentId: 'dept-011',
    level: PositionLevel.SUPERVISOR,
    reportToId: 'pos-010',
    description: 'CP测试部门管理',
    responsibilities: ['CP测试管理', '探针卡维护', '良率分析', '团队管理'],
    requirements: ['电子工程相关专业', '3年以上CP测试经验'],
    skills: ['CP测试', '数据分析', '团队管理'],
    headcount: 1,
    occupiedCount: 1,
    isKeyPosition: true,
    status: 'active',
    createdAt: new Date('2020-02-15'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin'
  },
  {
    id: 'pos-012',
    code: 'CP_ENG',
    name: 'CP测试工程师',
    departmentId: 'dept-011',
    level: PositionLevel.SPECIALIST,
    reportToId: 'pos-011',
    description: 'CP测试工程师',
    responsibilities: ['测试程序开发', '参数优化', '异常分析', '报告编写'],
    requirements: ['电子工程专业', '2年以上测试经验', '熟悉ATE设备'],
    skills: ['测试程序', '数据分析', '问题解决'],
    headcount: 3,
    occupiedCount: 2,
    isKeyPosition: false,
    status: 'active',
    createdAt: new Date('2020-03-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin'
  },
  {
    id: 'pos-013',
    code: 'CP_OPR',
    name: 'CP测试操作员',
    departmentId: 'dept-011',
    level: PositionLevel.OPERATOR,
    reportToId: 'pos-011',
    description: 'CP测试设备操作',
    responsibilities: ['设备操作', '数据记录', '异常报告', '5S维护'],
    requirements: ['高中以上学历', '1年以上操作经验'],
    skills: ['设备操作', '数据记录', '质量意识'],
    headcount: 8,
    occupiedCount: 7,
    isKeyPosition: false,
    status: 'active',
    createdAt: new Date('2020-03-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin'
  },

  // 技术部门岗位
  {
    id: 'pos-020',
    code: 'ENG_DIR',
    name: '工程总监',
    departmentId: 'dept-020',
    level: PositionLevel.DIRECTOR,
    description: '工程技术总体负责人',
    responsibilities: ['技术战略', '技术创新', '团队建设', '客户技术对接'],
    requirements: ['硕士以上学历', '8年以上技术管理经验', '半导体行业背景'],
    skills: ['技术管理', '创新驱动', '客户沟通'],
    headcount: 1,
    occupiedCount: 1,
    isKeyPosition: true,
    status: 'active',
    createdAt: new Date('2020-02-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin'
  },
  {
    id: 'pos-021',
    code: 'PE_MGR',
    name: '工艺工程经理',
    departmentId: 'dept-021',
    level: PositionLevel.MANAGER,
    reportToId: 'pos-020',
    description: '工艺工程部门管理',
    responsibilities: ['工艺开发', '参数优化', '标准化', '技术支持'],
    requirements: ['材料/电子工程专业', '5年以上工艺经验'],
    skills: ['工艺开发', 'DOE实验', '数据分析'],
    headcount: 1,
    occupiedCount: 1,
    isKeyPosition: true,
    status: 'active',
    createdAt: new Date('2020-02-15'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin'
  }
]

// 模拟员工数据
export const mockEmployees: Employee[] = [
  {
    id: 'emp-001',
    employeeNumber: 'CEO001',
    name: '张总',
    englishName: 'Michael Zhang',
    departmentId: 'dept-001',
    positionId: 'pos-001',
    email: '<EMAIL>',
    phoneNumber: '13901234567',
    hireDate: new Date('2020-01-01'),
    status: EmployeeStatus.ACTIVE,
    gender: 'male',
    birthDate: new Date('1975-06-15'),
    education: 'MBA',
    workLocation: '上海',
    emergencyContact: {
      name: '张夫人',
      relationship: '配偶',
      phoneNumber: '13901234568'
    },
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'emp-010',
    employeeNumber: 'PM001',
    name: '李生产',
    englishName: 'Peter Li',
    departmentId: 'dept-010',
    positionId: 'pos-010',
    email: '<EMAIL>',
    phoneNumber: '13902345678',
    hireDate: new Date('2020-02-01'),
    status: EmployeeStatus.ACTIVE,
    gender: 'male',
    birthDate: new Date('1980-03-20'),
    education: '本科',
    workLocation: '上海',
    emergencyContact: {
      name: '李太太',
      relationship: '配偶',
      phoneNumber: '13902345679'
    },
    createdAt: new Date('2020-02-01'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'emp-011',
    employeeNumber: 'CP001',
    name: '王测试',
    englishName: 'David Wang',
    departmentId: 'dept-011',
    positionId: 'pos-011',
    directSupervisorId: 'emp-010',
    email: '<EMAIL>',
    phoneNumber: '13903456789',
    hireDate: new Date('2020-02-15'),
    status: EmployeeStatus.ACTIVE,
    gender: 'male',
    birthDate: new Date('1985-08-10'),
    education: '本科',
    workLocation: '上海',
    emergencyContact: {
      name: '王妈妈',
      relationship: '母亲',
      phoneNumber: '13903456780'
    },
    createdAt: new Date('2020-02-15'),
    updatedAt: new Date('2024-01-15')
  }
]

// 模拟权限数据
export const mockPermissions: Permission[] = [
  {
    id: 'perm-001',
    code: 'SYSTEM',
    name: '系统管理',
    type: PermissionType.MENU,
    description: '系统管理权限',
    children: [
      {
        id: 'perm-001-001',
        code: 'USER_MANAGE',
        name: '用户管理',
        type: PermissionType.MENU,
        parentId: 'perm-001',
        resource: '/system/users',
        description: '用户管理菜单',
        createdAt: new Date('2020-01-01'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: 'perm-001-002',
        code: 'ORG_MANAGE',
        name: '组织管理',
        type: PermissionType.MENU,
        parentId: 'perm-001',
        resource: '/system/organization',
        description: '组织架构管理菜单',
        createdAt: new Date('2020-01-01'),
        updatedAt: new Date('2024-01-15')
      }
    ],
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'perm-002',
    code: 'PRODUCTION',
    name: '生产管理',
    type: PermissionType.MENU,
    description: '生产相关权限',
    children: [
      {
        id: 'perm-002-001',
        code: 'CP_TEST',
        name: 'CP测试',
        type: PermissionType.MENU,
        parentId: 'perm-002',
        resource: '/production/cp-test',
        description: 'CP测试管理',
        createdAt: new Date('2020-01-01'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: 'perm-002-002',
        code: 'ASSEMBLY',
        name: '封装管理',
        type: PermissionType.MENU,
        parentId: 'perm-002',
        resource: '/production/assembly',
        description: '封装工艺管理',
        createdAt: new Date('2020-01-01'),
        updatedAt: new Date('2024-01-15')
      }
    ],
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-15')
  }
]

// 模拟成本中心数据
export const mockCostCenters: CostCenter[] = [
  {
    id: 'cc-001',
    code: 'CC-001',
    name: '总经理办公室',
    type: 'management',
    departmentIds: ['dept-001'],
    managerId: 'emp-001',
    budget: {
      annual: 5000000,
      currency: 'CNY',
      year: 2024
    },
    description: '管理层成本中心',
    isActive: true,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin'
  },
  {
    id: 'cc-010',
    code: 'CC-010',
    name: '生产制造',
    type: 'production',
    departmentIds: ['dept-010', 'dept-011', 'dept-012', 'dept-013', 'dept-014'],
    managerId: 'emp-010',
    budget: {
      annual: 50000000,
      currency: 'CNY',
      year: 2024
    },
    description: '生产相关成本中心',
    isActive: true,
    createdAt: new Date('2020-02-01'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'system',
    updatedBy: 'admin'
  }
]

// 模拟变更记录数据
export const mockOrganizationChanges: OrganizationChange[] = [
  {
    id: 'change-001',
    type: 'department',
    action: 'create',
    targetId: 'dept-024',
    targetName: 'NPI开发部',
    newValue: {
      name: 'NPI开发部',
      type: 'technical',
      parentId: 'dept-020'
    },
    reason: '业务发展需要，新增NPI部门',
    approvedBy: 'emp-001',
    effectiveDate: new Date('2024-01-01'),
    createdAt: new Date('2024-01-01'),
    createdBy: 'emp-041'
  },
  {
    id: 'change-002',
    type: 'employee',
    action: 'update',
    targetId: 'emp-011',
    targetName: '王测试',
    oldValue: {
      positionId: 'pos-012'
    },
    newValue: {
      positionId: 'pos-011'
    },
    reason: '晋升为CP测试主管',
    approvedBy: 'emp-010',
    effectiveDate: new Date('2024-01-15'),
    createdAt: new Date('2024-01-15'),
    createdBy: 'emp-041'
  }
]

// 导出所有模拟数据
export const organizationMockData = {
  departments: mockDepartments,
  positions: mockPositions,
  employees: mockEmployees,
  permissions: mockPermissions,
  costCenters: mockCostCenters,
  changes: mockOrganizationChanges
}
