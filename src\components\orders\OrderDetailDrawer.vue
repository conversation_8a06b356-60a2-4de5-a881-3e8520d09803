<template>
  <el-drawer
v-model="visible" title="订单详情"
:size="600" direction="rtl"
>
    <div v-if="orderData" class="order-detail-content">
      <!-- 订单基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">订单编号：</span>
            <span class="value">{{ orderData.orderNumber }}</span>
          </div>
          <div class="info-item">
            <span class="label">订单状态：</span>
            <el-tag :type="getStatusTagType(orderData.status)">
              {{ getStatusText(orderData.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">优先级：</span>
            <el-tag :type="getPriorityTagType(orderData.priority)">
              {{ getPriorityText(orderData.priority) }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(orderData.createTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">交付日期：</span>
            <span class="value" :class="getDeliveryClass(orderData.deliveryDate)">
              {{ formatDate(orderData.deliveryDate) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 客户信息 -->
      <div class="detail-section">
        <h3 class="section-title">客户信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">客户名称：</span>
            <span class="value">{{ orderData.customer.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">客户代码：</span>
            <span class="value">{{ orderData.customer.code }}</span>
          </div>
        </div>
      </div>

      <!-- 产品信息 -->
      <div class="detail-section">
        <h3 class="section-title">产品信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">产品名称：</span>
            <span class="value">{{ orderData.productInfo.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">封装类型：</span>
            <span class="value">{{ orderData.productInfo.packageType }}</span>
          </div>
          <div class="info-item">
            <span class="label">订单数量：</span>
            <span class="value">{{ orderData.productInfo.quantity.toLocaleString() }}K pcs</span>
          </div>
        </div>
      </div>

      <!-- 进度信息 -->
      <div class="detail-section">
        <h3 class="section-title">生产进度</h3>
        <div class="progress-container">
          <div class="progress-header">
            <span>完成进度</span>
            <span class="progress-value">{{ orderData.progress }}%</span>
          </div>
          <el-progress
            :percentage="orderData.progress"
            :stroke-width="12"
            :color="getProgressColor(orderData.progress)"
          />
          <div class="progress-steps">
            <div
v-for="step in progressSteps" class="step"
:key="step.name"
>
              <div class="step-icon" :class="getStepClass(step, orderData.progress)">
                <svg viewBox="0 0 24 24"
v-html="step.icon"
/>
              </div>
              <span class="step-name">{{ step.name }}</span>
              <span class="step-progress">{{ step.progress }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="detail-section">
        <h3 class="section-title">操作日志</h3>
        <div class="timeline">
          <div
v-for="(log, index) in operationLogs" :key="index"
class="timeline-item"
>
            <div class="timeline-dot" />
            <div class="timeline-content">
              <div class="log-header">
                <span class="log-action">{{ log.action }}</span>
                <span class="log-time">{{ formatDateTime(log.time) }}</span>
              </div>
              <div class="log-details">
                {{ log.details }}
              </div>
              <div class="log-user">操作人：{{ log.user }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="loading-content">
      <el-skeleton :rows="10" animated />
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary"
@click="handleEdit"
>
编辑订单
</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'

  interface Props {
    modelValue: boolean
    orderId: string
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  const orderData = ref<any>(null)
  const loading = ref(false)

  const visible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  const progressSteps = [
    {
      name: 'CP电测',
      progress: 100,
      icon: '<circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>'
    },
    {
      name: 'Assembly',
      progress: 75,
      icon: '<rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/>'
    },
    {
      name: 'FT测试',
      progress: 45,
      icon: '<path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>'
    },
    {
      name: '交付',
      progress: 0,
      icon: '<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,15 17,10"/>'
    }
  ]

  const operationLogs = ref([
    {
      action: '订单创建',
      details: '订单已成功创建，等待确认',
      user: '张三',
      time: '2024-01-15 09:30:00'
    },
    {
      action: '订单确认',
      details: '客户已确认订单，开始生产安排',
      user: '李四',
      time: '2024-01-15 10:45:00'
    },
    {
      action: '生产开始',
      details: 'CP电测阶段开始',
      user: '系统',
      time: '2024-01-16 08:00:00'
    },
    {
      action: 'CP电测完成',
      details: '晶圆电测完成，良率 98.5%',
      user: '王五',
      time: '2024-01-17 16:30:00'
    }
  ])

  const loadOrderDetail = async (orderId: string) => {
    if (!orderId) return

    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟订单数据
      orderData.value = {
        id: orderId,
        orderNumber: `CIM${orderId.slice(-6)}`,
        customer: {
          name: 'Apple Inc.',
          code: 'C001'
        },
        productInfo: {
          name: 'A16 Bionic Chip',
          packageType: 'FC',
          quantity: 500
        },
        status: 'processing',
        progress: 65,
        priority: 'high',
        createTime: '2024-01-15T09:30:00',
        deliveryDate: '2024-02-15'
      }
    } finally {
      loading.value = false
    }
  }

  const getStatusText = (status: string) => {
    const statusMap = {
      pending: '待确认',
      processing: '生产中',
      testing: '测试中',
      completed: '已完成',
      cancelled: '已取消'
    }
    return statusMap[status as keyof typeof statusMap] || status
  }

  const getStatusTagType = (status: string) => {
    const typeMap = {
      pending: 'info',
      processing: 'warning',
      testing: 'primary',
      completed: 'success',
      cancelled: 'danger'
    }
    return typeMap[status as keyof typeof typeMap] || 'info'
  }

  const getPriorityText = (priority: string) => {
    const priorityMap = {
      low: '普通',
      medium: '一般',
      high: '紧急',
      urgent: '特急'
    }
    return priorityMap[priority as keyof typeof priorityMap] || priority
  }

  const getPriorityTagType = (priority: string) => {
    const typeMap = {
      low: 'info',
      medium: '',
      high: 'warning',
      urgent: 'danger'
    }
    return typeMap[priority as keyof typeof typeMap] || ''
  }

  const getProgressColor = (progress: number) => {
    if (progress < 30) return '#f56c6c'
    if (progress < 70) return '#e6a23c'
    return '#67c23a'
  }

  const getStepClass = (step: any, currentProgress: number) => {
    if (step.progress === 100) return 'step-icon--completed'
    if (step.progress > 0 && step.progress < 100) return 'step-icon--processing'
    return 'step-icon--pending'
  }

  const getDeliveryClass = (deliveryDate: string) => {
    const today = new Date()
    const delivery = new Date(deliveryDate)
    const diffTime = delivery.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays < 0) return 'delivery-overdue'
    if (diffDays <= 3) return 'delivery-urgent'
    if (diffDays <= 7) return 'delivery-warning'
    return 'delivery-normal'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const handleEdit = () => {
    // 触发编辑事件
    console.log('编辑订单:', orderData.value)
  }

  // 监听orderId变化
  watch(
    () => props.orderId,
    newId => {
      if (newId && props.modelValue) {
        loadOrderDetail(newId)
      }
    },
    { immediate: true }
  )

  watch(
    () => props.modelValue,
    isVisible => {
      if (isVisible && props.orderId) {
        loadOrderDetail(props.orderId)
      }
    }
  )
</script>

<style lang="scss" scoped>
  .order-detail-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
  }

  .loading-content {
    padding: var(--spacing-6);
  }

  .detail-section {
    .section-title {
      padding-bottom: var(--spacing-2);
      margin-bottom: var(--spacing-4);
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
      border-bottom: 2px solid var(--color-primary);
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .info-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-2) 0;

    .label {
      min-width: 80px;
      font-weight: var(--font-weight-medium);
      color: var(--color-text-secondary);
    }

    .value {
      color: var(--color-text-primary);

      &.delivery-overdue {
        color: var(--color-error);
      }

      &.delivery-urgent {
        color: var(--color-warning);
      }

      &.delivery-warning {
        color: var(--color-primary);
      }

      &.delivery-normal {
        color: var(--color-text-secondary);
      }
    }
  }

  .progress-container {
    .progress-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-3);

      .progress-value {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-bold);
        color: var(--color-primary);
      }
    }

    .progress-steps {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: var(--spacing-4);
      margin-top: var(--spacing-5);

      .step {
        text-align: center;

        .step-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          margin: 0 auto var(--spacing-2);
          border-radius: 50%;
          transition: all var(--transition-normal);

          svg {
            width: 20px;
            height: 20px;
            fill: none;
            stroke: currentcolor;
            stroke-width: 1.5;
          }

          &--completed {
            color: white;
            background-color: var(--color-success);
          }

          &--processing {
            color: white;
            background-color: var(--color-warning);
          }

          &--pending {
            color: var(--color-text-tertiary);
            background-color: var(--color-bg-tertiary);
          }
        }

        .step-name {
          display: block;
          margin-bottom: 2px;
          font-size: var(--font-size-sm);
          color: var(--color-text-primary);
        }

        .step-progress {
          font-size: var(--font-size-xs);
          color: var(--color-text-tertiary);
        }
      }
    }
  }

  .timeline {
    position: relative;

    &::before {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 12px;
      width: 2px;
      content: '';
      background-color: var(--color-border-light);
    }

    .timeline-item {
      position: relative;
      padding-bottom: var(--spacing-4);
      padding-left: var(--spacing-8);

      &:last-child {
        padding-bottom: 0;
      }

      .timeline-dot {
        position: absolute;
        top: 4px;
        left: 8px;
        width: 8px;
        height: 8px;
        background-color: var(--color-primary);
        border-radius: 50%;
      }

      .timeline-content {
        .log-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--spacing-1);

          .log-action {
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
          }

          .log-time {
            font-size: var(--font-size-xs);
            color: var(--color-text-tertiary);
          }
        }

        .log-details {
          margin-bottom: var(--spacing-1);
          color: var(--color-text-secondary);
        }

        .log-user {
          font-size: var(--font-size-xs);
          color: var(--color-text-tertiary);
        }
      }
    }
  }

  .drawer-footer {
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
  }
</style>
