# 生产环境配置
NODE_ENV=production

# 应用信息
VITE_APP_TITLE="IC封测CIM系统"
VITE_APP_VERSION="1.0.0"
VITE_APP_DESCRIPTION="IC Assembly & Testing CIM System"

# API配置
VITE_API_BASE_URL="https://api.ic-cim.com"
VITE_API_PREFIX="/api/v1"

# WebSocket配置
VITE_WS_BASE_URL="wss://api.ic-cim.com"
VITE_WS_PREFIX="/ws"

# 开发工具
VITE_OPEN_DEVTOOLS=false
VITE_SOURCEMAP=false

# 主题配置
VITE_DEFAULT_THEME="light"
VITE_THEME_STORAGE_KEY="ic-cim-theme"

# 调试开关
VITE_DEBUG=false
VITE_MOCK_DATA=false