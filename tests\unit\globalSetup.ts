import { vi } from 'vitest'

export async function setup() {
  // 全局测试设置
  console.log('Starting test suite...')

  // 设置全局变量
  process.env.NODE_ENV = 'test'

  // 设置时区
  process.env.TZ = 'UTC'

  // Mock全局fetch
  global.fetch = vi.fn()

  return async () => {
    // 清理
    console.log('Cleaning up test suite...')
  }
}

export async function teardown() {
  // 全局清理
  console.log('Test suite completed.')
}
