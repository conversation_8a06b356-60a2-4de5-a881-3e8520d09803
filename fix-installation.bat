@echo off
echo ==========================================
echo     修复D盘软件安装配置
echo ==========================================

echo.
echo [1/3] 修复Maven目录结构...
if exist "D:\Development\Java\maven\apache-maven-3.9.11-bin\apache-maven-3.9.11" (
    echo 移动Maven文件到正确位置...
    xcopy "D:\Development\Java\maven\apache-maven-3.9.11-bin\apache-maven-3.9.11\*" "D:\Development\Java\maven\" /E /H /Y >nul
    rmdir "D:\Development\Java\maven\apache-maven-3.9.11-bin" /S /Q >nul 2>&1
    echo ✓ Maven目录结构已修复
) else (
    echo ✓ Maven目录结构正确
)

echo.
echo [2/3] 设置环境变量...
echo 设置JAVA_HOME...
setx JAVA_HOME "D:\Development\Java\jdk-17" /M >nul
echo ✓ JAVA_HOME = D:\Development\Java\jdk-17

echo 设置MAVEN_HOME...
setx MAVEN_HOME "D:\Development\Java\maven" /M >nul
echo ✓ MAVEN_HOME = D:\Development\Java\maven

echo 设置REDIS_HOME...
setx REDIS_HOME "D:\Development\Redis" /M >nul
echo ✓ REDIS_HOME = D:\Development\Redis

echo.
echo [3/3] 更新PATH环境变量...
for /f "tokens=2*" %%A in ('reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set "systempath=%%B"

echo 当前系统PATH长度: 
echo %systempath% | find /c /v "" 

:: 检查并添加Java路径
echo %systempath% | findstr /C:"D:\Development\Java\jdk-17\bin" >nul
if errorlevel 1 (
    setx PATH "%systempath%;D:\Development\Java\jdk-17\bin" /M >nul
    echo ✓ 已添加Java bin到PATH
) else (
    echo ✓ Java bin已在PATH中
)

:: 检查并添加Maven路径
echo %systempath% | findstr /C:"D:\Development\Java\maven\bin" >nul
if errorlevel 1 (
    for /f "tokens=2*" %%A in ('reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set "newpath=%%B"
    setx PATH "%newpath%;D:\Development\Java\maven\bin" /M >nul
    echo ✓ 已添加Maven bin到PATH
) else (
    echo ✓ Maven bin已在PATH中
)

:: 检查并添加Redis路径
echo %systempath% | findstr /C:"D:\Development\Redis" >nul
if errorlevel 1 (
    for /f "tokens=2*" %%A in ('reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set "finalpath=%%B"
    setx PATH "%finalpath%;D:\Development\Redis" /M >nul
    echo ✓ 已添加Redis到PATH
) else (
    echo ✓ Redis已在PATH中
)

echo.
echo ==========================================
echo        配置修复完成！
echo ==========================================
echo.
echo 重要提醒：
echo 1. 请重启命令行窗口使环境变量生效
echo 2. 或者注销并重新登录Windows
echo 3. 然后运行验证脚本检查安装
echo.
echo 验证命令：
echo   java -version
echo   mvn --version
echo   redis-cli --version
echo.
pause