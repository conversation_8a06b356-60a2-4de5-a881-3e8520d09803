/**
 * IC封测CIM系统 - 用户管理组合式函数
 * User Management Composable for IC Packaging & Testing CIM System
 */

import { ref, reactive } from 'vue'
import type {
  UserInfo,
  UserQueryParams,
  UserListResponse,
  CreateUserRequest,
  UpdateUserRequest,
  Department,
  RoleConfig,
  PermissionConfig
} from '@/types/user'
import { MockApiHelper } from '@/api/config'
import type { ApiResponse } from '@/api/config'

/**
 * 用户管理组合式函数
 */
export function useUserManagement() {
  // 状态管理
  const users = ref<UserInfo[]>([])
  const loading = ref(false)
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 基础数据
  const departments = ref<Department[]>([])
  const roles = ref<RoleConfig[]>([])
  const permissions = ref<PermissionConfig[]>([])

  /**
   * 加载用户列表
   */
  const loadUsers = async (params?: Partial<UserQueryParams>): Promise<void> => {
    loading.value = true
    try {
      const queryParams = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...params
      }

      const response = await mockGetUsers(queryParams)

      if (response.success) {
        users.value = response.data.users
        pagination.total = response.data.total
        pagination.page = response.data.page
        pagination.pageSize = response.data.pageSize
      }
    } catch (error) {
      console.error('Failed to load users:', error)
      ElMessage.error('加载用户列表失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 搜索用户
   */
  const searchUsers = async (params: UserQueryParams): Promise<void> => {
    pagination.page = params.page || 1
    pagination.pageSize = params.pageSize || 20
    await loadUsers(params)
  }

  /**
   * 创建用户
   */
  const createUser = async (userData: CreateUserRequest): Promise<boolean> => {
    try {
      const response = await mockCreateUser(userData)

      if (response.success) {
        ElMessage.success('用户创建成功')
        await loadUsers()
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('Failed to create user:', error)
      ElMessage.error(error.message || '创建用户失败')
      return false
    }
  }

  /**
   * 更新用户
   */
  const updateUser = async (userData: UpdateUserRequest): Promise<boolean> => {
    try {
      const response = await mockUpdateUser(userData)

      if (response.success) {
        ElMessage.success('用户更新成功')
        await loadUsers()
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('Failed to update user:', error)
      ElMessage.error(error.message || '更新用户失败')
      return false
    }
  }

  /**
   * 删除用户
   */
  const deleteUser = async (userId: string): Promise<boolean> => {
    try {
      const response = await mockDeleteUser(userId)

      if (response.success) {
        ElMessage.success('用户删除成功')
        await loadUsers()
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('Failed to delete user:', error)
      ElMessage.error(error.message || '删除用户失败')
      return false
    }
  }

  /**
   * 切换用户状态
   */
  const toggleUserStatus = async (
    userId: string,
    status: 'active' | 'inactive' | 'locked'
  ): Promise<boolean> => {
    try {
      const response = await mockToggleUserStatus(userId, status)

      if (response.success) {
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('Failed to toggle user status:', error)
      throw error
    }
  }

  /**
   * 重置用户密码
   */
  const resetUserPassword = async (userId: string, newPassword: string): Promise<boolean> => {
    try {
      const response = await mockResetUserPassword(userId, newPassword)

      if (response.success) {
        ElMessage.success('密码重置成功')
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('Failed to reset password:', error)
      ElMessage.error(error.message || '重置密码失败')
      return false
    }
  }

  /**
   * 导出用户数据
   */
  const exportUsers = async (params?: UserQueryParams): Promise<void> => {
    try {
      // 模拟导出功能
      await MockApiHelper.delay('EXPORT')

      // 这里应该调用实际的导出API
      const exportData = users.value.map(user => ({
        用户名: user.username,
        真实姓名: user.realName,
        邮箱: user.email,
        电话: user.phone || '',
        部门: user.department,
        职位: user.position,
        状态: user.status === 'active' ? '启用' : user.status === 'inactive' ? '禁用' : '锁定',
        创建时间: user.createTime,
        最后登录: user.lastLoginTime || '从未登录'
      }))

      // 创建CSV内容
      const headers = Object.keys(exportData[0] || {})
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
      ].join('\n')

      // 下载文件
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `用户数据_${new Date().toISOString().split('T')[0]}.csv`
      link.click()
      URL.revokeObjectURL(url)
    } catch (error: any) {
      console.error('Failed to export users:', error)
      throw error
    }
  }

  /**
   * 加载部门列表
   */
  const loadDepartments = async (): Promise<void> => {
    try {
      const response = await mockGetDepartments()
      if (response.success) {
        departments.value = response.data
      }
    } catch (error) {
      console.error('Failed to load departments:', error)
    }
  }

  /**
   * 加载角色列表
   */
  const loadRoles = async (): Promise<void> => {
    try {
      const response = await mockGetRoles()
      if (response.success) {
        roles.value = response.data
      }
    } catch (error) {
      console.error('Failed to load roles:', error)
    }
  }

  /**
   * 加载权限列表
   */
  const loadPermissions = async (): Promise<void> => {
    try {
      const response = await mockGetPermissions()
      if (response.success) {
        permissions.value = response.data
      }
    } catch (error) {
      console.error('Failed to load permissions:', error)
    }
  }

  // Mock API 函数
  const mockGetUsers = async (
    params: Partial<UserQueryParams>
  ): Promise<ApiResponse<UserListResponse>> => {
    await MockApiHelper.delay('SEARCH')

    // 模拟用户数据
    const mockUsers: UserInfo[] = [
      {
        id: 'user_001',
        username: 'admin',
        email: '<EMAIL>',
        phone: '13800138000',
        realName: '系统管理员',
        avatar: '',
        department: 'IT部门',
        position: '系统管理员',
        roles: ['system_admin'],
        permissions: ['system:read', 'system:write', 'system:delete'],
        lastLoginTime: new Date().toISOString(),
        lastLoginIp: '*************',
        status: 'active',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        id: 'user_002',
        username: 'engineer01',
        email: '<EMAIL>',
        phone: '13800138001',
        realName: '张工程师',
        avatar: '',
        department: '工艺工程部',
        position: '工艺工程师',
        roles: ['process_engineer'],
        permissions: ['production:read', 'production:write', 'quality:read'],
        lastLoginTime: new Date(Date.now() - 3600000).toISOString(),
        lastLoginIp: '*************',
        status: 'active',
        createTime: '2024-01-02T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        id: 'user_003',
        username: 'operator01',
        email: '<EMAIL>',
        phone: '13800138002',
        realName: '李操作员',
        avatar: '',
        department: '生产部门',
        position: '生产操作员',
        roles: ['production_operator'],
        permissions: ['production:read', 'equipment:read'],
        lastLoginTime: new Date(Date.now() - 7200000).toISOString(),
        lastLoginIp: '*************',
        status: 'active',
        createTime: '2024-01-03T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        id: 'user_004',
        username: 'qc01',
        email: '<EMAIL>',
        phone: '13800138003',
        realName: '王质检员',
        avatar: '',
        department: '质量部门',
        position: '质量检验员',
        roles: ['qc_inspector'],
        permissions: ['quality:read', 'quality:write'],
        lastLoginTime: null,
        lastLoginIp: null,
        status: 'inactive',
        createTime: '2024-01-04T00:00:00.000Z',
        updateTime: new Date().toISOString()
      }
    ]

    // 应用筛选条件
    let filteredUsers = mockUsers

    if (params.keyword) {
      const keyword = params.keyword.toLowerCase()
      filteredUsers = filteredUsers.filter(
        user =>
          user.username.toLowerCase().includes(keyword) ||
          user.realName.toLowerCase().includes(keyword) ||
          user.email.toLowerCase().includes(keyword)
      )
    }

    if (params.department) {
      filteredUsers = filteredUsers.filter(user => user.department === params.department)
    }

    if (params.status) {
      filteredUsers = filteredUsers.filter(user => user.status === params.status)
    }

    if (params.role) {
      filteredUsers = filteredUsers.filter(user => user.roles.includes(params.role))
    }

    // 排序
    if (params.sortBy) {
      filteredUsers.sort((a, b) => {
        const aVal = a[params.sortBy!] || ''
        const bVal = b[params.sortBy!] || ''

        if (params.sortOrder === 'desc') {
          return bVal > aVal ? 1 : -1
        } else {
          return aVal > bVal ? 1 : -1
        }
      })
    }

    // 分页
    const page = params.page || 1
    const pageSize = params.pageSize || 20
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const paginatedUsers = filteredUsers.slice(start, end)

    return MockApiHelper.createPageResponse(
      paginatedUsers,
      filteredUsers.length,
      page,
      pageSize,
      '查询成功'
    )
  }

  const mockCreateUser = async (userData: CreateUserRequest): Promise<ApiResponse<UserInfo>> => {
    await MockApiHelper.delay('CREATE')

    const newUser: UserInfo = {
      id: `user_${Date.now()}`,
      ...userData,
      avatar: '',
      permissions: [],
      lastLoginTime: null,
      lastLoginIp: null,
      status: userData.status || 'active',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    return MockApiHelper.createSuccessResponse(newUser, '用户创建成功')
  }

  const mockUpdateUser = async (userData: UpdateUserRequest): Promise<ApiResponse<UserInfo>> => {
    await MockApiHelper.delay('UPDATE')

    // 模拟找到用户并更新
    const updatedUser: UserInfo = {
      id: userData.id,
      username: 'existing_username', // 实际应该从数据库获取
      ...userData,
      avatar: '',
      permissions: [],
      lastLoginTime: new Date().toISOString(),
      lastLoginIp: '*************',
      createTime: '2024-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    }

    return MockApiHelper.createSuccessResponse(updatedUser, '用户更新成功')
  }

  const mockDeleteUser = async (userId: string): Promise<ApiResponse> => {
    await MockApiHelper.delay('DELETE')
    return MockApiHelper.createSuccessResponse(null, '用户删除成功')
  }

  const mockToggleUserStatus = async (userId: string, status: string): Promise<ApiResponse> => {
    await MockApiHelper.delay('UPDATE')
    return MockApiHelper.createSuccessResponse(null, '状态更新成功')
  }

  const mockResetUserPassword = async (
    userId: string,
    newPassword: string
  ): Promise<ApiResponse> => {
    await MockApiHelper.delay('UPDATE')
    return MockApiHelper.createSuccessResponse(null, '密码重置成功')
  }

  const mockGetDepartments = async (): Promise<ApiResponse<Department[]>> => {
    await MockApiHelper.delay('SEARCH')

    const departments: Department[] = [
      {
        id: 'dept_001',
        code: 'IT',
        name: 'IT部门',
        level: 1,
        sort: 1,
        status: 'active',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        id: 'dept_002',
        code: 'PROCESS',
        name: '工艺工程部',
        level: 1,
        sort: 2,
        status: 'active',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        id: 'dept_003',
        code: 'PRODUCTION',
        name: '生产部门',
        level: 1,
        sort: 3,
        status: 'active',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        id: 'dept_004',
        code: 'QUALITY',
        name: '质量部门',
        level: 1,
        sort: 4,
        status: 'active',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        id: 'dept_005',
        code: 'EQUIPMENT',
        name: '设备部门',
        level: 1,
        sort: 5,
        status: 'active',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      }
    ]

    return MockApiHelper.createSuccessResponse(departments, '查询成功')
  }

  const mockGetRoles = async (): Promise<ApiResponse<RoleConfig[]>> => {
    await MockApiHelper.delay('SEARCH')

    const roles: RoleConfig[] = [
      {
        code: 'system_admin',
        name: '系统管理员',
        permissions: ['system:read', 'system:write', 'system:delete'],
        description: '系统管理员，拥有所有权限',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        code: 'process_engineer',
        name: '工艺工程师',
        permissions: ['production:read', 'production:write', 'quality:read'],
        description: '工艺工程师，负责工艺开发和优化',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        code: 'production_operator',
        name: '生产操作员',
        permissions: ['production:read', 'equipment:read'],
        description: '生产操作员，负责设备操作',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      },
      {
        code: 'qc_inspector',
        name: '质量检验员',
        permissions: ['quality:read', 'quality:write'],
        description: '质量检验员，负责产品质量检验',
        createTime: '2024-01-01T00:00:00.000Z',
        updateTime: new Date().toISOString()
      }
    ]

    return MockApiHelper.createSuccessResponse(roles, '查询成功')
  }

  const mockGetPermissions = async (): Promise<ApiResponse<PermissionConfig[]>> => {
    await MockApiHelper.delay('SEARCH')

    const permissions: PermissionConfig[] = [
      { code: 'system:read', name: '系统查看', type: 'menu', description: '系统管理查看权限' },
      { code: 'system:write', name: '系统编辑', type: 'button', description: '系统管理编辑权限' },
      { code: 'system:delete', name: '系统删除', type: 'button', description: '系统管理删除权限' },
      { code: 'user:read', name: '用户查看', type: 'menu', description: '用户管理查看权限' },
      { code: 'user:write', name: '用户编辑', type: 'button', description: '用户管理编辑权限' },
      { code: 'user:delete', name: '用户删除', type: 'button', description: '用户管理删除权限' },
      { code: 'production:read', name: '生产查看', type: 'menu', description: '生产管理查看权限' },
      {
        code: 'production:write',
        name: '生产编辑',
        type: 'button',
        description: '生产管理编辑权限'
      },
      { code: 'equipment:read', name: '设备查看', type: 'menu', description: '设备管理查看权限' },
      {
        code: 'equipment:write',
        name: '设备编辑',
        type: 'button',
        description: '设备管理编辑权限'
      },
      { code: 'quality:read', name: '质量查看', type: 'menu', description: '质量管理查看权限' },
      { code: 'quality:write', name: '质量编辑', type: 'button', description: '质量管理编辑权限' }
    ]

    return MockApiHelper.createSuccessResponse(permissions, '查询成功')
  }

  // 初始化基础数据
  const initialize = async (): Promise<void> => {
    await Promise.all([loadDepartments(), loadRoles(), loadPermissions()])
  }

  return {
    // 状态
    users,
    loading,
    pagination,
    departments,
    roles,
    permissions,

    // 方法
    loadUsers,
    searchUsers,
    createUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    resetUserPassword,
    exportUsers,
    loadDepartments,
    loadRoles,
    loadPermissions,
    initialize
  }
}
