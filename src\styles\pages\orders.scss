// IC封测CIM系统 - 订单管理页面样式

.orders-page {
  padding: var(--spacing-6);
  
  &__header {
    margin-bottom: var(--spacing-6);
  }
  
  &__filters {
    padding: var(--spacing-5);
    margin-bottom: var(--spacing-6);
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    
    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  &__table {
    overflow: hidden;
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
  }
}

.order-status {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-sm);
  
  &--pending {
    color: var(--color-text-inverse);
    background-color: var(--color-warning);
  }
  
  &--processing {
    color: var(--color-text-inverse);
    background-color: var(--color-primary);
  }
  
  &--completed {
    color: var(--color-text-inverse);
    background-color: var(--color-success);
  }
  
  &--cancelled {
    color: var(--color-text-inverse);
    background-color: var(--color-error);
  }
}