/**
 * 设备管理API接口
 * Equipment Management API Interface
 */

import axios from 'axios'
import type {
  Equipment,
  EquipmentQueryParams,
  EquipmentListResponse,
  EquipmentStatusDetail,
  EquipmentStatusResponse,
  EquipmentAlarm,
  AlarmQueryParams,
  AlarmListResponse,
  EquipmentEvent,
  EventQueryParams,
  EventListResponse,
  Recipe,
  RecipeListResponse,
  EquipmentConstant,
  MaintenancePlan,
  MaintenanceTask,
  MaintenanceQueryParams,
  MaintenanceListResponse,
  MaintenanceRecord,
  SecsMessage,
  SecsMessageListResponse,
  EquipmentStatistics,
  MaintenanceStatistics,
  EquipmentPerformance
} from '@/types/equipment'

const API_BASE = '/api/equipment'

// 设备基础管理API
export const equipmentApi = {
  // 获取设备列表
  getEquipmentList: (params: EquipmentQueryParams): Promise<EquipmentListResponse> => {
    return axios.get(`${API_BASE}/list`, { params })
  },

  // 获取设备详情
  getEquipmentDetail: (id: string): Promise<{ code: number; data: Equipment }> => {
    return axios.get(`${API_BASE}/${id}`)
  },

  // 创建设备
  createEquipment: (
    data: Omit<Equipment, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<{ code: number; data: Equipment }> => {
    return axios.post(`${API_BASE}`, data)
  },

  // 更新设备
  updateEquipment: (
    id: string,
    data: Partial<Equipment>
  ): Promise<{ code: number; data: Equipment }> => {
    return axios.put(`${API_BASE}/${id}`, data)
  },

  // 删除设备
  deleteEquipment: (id: string): Promise<{ code: number; message: string }> => {
    return axios.delete(`${API_BASE}/${id}`)
  },

  // 获取设备实时状态
  getEquipmentStatus: (ids?: string[]): Promise<EquipmentStatusResponse> => {
    const params = ids ? { ids: ids.join(',') } : {}
    return axios.get(`${API_BASE}/status`, { params })
  },

  // 获取设备统计数据
  getEquipmentStatistics: (): Promise<{ code: number; data: EquipmentStatistics }> => {
    return axios.get(`${API_BASE}/statistics`)
  },

  // 获取设备性能数据
  getEquipmentPerformance: (
    equipmentId: string,
    startDate: string,
    endDate: string
  ): Promise<{ code: number; data: EquipmentPerformance[] }> => {
    return axios.get(`${API_BASE}/${equipmentId}/performance`, {
      params: { startDate, endDate }
    })
  }
}

// 设备告警管理API
export const alarmApi = {
  // 获取告警列表
  getAlarmList: (params: AlarmQueryParams): Promise<AlarmListResponse> => {
    return axios.get(`${API_BASE}/alarms`, { params })
  },

  // 确认告警
  acknowledgeAlarm: (id: string, operator: string): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/alarms/${id}/acknowledge`, { operator })
  },

  // 清除告警
  clearAlarm: (id: string, operator: string): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/alarms/${id}/clear`, { operator })
  },

  // 批量确认告警
  batchAcknowledgeAlarms: (
    ids: string[],
    operator: string
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/alarms/batch-acknowledge`, { ids, operator })
  },

  // 获取活动告警统计
  getActiveAlarmStats: (): Promise<{
    code: number
    data: { critical: number; major: number; minor: number; warning: number }
  }> => {
    return axios.get(`${API_BASE}/alarms/stats`)
  }
}

// 设备事件日志API
export const eventApi = {
  // 获取事件日志列表
  getEventList: (params: EventQueryParams): Promise<EventListResponse> => {
    return axios.get(`${API_BASE}/events`, { params })
  },

  // 创建事件日志
  createEvent: (
    data: Omit<EquipmentEvent, 'id' | 'timestamp'>
  ): Promise<{ code: number; data: EquipmentEvent }> => {
    return axios.post(`${API_BASE}/events`, data)
  }
}

// Recipe管理API
export const recipeApi = {
  // 获取Recipe列表
  getRecipeList: (equipmentId?: string): Promise<RecipeListResponse> => {
    const params = equipmentId ? { equipmentId } : {}
    return axios.get(`${API_BASE}/recipes`, { params })
  },

  // 获取Recipe详情
  getRecipeDetail: (id: string): Promise<{ code: number; data: Recipe }> => {
    return axios.get(`${API_BASE}/recipes/${id}`)
  },

  // 创建Recipe
  createRecipe: (
    data: Omit<Recipe, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<{ code: number; data: Recipe }> => {
    return axios.post(`${API_BASE}/recipes`, data)
  },

  // 更新Recipe
  updateRecipe: (id: string, data: Partial<Recipe>): Promise<{ code: number; data: Recipe }> => {
    return axios.put(`${API_BASE}/recipes/${id}`, data)
  },

  // 删除Recipe
  deleteRecipe: (id: string): Promise<{ code: number; message: string }> => {
    return axios.delete(`${API_BASE}/recipes/${id}`)
  },

  // 激活Recipe
  activateRecipe: (id: string, equipmentId: string): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/recipes/${id}/activate`, { equipmentId })
  },

  // 下发Recipe到设备
  downloadRecipe: (id: string, equipmentId: string): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/recipes/${id}/download`, { equipmentId })
  }
}

// Equipment Constants管理API
export const constantApi = {
  // 获取设备常量列表
  getConstantList: (equipmentId: string): Promise<{ code: number; data: EquipmentConstant[] }> => {
    return axios.get(`${API_BASE}/${equipmentId}/constants`)
  },

  // 更新设备常量
  updateConstant: (
    equipmentId: string,
    constantId: string,
    value: string | number | boolean
  ): Promise<{ code: number; message: string }> => {
    return axios.put(`${API_BASE}/${equipmentId}/constants/${constantId}`, { value })
  },

  // 批量更新设备常量
  batchUpdateConstants: (
    equipmentId: string,
    constants: { id: string; value: string | number | boolean }[]
  ): Promise<{ code: number; message: string }> => {
    return axios.put(`${API_BASE}/${equipmentId}/constants/batch`, { constants })
  }
}

// 维护管理API
export const maintenanceApi = {
  // 获取维护计划列表
  getMaintenancePlans: (
    equipmentId?: string
  ): Promise<{ code: number; data: MaintenancePlan[] }> => {
    const params = equipmentId ? { equipmentId } : {}
    return axios.get(`${API_BASE}/maintenance/plans`, { params })
  },

  // 获取维护任务列表
  getMaintenanceTasks: (params: MaintenanceQueryParams): Promise<MaintenanceListResponse> => {
    return axios.get(`${API_BASE}/maintenance/tasks`, { params })
  },

  // 创建维护计划
  createMaintenancePlan: (
    data: Omit<MaintenancePlan, 'id' | 'createdAt'>
  ): Promise<{ code: number; data: MaintenancePlan }> => {
    return axios.post(`${API_BASE}/maintenance/plans`, data)
  },

  // 更新维护计划
  updateMaintenancePlan: (
    id: string,
    data: Partial<MaintenancePlan>
  ): Promise<{ code: number; data: MaintenancePlan }> => {
    return axios.put(`${API_BASE}/maintenance/plans/${id}`, data)
  },

  // 删除维护计划
  deleteMaintenancePlan: (id: string): Promise<{ code: number; message: string }> => {
    return axios.delete(`${API_BASE}/maintenance/plans/${id}`)
  },

  // 创建维护任务
  createMaintenanceTask: (
    data: Omit<MaintenanceTask, 'id' | 'createdAt'>
  ): Promise<{ code: number; data: MaintenanceTask }> => {
    return axios.post(`${API_BASE}/maintenance/tasks`, data)
  },

  // 更新维护任务
  updateMaintenanceTask: (
    id: string,
    data: Partial<MaintenanceTask>
  ): Promise<{ code: number; data: MaintenanceTask }> => {
    return axios.put(`${API_BASE}/maintenance/tasks/${id}`, data)
  },

  // 开始维护任务
  startMaintenanceTask: (
    id: string,
    operator: string
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/maintenance/tasks/${id}/start`, { operator })
  },

  // 完成维护任务
  completeMaintenanceTask: (
    id: string,
    data: { operator: string; notes?: string; usedParts: any[] }
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/maintenance/tasks/${id}/complete`, data)
  },

  // 取消维护任务
  cancelMaintenanceTask: (
    id: string,
    reason: string
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/maintenance/tasks/${id}/cancel`, { reason })
  },

  // 获取维护记录
  getMaintenanceRecords: (
    equipmentId?: string,
    startDate?: string,
    endDate?: string
  ): Promise<{ code: number; data: MaintenanceRecord[] }> => {
    const params = { equipmentId, startDate, endDate }
    return axios.get(`${API_BASE}/maintenance/records`, { params })
  },

  // 获取维护统计
  getMaintenanceStatistics: (
    equipmentId?: string
  ): Promise<{ code: number; data: MaintenanceStatistics }> => {
    const params = equipmentId ? { equipmentId } : {}
    return axios.get(`${API_BASE}/maintenance/statistics`, { params })
  }
}

// SECS/GEM通信API
export const secsGemApi = {
  // 获取SECS消息列表
  getSecsMessages: (
    equipmentId: string,
    params?: { startDate?: string; endDate?: string; stream?: number; function?: number }
  ): Promise<SecsMessageListResponse> => {
    return axios.get(`${API_BASE}/${equipmentId}/secs-messages`, { params })
  },

  // 发送SECS消息
  sendSecsMessage: (
    equipmentId: string,
    data: { stream: number; function: number; data?: any }
  ): Promise<{ code: number; message: string; data?: SecsMessage }> => {
    return axios.post(`${API_BASE}/${equipmentId}/secs-messages/send`, data)
  },

  // 获取GEM状态
  getGemState: (
    equipmentId: string
  ): Promise<{ code: number; data: { state: string; timestamp: string } }> => {
    return axios.get(`${API_BASE}/${equipmentId}/gem-state`)
  },

  // 设置GEM状态
  setGemState: (equipmentId: string, state: string): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/${equipmentId}/gem-state`, { state })
  },

  // 获取设备变量
  getStatusVariables: (
    equipmentId: string,
    variableIds?: string[]
  ): Promise<{
    code: number
    data: { id: string; name: string; value: any; unit?: string; timestamp: string }[]
  }> => {
    const params = variableIds ? { variableIds: variableIds.join(',') } : {}
    return axios.get(`${API_BASE}/${equipmentId}/status-variables`, { params })
  },

  // 获取设备事件
  getEquipmentEvents: (
    equipmentId: string,
    eventIds?: string[]
  ): Promise<{ code: number; data: { id: string; name: string; enabled: boolean }[] }> => {
    const params = eventIds ? { eventIds: eventIds.join(',') } : {}
    return axios.get(`${API_BASE}/${equipmentId}/equipment-events`, { params })
  },

  // 启用/禁用设备事件
  setEventEnabled: (
    equipmentId: string,
    eventId: string,
    enabled: boolean
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/${equipmentId}/equipment-events/${eventId}/enabled`, { enabled })
  }
}

// 设备控制API
export const equipmentControlApi = {
  // 远程启动设备
  startEquipment: (
    equipmentId: string,
    operator: string
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/${equipmentId}/control/start`, { operator })
  },

  // 远程停止设备
  stopEquipment: (
    equipmentId: string,
    operator: string
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/${equipmentId}/control/stop`, { operator })
  },

  // 暂停设备
  pauseEquipment: (
    equipmentId: string,
    operator: string
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/${equipmentId}/control/pause`, { operator })
  },

  // 恢复设备
  resumeEquipment: (
    equipmentId: string,
    operator: string
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/${equipmentId}/control/resume`, { operator })
  },

  // 复位设备
  resetEquipment: (
    equipmentId: string,
    operator: string
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/${equipmentId}/control/reset`, { operator })
  },

  // 设置设备为维护模式
  setMaintenanceMode: (
    equipmentId: string,
    enabled: boolean,
    operator: string
  ): Promise<{ code: number; message: string }> => {
    return axios.post(`${API_BASE}/${equipmentId}/control/maintenance-mode`, { enabled, operator })
  }
}
