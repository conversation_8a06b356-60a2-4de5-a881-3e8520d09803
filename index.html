<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="IC封装测试CIM系统 - 专业的半导体制造执行系统" />
    <meta name="keywords" content="IC封装,半导体测试,CIM系统,制造执行,极简设计" />
    <meta name="author" content="IC CIM Frontend Team" />
    
    <!-- 移除SCSS预加载，由Vite自动处理 -->
    
    <!-- 浅色主题默认样式 -->
    <style>
      /* 防止初始化闪烁的基础样式 */
      html {
        --color-bg-primary: #ffffff;
        --color-text-primary: #111827;
        background-color: var(--color-bg-primary);
        color: var(--color-text-primary);
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
      }
      
      #app {
        min-height: 100vh;
      }
      
      /* 加载动画 */
      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--color-bg-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #2563eb;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 深色主题检测 */
      @media (prefers-color-scheme: dark) {
        html {
          --color-bg-primary: #0f172a;
          --color-text-primary: #f8fafc;
        }
        
        .loading-spinner {
          border-color: #475569;
          border-top-color: #3b82f6;
        }
      }
    </style>
    
    <title>IC封测CIM系统</title>
  </head>
  <body>
    <div id="app">
      <!-- Vue应用将在这里渲染 -->
    </div>
    
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>