-- ========================================
-- 测试程序管理模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- 测试程序主表
CREATE TABLE test_programs (
    program_id VARCHAR(32) PRIMARY KEY COMMENT '测试程序ID',
    program_code VARCHAR(50) NOT NULL UNIQUE COMMENT '程序编码',
    program_name VARCHAR(200) NOT NULL COMMENT '程序名称',
    program_version VARCHAR(20) NOT NULL COMMENT '程序版本',
    
    -- 产品信息
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL COMMENT '产品编码',
    device_name VARCHAR(200) NOT NULL COMMENT '器件名称',
    package_type VARCHAR(50) NOT NULL COMMENT '封装类型',
    
    -- 测试类型
    test_type VARCHAR(30) NOT NULL COMMENT '测试类型(CP/FT/BI/HTOL/TC)',
    test_category VARCHAR(50) COMMENT '测试分类',
    test_phase VARCHAR(30) COMMENT '测试阶段(ENGINEERING/PRODUCTION/QUALIFICATION)',
    
    -- ATE信息
    ate_platform VARCHAR(50) NOT NULL COMMENT 'ATE平台',
    ate_model VARCHAR(100) NOT NULL COMMENT 'ATE型号',
    tester_configuration JSON COMMENT '测试机配置',
    
    -- 测试条件
    test_temperature DECIMAL(6,2) COMMENT '测试温度(℃)',
    supply_voltage DECIMAL(8,4) COMMENT '供电电压(V)',
    test_frequency DECIMAL(12,3) COMMENT '测试频率(Hz)',
    
    -- 程序信息
    program_description TEXT COMMENT '程序描述',
    test_specification TEXT COMMENT '测试规格',
    program_file_path VARCHAR(500) COMMENT '程序文件路径',
    program_checksum VARCHAR(64) COMMENT '程序校验和',
    
    -- 开发信息
    development_phase VARCHAR(30) COMMENT '开发阶段',
    npi_project_id VARCHAR(32) COMMENT 'NPI项目ID',
    customer_id VARCHAR(32) COMMENT '客户ID',
    
    -- 状态管理
    program_status VARCHAR(20) NOT NULL DEFAULT 'DEVELOPMENT' COMMENT '程序状态',
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    qualification_status VARCHAR(20) COMMENT '认证状态',
    
    -- 性能指标
    test_time_target DECIMAL(8,2) COMMENT '目标测试时间(秒)',
    test_time_actual DECIMAL(8,2) COMMENT '实际测试时间(秒)',
    throughput_target INT COMMENT '目标产能(UPH)',
    throughput_actual INT COMMENT '实际产能(UPH)',
    
    -- 生效信息
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 审批信息
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    approval_comments TEXT COMMENT '审批意见',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version_no INT DEFAULT 1 COMMENT '版本号',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_program_code (program_code),
    INDEX idx_program_product (product_id),
    INDEX idx_program_type (test_type),
    INDEX idx_program_ate (ate_platform, ate_model),
    INDEX idx_program_status (program_status),
    INDEX idx_program_npi (npi_project_id),
    INDEX idx_program_customer (customer_id),
    INDEX idx_program_version (program_code, program_version),
    INDEX idx_program_effective (effective_date, expiry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试程序主表';

-- 测试项目表
CREATE TABLE test_items (
    item_id VARCHAR(32) PRIMARY KEY COMMENT '测试项目ID',
    program_id VARCHAR(32) NOT NULL COMMENT '测试程序ID',
    item_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    item_name VARCHAR(200) NOT NULL COMMENT '项目名称',
    item_sequence INT NOT NULL COMMENT '测试序号',
    
    -- 测试类型
    item_type VARCHAR(50) NOT NULL COMMENT '项目类型',
    item_category VARCHAR(50) COMMENT '项目分类',
    measurement_type VARCHAR(30) COMMENT '测量类型',
    
    -- 测试条件
    test_condition JSON COMMENT '测试条件',
    supply_pins JSON COMMENT '供电引脚',
    input_pins JSON COMMENT '输入引脚',
    output_pins JSON COMMENT '输出引脚',
    
    -- 规格限制
    lower_limit DECIMAL(15,6) COMMENT '下限值',
    upper_limit DECIMAL(15,6) COMMENT '上限值',
    nominal_value DECIMAL(15,6) COMMENT '标称值',
    unit VARCHAR(20) COMMENT '单位',
    
    -- 测试参数
    test_parameters JSON COMMENT '测试参数配置',
    measurement_setup JSON COMMENT '测量设置',
    
    -- 质量要求
    is_critical TINYINT(1) DEFAULT 0 COMMENT '是否关键项目',
    statistical_requirement VARCHAR(50) COMMENT '统计要求',
    cpk_requirement DECIMAL(4,2) COMMENT 'Cpk要求',
    
    -- 执行控制
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    execution_group VARCHAR(50) COMMENT '执行组',
    parallel_execution TINYINT(1) DEFAULT 0 COMMENT '是否可并行',
    
    -- 失效处理
    failure_action VARCHAR(30) DEFAULT 'STOP' COMMENT '失效动作',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    
    -- 数据记录
    data_logging TINYINT(1) DEFAULT 1 COMMENT '是否记录数据',
    histogram_bins INT COMMENT '直方图分组数',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (program_id) REFERENCES test_programs(program_id) ON DELETE CASCADE,
    INDEX idx_item_program (program_id),
    INDEX idx_item_sequence (program_id, item_sequence),
    INDEX idx_item_code (item_code),
    INDEX idx_item_type (item_type),
    INDEX idx_item_category (item_category),
    INDEX idx_item_critical (is_critical),
    INDEX idx_item_group (execution_group),
    UNIQUE KEY uk_program_item_seq (program_id, item_sequence),
    UNIQUE KEY uk_program_item_code (program_id, item_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试项目表';

-- 测试数据模板表
CREATE TABLE test_data_templates (
    template_id VARCHAR(32) PRIMARY KEY COMMENT '模板ID',
    template_code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板编码',
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(30) NOT NULL COMMENT '模板类型',
    
    -- 产品关联
    product_family VARCHAR(100) COMMENT '产品系列',
    package_types JSON COMMENT '适用封装类型',
    
    -- 数据结构定义
    data_structure JSON NOT NULL COMMENT '数据结构定义',
    field_mappings JSON COMMENT '字段映射',
    
    -- 模板描述
    template_description TEXT COMMENT '模板描述',
    usage_notes TEXT COMMENT '使用说明',
    
    -- 版本信息
    template_version VARCHAR(20) NOT NULL COMMENT '模板版本',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认模板',
    
    -- 状态管理
    template_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '模板状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_template_code (template_code),
    INDEX idx_template_type (template_type),
    INDEX idx_template_family (product_family),
    INDEX idx_template_status (template_status),
    INDEX idx_template_default (is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试数据模板表';

-- 测试程序版本表
CREATE TABLE test_program_versions (
    version_id VARCHAR(32) PRIMARY KEY COMMENT '版本ID',
    program_id VARCHAR(32) NOT NULL COMMENT '测试程序ID',
    version_no VARCHAR(20) NOT NULL COMMENT '版本号',
    version_type VARCHAR(20) NOT NULL COMMENT '版本类型',
    
    -- 版本信息
    version_description TEXT COMMENT '版本说明',
    change_summary TEXT COMMENT '变更摘要',
    release_notes TEXT COMMENT '发布说明',
    
    -- 变更详情
    changes_made JSON COMMENT '具体变更内容',
    test_items_changed JSON COMMENT '变更的测试项目',
    
    -- 版本状态
    version_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '版本状态',
    is_current_version TINYINT(1) DEFAULT 0 COMMENT '是否当前版本',
    
    -- 程序文件
    program_files JSON COMMENT '程序文件列表',
    documentation_files JSON COMMENT '文档文件列表',
    
    -- 测试验证
    validation_status VARCHAR(20) COMMENT '验证状态',
    validation_results JSON COMMENT '验证结果',
    performance_benchmarks JSON COMMENT '性能基准',
    
    -- 时间信息
    created_date DATE NOT NULL COMMENT '创建日期',
    released_date DATE COMMENT '发布日期',
    effective_date DATE COMMENT '生效日期',
    retirement_date DATE COMMENT '退役日期',
    
    -- 审批信息
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    
    -- 发布信息
    released_by VARCHAR(32) COMMENT '发布人',
    release_type VARCHAR(20) COMMENT '发布类型',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (program_id) REFERENCES test_programs(program_id) ON DELETE CASCADE,
    INDEX idx_prog_ver_program (program_id),
    INDEX idx_prog_ver_no (version_no),
    INDEX idx_prog_ver_status (version_status),
    INDEX idx_prog_ver_current (is_current_version),
    INDEX idx_prog_ver_date (created_date),
    UNIQUE KEY uk_program_version (program_id, version_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试程序版本表';

-- 测试设备配置表
CREATE TABLE test_equipment_configs (
    config_id VARCHAR(32) PRIMARY KEY COMMENT '配置ID',
    program_id VARCHAR(32) NOT NULL COMMENT '测试程序ID',
    equipment_id VARCHAR(32) NOT NULL COMMENT '设备ID',
    config_name VARCHAR(200) NOT NULL COMMENT '配置名称',
    config_version VARCHAR(20) NOT NULL COMMENT '配置版本',
    
    -- 硬件配置
    hardware_config JSON COMMENT '硬件配置',
    interface_board VARCHAR(100) COMMENT '接口板',
    load_board VARCHAR(100) COMMENT '载具板',
    socket_type VARCHAR(50) COMMENT '测试座类型',
    
    -- 软件配置
    software_version VARCHAR(50) COMMENT '软件版本',
    driver_version VARCHAR(50) COMMENT '驱动版本',
    calibration_data JSON COMMENT '校准数据',
    
    -- 测试资源
    pin_resources JSON COMMENT '引脚资源分配',
    power_resources JSON COMMENT '电源资源分配',
    timing_resources JSON COMMENT '时序资源分配',
    
    -- 性能参数
    accuracy_specs JSON COMMENT '精度规格',
    measurement_ranges JSON COMMENT '测量范围',
    frequency_ranges JSON COMMENT '频率范围',
    
    -- 校准信息
    last_calibration_date DATE COMMENT '上次校准日期',
    calibration_due_date DATE COMMENT '校准到期日期',
    calibration_status VARCHAR(20) COMMENT '校准状态',
    
    -- 配置状态
    config_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '配置状态',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认配置',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (program_id) REFERENCES test_programs(program_id) ON DELETE CASCADE,
    INDEX idx_config_program (program_id),
    INDEX idx_config_equipment (equipment_id),
    INDEX idx_config_status (config_status),
    INDEX idx_config_calibration (calibration_due_date),
    INDEX idx_config_default (is_default),
    UNIQUE KEY uk_program_equipment_config (program_id, equipment_id, config_version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试设备配置表';

-- 测试程序执行历史表
CREATE TABLE test_program_executions (
    execution_id VARCHAR(32) PRIMARY KEY COMMENT '执行ID',
    program_id VARCHAR(32) NOT NULL COMMENT '测试程序ID',
    execution_code VARCHAR(50) NOT NULL UNIQUE COMMENT '执行编码',
    
    -- 执行信息
    lot_id VARCHAR(32) COMMENT '批次ID',
    wafer_id VARCHAR(32) COMMENT '晶圆ID',
    device_serial VARCHAR(100) COMMENT '器件序号',
    
    -- 设备信息
    equipment_id VARCHAR(32) NOT NULL COMMENT '测试设备ID',
    operator_id VARCHAR(32) COMMENT '操作员ID',
    shift VARCHAR(20) COMMENT '班次',
    
    -- 执行时间
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    execution_duration DECIMAL(8,2) COMMENT '执行时长(秒)',
    
    -- 执行结果
    execution_status VARCHAR(20) NOT NULL COMMENT '执行状态',
    overall_result VARCHAR(20) COMMENT '总体结果(PASS/FAIL)',
    test_count INT COMMENT '测试项目数',
    pass_count INT COMMENT '通过项目数',
    fail_count INT COMMENT '失败项目数',
    
    -- 环境条件
    temperature DECIMAL(6,2) COMMENT '测试温度(℃)',
    humidity DECIMAL(5,2) COMMENT '环境湿度(%)',
    
    -- 数据文件
    data_file_path VARCHAR(500) COMMENT '测试数据文件路径',
    log_file_path VARCHAR(500) COMMENT '日志文件路径',
    
    -- 质量标识
    quality_flag VARCHAR(20) DEFAULT 'GOOD' COMMENT '质量标识',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (program_id) REFERENCES test_programs(program_id) ON DELETE CASCADE,
    INDEX idx_exec_program (program_id),
    INDEX idx_exec_lot (lot_id),
    INDEX idx_exec_equipment (equipment_id),
    INDEX idx_exec_operator (operator_id),
    INDEX idx_exec_time (start_time),
    INDEX idx_exec_result (overall_result),
    INDEX idx_exec_status (execution_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试程序执行历史表';

-- 测试结果汇总表
CREATE TABLE test_result_summaries (
    summary_id VARCHAR(32) PRIMARY KEY COMMENT '汇总ID',
    execution_id VARCHAR(32) NOT NULL COMMENT '执行ID',
    program_id VARCHAR(32) NOT NULL COMMENT '测试程序ID',
    
    -- 汇总信息
    summary_date DATE NOT NULL COMMENT '汇总日期',
    summary_type VARCHAR(30) NOT NULL COMMENT '汇总类型',
    
    -- 统计数据
    total_devices_tested BIGINT COMMENT '总测试器件数',
    pass_devices BIGINT COMMENT '通过器件数',
    fail_devices BIGINT COMMENT '失败器件数',
    yield_rate DECIMAL(6,4) COMMENT '良率',
    
    -- 测试项目统计
    test_items_summary JSON COMMENT '测试项目汇总',
    failure_analysis JSON COMMENT '失效分析',
    
    -- 性能统计
    average_test_time DECIMAL(8,2) COMMENT '平均测试时间(秒)',
    throughput_uph INT COMMENT '产能(UPH)',
    equipment_utilization DECIMAL(5,2) COMMENT '设备利用率',
    
    -- 质量指标
    cpk_values JSON COMMENT 'Cpk值统计',
    sigma_levels JSON COMMENT 'Sigma水平统计',
    
    -- 异常统计
    outlier_count INT COMMENT '异常值数量',
    retest_count INT COMMENT '重测数量',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (execution_id) REFERENCES test_program_executions(execution_id) ON DELETE CASCADE,
    FOREIGN KEY (program_id) REFERENCES test_programs(program_id) ON DELETE CASCADE,
    INDEX idx_summary_execution (execution_id),
    INDEX idx_summary_program (program_id),
    INDEX idx_summary_date (summary_date),
    INDEX idx_summary_type (summary_type),
    INDEX idx_summary_yield (yield_rate)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试结果汇总表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. test_programs: 测试程序主表，定义测试程序基本信息
2. test_items: 测试项目表，程序中的具体测试项目
3. test_data_templates: 测试数据模板表，标准化数据结构
4. test_program_versions: 程序版本表，版本控制管理
5. test_equipment_configs: 测试设备配置表，设备适配配置
6. test_program_executions: 程序执行历史表，执行记录跟踪
7. test_result_summaries: 测试结果汇总表，统计分析数据

核心特性:
- 完整的测试程序生命周期管理
- 详细的测试项目和参数定义
- 严格的版本控制和审批流程
- 灵活的设备配置管理
- 全面的执行跟踪和结果分析
- 支持多种测试类型(CP/FT/BI等)
*/