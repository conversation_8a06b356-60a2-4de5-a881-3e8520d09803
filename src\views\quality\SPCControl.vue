<template>
  <div class="spc-control">
    <div class="spc-control__header">
      <div class="header-content">
        <h1>统计过程控制 (SPC)</h1>
        <p>实时监控生产过程质量，确保过程稳定性和产品一致性</p>
      </div>
      <div class="header-actions">
        <el-button
type="primary" @click="showAddDialog = true"
>
          <el-icon><Plus /></el-icon>
          新增监控项
        </el-button>
        <el-button @click="showSettingsDialog = true">
          <el-icon><Setting /></el-icon>
          系统配置
        </el-button>
      </div>
    </div>

    <div class="spc-control__filters">
      <div class="filter-group">
        <label>工序筛选:</label>
        <el-select v-model="selectedProcess" @change="filterData" clearable placeholder="选择工序">
          <el-option
            v-for="process in processes"
            :key="process"
            :label="process"
            :value="process"
          />
        </el-select>
      </div>
      <div class="filter-group">
        <label>参数筛选:</label>
        <el-select
          v-model="selectedParameter"
          clearable
          placeholder="选择参数"
          @change="filterData"
        >
          <el-option
v-for="param in parameters" :key="param"
:label="param" :value="param"
/>
        </el-select>
      </div>
      <div class="filter-group">
        <label>状态筛选:</label>
        <el-select v-model="selectedStatus" @change="filterData" clearable placeholder="选择状态">
          <el-option label="全部" value="" />
          <el-option label="正常" value="NORMAL" />
          <el-option label="警告" value="WARNING" />
          <el-option label="失控" value="OUT_OF_CONTROL" />
        </el-select>
      </div>
      <div class="filter-group">
        <label>时间范围:</label>
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          size="default"
          @change="filterData"
        />
      </div>
    </div>

    <div class="spc-control__overview">
      <div class="overview-card">
        <div class="card-icon normal">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-value">
            {{ normalCount }}
          </div>
          <div class="card-label">正常过程</div>
        </div>
      </div>
      <div class="overview-card">
        <div class="card-icon warning">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-value">
            {{ warningCount }}
          </div>
          <div class="card-label">警告过程</div>
        </div>
      </div>
      <div class="overview-card">
        <div class="card-icon danger">
          <el-icon><CircleClose /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-value">
            {{ outOfControlCount }}
          </div>
          <div class="card-label">失控过程</div>
        </div>
      </div>
      <div class="overview-card">
        <div class="card-icon primary">
          <el-icon><DataAnalysis /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-value">
            {{ averageCpk.toFixed(2) }}
          </div>
          <div class="card-label">平均Cpk</div>
        </div>
      </div>
    </div>

    <div class="spc-control__charts">
      <div
v-for="spcData in filteredSPCData" :key="spcData.id"
class="chart-wrapper"
>
        <SPCChart
          :spc-data="spcData"
          :show-data-table="showDataTables"
          :auto-refresh="autoRefresh"
          :refresh-interval="refreshInterval"
          @refresh="handleRefresh(spcData.id)"
          @export="handleExport(spcData.id, $event)"
          @violation-detected="handleViolationDetected(spcData.id, $event)"
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredSPCData.length === 0" class="spc-control__empty">
      <el-empty description="暂无SPC监控数据">
        <el-button type="primary"
@click="showAddDialog = true"
>
添加监控项
</el-button>
      </el-empty>
    </div>

    <!-- 新增监控项对话框 -->
    <el-dialog
v-model="showAddDialog" title="新增SPC监控项"
width="600px"
>
      <el-form
ref="addFormRef" :model="addForm"
:rules="addFormRules" label-width="120px"
>
        <el-form-item label="工序名称" prop="processName">
          <el-input v-model="addForm.processName" placeholder="请输入工序名称" />
        </el-form-item>
        <el-form-item label="监控参数" prop="parameter">
          <el-input v-model="addForm.parameter" placeholder="请输入监控参数" />
        </el-form-item>
        <el-form-item label="控制图类型" prop="chartType">
          <el-select v-model="addForm.chartType" placeholder="选择控制图类型">
            <el-option label="X̄-R图" value="xbar-r" />
            <el-option label="X̄-S图" value="xbar-s" />
            <el-option label="I-MR图" value="i-mr" />
            <el-option label="p图" value="p" />
            <el-option label="np图" value="np" />
          </el-select>
        </el-form-item>
        <el-form-item label="子组大小" prop="subgroupSize">
          <el-input-number
            v-model="addForm.subgroupSize"
            :min="2"
            :max="25"
            placeholder="子组样本数量"
          />
        </el-form-item>
        <el-form-item label="规格上限" prop="usl">
          <el-input-number
v-model="addForm.usl" :precision="4"
placeholder="规格上限"
/>
        </el-form-item>
        <el-form-item label="规格下限" prop="lsl">
          <el-input-number
v-model="addForm.lsl" :precision="4"
placeholder="规格下限"
/>
        </el-form-item>
        <el-form-item label="目标值" prop="target">
          <el-input-number
v-model="addForm.target" :precision="4"
placeholder="目标值"
/>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary"
@click="handleAddSPCItem" :loading="adding"
>
确认添加
</el-button>
      </template>
    </el-dialog>

    <!-- 系统配置对话框 -->
    <el-dialog
v-model="showSettingsDialog" title="SPC系统配置"
width="700px"
>
      <el-tabs>
        <el-tab-pane label="刷新设置">
          <el-form label-width="140px">
            <el-form-item label="自动刷新">
              <el-switch v-model="autoRefresh" />
            </el-form-item>
            <el-form-item
v-if="autoRefresh" label="刷新间隔(秒)"
>
              <el-input-number
v-model="refreshInterval" :min="5"
:max="300" :step="5"
/>
            </el-form-item>
            <el-form-item label="显示数据表">
              <el-switch v-model="showDataTables" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="违规规则">
          <div class="violation-rules">
            <div
v-for="rule in violationRules" :key="rule.id"
class="rule-item"
>
              <el-checkbox v-model="rule.isActive">
                {{ rule.name }}
              </el-checkbox>
              <el-tag :type="getSeverityTagType(rule.severity)" size="small">
                {{ rule.severity }}
              </el-tag>
              <span class="rule-description">{{ rule.description }}</span>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="告警设置">
          <el-form label-width="140px">
            <el-form-item label="邮件告警">
              <el-switch v-model="emailAlert" />
            </el-form-item>
            <el-form-item label="微信告警">
              <el-switch v-model="wechatAlert" />
            </el-form-item>
            <el-form-item label="告警阈值">
              <el-select v-model="alertThreshold">
                <el-option label="仅失控" value="OUT_OF_CONTROL" />
                <el-option label="警告及以上" value="WARNING" />
                <el-option label="所有异常" value="ALL" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <el-button @click="showSettingsDialog = false">取消</el-button>
        <el-button type="primary"
@click="saveSettings"
>
保存配置
</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    Plus,
    Setting,
    CircleCheck,
    Warning,
    CircleClose,
    DataAnalysis,
    Refresh,
    Download
  } from '@element-plus/icons-vue'
  import SPCChart from '@/components/quality/SPCChart.vue'
  import type { SPCData, ViolationRule } from '@/types/quality'
  import { useSPCData } from '@/composables/useSPCData'

  // 组合式函数
  const { spcDataList, loading, refreshSPCData, addSPCItem, updateSPCItem, deleteSPCItem } =
    useSPCData()

  // 响应式数据
  const selectedProcess = ref<string>('')
  const selectedParameter = ref<string>('')
  const selectedStatus = ref<string>('')
  const timeRange = ref<[Date, Date] | null>(null)

  const showAddDialog = ref(false)
  const showSettingsDialog = ref(false)
  const adding = ref(false)

  const autoRefresh = ref(true)
  const refreshInterval = ref(30000) // 30秒
  const showDataTables = ref(false)
  const emailAlert = ref(true)
  const wechatAlert = ref(false)
  const alertThreshold = ref<string>('WARNING')

  // 表单数据
  const addFormRef = ref()
  const addForm = ref({
    processName: '',
    parameter: '',
    chartType: 'xbar-r',
    subgroupSize: 5,
    usl: 0,
    lsl: 0,
    target: 0
  })

  const addFormRules = {
    processName: [{ required: true, message: '请输入工序名称', trigger: 'blur' }],
    parameter: [{ required: true, message: '请输入监控参数', trigger: 'blur' }],
    chartType: [{ required: true, message: '请选择控制图类型', trigger: 'change' }],
    subgroupSize: [{ required: true, message: '请输入子组大小', trigger: 'blur' }],
    usl: [{ required: true, message: '请输入规格上限', trigger: 'blur' }],
    lsl: [{ required: true, message: '请输入规格下限', trigger: 'blur' }],
    target: [{ required: true, message: '请输入目标值', trigger: 'blur' }]
  }

  // 违规规则配置
  const violationRules = ref<ViolationRule[]>([
    {
      id: 'nelson1',
      name: 'Nelson规则1',
      description: '单点超出3σ控制限',
      type: 'NELSON',
      severity: 'HIGH',
      isActive: true
    },
    {
      id: 'nelson2',
      name: 'Nelson规则2',
      description: '连续9点在中心线同侧',
      type: 'NELSON',
      severity: 'MEDIUM',
      isActive: true
    },
    {
      id: 'nelson3',
      name: 'Nelson规则3',
      description: '连续6点递增或递减',
      type: 'NELSON',
      severity: 'MEDIUM',
      isActive: true
    },
    {
      id: 'nelson4',
      name: 'Nelson规则4',
      description: '连续14点交替上下',
      type: 'NELSON',
      severity: 'LOW',
      isActive: false
    },
    {
      id: 'nelson5',
      name: 'Nelson规则5',
      description: '3点中有2点在2σ范围外',
      type: 'NELSON',
      severity: 'MEDIUM',
      isActive: true
    },
    {
      id: 'nelson6',
      name: 'Nelson规则6',
      description: '5点中有4点在1σ范围外',
      type: 'NELSON',
      severity: 'MEDIUM',
      isActive: true
    },
    {
      id: 'nelson7',
      name: 'Nelson规则7',
      description: '连续15点在1σ范围内',
      type: 'NELSON',
      severity: 'LOW',
      isActive: false
    },
    {
      id: 'nelson8',
      name: 'Nelson规则8',
      description: '连续8点在1σ范围外',
      type: 'NELSON',
      severity: 'HIGH',
      isActive: true
    }
  ])

  // 计算属性
  const filteredSPCData = computed(() => {
    let filtered = spcDataList.value

    if (selectedProcess.value) {
      filtered = filtered.filter(item => item.processName === selectedProcess.value)
    }

    if (selectedParameter.value) {
      filtered = filtered.filter(item => item.parameter === selectedParameter.value)
    }

    if (selectedStatus.value) {
      filtered = filtered.filter(item => {
        const hasStatus = item.sampleData.some(point => {
          if (selectedStatus.value === 'NORMAL') return point.result === 'NORMAL'
          if (selectedStatus.value === 'WARNING') return point.result === 'WARNING'
          if (selectedStatus.value === 'OUT_OF_CONTROL') return point.result === 'OUT_OF_CONTROL'
          return false
        })
        return hasStatus
      })
    }

    if (timeRange.value && timeRange.value[0] && timeRange.value[1]) {
      const [start, end] = timeRange.value
      filtered = filtered.filter(item => {
        const lastUpdate = new Date(item.lastUpdate)
        return lastUpdate >= start && lastUpdate <= end
      })
    }

    return filtered
  })

  const processes = computed(() => {
    return Array.from(new Set(spcDataList.value.map(item => item.processName)))
  })

  const parameters = computed(() => {
    return Array.from(new Set(spcDataList.value.map(item => item.parameter)))
  })

  const normalCount = computed(() => {
    return spcDataList.value.filter(item =>
      item.sampleData.every(point => point.result === 'NORMAL')
    ).length
  })

  const warningCount = computed(() => {
    return spcDataList.value.filter(
      item =>
        item.sampleData.some(point => point.result === 'WARNING') &&
        !item.sampleData.some(point => point.result === 'OUT_OF_CONTROL')
    ).length
  })

  const outOfControlCount = computed(() => {
    return spcDataList.value.filter(item =>
      item.sampleData.some(point => point.result === 'OUT_OF_CONTROL')
    ).length
  })

  const averageCpk = computed(() => {
    if (spcDataList.value.length === 0) return 0
    const totalCpk = spcDataList.value.reduce((sum, item) => sum + item.statistics.cpk, 0)
    return totalCpk / spcDataList.value.length
  })

  // 方法
  const filterData = () => {
    // 筛选逻辑已通过计算属性实现
  }

  const handleRefresh = async (id: string) => {
    try {
      await refreshSPCData(id)
      ElMessage.success('数据刷新成功')
    } catch (error) {
      ElMessage.error('数据刷新失败')
    }
  }

  const handleExport = (id: string, chartType: string) => {
    ElMessage.info(`导出SPC图: ${id}, 类型: ${chartType}`)
  }

  const handleViolationDetected = (id: string, violations: string[]) => {
    const spcItem = spcDataList.value.find(item => item.id === id)
    if (!spcItem) return

    ElMessageBox.alert(
      `检测到SPC违规:\n${violations.join('\n')}`,
      `工序: ${spcItem.processName} - ${spcItem.parameter}`,
      {
        confirmButtonText: '确定',
        type: 'warning'
      }
    )

    // 发送告警通知（根据配置）
    if (emailAlert.value || wechatAlert.value) {
      // 这里实现告警通知逻辑
      console.log('发送告警通知:', {
        id,
        violations,
        emailAlert: emailAlert.value,
        wechatAlert: wechatAlert.value
      })
    }
  }

  const handleAddSPCItem = async () => {
    if (!addFormRef.value) return

    const valid = await addFormRef.value.validate()
    if (!valid) return

    adding.value = true
    try {
      await addSPCItem(addForm.value)
      showAddDialog.value = false
      ElMessage.success('SPC监控项添加成功')

      // 重置表单
      addForm.value = {
        processName: '',
        parameter: '',
        chartType: 'xbar-r',
        subgroupSize: 5,
        usl: 0,
        lsl: 0,
        target: 0
      }
    } catch (error) {
      ElMessage.error('添加失败，请重试')
    } finally {
      adding.value = false
    }
  }

  const saveSettings = () => {
    // 保存系统配置
    const config = {
      autoRefresh: autoRefresh.value,
      refreshInterval: refreshInterval.value,
      showDataTables: showDataTables.value,
      violationRules: violationRules.value,
      emailAlert: emailAlert.value,
      wechatAlert: wechatAlert.value,
      alertThreshold: alertThreshold.value
    }

    localStorage.setItem('spc-config', JSON.stringify(config))
    showSettingsDialog.value = false
    ElMessage.success('配置保存成功')
  }

  const loadSettings = () => {
    const savedConfig = localStorage.getItem('spc-config')
    if (savedConfig) {
      const config = JSON.parse(savedConfig)
      autoRefresh.value = config.autoRefresh ?? true
      refreshInterval.value = config.refreshInterval ?? 30000
      showDataTables.value = config.showDataTables ?? false
      emailAlert.value = config.emailAlert ?? true
      wechatAlert.value = config.wechatAlert ?? false
      alertThreshold.value = config.alertThreshold ?? 'WARNING'

      if (config.violationRules) {
        violationRules.value = config.violationRules
      }
    }
  }

  const getSeverityTagType = (severity: string): string => {
    switch (severity) {
      case 'HIGH':
        return 'danger'
      case 'MEDIUM':
        return 'warning'
      case 'LOW':
        return 'info'
      default:
        return 'info'
    }
  }

  // 生命周期
  onMounted(() => {
    loadSettings()
    refreshSPCData()
  })
</script>

<style lang="scss" scoped>
  .spc-control {
    padding: var(--spacing-6);

    &__header {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      padding-bottom: var(--spacing-4);
      margin-bottom: var(--spacing-6);
      border-bottom: 2px solid var(--color-border-light);

      .header-content {
        h1 {
          margin: 0 0 var(--spacing-2) 0;
          font-size: 28px;
          font-weight: 700;
          color: var(--color-text-primary);
        }

        p {
          margin: 0;
          font-size: 16px;
          color: var(--color-text-secondary);
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    &__filters {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-4);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-6);
      background: var(--color-bg-secondary);
      border-radius: var(--radius-base);

      .filter-group {
        display: flex;
        gap: var(--spacing-2);
        align-items: center;
        min-width: 200px;

        label {
          font-weight: 500;
          color: var(--color-text-primary);
          white-space: nowrap;
        }
      }
    }

    &__overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-6);

      .overview-card {
        display: flex;
        align-items: center;
        padding: var(--spacing-5);
        background: var(--color-bg-primary);
        border-radius: var(--radius-base);
        box-shadow: var(--shadow-sm);

        .card-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          margin-right: var(--spacing-4);
          font-size: 24px;
          border-radius: var(--radius-round);

          &.normal {
            color: var(--color-success);
            background: var(--color-success-light);
          }

          &.warning {
            color: var(--color-warning);
            background: var(--color-warning-light);
          }

          &.danger {
            color: var(--color-danger);
            background: var(--color-danger-light);
          }

          &.primary {
            color: var(--color-primary);
            background: var(--color-primary-light);
          }
        }

        .card-content {
          .card-value {
            margin-bottom: var(--spacing-1);
            font-size: 32px;
            font-weight: 700;
            line-height: 1;
            color: var(--color-text-primary);
          }

          .card-label {
            font-size: 14px;
            color: var(--color-text-secondary);
          }
        }
      }
    }

    &__charts {
      .chart-wrapper {
        margin-bottom: var(--spacing-6);
      }
    }

    &__empty {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }
  }

  .violation-rules {
    .rule-item {
      display: flex;
      gap: var(--spacing-3);
      align-items: center;
      padding: var(--spacing-3) 0;
      border-bottom: 1px solid var(--color-border-light);

      &:last-child {
        border-bottom: none;
      }

      .rule-description {
        flex: 1;
        font-size: 14px;
        color: var(--color-text-secondary);
      }
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .spc-control {
      padding: var(--spacing-4);

      &__header {
        flex-direction: column;
        gap: var(--spacing-4);
        align-items: stretch;

        .header-actions {
          justify-content: flex-start;
        }
      }

      &__filters {
        flex-direction: column;

        .filter-group {
          min-width: auto;
        }
      }

      &__overview {
        grid-template-columns: 1fr;
      }
    }
  }
</style>
