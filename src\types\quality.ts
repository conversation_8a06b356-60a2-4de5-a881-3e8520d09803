// 质量管理模块核心数据类型定义
export interface QualityData {
  id: string
  timestamp: Date
  value: number
  specification: QualitySpecification
  result: 'PASS' | 'FAIL' | 'WARNING'
  location: string
  operator: string
}

export interface QualitySpecification {
  usl: number // 上规格限
  lsl: number // 下规格限
  target: number // 目标值
  ucl: number // 上控制限
  lcl: number // 下控制限
  unit: string
  parameter: string
}

// SPC统计过程控制相关类型
export interface SPCData {
  id: string
  processName: string
  parameter: string
  sampleData: SPCPoint[]
  controlLimits: ControlLimits
  statistics: SPCStatistics
  violationRules: ViolationRule[]
  lastUpdate: Date
}

export interface SPCPoint {
  id: string
  timestamp: Date
  sampleNumber: number
  values: number[] // 子组数据
  mean: number // 均值
  range: number // 极差
  standardDeviation: number // 标准偏差
  result: 'NORMAL' | 'WARNING' | 'OUT_OF_CONTROL'
  violatedRules: string[]
}

export interface ControlLimits {
  xbar: {
    ucl: number // X̄图上控制限
    cl: number // X̄图中心线
    lcl: number // X̄图下控制限
  }
  r: {
    ucl: number // R图上控制限
    cl: number // R图中心线
    lcl: number // R图下控制限
  }
  sigma: {
    ucl: number // σ图上控制限
    cl: number // σ图中心线
    lcl: number // σ图下控制限
  }
}

export interface SPCStatistics {
  cpk: number // 过程能力指数
  ppk: number // 过程性能指数
  cp: number // 过程能力
  pp: number // 过程性能
  ca: number // 过程准确度指数
  mean: number // 过程均值
  standardDeviation: number // 过程标准偏差
  yield: number // 良率
}

export interface ViolationRule {
  id: string
  name: string
  description: string
  type: 'NELSON' | 'WESTGARD' | 'CUSTOM'
  severity: 'LOW' | 'MEDIUM' | 'HIGH'
  isActive: boolean
}

// 质量追溯相关类型
export interface QualityTraceability {
  traceId: string
  lotNumber: string
  waferId?: string
  packageId?: string
  serialNumber?: string
  traceabilityChain: TraceabilityNode[]
  qualityHistory: QualityEvent[]
  currentStatus: QualityStatus
  customerInfo: CustomerInfo
}

export interface TraceabilityNode {
  id: string
  processStep: string
  timestamp: Date
  location: string
  operator: string
  equipment: string
  parameters: Record<string, any>
  qualityData: QualityData[]
  nextNodes: string[]
  previousNodes: string[]
}

export interface QualityEvent {
  id: string
  eventType: 'INSPECTION' | 'DEFECT' | 'REWORK' | 'SCRAP' | 'RELEASE'
  timestamp: Date
  description: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  rootCause?: string
  correctiveAction?: string
  preventiveAction?: string
  responsible: string
  status: 'OPEN' | 'IN_PROGRESS' | 'CLOSED'
}

export interface QualityStatus {
  overall: 'PASS' | 'FAIL' | 'HOLD' | 'SCRAP'
  iqc: QualityStepResult // 来料检验
  ipqc: QualityStepResult // 过程检验
  fqc: QualityStepResult // 成品检验
  reliability: QualityStepResult // 可靠性测试
}

export interface QualityStepResult {
  status: 'PASS' | 'FAIL' | 'PENDING' | 'SKIP'
  timestamp?: Date
  inspector: string
  notes?: string
  defects: DefectInfo[]
}

export interface DefectInfo {
  id: string
  type: string
  description: string
  severity: 'MINOR' | 'MAJOR' | 'CRITICAL'
  location: string
  quantity: number
  imageUrl?: string
}

// IATF16949合规管理相关类型
export interface ComplianceDocument {
  id: string
  documentType: 'CONTROL_PLAN' | 'FMEA' | 'MSA' | 'PPAP' | 'SPC' | 'PROCEDURE'
  title: string
  version: string
  status: 'DRAFT' | 'REVIEW' | 'APPROVED' | 'OBSOLETE'
  effectiveDate: Date
  nextReviewDate: Date
  owner: string
  approver: string
  content: string
  attachments: DocumentAttachment[]
  changeHistory: DocumentChange[]
}

export interface DocumentAttachment {
  id: string
  filename: string
  fileSize: number
  fileType: string
  uploadDate: Date
  uploadedBy: string
  url: string
}

export interface DocumentChange {
  id: string
  version: string
  changeDate: Date
  changedBy: string
  changeReason: string
  changeDescription: string
  approvedBy: string
}

export interface ComplianceAudit {
  id: string
  auditType: 'INTERNAL' | 'EXTERNAL' | 'SUPPLIER' | 'CUSTOMER'
  auditor: string
  auditDate: Date
  scope: string[]
  findings: AuditFinding[]
  correctionActions: CorrectiveAction[]
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  nextAuditDate?: Date
}

export interface AuditFinding {
  id: string
  clause: string // IATF16949条款
  nonConformityType: 'MINOR' | 'MAJOR' | 'CRITICAL'
  description: string
  evidence: string
  rootCause: string
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  dueDate: Date
}

export interface CorrectiveAction {
  id: string
  findingId: string
  description: string
  responsible: string
  dueDate: Date
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'OVERDUE'
  effectiveness: 'NOT_EVALUATED' | 'EFFECTIVE' | 'INEFFECTIVE'
  verificationDate?: Date
  verifiedBy?: string
}

// 质量检验相关类型
export interface QualityInspection {
  id: string
  inspectionType: 'IQC' | 'IPQC' | 'FQC' | 'OQC'
  lotNumber: string
  materialCode: string
  sampleSize: number
  inspectionPlan: InspectionPlan
  results: InspectionResult[]
  inspector: string
  inspectionDate: Date
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'REJECTED'
  conclusion: 'ACCEPT' | 'REJECT' | 'CONDITIONAL_ACCEPT'
}

export interface InspectionPlan {
  id: string
  planName: string
  applicableProducts: string[]
  inspectionItems: InspectionItem[]
  samplingPlan: SamplingPlan
  acceptanceCriteria: AcceptanceCriteria
}

export interface InspectionItem {
  id: string
  itemName: string
  testMethod: string
  specification: QualitySpecification
  criticalLevel: 'CRITICAL' | 'MAJOR' | 'MINOR'
  inspectionSequence: number
  equipmentRequired: string[]
}

export interface SamplingPlan {
  planType: 'SINGLE' | 'DOUBLE' | 'MULTIPLE' | 'CONTINUOUS'
  aql: number // Acceptable Quality Level
  sampleSize: number
  acceptanceNumber: number
  rejectionNumber: number
  inspectionLevel: 'I' | 'II' | 'III' | 'S1' | 'S2' | 'S3' | 'S4'
}

export interface AcceptanceCriteria {
  overallAql: number
  criticalAql: number
  majorAql: number
  minorAql: number
}

export interface InspectionResult {
  id: string
  inspectionItemId: string
  measuredValue: number
  result: 'PASS' | 'FAIL'
  notes?: string
  measurementUncertainty?: number
  equipment: string
  measurementDate: Date
}

// 质量分析相关类型
export interface QualityKPI {
  kpiName: string
  currentValue: number
  target: number
  unit: string
  trend: 'UP' | 'DOWN' | 'STABLE'
  trendPercentage: number
  status: 'EXCELLENT' | 'GOOD' | 'WARNING' | 'POOR'
  timeRange: string
  // 扩展字段用于UI显示
  title?: string // UI显示标题
  value?: string // 格式化的值
  change?: string // 变化描述
  icon?: string // 图标名称
  color?: string // 颜色类名
}

export interface QualityAnalytics {
  period: {
    start: Date
    end: Date
  }
  yieldAnalysis: YieldAnalysis
  defectAnalysis: DefectAnalysis
  customerComplaint: CustomerComplaintAnalysis
  qualityCost: QualityCostAnalysis
  processCapability: ProcessCapabilityAnalysis
}

export interface YieldAnalysis {
  overallYield: number
  yieldByProcess: Record<string, number>
  yieldTrend: TrendData[]
  firstPassYield: number
  finalYield: number
  reworkRate: number
  scrapRate: number
}

export interface DefectAnalysis {
  totalDefects: number
  defectRate: number
  defectsByType: Record<string, number>
  defectsByLocation: Record<string, number>
  paretoChart: ParetoData[]
  topDefects: DefectSummary[]
}

export interface DefectSummary {
  type: string
  count: number
  rate: number
  trend: 'INCREASING' | 'DECREASING' | 'STABLE'
  impact: 'HIGH' | 'MEDIUM' | 'LOW'
}

export interface CustomerComplaintAnalysis {
  totalComplaints: number
  complaintRate: number
  responseTime: {
    average: number
    target: number
  }
  resolutionTime: {
    average: number
    target: number
  }
  complaintsByType: Record<string, number>
  customerSatisfaction: number
}

export interface QualityCostAnalysis {
  totalQualityCost: number
  costByCategory: {
    prevention: number
    appraisal: number
    internalFailure: number
    externalFailure: number
  }
  costTrend: TrendData[]
  costAsPercentageOfSales: number
}

export interface ProcessCapabilityAnalysis {
  processes: ProcessCapabilityData[]
  overallCapability: number
  capabilityTrend: TrendData[]
}

export interface ProcessCapabilityData {
  processName: string
  parameter: string
  cp: number
  cpk: number
  pp: number
  ppk: number
  sigma: number
  yield: number
  status: 'EXCELLENT' | 'GOOD' | 'MARGINAL' | 'POOR'
}

export interface TrendData {
  date: string
  value: number
}

export interface ParetoData {
  category: string
  value: number
  percentage: number
  cumulativePercentage: number
}

export interface CustomerInfo {
  customerId: string
  customerName: string
  contactInfo: string
  qualityRequirements: QualityRequirement[]
}

export interface QualityRequirement {
  parameter: string
  specification: QualitySpecification
  testMethod: string
  frequency: string
  reportingRequirement: string
}

// 质量管理系统配置
export interface QualitySystemConfig {
  spcConfig: {
    updateInterval: number // 更新间隔(秒)
    chartTypes: string[]
    defaultSubgroupSize: number
    violationRules: ViolationRule[]
  }
  traceabilityConfig: {
    requiredFields: string[]
    retentionPeriod: number // 保留期(天)
    autoArchive: boolean
  }
  complianceConfig: {
    auditFrequency: number // 审核频率(月)
    documentReviewCycle: number // 文档评审周期(月)
    requiredTraining: string[]
  }
  inspectionConfig: {
    defaultSamplingPlan: SamplingPlan
    autoScheduling: boolean
    resultRetention: number // 结果保留期(年)
  }
}

// 客户投诉记录类型
export interface ComplaintRecord {
  id: string
  customerId: string
  customerName: string
  complaintDate: string
  complaintType: string
  severity: 'HIGH' | 'MEDIUM' | 'LOW'
  description: string
  status: 'OPEN' | 'INVESTIGATING' | 'RESOLVED' | 'CLOSED'
  assignedTo: string
  responseTime?: number // 响应时间（小时）
  resolutionTime?: number // 解决时间（小时）
  rootCause?: string
  corrective?: string
  preventive?: string
  customerSatisfaction?: number // 客户满意度评分
  createdAt: string
  updatedAt: string
}

// 质量成本类型别名
export type QualityCost = QualityCostAnalysis
