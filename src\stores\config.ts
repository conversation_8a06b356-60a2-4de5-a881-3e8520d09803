/**
 * IC封测CIM系统 - 系统配置状态管理
 * System Configuration Store for IC Packaging & Testing CIM System
 */

import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
// Element Plus组件通过unplugin-auto-import自动导入
import type { ApiResponse } from '@/api/config'

/**
 * 系统配置接口
 */
export interface SystemConfig {
  // 基础配置
  systemName: string
  systemVersion: string
  systemLogo?: string
  companyName: string
  companyLogo?: string
  copyRight: string

  // 业务配置
  factoryInfo: {
    name: string
    code: string
    address: string
    contact: string
    phone: string
    email: string
  }

  // 界面配置
  theme: {
    mode: 'light' | 'dark' | 'auto'
    primaryColor: string
    layout: 'side' | 'top' | 'mix'
    menuCollapse: boolean
    fixedHeader: boolean
    fixedSidebar: boolean
    showBreadcrumb: boolean
    showTabs: boolean
    showFooter: boolean
  }

  // 语言配置
  locale: {
    current: 'zh-CN' | 'en-US'
    available: Array<{ code: string; name: string; flag?: string }>
  }

  // 数据配置
  data: {
    pageSize: number
    maxPageSize: number
    cacheTimeout: number
    autoRefresh: boolean
    refreshInterval: number
  }

  // 上传配置
  upload: {
    maxSize: number // MB
    allowedTypes: string[]
    imageTypes: string[]
    documentTypes: string[]
  }

  // 安全配置
  security: {
    passwordPolicy: {
      minLength: number
      requireUppercase: boolean
      requireLowercase: boolean
      requireNumber: boolean
      requireSpecialChar: boolean
      expiryDays: number
    }
    sessionTimeout: number // 分钟
    maxLoginAttempts: number
    lockoutDuration: number // 分钟
  }

  // 通知配置
  notification: {
    email: {
      enabled: boolean
      smtp: {
        host: string
        port: number
        secure: boolean
        username: string
        password: string
      }
    }
    sms: {
      enabled: boolean
      provider: string
      accessKey: string
      secretKey: string
    }
    push: {
      enabled: boolean
      sound: boolean
      vibration: boolean
    }
  }

  // API配置
  api: {
    baseUrl: string
    timeout: number
    retryCount: number
    retryDelay: number
  }

  // 设备集成配置
  equipment: {
    secsGem: {
      enabled: boolean
      timeout: number
      heartbeat: number
      autoConnect: boolean
    }
    opc: {
      enabled: boolean
      server: string
      updateRate: number
    }
    modbus: {
      enabled: boolean
      timeout: number
      retryCount: number
    }
  }

  // 质量配置
  quality: {
    spc: {
      enabled: boolean
      sampleSize: number
      controlLimits: {
        ucl: number // Upper Control Limit
        lcl: number // Lower Control Limit
      }
      alertThresholds: {
        warning: number
        critical: number
      }
    }
    traceability: {
      enabled: boolean
      levels: string[]
      retention: number // 天数
    }
  }

  // 报表配置
  report: {
    formats: string[]
    maxExportRows: number
    scheduledReports: boolean
    emailReports: boolean
  }
}

/**
 * 用户偏好设置接口
 */
export interface UserPreference {
  userId: string
  theme: {
    mode: 'light' | 'dark' | 'auto'
    primaryColor?: string
    menuCollapse?: boolean
  }
  locale: string
  dashboard: {
    layout: string[]
    refreshInterval: number
    showWelcome: boolean
  }
  notifications: {
    email: boolean
    push: boolean
    sound: boolean
  }
  display: {
    pageSize: number
    density: 'comfortable' | 'compact' | 'default'
    showAdvancedOptions: boolean
  }
}

/**
 * 系统状态接口
 */
export interface SystemStatus {
  online: boolean
  version: string
  uptime: number
  lastUpdate: string
  services: Array<{
    name: string
    status: 'online' | 'offline' | 'warning'
    message?: string
  }>
  performance: {
    cpu: number
    memory: number
    disk: number
    network: number
  }
  database: {
    status: 'connected' | 'disconnected' | 'error'
    responseTime: number
  }
}

/**
 * 系统配置Store
 */
export const useConfigStore = defineStore('config', () => {
  // 状态管理
  const systemConfig = ref<SystemConfig>({
    systemName: 'IC封测CIM系统',
    systemVersion: '1.0.0',
    companyName: 'IC封测工厂',
    copyRight: '© 2024 IC封测CIM系统. All rights reserved.',

    factoryInfo: {
      name: 'IC封测生产工厂',
      code: 'FAB001',
      address: '中国上海市浦东新区张江高科技园区',
      contact: '张经理',
      phone: '+86-21-12345678',
      email: '<EMAIL>'
    },

    theme: {
      mode: 'light',
      primaryColor: '#409EFF',
      layout: 'side',
      menuCollapse: false,
      fixedHeader: true,
      fixedSidebar: true,
      showBreadcrumb: true,
      showTabs: true,
      showFooter: true
    },

    locale: {
      current: 'zh-CN',
      available: [
        { code: 'zh-CN', name: '简体中文', flag: '🇨🇳' },
        { code: 'en-US', name: 'English', flag: '🇺🇸' }
      ]
    },

    data: {
      pageSize: 20,
      maxPageSize: 100,
      cacheTimeout: 300000, // 5分钟
      autoRefresh: true,
      refreshInterval: 30000 // 30秒
    },

    upload: {
      maxSize: 10, // 10MB
      allowedTypes: ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx', '.xls', '.xlsx'],
      imageTypes: ['.jpg', '.jpeg', '.png', '.gif', '.bmp'],
      documentTypes: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
    },

    security: {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumber: true,
        requireSpecialChar: true,
        expiryDays: 90
      },
      sessionTimeout: 120, // 2小时
      maxLoginAttempts: 5,
      lockoutDuration: 30 // 30分钟
    },

    notification: {
      email: {
        enabled: false,
        smtp: {
          host: '',
          port: 587,
          secure: false,
          username: '',
          password: ''
        }
      },
      sms: {
        enabled: false,
        provider: '',
        accessKey: '',
        secretKey: ''
      },
      push: {
        enabled: true,
        sound: true,
        vibration: true
      }
    },

    api: {
      baseUrl: '/api',
      timeout: 30000,
      retryCount: 3,
      retryDelay: 1000
    },

    equipment: {
      secsGem: {
        enabled: true,
        timeout: 10000,
        heartbeat: 30000,
        autoConnect: true
      },
      opc: {
        enabled: false,
        server: '',
        updateRate: 1000
      },
      modbus: {
        enabled: false,
        timeout: 5000,
        retryCount: 3
      }
    },

    quality: {
      spc: {
        enabled: true,
        sampleSize: 25,
        controlLimits: {
          ucl: 3.0,
          lcl: -3.0
        },
        alertThresholds: {
          warning: 2.0,
          critical: 2.5
        }
      },
      traceability: {
        enabled: true,
        levels: ['Wafer', 'Die', 'Package', 'Test'],
        retention: 365 // 1年
      }
    },

    report: {
      formats: ['PDF', 'Excel', 'CSV'],
      maxExportRows: 10000,
      scheduledReports: true,
      emailReports: true
    }
  })

  const userPreference = ref<UserPreference | null>(null)
  const systemStatus = ref<SystemStatus>({
    online: true,
    version: '1.0.0',
    uptime: 0,
    lastUpdate: new Date().toISOString(),
    services: [],
    performance: {
      cpu: 0,
      memory: 0,
      disk: 0,
      network: 0
    },
    database: {
      status: 'connected',
      responseTime: 0
    }
  })

  const isLoading = ref(false)
  const lastSyncTime = ref<Date | null>(null)

  // 计算属性
  const currentTheme = computed(() => systemConfig.value.theme)
  const currentLocale = computed(() => systemConfig.value.locale.current)
  const isSystemOnline = computed(() => systemStatus.value.online)
  const criticalServices = computed(() =>
    systemStatus.value.services.filter(s => s.status === 'offline')
  )

  /**
   * 加载系统配置
   */
  const loadSystemConfig = async (): Promise<void> => {
    if (isLoading.value) return

    try {
      isLoading.value = true

      // 模拟API调用
      const response = await mockGetSystemConfig()

      if (response.success) {
        systemConfig.value = { ...systemConfig.value, ...response.data }
        lastSyncTime.value = new Date()

        console.log('[Config] System config loaded successfully')
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('[Config] Failed to load system config:', error)
      ElMessage.error('加载系统配置失败: ' + error.message)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 保存系统配置
   */
  const saveSystemConfig = async (config?: Partial<SystemConfig>): Promise<boolean> => {
    try {
      isLoading.value = true

      const configToSave = config ? { ...systemConfig.value, ...config } : systemConfig.value

      // 模拟API调用
      const response = await mockSaveSystemConfig(configToSave)

      if (response.success) {
        systemConfig.value = configToSave
        lastSyncTime.value = new Date()

        // 保存到本地存储
        saveConfigToStorage()

        ElMessage.success('系统配置保存成功')
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('[Config] Failed to save system config:', error)
      ElMessage.error('保存系统配置失败: ' + error.message)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 加载用户偏好
   */
  const loadUserPreference = async (userId: string): Promise<void> => {
    try {
      // 模拟API调用
      const response = await mockGetUserPreference(userId)

      if (response.success) {
        userPreference.value = response.data

        // 应用用户偏好到系统配置
        applyUserPreference()

        console.log('[Config] User preference loaded successfully')
      }
    } catch (error: any) {
      console.error('[Config] Failed to load user preference:', error)
    }
  }

  /**
   * 保存用户偏好
   */
  const saveUserPreference = async (preference: Partial<UserPreference>): Promise<boolean> => {
    if (!userPreference.value) return false

    try {
      const updatedPreference = { ...userPreference.value, ...preference }

      // 模拟API调用
      const response = await mockSaveUserPreference(updatedPreference)

      if (response.success) {
        userPreference.value = updatedPreference

        // 应用用户偏好
        applyUserPreference()

        // 保存到本地存储
        savePreferenceToStorage()

        ElMessage.success('用户偏好保存成功')
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('[Config] Failed to save user preference:', error)
      ElMessage.error('保存用户偏好失败: ' + error.message)
      return false
    }
  }

  /**
   * 应用用户偏好到系统配置
   */
  const applyUserPreference = (): void => {
    if (!userPreference.value) return

    // 应用主题设置
    if (userPreference.value.theme) {
      systemConfig.value.theme = {
        ...systemConfig.value.theme,
        ...userPreference.value.theme
      }
    }

    // 应用语言设置
    if (userPreference.value.locale) {
      systemConfig.value.locale.current = userPreference.value.locale as any
    }

    // 应用显示设置
    if (userPreference.value.display) {
      systemConfig.value.data.pageSize = userPreference.value.display.pageSize
    }
  }

  /**
   * 获取系统状态
   */
  const loadSystemStatus = async (): Promise<void> => {
    try {
      // 模拟API调用
      const response = await mockGetSystemStatus()

      if (response.success) {
        systemStatus.value = response.data
        console.log('[Config] System status updated')
      }
    } catch (error: any) {
      console.error('[Config] Failed to load system status:', error)
      systemStatus.value.online = false
    }
  }

  /**
   * 更新特定配置项
   */
  const updateConfig = async (path: string, value: any): Promise<boolean> => {
    try {
      // 使用路径更新配置
      const keys = path.split('.')
      let current: any = systemConfig.value

      // 导航到父对象
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {}
        }
        current = current[keys[i]]
      }

      // 设置值
      current[keys[keys.length - 1]] = value

      // 保存配置
      return await saveSystemConfig()
    } catch (error: any) {
      console.error('[Config] Failed to update config:', error)
      return false
    }
  }

  /**
   * 重置配置为默认值
   */
  const resetToDefaults = async (): Promise<boolean> => {
    try {
      // 模拟获取默认配置
      const response = await mockGetDefaultConfig()

      if (response.success) {
        systemConfig.value = response.data
        await saveSystemConfig()

        ElMessage.success('配置已重置为默认值')
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('[Config] Failed to reset config:', error)
      ElMessage.error('重置配置失败: ' + error.message)
      return false
    }
  }

  /**
   * 导出配置
   */
  const exportConfig = (): void => {
    try {
      const configJson = JSON.stringify(systemConfig.value, null, 2)
      const blob = new Blob([configJson], { type: 'application/json' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `cim-config-${new Date().toISOString().split('T')[0]}.json`
      link.click()

      URL.revokeObjectURL(url)
      ElMessage.success('配置导出成功')
    } catch (error) {
      console.error('[Config] Failed to export config:', error)
      ElMessage.error('配置导出失败')
    }
  }

  /**
   * 导入配置
   */
  const importConfig = async (file: File): Promise<boolean> => {
    try {
      const text = await file.text()
      const config = JSON.parse(text) as SystemConfig

      // 验证配置格式
      if (!config.systemName || !config.theme || !config.locale) {
        throw new Error('配置文件格式不正确')
      }

      return await saveSystemConfig(config)
    } catch (error: any) {
      console.error('[Config] Failed to import config:', error)
      ElMessage.error('配置导入失败: ' + error.message)
      return false
    }
  }

  /**
   * 保存配置到本地存储
   */
  const saveConfigToStorage = (): void => {
    try {
      localStorage.setItem('cim_system_config', JSON.stringify(systemConfig.value))
    } catch (error) {
      console.error('[Config] Failed to save config to storage:', error)
    }
  }

  /**
   * 保存偏好到本地存储
   */
  const savePreferenceToStorage = (): void => {
    try {
      if (userPreference.value) {
        localStorage.setItem('cim_user_preference', JSON.stringify(userPreference.value))
      }
    } catch (error) {
      console.error('[Config] Failed to save preference to storage:', error)
    }
  }

  /**
   * 从本地存储恢复配置
   */
  const restoreFromStorage = (): void => {
    try {
      const savedConfig = localStorage.getItem('cim_system_config')
      const savedPreference = localStorage.getItem('cim_user_preference')

      if (savedConfig) {
        const config = JSON.parse(savedConfig)
        systemConfig.value = { ...systemConfig.value, ...config }
      }

      if (savedPreference) {
        userPreference.value = JSON.parse(savedPreference)
        applyUserPreference()
      }
    } catch (error) {
      console.error('[Config] Failed to restore from storage:', error)
    }
  }

  // 监听配置变化，自动保存到本地存储
  watch(
    systemConfig,
    () => {
      saveConfigToStorage()
    },
    { deep: true }
  )

  watch(
    userPreference,
    () => {
      savePreferenceToStorage()
    },
    { deep: true }
  )

  // 模拟API方法
  const mockGetSystemConfig = async (): Promise<ApiResponse<SystemConfig>> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      success: true,
      data: systemConfig.value,
      message: '获取系统配置成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  const mockSaveSystemConfig = async (config: SystemConfig): Promise<ApiResponse> => {
    await new Promise(resolve => setTimeout(resolve, 800))
    return {
      success: true,
      data: null,
      message: '保存系统配置成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  const mockGetUserPreference = async (userId: string): Promise<ApiResponse<UserPreference>> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return {
      success: true,
      data: {
        userId,
        theme: {
          mode: 'light',
          primaryColor: '#409EFF',
          menuCollapse: false
        },
        locale: 'zh-CN',
        dashboard: {
          layout: ['orders', 'production', 'quality', 'equipment'],
          refreshInterval: 30000,
          showWelcome: true
        },
        notifications: {
          email: true,
          push: true,
          sound: true
        },
        display: {
          pageSize: 20,
          density: 'default',
          showAdvancedOptions: false
        }
      },
      message: '获取用户偏好成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  const mockSaveUserPreference = async (preference: UserPreference): Promise<ApiResponse> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      success: true,
      data: null,
      message: '保存用户偏好成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  const mockGetSystemStatus = async (): Promise<ApiResponse<SystemStatus>> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return {
      success: true,
      data: {
        online: true,
        version: '1.0.0',
        uptime: Math.floor(Date.now() / 1000),
        lastUpdate: new Date().toISOString(),
        services: [
          { name: 'Database', status: 'online' },
          { name: 'Cache', status: 'online' },
          { name: 'Message Queue', status: 'online' },
          { name: 'File Storage', status: 'online' },
          { name: 'Equipment Integration', status: 'online' }
        ],
        performance: {
          cpu: Math.floor(Math.random() * 100),
          memory: Math.floor(Math.random() * 100),
          disk: Math.floor(Math.random() * 100),
          network: Math.floor(Math.random() * 100)
        },
        database: {
          status: 'connected',
          responseTime: Math.floor(Math.random() * 100) + 10
        }
      },
      message: '获取系统状态成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  const mockGetDefaultConfig = async (): Promise<ApiResponse<SystemConfig>> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return {
      success: true,
      data: systemConfig.value, // 返回当前默认配置
      message: '获取默认配置成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  // 初始化
  if (typeof window !== 'undefined') {
    restoreFromStorage()
  }

  return {
    // 状态
    systemConfig,
    userPreference,
    systemStatus,
    isLoading,
    lastSyncTime,

    // 计算属性
    currentTheme,
    currentLocale,
    isSystemOnline,
    criticalServices,

    // 方法
    loadSystemConfig,
    saveSystemConfig,
    loadUserPreference,
    saveUserPreference,
    applyUserPreference,
    loadSystemStatus,
    updateConfig,
    resetToDefaults,
    exportConfig,
    importConfig,
    restoreFromStorage
  }
})
