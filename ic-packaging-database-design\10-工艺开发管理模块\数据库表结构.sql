-- ========================================
-- 工艺开发管理模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- 工艺流程主表
CREATE TABLE process_flows (
    flow_id VARCHAR(32) PRIMARY KEY COMMENT '工艺流程ID',
    flow_code VARCHAR(50) NOT NULL UNIQUE COMMENT '工艺流程编码',
    flow_name VARCHAR(200) NOT NULL COMMENT '工艺流程名称',
    flow_version VARCHAR(20) NOT NULL COMMENT '流程版本',
    
    -- 产品信息
    product_id VARCHAR(32) COMMENT '产品ID',
    product_code VARCHAR(50) COMMENT '产品编码',
    package_type VARCHAR(50) NOT NULL COMMENT '封装类型(QFP/BGA/CSP/FC)',
    die_size_category VARCHAR(20) COMMENT 'Die尺寸分类',
    
    -- 流程类型
    flow_type VARCHAR(30) NOT NULL COMMENT '流程类型(ASSEMBLY/TESTING/FULL)',
    process_category VARCHAR(50) COMMENT '工艺类别',
    complexity_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '复杂度(LOW/MEDIUM/HIGH)',
    
    -- 流程状态
    flow_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '流程状态',
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    
    -- 工艺指标
    target_yield DECIMAL(5,4) COMMENT '目标良率',
    target_cycle_time INT COMMENT '目标节拍(秒)',
    target_throughput INT COMMENT '目标产能(件/小时)',
    
    -- 质量要求
    quality_level VARCHAR(20) COMMENT '质量等级(AUTOMOTIVE/INDUSTRIAL/CONSUMER)',
    reliability_requirement VARCHAR(100) COMMENT '可靠性要求',
    
    -- 开发信息
    development_phase VARCHAR(30) COMMENT '开发阶段',
    npi_project_id VARCHAR(32) COMMENT 'NPI项目ID',
    customer_id VARCHAR(32) COMMENT '客户ID',
    
    -- 生效信息
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 审批信息
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    approval_comments TEXT COMMENT '审批意见',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version_no INT DEFAULT 1 COMMENT '版本号',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_flow_code (flow_code),
    INDEX idx_flow_product (product_id),
    INDEX idx_flow_package (package_type),
    INDEX idx_flow_type (flow_type),
    INDEX idx_flow_status (flow_status),
    INDEX idx_flow_npi (npi_project_id),
    INDEX idx_flow_customer (customer_id),
    INDEX idx_flow_effective (effective_date, expiry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工艺流程主表';

-- 工艺步骤表
CREATE TABLE process_steps (
    step_id VARCHAR(32) PRIMARY KEY COMMENT '工艺步骤ID',
    flow_id VARCHAR(32) NOT NULL COMMENT '工艺流程ID',
    step_code VARCHAR(50) NOT NULL COMMENT '步骤编码',
    step_name VARCHAR(200) NOT NULL COMMENT '步骤名称',
    step_sequence INT NOT NULL COMMENT '步骤序号',
    
    -- 步骤分类
    step_type VARCHAR(50) NOT NULL COMMENT '步骤类型',
    step_category VARCHAR(50) COMMENT '步骤类别',
    process_area VARCHAR(50) COMMENT '工艺区域',
    
    -- 操作信息
    operation_type VARCHAR(50) COMMENT '操作类型(AUTO/MANUAL/SEMI_AUTO)',
    standard_time DECIMAL(8,2) COMMENT '标准时间(秒)',
    setup_time DECIMAL(8,2) COMMENT '设置时间(秒)',
    
    -- 设备信息
    equipment_type VARCHAR(50) COMMENT '设备类型',
    equipment_model VARCHAR(100) COMMENT '设备型号',
    station_count INT DEFAULT 1 COMMENT '工位数量',
    
    -- 质量控制
    inspection_required TINYINT(1) DEFAULT 0 COMMENT '是否需要检验',
    inspection_type VARCHAR(30) COMMENT '检验类型',
    quality_checkpoint TINYINT(1) DEFAULT 0 COMMENT '是否质量检查点',
    
    -- 环境要求
    temperature_min DECIMAL(6,2) COMMENT '最低温度(℃)',
    temperature_max DECIMAL(6,2) COMMENT '最高温度(℃)',
    humidity_min DECIMAL(5,2) COMMENT '最低湿度(%)',
    humidity_max DECIMAL(5,2) COMMENT '最高湿度(%)',
    cleanroom_class VARCHAR(20) COMMENT '洁净度等级',
    
    -- 工艺参数模板
    parameter_template JSON COMMENT '工艺参数模板',
    
    -- 步骤描述
    step_description TEXT COMMENT '步骤描述',
    operation_instruction TEXT COMMENT '操作指导',
    safety_requirements TEXT COMMENT '安全要求',
    
    -- 前后关系
    predecessor_steps JSON COMMENT '前置步骤',
    successor_steps JSON COMMENT '后续步骤',
    
    -- 并行处理
    parallel_processing TINYINT(1) DEFAULT 0 COMMENT '是否可并行处理',
    parallel_group VARCHAR(20) COMMENT '并行组',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (flow_id) REFERENCES process_flows(flow_id) ON DELETE CASCADE,
    INDEX idx_step_flow (flow_id),
    INDEX idx_step_sequence (flow_id, step_sequence),
    INDEX idx_step_code (step_code),
    INDEX idx_step_type (step_type),
    INDEX idx_step_category (step_category),
    INDEX idx_step_equipment (equipment_type),
    UNIQUE KEY uk_step_sequence (flow_id, step_sequence)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工艺步骤表';

-- 工艺参数表
CREATE TABLE process_parameters (
    parameter_id VARCHAR(32) PRIMARY KEY COMMENT '参数ID',
    step_id VARCHAR(32) NOT NULL COMMENT '工艺步骤ID',
    parameter_code VARCHAR(50) NOT NULL COMMENT '参数编码',
    parameter_name VARCHAR(200) NOT NULL COMMENT '参数名称',
    parameter_type VARCHAR(30) NOT NULL COMMENT '参数类型',
    
    -- 参数值
    parameter_value VARCHAR(200) COMMENT '参数值',
    data_type VARCHAR(20) NOT NULL COMMENT '数据类型(NUMERIC/STRING/BOOLEAN)',
    unit VARCHAR(20) COMMENT '单位',
    
    -- 数值范围(用于数值型参数)
    min_value DECIMAL(15,6) COMMENT '最小值',
    max_value DECIMAL(15,6) COMMENT '最大值',
    target_value DECIMAL(15,6) COMMENT '目标值',
    tolerance_plus DECIMAL(15,6) COMMENT '正公差',
    tolerance_minus DECIMAL(15,6) COMMENT '负公差',
    
    -- 分类参数(用于分类型参数)
    allowed_values JSON COMMENT '允许值列表',
    default_value VARCHAR(200) COMMENT '默认值',
    
    -- 控制信息
    is_critical TINYINT(1) DEFAULT 0 COMMENT '是否关键参数',
    is_spc_monitored TINYINT(1) DEFAULT 0 COMMENT '是否SPC监控',
    control_method VARCHAR(50) COMMENT '控制方法',
    
    -- 设备关联
    equipment_parameter VARCHAR(100) COMMENT '设备参数名',
    plc_address VARCHAR(100) COMMENT 'PLC地址',
    secs_gem_parameter VARCHAR(100) COMMENT 'SECS/GEM参数名',
    
    -- 质量要求
    cpk_requirement DECIMAL(4,2) COMMENT 'Cpk要求',
    measurement_method VARCHAR(100) COMMENT '测量方法',
    measurement_frequency VARCHAR(50) COMMENT '测量频率',
    
    -- 开发信息
    development_stage VARCHAR(30) COMMENT '开发阶段',
    optimization_status VARCHAR(20) COMMENT '优化状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (step_id) REFERENCES process_steps(step_id) ON DELETE CASCADE,
    INDEX idx_param_step (step_id),
    INDEX idx_param_code (parameter_code),
    INDEX idx_param_name (parameter_name),
    INDEX idx_param_type (parameter_type),
    INDEX idx_param_critical (is_critical),
    INDEX idx_param_spc (is_spc_monitored),
    UNIQUE KEY uk_step_param (step_id, parameter_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工艺参数表';

-- DOE试验设计表
CREATE TABLE doe_experiments (
    experiment_id VARCHAR(32) PRIMARY KEY COMMENT '试验ID',
    flow_id VARCHAR(32) COMMENT '工艺流程ID',
    step_id VARCHAR(32) COMMENT '工艺步骤ID',
    experiment_code VARCHAR(50) NOT NULL UNIQUE COMMENT '试验编码',
    experiment_name VARCHAR(200) NOT NULL COMMENT '试验名称',
    
    -- 试验基本信息
    experiment_type VARCHAR(30) NOT NULL COMMENT 'DOE类型(FACTORIAL/TAGUCHI/RSM/SCREENING)',
    experiment_objective TEXT NOT NULL COMMENT '试验目的',
    experiment_scope TEXT COMMENT '试验范围',
    
    -- 试验设计
    factor_count INT NOT NULL COMMENT '因子数量',
    level_count INT NOT NULL COMMENT '水平数量',
    run_count INT NOT NULL COMMENT '试验次数',
    replication_count INT DEFAULT 1 COMMENT '重复次数',
    block_count INT COMMENT '区组数量',
    
    -- 试验状态
    experiment_status VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '试验状态',
    
    -- 试验计划
    planned_start_date DATE COMMENT '计划开始日期',
    planned_end_date DATE COMMENT '计划结束日期',
    actual_start_date DATE COMMENT '实际开始日期',
    actual_end_date DATE COMMENT '实际结束日期',
    
    -- 资源需求
    required_materials JSON COMMENT '所需材料',
    required_equipment JSON COMMENT '所需设备',
    required_personnel JSON COMMENT '所需人员',
    
    -- 试验负责人
    principal_investigator VARCHAR(32) NOT NULL COMMENT '主试验员',
    experiment_team JSON COMMENT '试验团队',
    
    -- 客户信息
    customer_id VARCHAR(32) COMMENT '客户ID',
    customer_approval_required TINYINT(1) DEFAULT 0 COMMENT '是否需要客户批准',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (flow_id) REFERENCES process_flows(flow_id) ON DELETE SET NULL,
    FOREIGN KEY (step_id) REFERENCES process_steps(step_id) ON DELETE SET NULL,
    INDEX idx_doe_code (experiment_code),
    INDEX idx_doe_flow (flow_id),
    INDEX idx_doe_step (step_id),
    INDEX idx_doe_status (experiment_status),
    INDEX idx_doe_investigator (principal_investigator),
    INDEX idx_doe_customer (customer_id),
    INDEX idx_doe_date (planned_start_date, planned_end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DOE试验设计表';

-- DOE因子表
CREATE TABLE doe_factors (
    factor_id VARCHAR(32) PRIMARY KEY COMMENT '因子ID',
    experiment_id VARCHAR(32) NOT NULL COMMENT '试验ID',
    factor_code VARCHAR(50) NOT NULL COMMENT '因子编码',
    factor_name VARCHAR(200) NOT NULL COMMENT '因子名称',
    factor_type VARCHAR(30) NOT NULL COMMENT '因子类型(QUANTITATIVE/QUALITATIVE)',
    
    -- 因子描述
    factor_description TEXT COMMENT '因子描述',
    parameter_id VARCHAR(32) COMMENT '关联工艺参数ID',
    
    -- 水平信息
    level_count INT NOT NULL COMMENT '水平数',
    level_values JSON NOT NULL COMMENT '水平值列表',
    units VARCHAR(20) COMMENT '单位',
    
    -- 控制信息
    is_hard_to_change TINYINT(1) DEFAULT 0 COMMENT '是否难变因子',
    control_method VARCHAR(100) COMMENT '控制方法',
    
    -- 预期影响
    expected_effect VARCHAR(20) COMMENT '预期效应(POSITIVE/NEGATIVE/NONE)',
    importance_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '重要程度',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (experiment_id) REFERENCES doe_experiments(experiment_id) ON DELETE CASCADE,
    INDEX idx_factor_experiment (experiment_id),
    INDEX idx_factor_code (factor_code),
    INDEX idx_factor_parameter (parameter_id),
    INDEX idx_factor_type (factor_type),
    UNIQUE KEY uk_experiment_factor (experiment_id, factor_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DOE因子表';

-- DOE响应表
CREATE TABLE doe_responses (
    response_id VARCHAR(32) PRIMARY KEY COMMENT '响应ID',
    experiment_id VARCHAR(32) NOT NULL COMMENT '试验ID',
    response_code VARCHAR(50) NOT NULL COMMENT '响应编码',
    response_name VARCHAR(200) NOT NULL COMMENT '响应名称',
    response_type VARCHAR(30) NOT NULL COMMENT '响应类型(CONTINUOUS/DISCRETE/ATTRIBUTE)',
    
    -- 响应描述
    response_description TEXT COMMENT '响应描述',
    measurement_method VARCHAR(200) COMMENT '测量方法',
    
    -- 目标信息
    target_value DECIMAL(15,6) COMMENT '目标值',
    target_type VARCHAR(20) COMMENT '目标类型(MAXIMIZE/MINIMIZE/TARGET)',
    specification_lower DECIMAL(15,6) COMMENT '规格下限',
    specification_upper DECIMAL(15,6) COMMENT '规格上限',
    
    -- 单位和精度
    units VARCHAR(20) COMMENT '单位',
    measurement_precision DECIMAL(8,6) COMMENT '测量精度',
    
    -- 重要性
    importance_weight DECIMAL(4,2) DEFAULT 1.0 COMMENT '重要性权重',
    priority_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '优先级',
    
    -- 质量特性
    quality_characteristic VARCHAR(50) COMMENT '质量特性',
    is_critical TINYINT(1) DEFAULT 0 COMMENT '是否关键响应',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (experiment_id) REFERENCES doe_experiments(experiment_id) ON DELETE CASCADE,
    INDEX idx_response_experiment (experiment_id),
    INDEX idx_response_code (response_code),
    INDEX idx_response_type (response_type),
    INDEX idx_response_critical (is_critical),
    UNIQUE KEY uk_experiment_response (experiment_id, response_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DOE响应表';

-- DOE试验运行表
CREATE TABLE doe_runs (
    run_id VARCHAR(32) PRIMARY KEY COMMENT '试验运行ID',
    experiment_id VARCHAR(32) NOT NULL COMMENT '试验ID',
    run_number INT NOT NULL COMMENT '试验运行号',
    run_sequence INT COMMENT '运行顺序',
    
    -- 运行条件
    factor_settings JSON NOT NULL COMMENT '因子设置',
    block_number INT COMMENT '区组号',
    replication_number INT DEFAULT 1 COMMENT '重复号',
    
    -- 运行状态
    run_status VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '运行状态',
    
    -- 执行信息
    execution_date DATE COMMENT '执行日期',
    operator_id VARCHAR(32) COMMENT '操作员ID',
    shift VARCHAR(20) COMMENT '班次',
    
    -- 环境条件
    temperature DECIMAL(6,2) COMMENT '环境温度(℃)',
    humidity DECIMAL(5,2) COMMENT '环境湿度(%)',
    
    -- 物料批次
    material_lots JSON COMMENT '物料批次信息',
    
    -- 设备信息
    equipment_used JSON COMMENT '使用设备',
    
    -- 备注
    run_notes TEXT COMMENT '运行备注',
    issues_encountered TEXT COMMENT '遇到的问题',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (experiment_id) REFERENCES doe_experiments(experiment_id) ON DELETE CASCADE,
    INDEX idx_run_experiment (experiment_id),
    INDEX idx_run_number (experiment_id, run_number),
    INDEX idx_run_status (run_status),
    INDEX idx_run_date (execution_date),
    INDEX idx_run_operator (operator_id),
    UNIQUE KEY uk_experiment_run (experiment_id, run_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DOE试验运行表';

-- DOE试验结果表
CREATE TABLE doe_results (
    result_id VARCHAR(32) PRIMARY KEY COMMENT '结果ID',
    run_id VARCHAR(32) NOT NULL COMMENT '试验运行ID',
    response_id VARCHAR(32) NOT NULL COMMENT '响应ID',
    
    -- 测量结果
    measured_value DECIMAL(15,6) COMMENT '测量值',
    measurement_time TIMESTAMP COMMENT '测量时间',
    
    -- 测量信息
    measurement_operator VARCHAR(32) COMMENT '测量员',
    measurement_equipment VARCHAR(100) COMMENT '测量设备',
    measurement_condition VARCHAR(200) COMMENT '测量条件',
    
    -- 数据质量
    is_valid TINYINT(1) DEFAULT 1 COMMENT '数据是否有效',
    outlier_flag TINYINT(1) DEFAULT 0 COMMENT '是否异常值',
    quality_flag VARCHAR(20) DEFAULT 'GOOD' COMMENT '数据质量标识',
    
    -- 备注
    measurement_notes TEXT COMMENT '测量备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (run_id) REFERENCES doe_runs(run_id) ON DELETE CASCADE,
    FOREIGN KEY (response_id) REFERENCES doe_responses(response_id) ON DELETE CASCADE,
    INDEX idx_result_run (run_id),
    INDEX idx_result_response (response_id),
    INDEX idx_result_time (measurement_time),
    INDEX idx_result_operator (measurement_operator),
    INDEX idx_result_valid (is_valid),
    UNIQUE KEY uk_run_response (run_id, response_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DOE试验结果表';

-- 工艺优化记录表
CREATE TABLE process_optimization (
    optimization_id VARCHAR(32) PRIMARY KEY COMMENT '优化ID',
    flow_id VARCHAR(32) NOT NULL COMMENT '工艺流程ID',
    step_id VARCHAR(32) COMMENT '工艺步骤ID',
    optimization_code VARCHAR(50) NOT NULL UNIQUE COMMENT '优化编码',
    optimization_title VARCHAR(200) NOT NULL COMMENT '优化标题',
    
    -- 优化信息
    optimization_type VARCHAR(30) NOT NULL COMMENT '优化类型',
    optimization_objective TEXT NOT NULL COMMENT '优化目标',
    current_performance JSON COMMENT '当前性能指标',
    target_performance JSON COMMENT '目标性能指标',
    
    -- 优化方法
    optimization_method VARCHAR(50) COMMENT '优化方法',
    doe_experiment_id VARCHAR(32) COMMENT '关联DOE试验ID',
    
    -- 参数变更
    parameter_changes JSON COMMENT '参数变更记录',
    
    -- 结果验证
    verification_method VARCHAR(100) COMMENT '验证方法',
    verification_results JSON COMMENT '验证结果',
    
    -- 效果评估
    improvement_achieved JSON COMMENT '实现的改进',
    cost_impact DECIMAL(15,2) COMMENT '成本影响',
    quality_impact TEXT COMMENT '质量影响',
    
    -- 状态管理
    optimization_status VARCHAR(20) NOT NULL DEFAULT 'IN_PROGRESS' COMMENT '优化状态',
    
    -- 负责人
    lead_engineer VARCHAR(32) NOT NULL COMMENT '主导工程师',
    team_members JSON COMMENT '团队成员',
    
    -- 时间信息
    start_date DATE NOT NULL COMMENT '开始日期',
    target_completion_date DATE COMMENT '目标完成日期',
    actual_completion_date DATE COMMENT '实际完成日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (flow_id) REFERENCES process_flows(flow_id) ON DELETE CASCADE,
    FOREIGN KEY (step_id) REFERENCES process_steps(step_id) ON DELETE SET NULL,
    FOREIGN KEY (doe_experiment_id) REFERENCES doe_experiments(experiment_id) ON DELETE SET NULL,
    INDEX idx_opt_flow (flow_id),
    INDEX idx_opt_step (step_id),
    INDEX idx_opt_doe (doe_experiment_id),
    INDEX idx_opt_status (optimization_status),
    INDEX idx_opt_engineer (lead_engineer),
    INDEX idx_opt_date (start_date, target_completion_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工艺优化记录表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. process_flows: 工艺流程主表，定义完整的工艺流程
2. process_steps: 工艺步骤表，流程中的具体步骤
3. process_parameters: 工艺参数表，每个步骤的详细参数
4. doe_experiments: DOE试验设计表，工艺优化试验
5. doe_factors: DOE因子表，试验设计的输入因子
6. doe_responses: DOE响应表，试验设计的输出响应
7. doe_runs: DOE试验运行表，具体的试验执行
8. doe_results: DOE试验结果表，试验的测量结果
9. process_optimization: 工艺优化记录表，优化活动跟踪

核心特性:
- 完整的工艺流程定义和参数管理
- 科学的DOE试验设计和数据收集
- 系统化的工艺优化跟踪
- 支持多级工艺流程和并行处理
- 完整的版本控制和审计跟踪
*/