<template>
  <el-dialog
    v-model="visible"
    title="权限管理"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="user-info">
      <el-descriptions :column="2"
border>
        <el-descriptions-item label="用户名">
          {{ userData?.username }}
        </el-descriptions-item>
        <el-descriptions-item label="真实姓名">
          {{ userData?.realName }}
        </el-descriptions-item>
        <el-descriptions-item label="部门">
          {{ userData?.department }}
        </el-descriptions-item>
        <el-descriptions-item label="职位">
          {{ userData?.position }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <el-tabs v-model="activeTab"
type="border-card">
      <!-- 角色管理 -->
      <el-tab-pane label="角色管理"
name="roles">
        <div class="tab-content">
          <div class="section-title">
            <h4>当前角色</h4>
            <p>用户通过角色获得对应的权限集合</p>
          </div>

          <el-form ref="rolesFormRef" :model="rolesForm" label-width="100px">
            <el-form-item label="分配角色">
              <el-select
                v-model="rolesForm.roles"
                multiple
                placeholder="请选择角色"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="role in allRoles"
                  :key="role.code"
                  :label="role.name"
                  :value="role.code"
                >
                  <div class="role-option">
                    <div class="role-name">
                      {{ role.name }}
                    </div>
                    <div class="role-description">
                      {{ role.description }}
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>

          <div class="current-roles">
            <h5>已分配角色详情：</h5>
            <div v-if="rolesForm.roles.length === 0"
class="empty-state">
              <el-empty description="暂未分配任何角色" :image-size="60" />
            </div>
            <div v-else
class="roles-grid">
              <el-card
                v-for="roleCode in rolesForm.roles"
                :key="roleCode"
                class="role-card"
                shadow="never"
              >
                <div class="role-header">
                  <div class="role-info">
                    <h6>{{ getRoleName(roleCode) }}</h6>
                    <p>{{ getRoleDescription(roleCode) }}</p>
                  </div>
                </div>

                <div class="role-permissions">
                  <h6>包含权限：</h6>
                  <div class="permission-tags">
                    <el-tag
                      v-for="permission in getRolePermissions(roleCode)"
                      :key="permission"
                      size="small"
                      type="info"
                    >
                      {{ getPermissionName(permission) }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 权限详情 -->
      <el-tab-pane label="权限详情"
name="permissions">
        <div class="tab-content">
          <div class="section-title">
            <h4>权限概览</h4>
            <p>查看用户通过角色获得的所有权限</p>
          </div>

          <div class="permissions-summary">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-statistic
                  title="总权限数"
                  :value="allUserPermissions.length"
                  prefix-icon="Lock"
                />
              </el-col>
              <el-col :span="8">
                <el-statistic title="菜单权限" :value="menuPermissions.length" prefix-icon="Menu" />
              </el-col>
              <el-col :span="8">
                <el-statistic
                  title="操作权限"
                  :value="buttonPermissions.length"
                  prefix-icon="Operation"
                />
              </el-col>
            </el-row>
          </div>

          <div class="permissions-list">
            <el-collapse v-model="activePermissionGroups"
accordion>
              <el-collapse-item
                v-for="group in permissionGroups"
                :key="group.name"
                :name="group.name"
              >
                <template #title>
                  <div class="permission-group-title">
                    <el-icon>
                      <component :is="group.icon" />
                    </el-icon>
                    <span>{{ group.label }}</span>
                    <el-tag size="small"
type="info"
>
{{ group.permissions.length }} 个权限
</el-tag>
                  </div>
                </template>

                <div class="permission-group-content">
                  <el-row :gutter="12">
                    <el-col
                      v-for="permission in group.permissions"
                      :key="permission.code"
                      :span="12"
                    >
                      <div class="permission-item">
                        <div class="permission-info">
                          <div class="permission-name">
                            {{ permission.name }}
                          </div>
                          <div class="permission-code">
                            {{ permission.code }}
                          </div>
                        </div>
                        <el-tag :type="getPermissionTypeColor(permission.type)" size="small">
                          {{ getPermissionTypeText(permission.type) }}
                        </el-tag>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="activeTab === 'roles'"
          type="primary"
          :loading="submitting"
          @click="handleSaveRoles"
        >
          {{ submitting ? '保存中...' : '保存角色' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import type { FormInstance } from 'element-plus'
  import type { UserInfo, RoleConfig, PermissionConfig } from '@/types/user'
  import {
    Lock,
    Menu,
    Operation,
    User,
    Setting,
    Box,
    Monitor,
    DataAnalysis,
    Document
  } from '@element-plus/icons-vue'

  interface Props {
    modelValue: boolean
    userData?: UserInfo | null
    permissions: PermissionConfig[]
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'success'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    userData: null
  })

  const emit = defineEmits<Emits>()

  // 对话框显示状态
  const visible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  // 表单引用
  const rolesFormRef = ref<FormInstance>()
  const submitting = ref(false)

  // 当前激活的标签页
  const activeTab = ref('roles')
  const activePermissionGroups = ref<string[]>([])

  // 角色表单数据
  const rolesForm = reactive({
    roles: [] as string[]
  })

  // 模拟角色数据
  const allRoles = ref<RoleConfig[]>([
    {
      code: 'system_admin',
      name: '系统管理员',
      permissions: ['system:read', 'system:write', 'system:delete', 'user:read', 'user:write'],
      description: '系统管理员，拥有所有系统管理权限'
    },
    {
      code: 'process_engineer',
      name: '工艺工程师',
      permissions: ['production:read', 'production:write', 'quality:read', 'equipment:read'],
      description: '工艺工程师，负责工艺开发和优化'
    },
    {
      code: 'production_operator',
      name: '生产操作员',
      permissions: ['production:read', 'equipment:read', 'equipment:operate'],
      description: '生产操作员，负责设备操作'
    },
    {
      code: 'quality_engineer',
      name: '质量工程师',
      permissions: ['quality:read', 'quality:write', 'quality:audit'],
      description: '质量工程师，负责质量管理'
    }
  ])

  // 计算属性
  const allUserPermissions = computed(() => {
    const permissionCodes = new Set<string>()

    rolesForm.roles.forEach(roleCode => {
      const role = allRoles.value.find(r => r.code === roleCode)
      if (role) {
        role.permissions.forEach(permission => {
          permissionCodes.add(permission)
        })
      }
    })

    return props.permissions.filter(p => permissionCodes.has(p.code))
  })

  const menuPermissions = computed(() => {
    return allUserPermissions.value.filter(p => p.type === 'menu')
  })

  const buttonPermissions = computed(() => {
    return allUserPermissions.value.filter(p => p.type === 'button')
  })

  const permissionGroups = computed(() => {
    const groups: Array<{
      name: string
      label: string
      icon: any
      permissions: PermissionConfig[]
    }> = []

    const groupMap = new Map<string, PermissionConfig[]>()

    allUserPermissions.value.forEach(permission => {
      const prefix = permission.code.split(':')[0]
      if (!groupMap.has(prefix)) {
        groupMap.set(prefix, [])
      }
      groupMap.get(prefix)!.push(permission)
    })

    groupMap.forEach((permissions, prefix) => {
      const groupConfig = getGroupConfig(prefix)
      groups.push({
        name: prefix,
        label: groupConfig.label,
        icon: groupConfig.icon,
        permissions
      })
    })

    return groups
  })

  /**
   * 获取分组配置
   */
  const getGroupConfig = (prefix: string) => {
    const configs = {
      system: { label: '系统管理', icon: Setting },
      user: { label: '用户管理', icon: User },
      production: { label: '生产管理', icon: Box },
      equipment: { label: '设备管理', icon: Monitor },
      quality: { label: '质量管理', icon: DataAnalysis },
      report: { label: '报表管理', icon: Document }
    }

    return configs[prefix] || { label: prefix.toUpperCase(), icon: Lock }
  }

  /**
   * 获取角色名称
   */
  const getRoleName = (roleCode: string): string => {
    const role = allRoles.value.find(r => r.code === roleCode)
    return role?.name || roleCode
  }

  /**
   * 获取角色描述
   */
  const getRoleDescription = (roleCode: string): string => {
    const role = allRoles.value.find(r => r.code === roleCode)
    return role?.description || ''
  }

  /**
   * 获取角色权限
   */
  const getRolePermissions = (roleCode: string): string[] => {
    const role = allRoles.value.find(r => r.code === roleCode)
    return role?.permissions || []
  }

  /**
   * 获取权限名称
   */
  const getPermissionName = (permissionCode: string): string => {
    const permission = props.permissions.find(p => p.code === permissionCode)
    return permission?.name || permissionCode
  }

  /**
   * 获取权限类型颜色
   */
  const getPermissionTypeColor = (type: string): string => {
    const colors = {
      menu: 'primary',
      button: 'success',
      api: 'warning'
    }
    return colors[type] || 'info'
  }

  /**
   * 获取权限类型文本
   */
  const getPermissionTypeText = (type: string): string => {
    const texts = {
      menu: '菜单',
      button: '按钮',
      api: 'API'
    }
    return texts[type] || type
  }

  /**
   * 保存角色分配
   */
  const handleSaveRoles = async (): Promise<void> => {
    if (!props.userData) return

    try {
      submitting.value = true

      // 模拟保存角色分配
      await new Promise(resolve => setTimeout(resolve, 1000))

      ElMessage.success('角色分配成功')
      emit('success')
      handleClose()
    } catch (error) {
      console.error('Save roles error:', error)
      ElMessage.error('保存失败')
    } finally {
      submitting.value = false
    }
  }

  /**
   * 处理关闭
   */
  const handleClose = (): void => {
    visible.value = false
    activeTab.value = 'roles'
    activePermissionGroups.value = []
  }

  /**
   * 初始化表单数据
   */
  const initFormData = (): void => {
    if (props.userData) {
      rolesForm.roles = [...props.userData.roles]
    }
  }

  // 监听对话框打开
  watch(
    () => props.modelValue,
    newValue => {
      if (newValue) {
        initFormData()
      }
    },
    { immediate: true }
  )
</script>

<style lang="scss" scoped>
  .user-info {
    margin-bottom: 24px;
  }

  .tab-content {
    padding: 16px 0;
  }

  .section-title {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      color: var(--color-text-primary);
    }

    p {
      margin: 0;
      font-size: 14px;
      color: var(--color-text-secondary);
    }
  }

  .current-roles {
    margin-top: 24px;

    h5 {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: var(--color-text-primary);
    }
  }

  .roles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
  }

  .role-card {
    border: 1px solid var(--color-border-lighter);

    .role-header {
      margin-bottom: 12px;

      .role-info {
        h6 {
          margin: 0 0 4px 0;
          font-size: 14px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        p {
          margin: 0;
          font-size: 12px;
          color: var(--color-text-secondary);
          line-height: 1.4;
        }
      }
    }

    .role-permissions {
      h6 {
        margin: 0 0 8px 0;
        font-size: 12px;
        color: var(--color-text-regular);
      }

      .permission-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .el-tag {
          font-size: 11px;
        }
      }
    }
  }

  .role-option {
    .role-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-primary);
    }

    .role-description {
      font-size: 12px;
      color: var(--color-text-secondary);
      margin-top: 2px;
    }
  }

  .permissions-summary {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--color-bg-light);
    border-radius: 6px;
  }

  .permission-group-title {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;

    .el-icon {
      color: var(--color-primary);
    }

    span {
      flex: 1;
      font-weight: 500;
    }
  }

  .permission-group-content {
    padding: 16px;
  }

  .permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border: 1px solid var(--color-border-lighter);
    border-radius: 4px;
    margin-bottom: 8px;

    .permission-info {
      flex: 1;

      .permission-name {
        font-size: 13px;
        font-weight: 500;
        color: var(--color-text-primary);
        margin-bottom: 2px;
      }

      .permission-code {
        font-size: 11px;
        color: var(--color-text-placeholder);
        font-family: monospace;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }

  :deep(.el-collapse-item__header) {
    padding: 12px 16px;
    font-weight: 500;
  }

  :deep(.el-descriptions__label) {
    font-weight: 500;
    color: var(--color-text-primary);
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 2vh auto;
    }

    .roles-grid {
      grid-template-columns: 1fr;
    }

    .permissions-summary {
      :deep(.el-col) {
        margin-bottom: 12px;
      }
    }

    .permission-group-content {
      :deep(.el-col) {
        span: 24;
      }
    }
  }
</style>
