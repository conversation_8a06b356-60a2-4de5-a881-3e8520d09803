<template>
  <div class="production-dashboard">
    <!-- 顶部KPI指标区域 -->
    <div class="dashboard-header">
      <div class="kpi-grid">
        <KPICard
          title="日产量"
          :value="productionKPI?.dailyOutput || 0"
          unit="pcs"
          :trend="outputTrend"
          :target="productionKPI?.outputTarget"
          type="primary"
          icon="trend"
          size="large"
          :update-time="productionKPI?.updateTime"
          subtitle="今日芯片产量统计"
        />

        <KPICard
          title="整体良率"
          :value="productionKPI?.overallYield || 0"
          unit="%"
          :trend="yieldTrend"
          :target="productionKPI?.yieldTarget"
          type="success"
          icon="analysis"
          size="large"
          :update-time="productionKPI?.updateTime"
          subtitle="全工序良率水平"
        />

        <KPICard
          title="设备稼动率"
          :value="productionKPI?.oeeRate || 0"
          unit="%"
          :trend="oeeTrend"
          :target="85"
          type="warning"
          icon="monitor"
          size="large"
          :update-time="productionKPI?.updateTime"
          subtitle="设备有效作业时间比"
        />

        <KPICard
          title="在制品"
          :value="productionKPI?.wipCount || 0"
          unit="lots"
          type="info"
          icon="warning"
          size="large"
          :update-time="productionKPI?.updateTime"
          subtitle="当前在制品批次数"
        />
      </div>
    </div>

    <!-- 中间图表区域 -->
    <div class="dashboard-content">
      <!-- 左侧：生产趋势图 -->
      <div class="chart-section">
        <ChartPanel
          title="生产趋势分析"
          subtitle="产量、良率、OEE实时趋势监控"
          :options="trendChartOptions"
          :loading="loading"
          :time-ranges="timeRanges"
          :default-time-range="timeRange"
          height="400px"
          :show-footer="true"
          :data-count="trendDataCount"
          :update-time="productionKPI?.updateTime"
          @time-range-change="handleTimeRangeChange"
          @refresh="refreshProductionData"
        />
      </div>

      <!-- 右侧：产线状态 -->
      <div class="status-section">
        <div class="section-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><Monitor /></el-icon>
              产线状态监控
            </h3>
            <div class="header-actions">
              <el-button
                size="small"
                :icon="Refresh"
                :loading="loading"
                @click="refreshProductionData"
              >
                刷新
              </el-button>
            </div>
          </div>

          <div class="production-lines">
            <div
v-for="line in productionLines" :key="line.lineId"
class="line-item"
>
              <div class="line-header">
                <StatusIndicator
                  :status="getLineStatus(line.status)"
                  :label="line.lineName"
                  :value="`${line.progress}%`"
                  size="medium"
                />
              </div>

              <div class="line-details">
                <div class="detail-item">
                  <span class="label">当前批次</span>
                  <span class="value">{{ line.currentLot || '-' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">工序</span>
                  <span class="value">{{ getProcessName(line.processStep) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">操作员</span>
                  <span class="value">{{ line.operatorCount }}人</span>
                </div>
              </div>

              <el-progress
                :percentage="line.progress"
                :color="getProgressColor(line.status)"
                :stroke-width="4"
                :show-text="false"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部统计区域 -->
    <div class="dashboard-footer">
      <!-- 工序产量分布 -->
      <div class="chart-section">
        <ChartPanel
          title="工序产量分布"
          subtitle="各工序当日完成情况"
          :options="processChartOptions"
          :loading="loading"
          height="300px"
          :show-footer="false"
        />
      </div>

      <!-- 小时产量趋势 -->
      <div class="chart-section">
        <ChartPanel
          title="小时产量趋势"
          subtitle="最近24小时产量变化"
          :options="hourlyChartOptions"
          :loading="loading"
          height="300px"
          :show-footer="false"
        />
      </div>

      <!-- 预测分析 -->
      <div class="prediction-section">
        <div class="section-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><TrendCharts /></el-icon>
              生产预测
            </h3>
          </div>

          <div class="prediction-content">
            <div class="prediction-item">
              <div class="metric">
                <div class="metric-value">
                  {{ predictedOutput.toLocaleString() }}
                </div>
                <div class="metric-label">预计日产量 (pcs)</div>
              </div>
              <div class="confidence">
                <el-progress
                  type="circle"
                  :width="60"
                  :percentage="confidenceLevel"
                  :color="confidenceLevel > 80 ? '#67c23a' : '#e6a23c'"
                />
                <div class="confidence-label">置信度</div>
              </div>
            </div>

            <div class="recommendations">
              <h4>建议措施</h4>
              <ul>
                <li v-for="(rec, index) in recommendations" :key="index">
                  {{ rec }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue'
  import { Monitor, Refresh, TrendCharts } from '@element-plus/icons-vue'
  import { KPICard, ChartPanel, StatusIndicator } from '@/components/monitoring'
  import { useMonitoring } from '@/composables/useMonitoring'
  import type { EChartsOption } from 'echarts'
  import type { StatusType } from '@/components/monitoring'

  // 使用监控数据管理
  const { loading, productionKPI, productionTrend, config, refreshProductionData, setTimeRange } =
    useMonitoring()

  // 响应式数据
  const timeRange = ref('24h')
  const predictedOutput = ref(128000)
  const confidenceLevel = ref(87)
  const recommendations = ref([
    '建议增加Prober #2的运行时间',
    '优化线键合工序参数设置',
    '关注Molding Machine维护计划'
  ])

  // 时间范围选项
  const timeRanges = [
    { label: '1小时', value: '1h' },
    { label: '4小时', value: '4h' },
    { label: '12小时', value: '12h' },
    { label: '24小时', value: '24h' },
    { label: '7天', value: '7d' }
  ]

  // 模拟产线数据
  const productionLines = ref([
    {
      lineId: 'LINE001',
      lineName: 'CP测试线',
      status: 'running',
      currentLot: 'LT240825001',
      processStep: 'CP',
      progress: 78,
      operatorCount: 2,
      lastUpdateTime: new Date().toISOString()
    },
    {
      lineId: 'LINE002',
      lineName: '封装线A',
      status: 'running',
      currentLot: 'LT240825002',
      processStep: 'Assembly',
      progress: 65,
      operatorCount: 4,
      lastUpdateTime: new Date().toISOString()
    },
    {
      lineId: 'LINE003',
      lineName: '封装线B',
      status: 'idle',
      currentLot: '',
      processStep: 'Assembly',
      progress: 0,
      operatorCount: 0,
      lastUpdateTime: new Date().toISOString()
    },
    {
      lineId: 'LINE004',
      lineName: '最终测试线',
      status: 'running',
      currentLot: 'LT240825003',
      processStep: 'FT',
      progress: 92,
      operatorCount: 3,
      lastUpdateTime: new Date().toISOString()
    }
  ])

  // 计算属性
  const outputTrend = computed(() => {
    if (!productionKPI.value) return undefined
    const { dailyOutput, outputTarget } = productionKPI.value
    return ((dailyOutput - outputTarget) / outputTarget) * 100
  })

  const yieldTrend = computed(() => {
    if (!productionKPI.value) return undefined
    const { overallYield, yieldTarget } = productionKPI.value
    return ((overallYield - yieldTarget) / yieldTarget) * 100
  })

  const oeeTrend = computed(() => {
    // 模拟OEE趋势，实际应该从历史数据计算
    return 2.3
  })

  const trendDataCount = computed(() => {
    return productionTrend.value.reduce((sum, series) => sum + (series.data?.length || 0), 0)
  })

  // 生产趋势图表配置
  const trendChartOptions = computed<EChartsOption>(() => {
    if (!productionTrend.value.length) {
      return { title: { text: '暂无数据', left: 'center', top: 'middle' } }
    }

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: (params: any) => {
          let result = `<div style="margin-bottom: 8px;">${params[0].axisValueLabel}</div>`
          params.forEach((param: any) => {
            const unit = param.seriesName === '产量' ? ' pcs' : '%'
            result += `<div style="display: flex; align-items: center; margin-bottom: 4px;">
            <span style="width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span style="flex: 1;">${param.seriesName}</span>
            <span style="font-weight: bold;">${param.value}${unit}</span>
          </div>`
          })
          return result
        }
      },
      legend: {
        data: productionTrend.value?.map(s => s.name) || [],
        top: 10,
        textStyle: {
          color: 'var(--color-text-primary)'
        }
      },
      grid: {
        left: '3%',
        right: '10%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data:
          productionTrend.value?.[0]?.data?.map(d => {
            const date = new Date(d.timestamp)
            return date.getHours() + ':' + String(date.getMinutes()).padStart(2, '0')
          }) || [],
        axisLine: {
          lineStyle: {
            color: 'var(--color-border-light)'
          }
        },
        axisLabel: {
          color: 'var(--color-text-secondary)'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '产量 (pcs)',
          position: 'left',
          axisLine: {
            lineStyle: {
              color: '#1890ff'
            }
          },
          axisLabel: {
            color: 'var(--color-text-secondary)',
            formatter: (value: number) => value.toLocaleString()
          },
          splitLine: {
            lineStyle: {
              color: 'var(--color-border-lighter)',
              type: 'dashed'
            }
          }
        },
        {
          type: 'value',
          name: '百分比 (%)',
          position: 'right',
          axisLine: {
            lineStyle: {
              color: '#52c41a'
            }
          },
          axisLabel: {
            color: 'var(--color-text-secondary)',
            formatter: '{value}%'
          }
        }
      ],
      series: productionTrend.value?.map((series, index) => ({
        name: series.name,
        type: 'line',
        yAxisIndex: series.name === '产量' ? 0 : 1,
        data: series.data?.map(d => d.value) || [],
        smooth: true,
        lineStyle: {
          width: 3
        },
        areaStyle:
          series.name === '产量'
            ? {
                opacity: 0.1
              }
            : undefined,
        emphasis: {
          focus: 'series'
        }
      }))
    }
  })

  // 工序产量分布图表
  const processChartOptions = computed<EChartsOption>(() => ({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} pcs ({d}%)'
    },
    legend: {
      bottom: 10,
      left: 'center',
      textStyle: {
        color: 'var(--color-text-primary)'
      }
    },
    series: [
      {
        name: '工序产量',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 45200, name: 'CP测试', itemStyle: { color: '#1890ff' } },
          { value: 38600, name: '线键合', itemStyle: { color: '#52c41a' } },
          { value: 35400, name: '塑封', itemStyle: { color: '#faad14' } },
          { value: 41800, name: '最终测试', itemStyle: { color: '#f5222d' } }
        ]
      }
    ]
  }))

  // 小时产量趋势图表
  const hourlyChartOptions = computed<EChartsOption>(() => {
    const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)
    const data = hours.map(() => Math.floor(Math.random() * 2000) + 3000)

    return {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const param = params[0]
          return `${param.name}<br/>产量: ${param.value.toLocaleString()} pcs`
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: hours,
        axisLabel: {
          color: 'var(--color-text-secondary)',
          interval: 3
        },
        axisLine: {
          lineStyle: {
            color: 'var(--color-border-light)'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: 'var(--color-text-secondary)',
          formatter: (value: number) => value.toLocaleString()
        },
        splitLine: {
          lineStyle: {
            color: 'var(--color-border-lighter)',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '小时产量',
          type: 'bar',
          data,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#40a9ff' },
                { offset: 1, color: '#1890ff' }
              ]
            },
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              color: '#096dd9'
            }
          }
        }
      ]
    }
  })

  // 方法
  const getLineStatus = (status: string): StatusType => {
    const statusMap: Record<string, StatusType> = {
      running: 'running',
      idle: 'idle',
      maintenance: 'maintenance',
      alarm: 'alarm',
      offline: 'offline'
    }
    return statusMap[status] || 'unknown'
  }

  const getProcessName = (step: string): string => {
    const nameMap: Record<string, string> = {
      CP: 'CP测试',
      Assembly: '封装',
      FT: '最终测试',
      Packaging: '包装'
    }
    return nameMap[step] || step
  }

  const getProgressColor = (status: string): string => {
    switch (status) {
      case 'running':
        return '#67c23a'
      case 'idle':
        return '#909399'
      case 'maintenance':
        return '#e6a23c'
      case 'alarm':
        return '#f56c6c'
      default:
        return '#409eff'
    }
  }

  const handleTimeRangeChange = (range: string) => {
    timeRange.value = range
    setTimeRange(range as any)
  }

  // 生命周期
  onMounted(() => {
    refreshProductionData()
  })
</script>

<style lang="scss" scoped>
  .production-dashboard {
    min-height: 100vh;
    padding: var(--spacing-6);
    background: var(--color-bg-page);

    @media (width >= 1920px) {
      padding: var(--spacing-8);
    }
  }

  .dashboard-header {
    margin-bottom: var(--spacing-6);

    .kpi-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-4);

      @media (width >= 1920px) {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-6);
      }
    }
  }

  .dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-6);

    @media (width <= 1200px) {
      grid-template-columns: 1fr;
    }
  }

  .dashboard-footer {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-6);

    @media (width <= 1200px) {
      grid-template-columns: 1fr;
    }

    @media (width >= 1200px) and (width <= 1600px) {
      grid-template-columns: 1fr 1fr;

      .prediction-section {
        grid-column: 1 / -1;
      }
    }
  }

  .section-card {
    overflow: hidden;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-lg);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-5);
      border-bottom: 1px solid var(--color-border-lighter);

      .card-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);

        .el-icon {
          margin-right: var(--spacing-2);
          color: var(--color-primary);
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-2);
        align-items: center;
      }
    }
  }

  .production-lines {
    padding: var(--spacing-5);

    .line-item {
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-3);
      border: 1px solid var(--color-border-lighter);
      border-radius: var(--radius-base);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--color-border-light);
        box-shadow: var(--shadow-sm);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .line-header {
        margin-bottom: var(--spacing-3);
      }

      .line-details {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--spacing-3);

        @media (width <= 768px) {
          flex-direction: column;
          gap: var(--spacing-2);
        }

        .detail-item {
          display: flex;
          flex-direction: column;

          .label {
            margin-bottom: var(--spacing-1);
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
          }

          .value {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
          }
        }
      }
    }
  }

  .prediction-content {
    padding: var(--spacing-5);

    .prediction-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-5);
      background: var(--color-bg-secondary);
      border-radius: var(--radius-base);

      .metric {
        .metric-value {
          font-size: 2rem;
          font-weight: var(--font-weight-bold);
          line-height: 1.2;
          color: var(--color-primary);
        }

        .metric-label {
          margin-top: var(--spacing-1);
          font-size: var(--font-size-sm);
          color: var(--color-text-secondary);
        }
      }

      .confidence {
        display: flex;
        flex-direction: column;
        align-items: center;

        .confidence-label {
          margin-top: var(--spacing-2);
          font-size: var(--font-size-xs);
          color: var(--color-text-secondary);
        }
      }
    }

    .recommendations {
      h4 {
        margin: 0 0 var(--spacing-3) 0;
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);
      }

      ul {
        padding-left: var(--spacing-4);
        margin: 0;

        li {
          margin-bottom: var(--spacing-2);
          font-size: var(--font-size-sm);
          line-height: 1.6;
          color: var(--color-text-secondary);

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  // 深色主题适配
  .dark {
    .production-dashboard {
      background: var(--color-bg-page);
    }

    .section-card {
      background: var(--color-bg-secondary);
      border-color: var(--color-border-dark);

      .card-header {
        border-bottom-color: var(--color-border-dark);
      }
    }

    .line-item {
      background: var(--color-bg-tertiary);
      border-color: var(--color-border-dark);

      &:hover {
        background: var(--color-bg-primary);
      }
    }

    .prediction-item {
      background: var(--color-bg-tertiary);
    }
  }
</style>
