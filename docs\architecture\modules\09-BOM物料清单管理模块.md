# BOM物料清单管理模块设计

## 1. 模块概述

### 1.1 模块定位
BOM（Bill of Materials）物料清单管理模块是IC封测CIM系统的核心数据管理模块，专门管理从产品设计到制造全过程的物料清单信息。该模块支持多层级BOM结构、版本控制、替代料管理、成本核算等复杂业务需求，是连接NPI、采购、生产、成本核算的关键纽带。

### 1.2 IC封测行业BOM特点
- **层级复杂性**：产品BOM → 封装BOM → 工艺BOM → 测试BOM → 包装BOM
- **版本众多**：同一产品的不同版本、不同客户定制版本
- **替代料复杂**：主料、第一替代料、第二替代料的层级管理
- **成本敏感性**：物料成本直接影响产品毛利和竞争力
- **供应商多样性**：一种物料可能有多个供应商，需要供应商绑定
- **质量等级差异**：汽车级、工业级、消费级物料的严格区分

### 1.3 核心业务价值
- **精确成本核算**：提供精确到小数点后4位的成本计算
- **供应链优化**：通过替代料和多供应商管理，降低供应风险
- **版本控制完整**：支持完整的BOM版本演进历史追踪
- **采购计划优化**：基于BOM自动生成准确的采购计划
- **成本分析深入**：多维度成本分析，支持价值工程决策

### 1.4 应用场景覆盖
```
BOM物料清单管理应用场景
├── 产品BOM管理
│   ├── 主BOM创建与维护
│   ├── BOM版本管理
│   ├── BOM变更管理
│   └── BOM审批流程
├── 层级BOM管理
│   ├── 封装BOM（Package BOM）
│   ├── 工艺BOM（Process BOM）
│   ├── 测试BOM（Test BOM）
│   └── 包装BOM（Packing BOM）
├── 替代料管理
│   ├── 主料定义
│   ├── 替代料层级管理
│   ├── 替代比例控制
│   └── 替代料切换管理
├── 成本管理
│   ├── 标准成本维护
│   ├── 实际成本跟踪
│   ├── 成本偏差分析
│   └── 目标成本管理
├── 供应商管理
│   ├── 供应商绑定
│   ├── 供应商评估
│   ├── 供应商切换
│   └── 多供应商策略
├── 采购计划
│   ├── MRP运算
│   ├── 采购建议生成
│   ├── 库存需求分析
│   └── 采购时机优化
├── 质量等级管理
│   ├── 汽车级物料管理
│   ├── 工业级物料管理
│   ├── 消费级物料管理
│   └── 质量等级追溯
└── 工程变更管理
    ├── ECN（Engineering Change Notice）
    ├── 变更影响分析
    ├── 变更成本分析
    └── 变更执行跟踪
```

## 2. IC封测专业BOM架构设计

### 2.1 技术架构
```
BOM物料清单管理模块架构
├── 多层级BOM管理引擎      # 支持复杂层级结构的BOM管理
├── 版本控制系统            # 完整的BOM版本控制和变更追踪
├── 替代料智能管理          # 动态替代料选择和优化
├── 成本核算引擎            # 精确的多维度成本计算
├── MRP需求计划系统         # 基于BOM的物料需求计划
├── 供应商协同平台          # 供应商信息和协作管理
├── 工程变更管理系统        # ECN流程和变更影响分析
├── 质量等级控制系统        # 不同质量等级物料的管控
├── BOM数据分析引擎         # BOM数据挖掘和优化分析
└── 外部系统集成接口        # ERP、PDM、SCM等系统集成
```

### 2.2 核心数据模型

#### 2.2.1 BOM主数据管理
```sql
-- 产品主BOM表
CREATE TABLE ic_product_bom (
    bom_id VARCHAR(30) PRIMARY KEY,
    product_code VARCHAR(100) NOT NULL,       -- 产品编码
    product_name VARCHAR(200),                -- 产品名称
    bom_version VARCHAR(20) NOT NULL,         -- BOM版本
    customer_id VARCHAR(30),                  -- 客户ID
    customer_part_number VARCHAR(100),        -- 客户料号
    package_type VARCHAR(50),                 -- 封装类型：QFP/BGA/CSP/FC等
    quality_grade ENUM('automotive','industrial','consumer','telecom'), -- 质量等级
    application_field VARCHAR(100),           -- 应用领域
    target_cost DECIMAL(12,4),               -- 目标成本
    standard_cost DECIMAL(12,4),             -- 标准成本
    latest_cost DECIMAL(12,4),               -- 最新成本
    bom_status ENUM('draft','review','approved','active','obsolete'), -- BOM状态
    effective_date DATE,                      -- 生效日期
    obsolete_date DATE,                       -- 失效日期
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_by VARCHAR(20),
    updated_at TIMESTAMP,
    approved_by VARCHAR(20),
    approved_at TIMESTAMP,
    
    UNIQUE KEY uk_product_version (product_code, bom_version),
    INDEX idx_customer_product (customer_id, product_code),
    INDEX idx_package_quality (package_type, quality_grade),
    INDEX idx_status_effective (bom_status, effective_date)
);

-- BOM明细表（多层级支持）
CREATE TABLE ic_bom_details (
    detail_id VARCHAR(30) PRIMARY KEY,
    bom_id VARCHAR(30) NOT NULL,             -- 主BOM ID
    parent_item_id VARCHAR(30),              -- 父项物料ID（层级结构）
    sequence_number INT,                      -- 序号
    item_code VARCHAR(100) NOT NULL,         -- 物料编码
    item_name VARCHAR(200),                  -- 物料名称
    item_type ENUM('raw_material','component','sub_assembly','finished_good'), -- 物料类型
    bom_level INT DEFAULT 0,                 -- BOM层级（0为顶层）
    quantity_per_unit DECIMAL(12,6),        -- 单位用量
    unit_of_measure VARCHAR(20),             -- 计量单位
    material_specification TEXT,             -- 物料规格
    supplier_id VARCHAR(30),                 -- 主供应商ID
    supplier_part_number VARCHAR(100),       -- 供应商料号
    lead_time_days INT,                      -- 采购周期(天)
    min_order_qty DECIMAL(12,3),            -- 最小订购量
    std_pack_qty DECIMAL(12,3),             -- 标准包装量
    unit_cost DECIMAL(12,4),                 -- 单价
    extended_cost DECIMAL(12,4),             -- 扩展成本
    cost_center VARCHAR(50),                 -- 成本中心
    usage_type ENUM('production','testing','packing','documentation'), -- 用途类型
    scrap_factor DECIMAL(5,4) DEFAULT 0,    -- 损耗率
    phantom_flag BOOLEAN DEFAULT FALSE,      -- 虚拟件标志
    critical_flag BOOLEAN DEFAULT FALSE,     -- 关键物料标志
    long_leadtime_flag BOOLEAN DEFAULT FALSE, -- 长周期物料标志
    substitution_allowed BOOLEAN DEFAULT TRUE, -- 是否允许替代
    engineering_change_level VARCHAR(20),    -- 工程变更等级
    comments TEXT,                           -- 备注
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_bom_level (bom_id, bom_level),
    INDEX idx_parent_item (parent_item_id),
    INDEX idx_item_code (item_code),
    INDEX idx_supplier (supplier_id),
    INDEX idx_critical (critical_flag, long_leadtime_flag)
);

-- BOM层级类型定义表
CREATE TABLE ic_bom_level_types (
    level_type_id VARCHAR(30) PRIMARY KEY,
    level_name VARCHAR(50) UNIQUE,           -- 层级名称
    level_code VARCHAR(20) UNIQUE,           -- 层级编码
    sequence_order INT,                      -- 排序
    description TEXT,                        -- 描述
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_sequence (sequence_order)
);

-- 预定义层级类型数据
INSERT INTO ic_bom_level_types (level_type_id, level_name, level_code, sequence_order, description) VALUES
('L001', 'Product BOM', 'PROD', 1, '产品主BOM，定义最终产品的组成'),
('L002', 'Package BOM', 'PKG', 2, '封装BOM，定义封装过程所需的物料'),
('L003', 'Process BOM', 'PROC', 3, '工艺BOM，定义各工艺步骤的物料消耗'),
('L004', 'Test BOM', 'TEST', 4, '测试BOM，定义测试过程的消耗品'),
('L005', 'Packing BOM', 'PACK', 5, '包装BOM，定义包装物料');
```

#### 2.2.2 替代料管理
```sql
-- 替代料关系表
CREATE TABLE ic_material_substitution (
    substitution_id VARCHAR(30) PRIMARY KEY,
    bom_detail_id VARCHAR(30),               -- BOM明细ID
    primary_item_code VARCHAR(100),          -- 主料编码
    substitute_item_code VARCHAR(100),       -- 替代料编码
    substitution_level INT,                  -- 替代级别：1=第一替代料，2=第二替代料
    substitution_ratio DECIMAL(8,4) DEFAULT 1.0, -- 替代比例
    substitution_type ENUM('form_fit_function','partial','temporary'), -- 替代类型
    quality_approval_status ENUM('pending','approved','rejected'), -- 质量认可状态
    customer_approval_status ENUM('pending','approved','rejected'), -- 客户认可状态
    cost_impact DECIMAL(10,4),              -- 成本影响
    lead_time_impact INT,                   -- 交期影响(天)
    effective_date DATE,                    -- 生效日期
    obsolete_date DATE,                     -- 失效日期
    approval_document VARCHAR(200),         -- 认可文件
    usage_restrictions TEXT,                -- 使用限制
    substitution_reason VARCHAR(500),       -- 替代原因
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    approved_by VARCHAR(20),
    approved_at TIMESTAMP,
    
    INDEX idx_primary_item (primary_item_code),
    INDEX idx_substitute_item (substitute_item_code),
    INDEX idx_level_status (substitution_level, quality_approval_status),
    UNIQUE KEY uk_substitution (bom_detail_id, substitute_item_code)
);

-- 替代料使用历史表
CREATE TABLE ic_substitution_usage_history (
    usage_id VARCHAR(30) PRIMARY KEY,
    substitution_id VARCHAR(30),            -- 替代料关系ID
    work_order_id VARCHAR(30),              -- 工单ID
    lot_number VARCHAR(100),                -- 批次号
    quantity_used DECIMAL(12,3),           -- 使用数量
    usage_reason ENUM('shortage','cost_optimization','quality_issue','planned'), -- 使用原因
    usage_date DATE,                        -- 使用日期
    operator_id VARCHAR(20),                -- 操作员
    quality_result ENUM('pass','fail','pending'), -- 质量结果
    cost_saving DECIMAL(10,2),             -- 成本节约
    performance_impact TEXT,                -- 性能影响
    
    INDEX idx_substitution_date (substitution_id, usage_date),
    INDEX idx_work_order (work_order_id),
    INDEX idx_usage_reason (usage_reason)
);
```

#### 2.2.3 版本控制管理
```sql
-- BOM版本控制表
CREATE TABLE ic_bom_version_control (
    version_id VARCHAR(30) PRIMARY KEY,
    bom_id VARCHAR(30),                     -- BOM主表ID
    version_number VARCHAR(20),             -- 版本号
    parent_version_id VARCHAR(30),         -- 父版本ID
    version_type ENUM('major','minor','patch','emergency'), -- 版本类型
    change_reason VARCHAR(500),             -- 变更原因
    change_description TEXT,                -- 变更描述
    change_impact_analysis TEXT,            -- 变更影响分析
    cost_impact DECIMAL(12,4),             -- 成本影响
    effectivity_date DATE,                  -- 生效日期
    phase_out_date DATE,                    -- 淘汰日期
    version_status ENUM('draft','review','approved','active','superseded','obsolete'), -- 版本状态
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    approved_by VARCHAR(20),
    approved_at TIMESTAMP,
    
    INDEX idx_bom_version (bom_id, version_number),
    INDEX idx_parent_version (parent_version_id),
    INDEX idx_status_date (version_status, effectivity_date)
);

-- BOM变更记录表
CREATE TABLE ic_bom_change_history (
    change_id VARCHAR(30) PRIMARY KEY,
    bom_id VARCHAR(30),                     -- BOM主表ID
    version_from VARCHAR(20),               -- 源版本
    version_to VARCHAR(20),                 -- 目标版本
    change_type ENUM('add','modify','delete','substitute'), -- 变更类型
    item_code VARCHAR(100),                 -- 涉及物料
    field_changed VARCHAR(100),             -- 变更字段
    old_value TEXT,                         -- 旧值
    new_value TEXT,                         -- 新值
    change_reason VARCHAR(500),             -- 变更原因
    change_date TIMESTAMP,                  -- 变更时间
    changed_by VARCHAR(20),                 -- 变更人
    approval_required BOOLEAN DEFAULT TRUE, -- 是否需要审批
    approved_by VARCHAR(20),                -- 审批人
    approved_at TIMESTAMP,                  -- 审批时间
    
    INDEX idx_bom_versions (bom_id, version_from, version_to),
    INDEX idx_item_code (item_code),
    INDEX idx_change_type (change_type),
    INDEX idx_change_date (change_date)
);
```

#### 2.2.4 成本管理
```sql
-- 物料成本主表
CREATE TABLE ic_material_costs (
    cost_id VARCHAR(30) PRIMARY KEY,
    item_code VARCHAR(100) NOT NULL,        -- 物料编码
    supplier_id VARCHAR(30),                -- 供应商ID
    cost_type ENUM('standard','actual','target','quote'), -- 成本类型
    currency_code VARCHAR(10) DEFAULT 'CNY', -- 币种
    unit_cost DECIMAL(12,4),                -- 单位成本
    cost_effective_date DATE,               -- 成本生效日期
    cost_expiry_date DATE,                  -- 成本失效日期
    cost_source ENUM('supplier_quote','purchase_order','market_price','engineering_estimate'), -- 成本来源
    purchase_volume_min DECIMAL(12,3),     -- 最小采购量
    purchase_volume_max DECIMAL(12,3),     -- 最大采购量
    price_break_info JSON,                  -- 价格阶梯信息
    total_cost_ownership DECIMAL(12,4),    -- 总拥有成本TCO
    freight_cost DECIMAL(8,4),             -- 运费
    duty_cost DECIMAL(8,4),                -- 关税
    handling_cost DECIMAL(8,4),            -- 处理费用
    quality_cost DECIMAL(8,4),             -- 质量成本
    inventory_carrying_cost DECIMAL(8,4),   -- 库存持有成本
    last_updated_at TIMESTAMP,
    updated_by VARCHAR(20),
    
    UNIQUE KEY uk_item_supplier_type (item_code, supplier_id, cost_type),
    INDEX idx_item_cost_type (item_code, cost_type),
    INDEX idx_effective_date (cost_effective_date),
    INDEX idx_supplier (supplier_id)
);

-- BOM成本汇总表
CREATE TABLE ic_bom_cost_rollup (
    rollup_id VARCHAR(30) PRIMARY KEY,
    bom_id VARCHAR(30),                     -- BOM主表ID
    version_number VARCHAR(20),             -- BOM版本
    cost_type ENUM('standard','actual','target'), -- 成本类型
    calculation_date DATE,                  -- 计算日期
    raw_material_cost DECIMAL(12,4),       -- 原材料成本
    component_cost DECIMAL(12,4),          -- 组件成本
    sub_assembly_cost DECIMAL(12,4),       -- 子装配成本
    direct_labor_cost DECIMAL(12,4),       -- 直接人工成本
    manufacturing_overhead DECIMAL(12,4),   -- 制造费用
    total_material_cost DECIMAL(12,4),     -- 物料总成本
    total_manufacturing_cost DECIMAL(12,4), -- 制造总成本
    margin_percentage DECIMAL(5,2),        -- 毛利率%
    target_selling_price DECIMAL(12,4),    -- 目标销售价格
    cost_variance DECIMAL(12,4),           -- 成本偏差
    variance_percentage DECIMAL(5,2),      -- 偏差百分比
    calculation_method VARCHAR(100),        -- 计算方法
    calculation_parameters JSON,            -- 计算参数
    calculated_by VARCHAR(20),
    calculated_at TIMESTAMP,
    
    INDEX idx_bom_version_type (bom_id, version_number, cost_type),
    INDEX idx_calculation_date (calculation_date)
);
```

## 3. BOM管理引擎

### 3.1 多层级BOM处理引擎
```java
@Service
public class MultilevelBOMService {
    
    @Autowired
    private BOMRepository bomRepository;
    
    @Autowired
    private BOMDetailRepository bomDetailRepository;
    
    @Autowired
    private SubstitutionService substitutionService;
    
    /**
     * 创建多层级BOM
     */
    public ICProductBOM createMultilevelBOM(MultilevelBOMRequest request) {
        // 1. 验证BOM结构完整性
        validateBOMStructure(request);
        
        // 2. 创建主BOM
        ICProductBOM mainBOM = createMainBOM(request);
        
        // 3. 创建层级BOM明细
        List<ICBOMDetail> allDetails = new ArrayList<>();
        
        // 创建产品层BOM
        List<ICBOMDetail> productLevelDetails = createBOMLevel(
            mainBOM.getBomId(), request.getProductBOM(), 0, null);
        allDetails.addAll(productLevelDetails);
        
        // 创建封装层BOM
        if (request.getPackageBOM() != null) {
            List<ICBOMDetail> packageLevelDetails = createBOMLevel(
                mainBOM.getBomId(), request.getPackageBOM(), 1, "PKG");
            allDetails.addAll(packageLevelDetails);
        }
        
        // 创建工艺层BOM
        if (request.getProcessBOM() != null) {
            List<ICBOMDetail> processLevelDetails = createBOMLevel(
                mainBOM.getBomId(), request.getProcessBOM(), 2, "PROC");
            allDetails.addAll(processLevelDetails);
        }
        
        // 创建测试层BOM
        if (request.getTestBOM() != null) {
            List<ICBOMDetail> testLevelDetails = createBOMLevel(
                mainBOM.getBomId(), request.getTestBOM(), 3, "TEST");
            allDetails.addAll(testLevelDetails);
        }
        
        // 创建包装层BOM
        if (request.getPackingBOM() != null) {
            List<ICBOMDetail> packingLevelDetails = createBOMLevel(
                mainBOM.getBomId(), request.getPackingBOM(), 4, "PACK");
            allDetails.addAll(packingLevelDetails);
        }
        
        // 4. 批量保存BOM明细
        bomDetailRepository.saveAll(allDetails);
        
        // 5. 计算BOM成本
        BOMCostRollup costRollup = calculateBOMCost(mainBOM.getBomId());
        
        // 6. 更新主BOM成本信息
        mainBOM.setLatestCost(costRollup.getTotalMaterialCost());
        bomRepository.save(mainBOM);
        
        // 7. 创建默认替代料关系
        createDefaultSubstitutions(allDetails);
        
        return mainBOM;
    }
    
    /**
     * BOM展开算法（递归展开所有层级）
     */
    public BOMExplosionResult explodeBOM(String bomId, int maxLevels, boolean includeSubstitutes) {
        ICProductBOM mainBOM = bomRepository.findById(bomId)
            .orElseThrow(() -> new EntityNotFoundException("BOM不存在"));
        
        BOMExplosionResult result = new BOMExplosionResult();
        result.setBomId(bomId);
        result.setProductCode(mainBOM.getProductCode());
        result.setExplosionDate(LocalDateTime.now());
        result.setMaxLevels(maxLevels);
        
        // 递归展开BOM结构
        List<BOMExplosionItem> explodedItems = new ArrayList<>();
        explodeBOMRecursive(bomId, null, 0, maxLevels, explodedItems, 1.0);
        
        result.setExplodedItems(explodedItems);
        
        // 如果包含替代料，添加替代料信息
        if (includeSubstitutes) {
            addSubstituteInformation(result);
        }
        
        // 计算汇总信息
        calculateExplosionSummary(result);
        
        return result;
    }
    
    private void explodeBOMRecursive(String bomId, String parentItemId, int currentLevel, 
                                   int maxLevels, List<BOMExplosionItem> result, 
                                   double cumulativeQuantity) {
        if (currentLevel > maxLevels) {
            return;
        }
        
        List<ICBOMDetail> details = bomDetailRepository.findByBomIdAndParentItemId(bomId, parentItemId);
        
        for (ICBOMDetail detail : details) {
            BOMExplosionItem item = new BOMExplosionItem();
            item.setLevel(currentLevel);
            item.setItemCode(detail.getItemCode());
            item.setItemName(detail.getItemName());
            item.setQuantityPerUnit(detail.getQuantityPerUnit());
            item.setCumulativeQuantity(cumulativeQuantity * detail.getQuantityPerUnit().doubleValue());
            item.setUnitOfMeasure(detail.getUnitOfMeasure());
            item.setUnitCost(detail.getUnitCost());
            item.setExtendedCost(detail.getUnitCost().multiply(
                BigDecimal.valueOf(item.getCumulativeQuantity())));
            item.setSupplierPartNumber(detail.getSupplierPartNumber());
            item.setLeadTimeDays(detail.getLeadTimeDays());
            item.setCriticalFlag(detail.getCriticalFlag());
            
            result.add(item);
            
            // 递归处理子项
            explodeBOMRecursive(bomId, detail.getDetailId(), currentLevel + 1, 
                              maxLevels, result, item.getCumulativeQuantity());
        }
    }
    
    /**
     * BOM结构验证
     */
    public BOMValidationResult validateBOMStructure(String bomId) {
        BOMValidationResult result = new BOMValidationResult();
        result.setBomId(bomId);
        result.setValidationDate(LocalDateTime.now());
        
        List<BOMValidationIssue> issues = new ArrayList<>();
        
        // 1. 循环引用检查
        List<String> circularReferences = checkCircularReferences(bomId);
        if (!circularReferences.isEmpty()) {
            issues.add(new BOMValidationIssue(
                ValidationSeverity.ERROR, 
                "循环引用", 
                "检测到循环引用: " + String.join(" -> ", circularReferences)
            ));
        }
        
        // 2. 孤立节点检查
        List<String> orphanNodes = checkOrphanNodes(bomId);
        for (String orphanNode : orphanNodes) {
            issues.add(new BOMValidationIssue(
                ValidationSeverity.WARNING,
                "孤立节点",
                "物料 " + orphanNode + " 没有父节点"
            ));
        }
        
        // 3. 物料主数据完整性检查
        List<String> invalidMaterials = checkMaterialMasterData(bomId);
        for (String invalidMaterial : invalidMaterials) {
            issues.add(new BOMValidationIssue(
                ValidationSeverity.ERROR,
                "物料主数据缺失",
                "物料 " + invalidMaterial + " 的主数据不完整或不存在"
            ));
        }
        
        // 4. 成本数据完整性检查
        List<String> missingCostItems = checkCostDataCompleteness(bomId);
        for (String missingCostItem : missingCostItems) {
            issues.add(new BOMValidationIssue(
                ValidationSeverity.WARNING,
                "成本数据缺失",
                "物料 " + missingCostItem + " 缺少成本信息"
            ));
        }
        
        // 5. 供应商信息检查
        List<String> missingSupplierItems = checkSupplierInformation(bomId);
        for (String missingSupplierItem : missingSupplierItems) {
            issues.add(new BOMValidationIssue(
                ValidationSeverity.WARNING,
                "供应商信息缺失",
                "物料 " + missingSupplierItem + " 缺少供应商信息"
            ));
        }
        
        result.setIssues(issues);
        result.setValidationPassed(issues.stream().noneMatch(
            issue -> issue.getSeverity() == ValidationSeverity.ERROR));
        
        return result;
    }
    
    /**
     * BOM比较分析
     */
    public BOMComparisonResult compareBOMVersions(String bomId, String version1, String version2) {
        BOMComparisonResult result = new BOMComparisonResult();
        result.setBomId(bomId);
        result.setVersion1(version1);
        result.setVersion2(version2);
        result.setComparisonDate(LocalDateTime.now());
        
        // 获取两个版本的BOM展开结构
        BOMExplosionResult explosion1 = explodeBOM(bomId + ":" + version1, 10, false);
        BOMExplosionResult explosion2 = explodeBOM(bomId + ":" + version2, 10, false);
        
        Map<String, BOMExplosionItem> items1 = explosion1.getExplodedItems().stream()
            .collect(Collectors.toMap(BOMExplosionItem::getItemCode, item -> item));
        Map<String, BOMExplosionItem> items2 = explosion2.getExplodedItems().stream()
            .collect(Collectors.toMap(BOMExplosionItem::getItemCode, item -> item));
        
        List<BOMDifference> differences = new ArrayList<>();
        
        // 检查新增的物料
        for (String itemCode : items2.keySet()) {
            if (!items1.containsKey(itemCode)) {
                differences.add(new BOMDifference(
                    DifferenceType.ADDED,
                    itemCode,
                    null,
                    items2.get(itemCode)
                ));
            }
        }
        
        // 检查删除的物料
        for (String itemCode : items1.keySet()) {
            if (!items2.containsKey(itemCode)) {
                differences.add(new BOMDifference(
                    DifferenceType.REMOVED,
                    itemCode,
                    items1.get(itemCode),
                    null
                ));
            }
        }
        
        // 检查修改的物料
        for (String itemCode : items1.keySet()) {
            if (items2.containsKey(itemCode)) {
                BOMExplosionItem item1 = items1.get(itemCode);
                BOMExplosionItem item2 = items2.get(itemCode);
                
                if (!item1.equals(item2)) {
                    differences.add(new BOMDifference(
                        DifferenceType.MODIFIED,
                        itemCode,
                        item1,
                        item2
                    ));
                }
            }
        }
        
        result.setDifferences(differences);
        
        // 计算成本影响
        BigDecimal costImpact = calculateCostImpact(differences);
        result.setCostImpact(costImpact);
        
        return result;
    }
}
```

### 3.2 替代料智能管理
```java
@Service
public class IntelligentSubstitutionService {
    
    @Autowired
    private SubstitutionRepository substitutionRepository;
    
    @Autowired
    private MaterialCostService materialCostService;
    
    @Autowired
    private SupplierService supplierService;
    
    /**
     * 智能替代料推荐
     */
    public List<SubstitutionRecommendation> recommendSubstitutions(String itemCode, 
                                                                 SubstitutionContext context) {
        // 1. 获取当前物料信息
        MaterialInfo currentMaterial = materialService.getMaterialInfo(itemCode);
        
        // 2. 基于相似性搜索潜在替代料
        List<MaterialInfo> candidateMaterials = findSimilarMaterials(currentMaterial, context);
        
        List<SubstitutionRecommendation> recommendations = new ArrayList<>();
        
        for (MaterialInfo candidate : candidateMaterials) {
            SubstitutionRecommendation recommendation = new SubstitutionRecommendation();
            recommendation.setCurrentItemCode(itemCode);
            recommendation.setSubstituteItemCode(candidate.getItemCode());
            
            // 3. 技术兼容性分析
            TechnicalCompatibility techCompatibility = analyzeTechnicalCompatibility(
                currentMaterial, candidate);
            recommendation.setTechnicalCompatibility(techCompatibility);
            
            // 4. 成本影响分析
            CostImpactAnalysis costImpact = analyzeCostImpact(currentMaterial, candidate, context);
            recommendation.setCostImpact(costImpact);
            
            // 5. 供应链风险评估
            SupplyChainRisk supplyRisk = assessSupplyChainRisk(candidate, context);
            recommendation.setSupplyChainRisk(supplyRisk);
            
            // 6. 质量风险评估
            QualityRisk qualityRisk = assessQualityRisk(currentMaterial, candidate, context);
            recommendation.setQualityRisk(qualityRisk);
            
            // 7. 综合推荐评分
            double overallScore = calculateOverallScore(techCompatibility, costImpact, 
                                                       supplyRisk, qualityRisk);
            recommendation.setOverallScore(overallScore);
            
            // 8. 实施复杂度评估
            ImplementationComplexity complexity = assessImplementationComplexity(
                currentMaterial, candidate, context);
            recommendation.setImplementationComplexity(complexity);
            
            if (overallScore > 0.6) { // 推荐阈值
                recommendations.add(recommendation);
            }
        }
        
        // 按综合评分排序
        recommendations.sort((a, b) -> Double.compare(b.getOverallScore(), a.getOverallScore()));
        
        return recommendations;
    }
    
    /**
     * 动态替代料选择
     */
    public SubstitutionDecision selectOptimalSubstitute(String bomDetailId, 
                                                       SelectionCriteria criteria) {
        ICBOMDetail bomDetail = bomDetailRepository.findById(bomDetailId)
            .orElseThrow(() -> new EntityNotFoundException("BOM明细不存在"));
        
        // 获取所有可用的替代料
        List<ICMaterialSubstitution> availableSubstitutes = substitutionRepository
            .findByBomDetailIdAndQualityApprovalStatus(bomDetailId, ApprovalStatus.APPROVED);
        
        if (availableSubstitutes.isEmpty()) {
            return SubstitutionDecision.noSubstituteAvailable(bomDetailId);
        }
        
        SubstitutionDecision bestDecision = null;
        double bestScore = -1;
        
        for (ICMaterialSubstitution substitute : availableSubstitutes) {
            // 1. 检查库存可用性
            InventoryAvailability inventory = checkInventoryAvailability(
                substitute.getSubstituteItemCode(), criteria.getRequiredQuantity());
            
            if (inventory.getAvailableQuantity().compareTo(criteria.getRequiredQuantity()) < 0 
                && !criteria.isAllowPartialSubstitution()) {
                continue; // 库存不足且不允许部分替代
            }
            
            // 2. 计算决策评分
            double score = calculateSubstitutionScore(substitute, criteria, inventory);
            
            if (score > bestScore) {
                bestScore = score;
                bestDecision = createSubstitutionDecision(substitute, inventory, score);
            }
        }
        
        return bestDecision != null ? bestDecision : 
               SubstitutionDecision.noSuitableSubstitute(bomDetailId);
    }
    
    /**
     * 替代料性能监控
     */
    public SubstitutionPerformanceReport monitorSubstitutionPerformance(String substitutionId, 
                                                                       LocalDate fromDate, 
                                                                       LocalDate toDate) {
        ICMaterialSubstitution substitution = substitutionRepository.findById(substitutionId)
            .orElseThrow(() -> new EntityNotFoundException("替代料关系不存在"));
        
        // 1. 获取使用历史记录
        List<ICSubstitutionUsageHistory> usageHistory = substitutionUsageRepository
            .findBySubstitutionIdAndUsageDateBetween(substitutionId, fromDate, toDate);
        
        SubstitutionPerformanceReport report = new SubstitutionPerformanceReport();
        report.setSubstitutionId(substitutionId);
        report.setPrimaryItem(substitution.getPrimaryItemCode());
        report.setSubstituteItem(substitution.getSubstituteItemCode());
        report.setReportPeriod(new DateRange(fromDate, toDate));
        
        // 2. 使用频率分析
        int totalUsages = usageHistory.size();
        BigDecimal totalQuantityUsed = usageHistory.stream()
            .map(ICSubstitutionUsageHistory::getQuantityUsed)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        report.setTotalUsages(totalUsages);
        report.setTotalQuantityUsed(totalQuantityUsed);
        report.setAverageQuantityPerUsage(
            totalUsages > 0 ? totalQuantityUsed.divide(BigDecimal.valueOf(totalUsages), 
                                                      RoundingMode.HALF_UP) : BigDecimal.ZERO);
        
        // 3. 质量表现分析
        long passedUsages = usageHistory.stream()
            .mapToLong(usage -> QualityResult.PASS.equals(usage.getQualityResult()) ? 1 : 0)
            .sum();
        
        double qualityPerformance = totalUsages > 0 ? 
            (double) passedUsages / totalUsages * 100 : 0;
        report.setQualityPerformance(qualityPerformance);
        
        // 4. 成本节约分析
        BigDecimal totalCostSaving = usageHistory.stream()
            .map(ICSubstitutionUsageHistory::getCostSaving)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        report.setTotalCostSaving(totalCostSaving);
        report.setCostSavingPercentage(calculateCostSavingPercentage(substitution, totalCostSaving));
        
        // 5. 使用原因分析
        Map<String, Long> usageReasonStats = usageHistory.stream()
            .collect(Collectors.groupingBy(
                usage -> usage.getUsageReason().name(),
                Collectors.counting()
            ));
        report.setUsageReasonDistribution(usageReasonStats);
        
        // 6. 趋势分析
        List<UsageTrendData> trendData = generateUsageTrendData(usageHistory, fromDate, toDate);
        report.setUsageTrend(trendData);
        
        // 7. 问题和建议
        List<String> issues = identifyPerformanceIssues(report);
        List<String> recommendations = generateImprovementRecommendations(report, issues);
        
        report.setIdentifiedIssues(issues);
        report.setRecommendations(recommendations);
        
        return report;
    }
}
```

### 3.3 成本核算引擎
```java
@Service
public class BOMCostCalculationService {
    
    @Autowired
    private MaterialCostRepository materialCostRepository;
    
    @Autowired
    private BOMCostRollupRepository costRollupRepository;
    
    @Autowired
    private CostingParameterService costingParameterService;
    
    /**
     * BOM成本滚算（支持多种成本类型）
     */
    public BOMCostRollup calculateBOMCost(String bomId, CostType costType, LocalDate costDate) {
        ICProductBOM bom = bomRepository.findById(bomId)
            .orElseThrow(() -> new EntityNotFoundException("BOM不存在"));
        
        BOMCostRollup rollup = new BOMCostRollup();
        rollup.setRollupId(IdGenerator.generateId());
        rollup.setBomId(bomId);
        rollup.setVersionNumber(bom.getBomVersion());
        rollup.setCostType(costType);
        rollup.setCalculationDate(costDate);
        
        // 1. 展开BOM结构
        BOMExplosionResult explosion = bomService.explodeBOM(bomId, 10, false);
        
        // 2. 按成本分类汇总
        BigDecimal rawMaterialCost = BigDecimal.ZERO;
        BigDecimal componentCost = BigDecimal.ZERO;
        BigDecimal subAssemblyCost = BigDecimal.ZERO;
        
        for (BOMExplosionItem item : explosion.getExplodedItems()) {
            // 获取物料成本
            BigDecimal itemUnitCost = getMaterialCost(item.getItemCode(), costType, costDate);
            BigDecimal itemTotalCost = itemUnitCost.multiply(
                BigDecimal.valueOf(item.getCumulativeQuantity()));
            
            // 根据物料类型分类
            switch (item.getItemType()) {
                case RAW_MATERIAL:
                    rawMaterialCost = rawMaterialCost.add(itemTotalCost);
                    break;
                case COMPONENT:
                    componentCost = componentCost.add(itemTotalCost);
                    break;
                case SUB_ASSEMBLY:
                    subAssemblyCost = subAssemblyCost.add(itemTotalCost);
                    break;
            }
        }
        
        rollup.setRawMaterialCost(rawMaterialCost);
        rollup.setComponentCost(componentCost);
        rollup.setSubAssemblyCost(subAssemblyCost);
        
        // 3. 计算制造成本
        ManufacturingCostComponents mfgCosts = calculateManufacturingCosts(bom);
        rollup.setDirectLaborCost(mfgCosts.getDirectLaborCost());
        rollup.setManufacturingOverhead(mfgCosts.getManufacturingOverhead());
        
        // 4. 计算总成本
        BigDecimal totalMaterialCost = rawMaterialCost.add(componentCost).add(subAssemblyCost);
        BigDecimal totalManufacturingCost = totalMaterialCost
            .add(mfgCosts.getDirectLaborCost())
            .add(mfgCosts.getManufacturingOverhead());
        
        rollup.setTotalMaterialCost(totalMaterialCost);
        rollup.setTotalManufacturingCost(totalManufacturingCost);
        
        // 5. 计算成本偏差（如果是实际成本）
        if (costType == CostType.ACTUAL) {
            BOMCostRollup standardCostRollup = getLatestStandardCostRollup(bomId);
            if (standardCostRollup != null) {
                BigDecimal costVariance = totalManufacturingCost
                    .subtract(standardCostRollup.getTotalManufacturingCost());
                BigDecimal variancePercentage = costVariance
                    .divide(standardCostRollup.getTotalManufacturingCost(), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                
                rollup.setCostVariance(costVariance);
                rollup.setVariancePercentage(variancePercentage);
            }
        }
        
        // 6. 设置计算方法和参数
        rollup.setCalculationMethod("BOM_EXPLOSION_ROLLUP");
        rollup.setCalculationParameters(createCalculationParameters(costType, costDate));
        rollup.setCalculatedBy(getCurrentUserId());
        rollup.setCalculatedAt(LocalDateTime.now());
        
        return costRollupRepository.save(rollup);
    }
    
    /**
     * 目标成本分解
     */
    public TargetCostBreakdown breakdownTargetCost(String bomId, BigDecimal targetTotalCost) {
        ICProductBOM bom = bomRepository.findById(bomId)
            .orElseThrow(() -> new EntityNotFoundException("BOM不存在"));
        
        TargetCostBreakdown breakdown = new TargetCostBreakdown();
        breakdown.setBomId(bomId);
        breakdown.setTargetTotalCost(targetTotalCost);
        
        // 1. 获取当前成本结构
        BOMCostRollup currentCostRollup = getLatestStandardCostRollup(bomId);
        if (currentCostRollup == null) {
            throw new IllegalStateException("无法获取当前成本结构");
        }
        
        // 2. 计算成本结构比例
        BigDecimal currentTotal = currentCostRollup.getTotalManufacturingCost();
        double materialRatio = currentCostRollup.getTotalMaterialCost()
            .divide(currentTotal, 4, RoundingMode.HALF_UP).doubleValue();
        double laborRatio = currentCostRollup.getDirectLaborCost()
            .divide(currentTotal, 4, RoundingMode.HALF_UP).doubleValue();
        double overheadRatio = currentCostRollup.getManufacturingOverhead()
            .divide(currentTotal, 4, RoundingMode.HALF_UP).doubleValue();
        
        // 3. 按比例分配目标成本
        BigDecimal targetMaterialCost = targetTotalCost.multiply(BigDecimal.valueOf(materialRatio));
        BigDecimal targetLaborCost = targetTotalCost.multiply(BigDecimal.valueOf(laborRatio));
        BigDecimal targetOverheadCost = targetTotalCost.multiply(BigDecimal.valueOf(overheadRatio));
        
        breakdown.setTargetMaterialCost(targetMaterialCost);
        breakdown.setTargetLaborCost(targetLaborCost);
        breakdown.setTargetOverheadCost(targetOverheadCost);
        
        // 4. 分解到具体物料
        BOMExplosionResult explosion = bomService.explodeBOM(bomId, 10, false);
        List<ItemTargetCost> itemTargetCosts = new ArrayList<>();
        
        for (BOMExplosionItem item : explosion.getExplodedItems()) {
            if (isDirectMaterial(item)) {
                BigDecimal currentItemCost = item.getExtendedCost();
                double itemCostRatio = currentItemCost
                    .divide(currentCostRollup.getTotalMaterialCost(), 6, RoundingMode.HALF_UP)
                    .doubleValue();
                
                BigDecimal itemTargetCost = targetMaterialCost.multiply(
                    BigDecimal.valueOf(itemCostRatio));
                BigDecimal itemTargetUnitCost = itemTargetCost.divide(
                    BigDecimal.valueOf(item.getCumulativeQuantity()), 4, RoundingMode.HALF_UP);
                
                ItemTargetCost targetCost = new ItemTargetCost();
                targetCost.setItemCode(item.getItemCode());
                targetCost.setCurrentUnitCost(item.getUnitCost());
                targetCost.setTargetUnitCost(itemTargetUnitCost);
                targetCost.setCostReductionRequired(
                    item.getUnitCost().subtract(itemTargetUnitCost));
                targetCost.setCostReductionPercentage(
                    targetCost.getCostReductionRequired()
                        .divide(item.getUnitCost(), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100))
                );
                
                itemTargetCosts.add(targetCost);
            }
        }
        
        breakdown.setItemTargetCosts(itemTargetCosts);
        
        // 5. 识别成本改善机会
        List<CostImprovementOpportunity> opportunities = identifyCostImprovementOpportunities(
            breakdown, itemTargetCosts);
        breakdown.setImprovementOpportunities(opportunities);
        
        return breakdown;
    }
    
    /**
     * 价值工程分析
     */
    public ValueEngineeringAnalysis performValueEngineering(String bomId, 
                                                           ValueEngineeringCriteria criteria) {
        ValueEngineeringAnalysis analysis = new ValueEngineeringAnalysis();
        analysis.setBomId(bomId);
        analysis.setAnalysisDate(LocalDateTime.now());
        analysis.setCriteria(criteria);
        
        // 1. 获取BOM展开和成本结构
        BOMExplosionResult explosion = bomService.explodeBOM(bomId, 10, false);
        BOMCostRollup costRollup = getLatestStandardCostRollup(bomId);
        
        List<ValueAnalysisItem> valueItems = new ArrayList<>();
        
        for (BOMExplosionItem item : explosion.getExplodedItems()) {
            ValueAnalysisItem valueItem = new ValueAnalysisItem();
            valueItem.setItemCode(item.getItemCode());
            valueItem.setItemName(item.getItemName());
            valueItem.setCurrentCost(item.getExtendedCost());
            valueItem.setQuantity(BigDecimal.valueOf(item.getCumulativeQuantity()));
            
            // 2. 功能分析
            List<ItemFunction> functions = analyzeFunctions(item.getItemCode(), criteria);
            valueItem.setFunctions(functions);
            
            // 3. 价值评分（成本 vs 功能重要性）
            double functionalImportance = calculateFunctionalImportance(functions);
            double costPercentage = item.getExtendedCost()
                .divide(costRollup.getTotalMaterialCost(), 6, RoundingMode.HALF_UP)
                .doubleValue() * 100;
            
            double valueIndex = functionalImportance / costPercentage;
            valueItem.setValueIndex(valueIndex);
            
            // 4. 识别改善机会
            if (valueIndex < criteria.getLowValueThreshold()) {
                List<ValueImprovementIdea> ideas = generateImprovementIdeas(valueItem, criteria);
                valueItem.setImprovementIdeas(ideas);
                valueItem.setPriority(ValueImprovementPriority.HIGH);
            } else if (valueIndex < criteria.getMediumValueThreshold()) {
                valueItem.setPriority(ValueImprovementPriority.MEDIUM);
            } else {
                valueItem.setPriority(ValueImprovementPriority.LOW);
            }
            
            valueItems.add(valueItem);
        }
        
        analysis.setValueItems(valueItems);
        
        // 5. 汇总分析结果
        summarizeValueAnalysis(analysis);
        
        return analysis;
    }
}
```

---

*BOM物料清单管理模块为IC封测CIM系统提供了完整的多层级BOM管理、智能替代料优化、精确成本核算等核心功能，是连接产品设计与制造执行的关键数据枢纽*