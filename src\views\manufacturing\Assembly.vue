<template>
  <div class="assembly-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title">
        <h1>封装工艺控制</h1>
        <p>IC Assembly Process Control</p>
      </div>
      <div class="page-actions">
        <CButton
:loading="loading" @click="refreshData"
>
          <el-icon><RefreshRight /></el-icon>
          刷新数据
        </CButton>
        <CButton type="primary" @click="openNewLotDialog">
          <el-icon><Plus /></el-icon>
          新建批次
        </CButton>
      </div>
    </div>

    <!-- 实时概览 -->
    <div class="overview-section">
      <div class="kpi-cards">
        <div class="kpi-card">
          <div class="kpi-value">
            {{ dashboardData.assembly.activePackages }}
          </div>
          <div class="kpi-label">在制批次</div>
          <div class="kpi-trend up">+5.2%</div>
        </div>
        <div class="kpi-card">
          <div class="kpi-value">{{ dashboardData.assembly.avgCycleTime.toFixed(1) }}h</div>
          <div class="kpi-label">平均周期</div>
          <div class="kpi-trend down">-8.3%</div>
        </div>
        <div class="kpi-card">
          <div class="kpi-value">{{ (dashboardData.assembly.defectRate * 100).toFixed(2) }}%</div>
          <div class="kpi-label">缺陷率</div>
          <div class="kpi-trend down">-2.1%</div>
        </div>
        <div class="kpi-card">
          <div class="kpi-value">
            {{ formatNumber(dashboardData.assembly.throughput) }}
          </div>
          <div class="kpi-label">日产能 (Units)</div>
          <div class="kpi-trend up">+12.5%</div>
        </div>
      </div>
    </div>

    <div class="main-content">
      <!-- 左侧：工艺流程监控 -->
      <div class="process-section">
        <div class="section-header">
          <h2>工艺流程监控</h2>
          <div class="process-filter">
            <el-select v-model="selectedProcess" placeholder="选择工艺" size="small">
              <el-option label="全部工艺" value="all" />
              <el-option label="贴片 (Die Attach)" value="die_attach" />
              <el-option label="线键合 (Wire Bond)" value="wire_bond" />
              <el-option label="塑封 (Molding)" value="molding" />
              <el-option label="切筋成型" value="trim_form" />
            </el-select>
          </div>
        </div>

        <!-- 工艺设备状态 -->
        <div class="equipment-grid">
          <ManufacturingEquipmentCard
            v-for="equipment in filteredEquipments"
            :key="equipment.id"
            :equipment="equipment"
            @view-details="viewEquipmentDetails"
            @control-equipment="controlEquipment"
          />
        </div>

        <!-- 工艺参数监控 -->
        <div class="process-parameters-section">
          <ProcessParameterTable
            :parameters="processParameters"
            :loading="parameterLoading"
            @refresh="refreshProcessParameters"
            @edit-parameter="editProcessParameter"
            @view-history="viewParameterHistory"
            @export-data="exportParameterData"
          />
        </div>
      </div>

      <!-- 右侧：生产批次管理 -->
      <div class="lot-section">
        <div class="section-header">
          <h2>生产批次管理</h2>
          <div class="lot-filters">
            <el-select v-model="lotStatusFilter" placeholder="状态筛选" size="small" clearable>
              <el-option label="计划中" value="planned" />
              <el-option label="已发布" value="released" />
              <el-option label="进行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="暂停" value="hold" />
            </el-select>
            <el-select v-model="packageTypeFilter" placeholder="封装类型" size="small" clearable>
              <el-option label="QFP" value="QFP" />
              <el-option label="BGA" value="BGA" />
              <el-option label="CSP" value="CSP" />
              <el-option label="FC" value="FC" />
            </el-select>
          </div>
        </div>

        <!-- 批次列表 -->
        <div class="lot-list">
          <div
            v-for="record in filteredAssemblyRecords"
            :key="record.id"
            class="lot-card"
            :class="`lot-card--${record.status}`"
            @click="selectLot(record)"
          >
            <div class="lot-card__header">
              <div class="lot-info">
                <div class="lot-number">
                  {{ record.lotNumber }}
                </div>
                <div class="package-info">
{{ record.packageType }} - {{ record.customerPN }}
</div>
              </div>
              <el-tag :type="getLotStatusType(record.status)" size="small">
                {{ getLotStatusText(record.status) }}
              </el-tag>
            </div>

            <div class="lot-card__content">
              <div class="lot-progress">
                <div class="progress-info">
                  <span>进度: {{ record.processedQty }} / {{ record.quantity }}</span>
                  <span>{{ ((record.processedQty / record.quantity) * 100).toFixed(1) }}%</span>
                </div>
                <el-progress
                  :percentage="(record.processedQty / record.quantity) * 100"
                  :stroke-width="8"
                  :show-text="false"
                />
              </div>

              <div class="process-status">
                <div class="process-step" :class="getProcessStepClass('die_attach', record)">
                  <span class="step-name">贴片</span>
                  <el-icon>
                    <CircleCheckFilled v-if="isProcessCompleted('die_attach', record)" />
                  </el-icon>
                </div>
                <div class="process-step" :class="getProcessStepClass('wire_bond', record)">
                  <span class="step-name">线键合</span>
                  <el-icon>
                    <CircleCheckFilled v-if="isProcessCompleted('wire_bond', record)" />
                  </el-icon>
                </div>
                <div class="process-step" :class="getProcessStepClass('molding', record)">
                  <span class="step-name">塑封</span>
                  <el-icon>
                    <CircleCheckFilled v-if="isProcessCompleted('molding', record)" />
                  </el-icon>
                </div>
              </div>
            </div>

            <div class="lot-card__footer">
              <span class="operator">操作员: {{ record.operator }}</span>
              <span class="time">{{ formatTime(record.startTime) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工艺详情面板 -->
    <div
v-if="selectedLot" class="process-details-panel"
>
      <el-dialog
        v-model="detailsPanelVisible"
        :title="`${selectedLot.lotNumber} - 工艺详情`"
        width="90%"
        top="5vh"
      >
        <el-tabs v-model="detailsActiveTab" type="border-card">
          <!-- 贴片工艺 -->
          <el-tab-pane label="贴片工艺" name="die-attach">
            <div class="process-detail-content">
              <div class="process-summary">
                <h3>贴片工艺参数</h3>
                <div class="parameter-grid">
                  <div class="param-item">
                    <span class="param-label">贴片温度:</span>
                    <span class="param-value">{{ selectedLot.dieAttach.temperature }}°C</span>
                    <span
                      class="param-status"
                      :class="getQualityResultClass(selectedLot.dieAttach.result)"
                    >
                      {{ getQualityResultText(selectedLot.dieAttach.result) }}
                    </span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">贴片压力:</span>
                    <span class="param-value">{{ selectedLot.dieAttach.force }}N</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">贴片时间:</span>
                    <span class="param-value">{{ selectedLot.dieAttach.time }}s</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">导电胶:</span>
                    <span class="param-value">{{ selectedLot.dieAttach.epoxy }}</span>
                  </div>
                </div>
              </div>

              <!-- 贴片质量分析图表 -->
              <div class="quality-chart">
                <div
id="die-attach-chart" style="width: 100%; height: 300px"
/>
              </div>
            </div>
          </el-tab-pane>

          <!-- 线键合工艺 -->
          <el-tab-pane label="线键合工艺" name="wire-bond">
            <div class="process-detail-content">
              <div class="process-summary">
                <h3>线键合工艺参数</h3>
                <div class="parameter-grid">
                  <div class="param-item">
                    <span class="param-label">金线规格:</span>
                    <span class="param-value">{{ selectedLot.wireBond.wireType }}</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">第一点键合力:</span>
                    <span class="param-value">{{ selectedLot.wireBond.bondForce1 }}gf</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">第二点键合力:</span>
                    <span class="param-value">{{ selectedLot.wireBond.bondForce2 }}gf</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">超声功率:</span>
                    <span class="param-value">{{ selectedLot.wireBond.ultrasonicPower }}%</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">线弧高度:</span>
                    <span class="param-value">{{ selectedLot.wireBond.loopHeight }}μm</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">线数良率:</span>
                    <span class="param-value">
                      {{ selectedLot.wireBond.passedWires }} /
                      {{ selectedLot.wireBond.totalWires }} ({{
                        (
                          (selectedLot.wireBond.passedWires / selectedLot.wireBond.totalWires) *
                          100
                        ).toFixed(1)
                      }}%)
                    </span>
                    <span
                      class="param-status"
                      :class="getQualityResultClass(selectedLot.wireBond.result)"
                    >
                      {{ getQualityResultText(selectedLot.wireBond.result) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 线键合质量趋势 -->
              <div class="quality-chart">
                <div
id="wire-bond-chart" style="width: 100%; height: 300px"
/>
              </div>
            </div>
          </el-tab-pane>

          <!-- 塑封工艺 -->
          <el-tab-pane label="塑封工艺" name="molding">
            <div class="process-detail-content">
              <div class="process-summary">
                <h3>塑封工艺参数</h3>
                <div class="parameter-grid">
                  <div class="param-item">
                    <span class="param-label">封装料:</span>
                    <span class="param-value">{{ selectedLot.molding.compound }}</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">塑封温度:</span>
                    <span class="param-value">{{ selectedLot.molding.temperature }}°C</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">塑封压力:</span>
                    <span class="param-value">{{ selectedLot.molding.pressure }}MPa</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">固化时间:</span>
                    <span class="param-value">{{ selectedLot.molding.time }}s</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">厚度:</span>
                    <span class="param-value">{{ selectedLot.molding.thickness }}mm</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">空洞率:</span>
                    <span class="param-value">{{ selectedLot.molding.voidRatio.toFixed(2) }}%</span>
                    <span
                      class="param-status"
                      :class="getQualityResultClass(selectedLot.molding.result)"
                    >
                      {{ getQualityResultText(selectedLot.molding.result) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 空洞率分布图 -->
              <div class="quality-chart">
                <div
id="molding-chart" style="width: 100%; height: 300px"
/>
              </div>
            </div>
          </el-tab-pane>

          <!-- SPC控制图 -->
          <el-tab-pane label="SPC控制图" name="spc">
            <div class="spc-content">
              <div class="spc-controls">
                <el-select v-model="selectedSPCParameter" placeholder="选择参数">
                  <el-option label="贴片温度" value="die_attach_temp" />
                  <el-option label="键合力" value="bond_force" />
                  <el-option label="塑封压力" value="molding_pressure" />
                  <el-option label="空洞率" value="void_ratio" />
                </el-select>
                <CButton size="small" @click="refreshSPCData">
                  <el-icon><RefreshRight /></el-icon>
                  刷新数据
                </CButton>
              </div>

              <div class="spc-chart">
                <div
id="spc-control-chart" style="width: 100%; height: 400px"
/>
              </div>

              <div
v-if="spcData" class="spc-statistics"
>
                <div class="spc-stats-grid">
                  <div class="stat-item">
                    <div class="stat-label">均值 (X̄)</div>
                    <div class="stat-value">
                      {{ spcData.mean.toFixed(3) }}
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">标准差 (σ)</div>
                    <div class="stat-value">
                      {{ spcData.stdDev.toFixed(3) }}
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">Cpk</div>
                    <div class="stat-value" :class="getCpkClass(spcData.cpk)">
                      {{ spcData.cpk.toFixed(2) }}
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">UCL</div>
                    <div class="stat-value">
                      {{ spcData.ucl.toFixed(3) }}
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">LCL</div>
                    <div class="stat-value">
                      {{ spcData.lcl.toFixed(3) }}
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">状态</div>
                    <div
                      class="stat-value"
                      :class="spcData.outOfControl ? 'out-of-control' : 'in-control'"
                    >
                      {{ spcData.outOfControl ? '失控' : '受控' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-dialog>
    </div>

    <!-- 新建批次弹窗 -->
    <el-dialog v-model="newLotDialogVisible" title="新建封装批次" width="60%">
      <el-form :model="newLotForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="批次号" required>
              <el-input v-model="newLotForm.lotNumber" placeholder="请输入批次号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户料号" required>
              <el-input v-model="newLotForm.customerPN" placeholder="请输入客户料号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="封装类型" required>
              <el-select v-model="newLotForm.packageType" placeholder="请选择封装类型">
                <el-option label="QFP" value="QFP" />
                <el-option label="BGA" value="BGA" />
                <el-option label="CSP" value="CSP" />
                <el-option label="FC" value="FC" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产数量" required>
              <el-input-number v-model="newLotForm.quantity" :min="1" :step="1000" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级">
              <el-select v-model="newLotForm.priority">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="urgent" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作员">
              <el-input v-model="newLotForm.operator" placeholder="请输入操作员" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <CButton @click="newLotDialogVisible = false">取消</CButton>
        <CButton type="primary"
@click="createNewLot" :loading="creating"
>
创建批次
</CButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, nextTick } from 'vue'
  import type {
    AssemblyRecord,
    Equipment,
    ProcessParameter,
    DashboardData,
    SPCData,
    ProcessStatus,
    QualityResult,
    PackageType
  } from '@/types/manufacturing'
  import CButton from '@/components/base/CButton.vue'
  import { ManufacturingEquipmentCard, ProcessParameterTable } from '@/components/manufacturing'
  import { RefreshRight, Plus, CircleCheckFilled } from '@element-plus/icons-vue'

  // 响应式数据
  const loading = ref(false)
  const creating = ref(false)
  const parameterLoading = ref(false)
  const newLotDialogVisible = ref(false)
  const detailsPanelVisible = ref(false)
  const selectedProcess = ref('all')
  const lotStatusFilter = ref('')
  const packageTypeFilter = ref('')
  const detailsActiveTab = ref('die-attach')
  const selectedSPCParameter = ref('die_attach_temp')

  // 模拟数据
  const dashboardData = ref<DashboardData>({
    totalEquipment: 12,
    runningEquipment: 8,
    overallOEE: 85.2,
    dailyOutput: 125000,
    cpTesting: {
      activeWafers: 0,
      avgYield: 0,
      totalTestedDie: 0,
      equipmentUtilization: 0
    },
    assembly: {
      activePackages: 18,
      avgCycleTime: 12.5,
      defectRate: 0.0025,
      throughput: 45000
    },
    finalTest: {
      testedUnits: 0,
      passRate: 0,
      avgTestTime: 0,
      handlerEfficiency: 0
    }
  })

  const assemblyEquipments = ref<Equipment[]>([
    {
      id: 'die-attach-01',
      name: 'DA-001',
      model: 'ASM Eagle 60',
      station: 'DA-ST01',
      status: 'running',
      lastUpdated: new Date().toISOString(),
      utilization: 92.5,
      oee: 88.3
    },
    {
      id: 'wire-bond-01',
      name: 'WB-001',
      model: 'K&S IConn Pro',
      station: 'WB-ST01',
      status: 'running',
      lastUpdated: new Date().toISOString(),
      utilization: 89.7,
      oee: 85.2
    },
    {
      id: 'molding-01',
      name: 'MD-001',
      model: 'TOWA YPS-D',
      station: 'MD-ST01',
      status: 'idle',
      lastUpdated: new Date().toISOString(),
      utilization: 76.4,
      oee: 82.1
    }
  ])

  const processParameters = ref<ProcessParameter[]>([
    {
      id: 'param-da-temp',
      name: '贴片温度',
      unit: '°C',
      targetValue: 380.0,
      tolerance: 5.0,
      currentValue: 382.5,
      minLimit: 370.0,
      maxLimit: 390.0,
      status: 'normal'
    },
    {
      id: 'param-wb-force',
      name: '键合力',
      unit: 'gf',
      targetValue: 65.0,
      tolerance: 3.0,
      currentValue: 67.2,
      minLimit: 60.0,
      maxLimit: 70.0,
      status: 'warning'
    }
  ])

  const assemblyRecords = ref<AssemblyRecord[]>([])
  const selectedLot = ref<AssemblyRecord | null>(null)
  const spcData = ref<SPCData | null>(null)

  const newLotForm = reactive({
    lotNumber: '',
    customerPN: '',
    packageType: 'QFP' as PackageType,
    quantity: 10000,
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
    operator: ''
  })

  // 计算属性
  const filteredEquipments = computed(() => {
    if (selectedProcess.value === 'all') {
      return assemblyEquipments.value
    }
    return assemblyEquipments.value.filter(eq =>
      eq.station.toLowerCase().includes(selectedProcess.value.replace('_', ''))
    )
  })

  const filteredAssemblyRecords = computed(() => {
    let filtered = assemblyRecords.value

    if (lotStatusFilter.value) {
      filtered = filtered.filter(record => record.status === lotStatusFilter.value)
    }

    if (packageTypeFilter.value) {
      filtered = filtered.filter(record => record.packageType === packageTypeFilter.value)
    }

    return filtered
  })

  // 方法
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('zh-CN').format(num)
  }

  const formatTime = (timeStr: string): string => {
    return new Date(timeStr).toLocaleString('zh-CN')
  }

  const getLotStatusType = (status: ProcessStatus) => {
    const typeMap = {
      planned: 'info',
      released: 'primary',
      running: 'success',
      completed: 'success',
      hold: 'warning'
    }
    return typeMap[status] || 'info'
  }

  const getLotStatusText = (status: ProcessStatus): string => {
    const textMap = {
      planned: '计划中',
      released: '已发布',
      running: '进行中',
      completed: '已完成',
      hold: '暂停'
    }
    return textMap[status] || '未知'
  }

  const getProcessStepClass = (step: string, record: AssemblyRecord): string => {
    const stepMap: Record<string, boolean> = {
      die_attach: record.dieAttach.result === 'pass',
      wire_bond: record.wireBond.result === 'pass',
      molding: record.molding.result === 'pass'
    }

    return stepMap[step] ? 'step-completed' : 'step-pending'
  }

  const isProcessCompleted = (step: string, record: AssemblyRecord): boolean => {
    const stepMap: Record<string, boolean> = {
      die_attach: record.dieAttach.result === 'pass',
      wire_bond: record.wireBond.result === 'pass',
      molding: record.molding.result === 'pass'
    }

    return stepMap[step] || false
  }

  const getQualityResultClass = (result: QualityResult): string => {
    const classMap = {
      pass: 'result-pass',
      fail: 'result-fail',
      retest: 'result-retest',
      pending: 'result-pending'
    }
    return classMap[result] || 'result-pending'
  }

  const getQualityResultText = (result: QualityResult): string => {
    const textMap = {
      pass: '通过',
      fail: '失败',
      retest: '重测',
      pending: '待测'
    }
    return textMap[result] || '未知'
  }

  const getCpkClass = (cpk: number): string => {
    if (cpk >= 1.67) return 'cpk-excellent'
    if (cpk >= 1.33) return 'cpk-good'
    if (cpk >= 1.0) return 'cpk-fair'
    return 'cpk-poor'
  }

  // 事件处理方法
  const refreshData = async () => {
    loading.value = true
    try {
      await loadAssemblyRecords()
      ElMessage.success('数据已刷新')
    } finally {
      loading.value = false
    }
  }

  const openNewLotDialog = () => {
    newLotDialogVisible.value = true
  }

  const createNewLot = async () => {
    creating.value = true
    try {
      // 创建新批次逻辑
      ElMessage.success('批次已创建')
      newLotDialogVisible.value = false
      await refreshData()
    } finally {
      creating.value = false
    }
  }

  const selectLot = (lot: AssemblyRecord) => {
    selectedLot.value = lot
    detailsPanelVisible.value = true
    loadSPCData()
  }

  const viewEquipmentDetails = (equipmentId: string) => {
    console.log('查看设备详情:', equipmentId)
  }

  const controlEquipment = (equipmentId: string) => {
    console.log('控制设备:', equipmentId)
  }

  const refreshProcessParameters = () => {
    parameterLoading.value = true
    setTimeout(() => {
      parameterLoading.value = false
      ElMessage.success('参数已刷新')
    }, 1000)
  }

  const editProcessParameter = (parameter: ProcessParameter) => {
    console.log('编辑工艺参数:', parameter.id)
  }

  const viewParameterHistory = (parameter: ProcessParameter) => {
    console.log('查看参数历史:', parameter.id)
  }

  const exportParameterData = (parameter: ProcessParameter) => {
    console.log('导出参数数据:', parameter.id)
  }

  const refreshSPCData = () => {
    loadSPCData()
  }

  // 数据加载方法
  const loadAssemblyRecords = async () => {
    // 模拟API调用
    const mockRecords: AssemblyRecord[] = [
      {
        id: 'lot-001',
        lotNumber: 'ASM001',
        packageType: 'QFP',
        customerPN: 'ABC123',
        quantity: 50000,
        processedQty: 35000,
        dieAttach: {
          temperature: 380,
          force: 25.5,
          time: 3.2,
          epoxy: 'AG-8020',
          result: 'pass'
        },
        wireBond: {
          wireType: '1mil Au',
          bondForce1: 65,
          bondForce2: 45,
          ultrasonicPower: 85,
          bondTime1: 20,
          bondTime2: 15,
          loopHeight: 50,
          totalWires: 64,
          passedWires: 64,
          result: 'pass'
        },
        molding: {
          compound: 'EME-G770H',
          temperature: 175,
          pressure: 8.5,
          time: 90,
          thickness: 1.2,
          voidRatio: 0.8,
          result: 'pass'
        },
        operator: 'op001',
        equipment: assemblyEquipments.value[0],
        startTime: '2024-01-25T08:00:00Z',
        status: 'running'
      }
    ]
    assemblyRecords.value = mockRecords
  }

  const loadSPCData = async () => {
    // 模拟SPC数据
    spcData.value = {
      parameterId: selectedSPCParameter.value,
      parameterName: '贴片温度',
      samples: [378, 381, 379, 382, 380, 383, 378, 381, 379, 380],
      mean: 380.1,
      stdDev: 1.8,
      cpk: 1.85,
      ucl: 385.5,
      lcl: 374.7,
      usl: 390.0,
      lsl: 370.0,
      outOfControl: false
    }
  }

  // 生命周期
  onMounted(() => {
    loadAssemblyRecords()
  })
</script>

<style lang="scss" scoped>
  .assembly-page {
    min-height: 100vh;
    padding: var(--spacing-6);
    background: var(--color-bg-page);
  }

  .page-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);

    .page-title {
      h1 {
        margin: 0 0 var(--spacing-1) 0;
        font-size: var(--font-size-2xl);
        font-weight: 600;
        color: var(--color-text-primary);
      }

      p {
        margin: 0;
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    .page-actions {
      display: flex;
      gap: var(--spacing-2);
    }
  }

  .overview-section {
    margin-bottom: var(--spacing-6);
  }

  .kpi-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
  }

  .kpi-card {
    padding: var(--spacing-4);
    text-align: center;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);

    .kpi-value {
      margin-bottom: var(--spacing-2);
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-primary);
    }

    .kpi-label {
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .kpi-trend {
      font-size: var(--font-size-sm);
      font-weight: 600;

      &.up {
        color: var(--color-success);

        &::before {
          margin-right: 2px;
          content: '↑';
        }
      }

      &.down {
        color: var(--color-danger);

        &::before {
          margin-right: 2px;
          content: '↓';
        }
      }
    }
  }

  .main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-6);
  }

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);

    h2 {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--color-text-primary);
    }
  }

  .process-section {
    height: fit-content;
    padding: var(--spacing-4);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);
  }

  .equipment-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-6);
  }

  .lot-section {
    padding: var(--spacing-4);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);
  }

  .lot-filters {
    display: flex;
    gap: var(--spacing-2);
  }

  .lot-list {
    max-height: 600px;
    overflow-y: auto;
  }

  .lot-card {
    padding: var(--spacing-3);
    margin-bottom: var(--spacing-3);
    cursor: pointer;
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--color-primary);
      box-shadow: var(--shadow-md);
    }

    &--running {
      border-left: 4px solid var(--color-success);
    }

    &--completed {
      border-left: 4px solid var(--color-primary);
    }

    &--hold {
      border-left: 4px solid var(--color-warning);
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-3);

      .lot-number {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .package-info {
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    &__content {
      margin-bottom: var(--spacing-3);
    }

    &__footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: var(--spacing-2);
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
      border-top: 1px solid var(--color-border-lighter);
    }
  }

  .lot-progress {
    margin-bottom: var(--spacing-3);

    .progress-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
  }

  .process-status {
    display: flex;
    gap: var(--spacing-3);
  }

  .process-step {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;
    font-size: var(--font-size-sm);

    &.step-completed {
      color: var(--color-success);
    }

    &.step-pending {
      color: var(--color-text-secondary);
    }

    .step-name {
      font-weight: 500;
    }
  }

  .process-detail-content {
    padding: var(--spacing-4);
  }

  .process-summary {
    margin-bottom: var(--spacing-6);

    h3 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .parameter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-3);
  }

  .param-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);

    .param-label {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .param-value {
      font-size: var(--font-size-base);
      font-weight: 500;
      color: var(--color-text-primary);
    }

    .param-status {
      font-size: var(--font-size-sm);
      font-weight: 600;

      &.result-pass {
        color: var(--color-success);
      }

      &.result-fail {
        color: var(--color-danger);
      }

      &.result-retest {
        color: var(--color-warning);
      }

      &.result-pending {
        color: var(--color-text-secondary);
      }
    }
  }

  .quality-chart {
    padding: var(--spacing-4);
    margin-top: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);
  }

  .spc-content {
    padding: var(--spacing-4);
  }

  .spc-controls {
    display: flex;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-4);
  }

  .spc-chart {
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);
  }

  .spc-statistics {
    padding: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);
  }

  .spc-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-3);
  }

  .stat-item {
    text-align: center;

    .stat-label {
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .stat-value {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--color-text-primary);

      &.cpk-excellent {
        color: var(--color-success);
      }

      &.cpk-good {
        color: var(--color-primary);
      }

      &.cpk-fair {
        color: var(--color-warning);
      }

      &.cpk-poor {
        color: var(--color-danger);
      }

      &.in-control {
        color: var(--color-success);
      }

      &.out-of-control {
        color: var(--color-danger);
      }
    }
  }

  @media (width <= 1200px) {
    .main-content {
      grid-template-columns: 1fr;
    }
  }

  @media (width <= 768px) {
    .assembly-page {
      padding: var(--spacing-4);
    }

    .page-header {
      flex-direction: column;
      gap: var(--spacing-3);
      align-items: stretch;

      .page-actions {
        justify-content: center;
      }
    }

    .kpi-cards {
      grid-template-columns: 1fr;
    }

    .lot-filters {
      flex-direction: column;
    }

    .parameter-grid {
      grid-template-columns: 1fr;
    }

    .spc-stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
</style>
