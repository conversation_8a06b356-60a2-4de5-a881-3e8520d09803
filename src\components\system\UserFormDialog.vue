<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑用户' : '新增用户'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" @submit.prevent>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名"
prop="username">
            <el-input
              v-model="formData.username"
              placeholder="请输入用户名"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="真实姓名"
prop="realName">
            <el-input v-model="formData.realName" placeholder="请输入真实姓名" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱"
prop="email">
            <el-input v-model="formData.email" type="email" placeholder="请输入邮箱" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="电话"
prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入电话号码" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门"
prop="department">
            <el-select
              v-model="formData.department"
              placeholder="请选择部门"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.name"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="职位"
prop="position">
            <el-input v-model="formData.position" placeholder="请输入职位" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="角色"
prop="roles">
        <el-select
          v-model="formData.roles"
          placeholder="请选择角色"
          style="width: 100%"
          multiple
          clearable
        >
          <el-option v-for="role in roles" :key="role.code" :label="role.name" :value="role.code">
            <div class="role-option">
              <div class="role-name">
                {{ role.name }}
              </div>
              <div class="role-description">
                {{ role.description }}
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-if="!isEdit"
label="初始密码" prop="password">
        <el-input
          v-model="formData.password"
          type="password"
          placeholder="请输入初始密码"
          show-password
          clearable
        />
        <div class="form-hint">
密码长度至少6位，包含字母和数字
</div>
      </el-form-item>

      <el-form-item label="状态"
prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="active">启用</el-radio>
          <el-radio value="inactive">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitting ? '保存中...' : '确定' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import type { FormInstance, FormRules } from 'element-plus'
  import type {
    UserInfo,
    CreateUserRequest,
    UpdateUserRequest,
    Department,
    RoleConfig
  } from '@/types/user'
  import { useUserManagement } from '@/composables/useUserManagement'

  interface Props {
    modelValue: boolean
    userData?: UserInfo | null
    isEdit?: boolean
    departments: Department[]
    roles: RoleConfig[]
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'success'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    userData: null,
    isEdit: false
  })

  const emit = defineEmits<Emits>()

  const { createUser, updateUser } = useUserManagement()

  // 对话框显示状态
  const visible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  // 表单引用
  const formRef = ref<FormInstance>()
  const submitting = ref(false)

  // 表单数据
  const formData = reactive<CreateUserRequest & UpdateUserRequest>({
    id: '',
    username: '',
    email: '',
    phone: '',
    realName: '',
    password: '',
    department: '',
    position: '',
    roles: [],
    status: 'active'
  })

  // 表单验证规则
  const formRules: FormRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '用户名长度在3-20个字符', trigger: 'blur' },
      {
        pattern: /^[a-zA-Z0-9_]+$/,
        message: '用户名只能包含字母、数字和下划线',
        trigger: 'blur'
      }
    ],
    realName: [
      { required: true, message: '请输入真实姓名', trigger: 'blur' },
      { min: 2, max: 10, message: '姓名长度在2-10个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    phone: [
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur'
      }
    ],
    department: [{ required: true, message: '请选择部门', trigger: 'change' }],
    position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
    roles: [{ required: true, message: '请选择角色', trigger: 'change', type: 'array', min: 1 }],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度在6-20个字符', trigger: 'blur' },
      {
        pattern: /^(?=.*[a-zA-Z])(?=.*\d).+$/,
        message: '密码必须包含字母和数字',
        trigger: 'blur'
      }
    ],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }

  /**
   * 初始化表单数据
   */
  const initFormData = (): void => {
    if (props.isEdit && props.userData) {
      Object.assign(formData, {
        id: props.userData.id,
        username: props.userData.username,
        email: props.userData.email,
        phone: props.userData.phone || '',
        realName: props.userData.realName,
        department: props.userData.department,
        position: props.userData.position,
        roles: [...props.userData.roles],
        status: props.userData.status,
        password: '' // 编辑时不显示密码
      })
    } else {
      // 重置为默认值
      Object.assign(formData, {
        id: '',
        username: '',
        email: '',
        phone: '',
        realName: '',
        password: '',
        department: '',
        position: '',
        roles: [],
        status: 'active'
      })
    }
  }

  /**
   * 处理提交
   */
  const handleSubmit = async (): Promise<void> => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      submitting.value = true

      let success = false

      if (props.isEdit) {
        // 编辑用户
        const updateData: UpdateUserRequest = {
          id: formData.id,
          email: formData.email,
          phone: formData.phone || undefined,
          realName: formData.realName,
          department: formData.department,
          position: formData.position,
          roles: formData.roles,
          status: formData.status
        }
        success = await updateUser(updateData)
      } else {
        // 新增用户
        const createData: CreateUserRequest = {
          username: formData.username,
          email: formData.email,
          phone: formData.phone || undefined,
          realName: formData.realName,
          password: formData.password,
          department: formData.department,
          position: formData.position,
          roles: formData.roles,
          status: formData.status
        }
        success = await createUser(createData)
      }

      if (success) {
        emit('success')
        handleClose()
      }
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      submitting.value = false
    }
  }

  /**
   * 处理关闭
   */
  const handleClose = (): void => {
    visible.value = false

    // 延迟重置表单，避免动画闪烁
    setTimeout(() => {
      formRef.value?.resetFields()
      initFormData()
    }, 300)
  }

  // 监听对话框打开，初始化表单数据
  watch(
    () => props.modelValue,
    newValue => {
      if (newValue) {
        initFormData()
      }
    },
    { immediate: true }
  )
</script>

<style lang="scss" scoped>
  .role-option {
    .role-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-primary);
    }

    .role-description {
      font-size: 12px;
      color: var(--color-text-secondary);
      margin-top: 2px;
    }
  }

  .form-hint {
    font-size: 12px;
    color: var(--color-text-placeholder);
    margin-top: 4px;
    line-height: 1.4;
  }

  :deep(.el-dialog) {
    border-radius: 8px;
  }

  :deep(.el-dialog__header) {
    background: var(--color-bg-light);
    border-bottom: 1px solid var(--color-border-lighter);
    border-radius: 8px 8px 0 0;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--color-text-primary);
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 5vh auto;
    }

    :deep(.el-dialog__body) {
      padding: 16px;
    }

    .el-row {
      .el-col {
        margin-bottom: 0;
      }
    }
  }
</style>
