/**
 * IC封测CIM系统 - 用户相关类型定义
 * User Management Type Definitions for IC Packaging & Testing CIM System
 */

/**
 * 用户基本信息接口
 */
export interface UserInfo {
  id: string
  username: string
  email: string
  phone?: string
  realName: string
  avatar?: string
  department: string
  position: string
  roles: string[]
  permissions: string[]
  lastLoginTime?: string
  lastLoginIp?: string
  status: 'active' | 'inactive' | 'locked'
  createTime: string
  updateTime: string
}

/**
 * 用户创建请求接口
 */
export interface CreateUserRequest {
  username: string
  email: string
  phone?: string
  realName: string
  password: string
  department: string
  position: string
  roles: string[]
  status?: 'active' | 'inactive'
}

/**
 * 用户更新请求接口
 */
export interface UpdateUserRequest {
  id: string
  email?: string
  phone?: string
  realName?: string
  department?: string
  position?: string
  roles?: string[]
  status?: 'active' | 'inactive' | 'locked'
}

/**
 * 用户查询参数接口
 */
export interface UserQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  department?: string
  status?: 'active' | 'inactive' | 'locked' | ''
  role?: string
  createTimeStart?: string
  createTimeEnd?: string
  sortBy?: 'createTime' | 'updateTime' | 'lastLoginTime' | 'username'
  sortOrder?: 'asc' | 'desc'
}

/**
 * 用户列表响应接口
 */
export interface UserListResponse {
  users: UserInfo[]
  total: number
  page: number
  pageSize: number
}

/**
 * 登录请求接口
 */
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
  captchaKey?: string
  rememberMe?: boolean
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  user: UserInfo
  token: string
  refreshToken: string
  expiresIn: number
}

/**
 * 修改密码请求接口
 */
export interface ChangePasswordRequest {
  userId: string
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

/**
 * 重置密码请求接口
 */
export interface ResetPasswordRequest {
  userId: string
  newPassword: string
  confirmPassword: string
}

/**
 * 权限配置接口
 */
export interface PermissionConfig {
  code: string
  name: string
  type: 'menu' | 'button' | 'api'
  resource?: string
  description?: string
  parentCode?: string
  level?: number
  sort?: number
}

/**
 * 角色配置接口
 */
export interface RoleConfig {
  code: string
  name: string
  permissions: string[]
  description?: string
  level?: number
  createTime?: string
  updateTime?: string
}

/**
 * 部门信息接口
 */
export interface Department {
  id: string
  code: string
  name: string
  parentId?: string
  level: number
  sort: number
  description?: string
  status: 'active' | 'inactive'
  createTime: string
  updateTime: string
}

/**
 * 职位信息接口
 */
export interface Position {
  id: string
  code: string
  name: string
  department: string
  level: number
  description?: string
  status: 'active' | 'inactive'
  createTime: string
  updateTime: string
}

/**
 * 用户操作日志接口
 */
export interface UserOperationLog {
  id: string
  userId: string
  username: string
  operation: string
  module: string
  description: string
  ip: string
  userAgent: string
  status: 'success' | 'failed'
  errorMessage?: string
  createTime: string
}

/**
 * 在线用户信息接口
 */
export interface OnlineUser {
  userId: string
  username: string
  realName: string
  avatar?: string
  department: string
  position: string
  loginTime: string
  lastActiveTime: string
  ip: string
  location?: string
  browser?: string
  os?: string
}

/**
 * 用户统计信息接口
 */
export interface UserStatistics {
  totalUsers: number
  activeUsers: number
  inactiveUsers: number
  lockedUsers: number
  onlineUsers: number
  newUsersToday: number
  newUsersThisMonth: number
  departmentStats: Array<{
    department: string
    count: number
  }>
  roleStats: Array<{
    role: string
    count: number
  }>
}

/**
 * IC封测专业角色枚举
 */
export enum ICRoles {
  // 系统管理
  SYSTEM_ADMIN = 'system_admin',
  SYSTEM_OPERATOR = 'system_operator',

  // 订单管理
  ORDER_MANAGER = 'order_manager',
  ORDER_OPERATOR = 'order_operator',

  // 生产管理
  PRODUCTION_MANAGER = 'production_manager',
  PRODUCTION_PLANNER = 'production_planner',
  PRODUCTION_OPERATOR = 'production_operator',

  // 工艺工程
  PROCESS_ENGINEER = 'process_engineer',
  TEST_ENGINEER = 'test_engineer',
  PACKAGING_ENGINEER = 'packaging_engineer',

  // 设备管理
  EQUIPMENT_MANAGER = 'equipment_manager',
  EQUIPMENT_ENGINEER = 'equipment_engineer',
  MAINTENANCE_TECHNICIAN = 'maintenance_technician',

  // 质量管理
  QUALITY_MANAGER = 'quality_manager',
  QUALITY_ENGINEER = 'quality_engineer',
  QC_INSPECTOR = 'qc_inspector',

  // 物料管理
  WAREHOUSE_MANAGER = 'warehouse_manager',
  WAREHOUSE_OPERATOR = 'warehouse_operator',
  PROCUREMENT_SPECIALIST = 'procurement_specialist',

  // 客户服务
  CUSTOMER_SERVICE_MANAGER = 'customer_service_manager',
  CUSTOMER_SERVICE_REP = 'customer_service_rep',

  // 财务
  FINANCE_MANAGER = 'finance_manager',
  FINANCE_CLERK = 'finance_clerk',

  // 只读角色
  READONLY_USER = 'readonly_user',
  GUEST = 'guest'
}

/**
 * IC封测专业权限枚举
 */
export enum ICPermissions {
  // 系统管理权限
  SYSTEM_READ = 'system:read',
  SYSTEM_WRITE = 'system:write',
  SYSTEM_DELETE = 'system:delete',
  SYSTEM_CONFIG = 'system:config',

  // 用户管理权限
  USER_READ = 'user:read',
  USER_WRITE = 'user:write',
  USER_DELETE = 'user:delete',
  USER_ROLE_ASSIGN = 'user:role:assign',

  // 订单管理权限
  ORDER_READ = 'order:read',
  ORDER_WRITE = 'order:write',
  ORDER_DELETE = 'order:delete',
  ORDER_APPROVE = 'order:approve',
  ORDER_EXPORT = 'order:export',

  // 生产管理权限
  PRODUCTION_READ = 'production:read',
  PRODUCTION_WRITE = 'production:write',
  PRODUCTION_CONTROL = 'production:control',
  PRODUCTION_SCHEDULE = 'production:schedule',

  // CP测试权限
  CP_READ = 'cp:read',
  CP_OPERATE = 'cp:operate',
  CP_CONFIG = 'cp:config',

  // 封装工艺权限
  ASSEMBLY_READ = 'assembly:read',
  ASSEMBLY_OPERATE = 'assembly:operate',
  ASSEMBLY_CONFIG = 'assembly:config',

  // 最终测试权限
  FT_READ = 'ft:read',
  FT_OPERATE = 'ft:operate',
  FT_CONFIG = 'ft:config',

  // 设备管理权限
  EQUIPMENT_READ = 'equipment:read',
  EQUIPMENT_WRITE = 'equipment:write',
  EQUIPMENT_CONTROL = 'equipment:control',
  EQUIPMENT_MAINTENANCE = 'equipment:maintenance',
  SECS_GEM_CONFIG = 'secs_gem:config',

  // 质量管理权限
  QUALITY_READ = 'quality:read',
  QUALITY_WRITE = 'quality:write',
  QUALITY_APPROVE = 'quality:approve',
  SPC_CONFIG = 'spc:config',
  QUALITY_AUDIT = 'quality:audit',

  // 物料管理权限
  MATERIAL_READ = 'material:read',
  MATERIAL_WRITE = 'material:write',
  INVENTORY_CONTROL = 'inventory:control',
  WAREHOUSE_OPERATE = 'warehouse:operate',

  // 报表权限
  REPORT_READ = 'report:read',
  REPORT_EXPORT = 'report:export',
  REPORT_CONFIG = 'report:config',

  // 监控权限
  MONITORING_READ = 'monitoring:read',
  MONITORING_CONFIG = 'monitoring:config'
}
