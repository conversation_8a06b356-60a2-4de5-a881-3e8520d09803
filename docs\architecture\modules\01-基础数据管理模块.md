# 基础数据管理模块设计 (IC封装测试专业版)

## 1. 模块概述

### 1.1 模块定位
基础数据管理模块是IC封装测试CIM系统的核心基础模块，专门针对半导体后端制造工艺设计，提供IC封测行业专用的主数据管理服务。覆盖从晶圆到成品的完整IC封测供应链数据管理，确保符合JEDEC、IPC、IATF16949等行业标准的数据一致性、完整性和可追溯性。

### 1.2 复用价值
- **高复用性**：支持IC封测专业数据类型（晶圆、芯片、封装体、测试程序等）
- **行业标准**：符合SEMI、JEDEC、IPC等半导体行业标准
- **减少开发量**：预计减少70%的IC封测基础数据相关开发工作
- **提高一致性**：统一IC封测行业术语和数据规范

### 1.3 IC封测行业特色
- **晶圆级管理**：支持Wafer Map、Die坐标、Bin分类等半导体特有数据
- **封装专业性**：支持Package类型、Lead Frame、Substrate等专业封装数据
- **测试专业性**：支持Test Program、ATE配置、Handler设置等测试数据
- **追溯完整性**：从Wafer Lot到最终产品的完整Traceability数据链

## 2. 功能设计

### 2.1 核心功能架构
```
IC封测基础数据管理模块
├── 半导体数据模型引擎     # IC封测专业数据模型定义
├── 行业标准编码引擎       # 符合JEDEC/SEMI标准的编码生成
├── IC工艺数据验证引擎     # 半导体工艺参数完整性验证  
├── 版本控制引擎          # IC产品生命周期版本管理
├── 封测分类层级引擎       # CP/Assembly/FT工序分类管理
├── 晶圆数据导入引擎       # Wafer Map、测试数据批量导入
├── ERP/MRP数据同步引擎   # 与半导体供应链系统同步
└── SECS/GEM数据集成引擎  # 设备数据标准化接口
```

### 2.2 IC封测专业数据模型

#### 2.2.1 IC封测主数据分类体系
```
IC封测主数据分类
├── 产品数据 (Product Data)
│   ├── IC产品定义 (IC_PRODUCT)
│   ├── 封装类型 (PACKAGE_TYPE) - QFP/BGA/CSP/FC等
│   ├── 引脚配置 (PIN_CONFIGURATION)
│   └── 测试规格 (TEST_SPECIFICATION)
├── 物料数据 (Material Data) 
│   ├── 晶圆物料 (WAFER_MATERIAL)
│   ├── 封装物料 (PACKAGE_MATERIAL)
│   │   ├── Lead Frame (LEAD_FRAME)
│   │   ├── Substrate (SUBSTRATE)
│   │   ├── Die Attach (DIE_ATTACH)
│   │   ├── Bond Wire (BOND_WIRE)
│   │   └── Molding Compound (MOLDING_COMPOUND)
│   └── 化学品 (CHEMICAL_MATERIAL)
├── 设备数据 (Equipment Data)
│   ├── Probe Station (PROBE_STATION)
│   ├── Die Bonder (DIE_BONDER)
│   ├── Wire Bonder (WIRE_BONDER)
│   ├── Molding Press (MOLDING_PRESS)
│   ├── Trim & Form (TRIM_FORM)
│   └── Test Handler (TEST_HANDLER)
├── 工艺数据 (Process Data)
│   ├── CP工艺参数 (CP_PROCESS)
│   ├── Assembly工艺参数 (ASSEMBLY_PROCESS)
│   ├── FT工艺参数 (FT_PROCESS)
│   └── 特殊工艺参数 (SPECIAL_PROCESS)
└── 质量数据 (Quality Data)
    ├── 测试项目 (TEST_ITEM)
    ├── 电参数标准 (ELECTRICAL_SPEC)
    ├── 可靠性标准 (RELIABILITY_SPEC)
    └── 外观标准 (VISUAL_SPEC)
```

#### 2.2.2 主数据实体设计
```sql
-- IC封测主数据定义表
CREATE TABLE ic_master_data_types (
    type_id VARCHAR(20) PRIMARY KEY,
    type_name VARCHAR(100),
    type_code VARCHAR(50),
    table_name VARCHAR(100),        -- 对应的数据表
    category ENUM('PRODUCT','MATERIAL','EQUIPMENT','PROCESS','QUALITY'),
    description TEXT,
    is_hierarchical BOOLEAN,        -- 是否支持层级结构
    coding_rule_id VARCHAR(20),     -- 编码规则ID
    jedec_standard VARCHAR(100),    -- 相关JEDEC标准
    semi_standard VARCHAR(100),     -- 相关SEMI标准
    status ENUM('active','inactive'),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- IC封测主数据表
CREATE TABLE ic_master_data_items (
    item_id VARCHAR(30) PRIMARY KEY,
    type_id VARCHAR(20),            -- 数据类型
    item_code VARCHAR(100),         -- 编码
    item_name VARCHAR(200),         -- 名称
    parent_id VARCHAR(30),          -- 父级ID（支持层级）
    level_code VARCHAR(10),         -- 层级码
    sort_order INT,                 -- 排序
    properties JSON,                -- 扩展属性（动态字段）
    
    -- IC封测专业字段
    package_type ENUM('QFP','BGA','CSP','FC','DIP','SOP','QFN','LGA'),
    process_step ENUM('CP','ASSEMBLY','FT','FINAL_INSPECTION'),
    material_category ENUM('WAFER','LEADFRAME','SUBSTRATE','WIRE','COMPOUND','CHEMICAL'),
    equipment_type ENUM('PROBE','DIE_BONDER','WIRE_BONDER','MOLDING','HANDLER','TESTER'),
    jedec_part_number VARCHAR(100), -- JEDEC标准件号
    ipc_classification VARCHAR(50), -- IPC分类
    
    status ENUM('active','inactive','deprecated','under_development'),
    version VARCHAR(20),            -- 版本号
    effective_date DATE,            -- 生效日期
    expiry_date DATE,              -- 失效日期
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_by VARCHAR(20),
    updated_at TIMESTAMP,
    
    INDEX idx_type_code (type_id, item_code),
    INDEX idx_parent (parent_id),
    INDEX idx_package_type (package_type),
    INDEX idx_process_step (process_step),
    INDEX idx_jedec (jedec_part_number),
    INDEX idx_status (status, effective_date, expiry_date)
);
```

#### 2.2.3 IC封测专业扩展数据表

```sql
-- IC产品规格扩展表
CREATE TABLE ic_product_specifications (
    spec_id VARCHAR(30) PRIMARY KEY,
    item_id VARCHAR(30),            -- 关联主数据ID
    die_size_x DECIMAL(8,3),        -- Die尺寸X (um)
    die_size_y DECIMAL(8,3),        -- Die尺寸Y (um) 
    die_thickness DECIMAL(8,2),     -- Die厚度 (um)
    pin_count INT,                  -- 引脚数量
    pitch DECIMAL(8,3),            -- 引脚间距 (mm)
    body_size_x DECIMAL(8,3),       -- 封装体尺寸X (mm)
    body_size_y DECIMAL(8,3),       -- 封装体尺寸Y (mm)
    package_height DECIMAL(8,3),    -- 封装高度 (mm)
    thermal_resistance DECIMAL(8,2), -- 热阻 (°C/W)
    operating_temp_min INT,         -- 工作温度范围最低 (°C)
    operating_temp_max INT,         -- 工作温度范围最高 (°C)
    moisture_level ENUM('MSL1','MSL2','MSL3','MSL4','MSL5','MSL6'), -- 湿度敏感等级
    jedec_outline VARCHAR(50),      -- JEDEC标准外形代码
    
    INDEX idx_item (item_id),
    INDEX idx_package_size (body_size_x, body_size_y)
);

-- 晶圆信息扩展表
CREATE TABLE ic_wafer_specifications (
    spec_id VARCHAR(30) PRIMARY KEY,
    item_id VARCHAR(30),            -- 关联主数据ID
    wafer_size ENUM('4_INCH','6_INCH','8_INCH','12_INCH'), -- 晶圆尺寸
    wafer_thickness DECIMAL(8,2),   -- 晶圆厚度 (um)
    crystal_orientation ENUM('100','111'), -- 晶向
    resistivity_min DECIMAL(8,2),   -- 电阻率范围最小 (ohm-cm)
    resistivity_max DECIMAL(8,2),   -- 电阻率范围最大 (ohm-cm)
    doping_type ENUM('N_TYPE','P_TYPE'), -- 掺杂类型
    surface_treatment VARCHAR(100), -- 表面处理
    backside_grinding BOOLEAN,      -- 是否背面研磨
    die_per_wafer INT,             -- 每片芯片数
    gross_die INT,                 -- 总芯片数
    test_die_count INT,            -- 测试芯片数
    
    INDEX idx_item (item_id),
    INDEX idx_wafer_size (wafer_size)
);

-- 测试参数扩展表  
CREATE TABLE ic_test_specifications (
    spec_id VARCHAR(30) PRIMARY KEY,
    item_id VARCHAR(30),            -- 关联主数据ID
    test_condition_temp_min INT,    -- 测试温度范围最低 (°C)
    test_condition_temp_max INT,    -- 测试温度范围最高 (°C)  
    vdd_min DECIMAL(8,3),          -- 电源电压最小值 (V)
    vdd_typ DECIMAL(8,3),          -- 电源电压典型值 (V)
    vdd_max DECIMAL(8,3),          -- 电源电压最大值 (V)
    test_frequency DECIMAL(12,0),   -- 测试频率 (Hz)
    test_pattern_count INT,         -- 测试图案数量
    test_time_limit INT,            -- 单颗测试时间限制 (ms)
    parallel_test_sites INT,        -- 并行测试站点数
    handler_type VARCHAR(50),       -- Handler类型
    ate_platform VARCHAR(50),       -- ATE平台
    
    INDEX idx_item (item_id),
    INDEX idx_ate_platform (ate_platform)
);
```

#### 2.2.4 IC封测编码规则配置
```sql
-- IC封测编码规则表  
CREATE TABLE ic_coding_rules (
    rule_id VARCHAR(20) PRIMARY KEY,
    rule_name VARCHAR(100),
    rule_pattern VARCHAR(200),      -- 编码模式：{prefix}-{date:YYYYMM}-{sequence:4}
    prefix VARCHAR(20),             -- 固定前缀
    date_format VARCHAR(20),        -- 日期格式
    sequence_length INT,            -- 流水号长度
    sequence_start INT,             -- 流水号起始值
    reset_cycle ENUM('none','daily','monthly','yearly'), -- 重置周期
    separator VARCHAR(5),           -- 分隔符
    industry_standard VARCHAR(100), -- 遵循的行业标准 (JEDEC/SEMI)
    data_category ENUM('PRODUCT','MATERIAL','EQUIPMENT','PROCESS','QUALITY'),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP
);

-- IC封测行业标准编码规则配置
INSERT INTO ic_coding_rules VALUES 
('IC_PROD_001', 'IC产品编码规则', 'IC-{package_type:3}-{date:YYYYMM}-{sequence:4}', 'IC', 'YYYYMM', 4, 1, 'monthly', '-', 'JEDEC JEP95', 'PRODUCT', 'IC产品主编码规则', TRUE, NOW()),
('WAF_MAT_001', '晶圆物料编码规则', 'WAF-{wafer_size:2}-{date:YYYYMM}-{sequence:4}', 'WAF', 'YYYYMM', 4, 1, 'monthly', '-', 'SEMI M1', 'MATERIAL', '晶圆物料编码规则', TRUE, NOW()),
('PKG_MAT_001', '封装物料编码规则', 'PKG-{material_type:3}-{date:YYYYMM}-{sequence:4}', 'PKG', 'YYYYMM', 4, 1, 'monthly', '-', 'IPC-T-50', 'MATERIAL', '封装物料编码规则', TRUE, NOW()),
('EQP_ATE_001', 'ATE设备编码规则', 'ATE-{equipment_type:3}-{date:YYYYMM}-{sequence:4}', 'ATE', 'YYYYMM', 4, 1, 'monthly', '-', 'SEMI E4/E5', 'EQUIPMENT', 'ATE设备编码规则', TRUE, NOW()),
('TST_PGM_001', '测试程序编码规则', 'TST-{ic_family:3}-{version:2}-{sequence:4}', 'TST', 'VV', 4, 1, 'none', '-', 'JEDEC JEP138', 'PROCESS', '测试程序编码规则', TRUE, NOW());

-- 编码序列表
CREATE TABLE coding_sequences (
    sequence_id VARCHAR(30) PRIMARY KEY,
    rule_id VARCHAR(20),
    cycle_key VARCHAR(50),          -- 周期标识（如202501）
    current_value INT,              -- 当前值
    last_generated_at TIMESTAMP
);
```

### 2.3 IC工艺数据验证引擎

#### 2.3.1 IC封测专业验证规则配置
```sql
-- IC封测验证规则表
CREATE TABLE ic_validation_rules (
    rule_id VARCHAR(30) PRIMARY KEY,
    type_id VARCHAR(20),            -- 应用的数据类型
    field_name VARCHAR(100),        -- 字段名
    rule_type ENUM('required','unique','format','range','custom','jedec_std','semi_std'),
    rule_expression TEXT,           -- 规则表达式
    error_message VARCHAR(500),     -- 错误提示
    industry_standard VARCHAR(100), -- 相关行业标准
    severity ENUM('error','warning','info'), -- 严重级别
    is_active BOOLEAN DEFAULT TRUE
);

-- IC封测专业验证规则配置数据
INSERT INTO ic_validation_rules VALUES
-- IC产品验证规则
('IC_PROD_RULE_001', 'IC_PRODUCT', 'die_size_x', 'range', '{"min": 0.5, "max": 50.0}', 'Die尺寸X必须在0.5-50.0mm范围内', 'JEDEC JEP95', 'error', TRUE),
('IC_PROD_RULE_002', 'IC_PRODUCT', 'pin_count', 'range', '{"min": 1, "max": 2000}', '引脚数量必须在1-2000范围内', 'JEDEC JEP95', 'error', TRUE),
('IC_PROD_RULE_003', 'IC_PRODUCT', 'moisture_level', 'jedec_std', '{"standard": "J-STD-020"}', '湿度敏感等级必须符合J-STD-020标准', 'JEDEC J-STD-020', 'error', TRUE),

-- 晶圆验证规则
('WAF_RULE_001', 'WAFER_MATERIAL', 'wafer_size', 'semi_std', '{"standard": "SEMI M1-0302"}', '晶圆尺寸必须符合SEMI M1标准', 'SEMI M1-0302', 'error', TRUE),
('WAF_RULE_002', 'WAFER_MATERIAL', 'wafer_thickness', 'range', '{"min": 50, "max": 1000}', '晶圆厚度必须在50-1000um范围内', 'SEMI M1-0302', 'error', TRUE),

-- 封装物料验证规则  
('PKG_RULE_001', 'PACKAGE_MATERIAL', 'thermal_resistance', 'range', '{"min": 0.1, "max": 500.0}', '热阻值必须在0.1-500°C/W范围内', 'JEDEC JESD51', 'warning', TRUE),
('PKG_RULE_002', 'PACKAGE_MATERIAL', 'operating_temp_max', 'custom', '{"expression": "operating_temp_max > operating_temp_min && operating_temp_max <= 200"}', '最高工作温度必须大于最低温度且不超过200°C', 'JEDEC JESD22', 'error', TRUE),

-- 测试设备验证规则
('TST_EQP_RULE_001', 'TEST_EQUIPMENT', 'parallel_test_sites', 'range', '{"min": 1, "max": 512}', '并行测试站点数必须在1-512范围内', 'SEMI E10', 'error', TRUE),
('TST_EQP_RULE_002', 'TEST_EQUIPMENT', 'test_frequency', 'range', '{"min": 1000, "max": 10000000000}', '测试频率必须在1KHz-10GHz范围内', 'IEEE 1149.1', 'warning', TRUE);
```

#### 2.3.2 IC封测专业验证引擎实现
```java
@Service
public class ICDataValidationEngine {
    
    @Autowired
    private JedecStandardValidator jedecValidator;
    
    @Autowired
    private SemiStandardValidator semiValidator;
    
    public ICValidationResult validateICMasterData(String typeId, ICMasterDataItem item) {
        List<ICValidationRule> rules = getICValidationRules(typeId);
        ICValidationResult result = new ICValidationResult();
        
        for (ICValidationRule rule : rules) {
            switch (rule.getRuleType()) {
                case REQUIRED:
                    validateRequired(item, rule, result);
                    break;
                case UNIQUE:
                    validateUnique(item, rule, result);
                    break;
                case FORMAT:
                    validateFormat(item, rule, result);
                    break;
                case RANGE:
                    validateRange(item, rule, result);
                    break;
                case CUSTOM:
                    validateCustom(item, rule, result);
                    break;
                case JEDEC_STD:
                    validateJedecStandard(item, rule, result);
                    break;
                case SEMI_STD:
                    validateSemiStandard(item, rule, result);
                    break;
            }
        }
        
        return result;
    }
    
    private void validateJedecStandard(ICMasterDataItem item, ICValidationRule rule, 
                                     ICValidationResult result) {
        try {
            JsonNode ruleConfig = objectMapper.readTree(rule.getRuleExpression());
            String standard = ruleConfig.get("standard").asText();
            
            boolean isValid = jedecValidator.validate(item, standard);
            if (!isValid) {
                result.addValidationError(new ValidationError(
                    rule.getFieldName(),
                    rule.getErrorMessage(),
                    rule.getSeverity(),
                    rule.getIndustryStandard()
                ));
            }
        } catch (Exception e) {
            log.error("JEDEC标准验证失败: {}", e.getMessage());
            result.addValidationError(new ValidationError(
                rule.getFieldName(),
                "JEDEC标准验证执行失败",
                Severity.ERROR,
                rule.getIndustryStandard()
            ));
        }
    }
    
    private void validateSemiStandard(ICMasterDataItem item, ICValidationRule rule, 
                                    ICValidationResult result) {
        try {
            JsonNode ruleConfig = objectMapper.readTree(rule.getRuleExpression());
            String standard = ruleConfig.get("standard").asText();
            
            boolean isValid = semiValidator.validate(item, standard);
            if (!isValid) {
                result.addValidationError(new ValidationError(
                    rule.getFieldName(),
                    rule.getErrorMessage(),
                    rule.getSeverity(),
                    rule.getIndustryStandard()
                ));
            }
        } catch (Exception e) {
            log.error("SEMI标准验证失败: {}", e.getMessage());
            result.addValidationError(new ValidationError(
                rule.getFieldName(),
                "SEMI标准验证执行失败",
                Severity.ERROR,
                rule.getIndustryStandard()
            ));
        }
    }
}

// JEDEC标准验证器
@Component
public class JedecStandardValidator {
    
    public boolean validate(ICMasterDataItem item, String standard) {
        switch (standard) {
            case "J-STD-020":
                return validateMoistureSensitivityLevel(item);
            case "JESD22":
                return validateReliabilityTest(item);
            case "JEP95":
                return validatePackageDimensions(item);
            default:
                log.warn("未支持的JEDEC标准: {}", standard);
                return true;
        }
    }
    
    private boolean validateMoistureSensitivityLevel(ICMasterDataItem item) {
        String mslLevel = item.getProperties().get("moisture_level").toString();
        return Arrays.asList("MSL1", "MSL2", "MSL3", "MSL4", "MSL5", "MSL6")
                     .contains(mslLevel);
    }
}

// SEMI标准验证器
@Component  
public class SemiStandardValidator {
    
    public boolean validate(ICMasterDataItem item, String standard) {
        switch (standard) {
            case "SEMI M1-0302":
                return validateWaferSpecification(item);
            case "SEMI E4":
                return validateSecsGemCompliance(item);
            case "SEMI E10":
                return validateTestEquipmentSpec(item);
            default:
                log.warn("未支持的SEMI标准: {}", standard);
                return true;
        }
    }
    
    private boolean validateWaferSpecification(ICMasterDataItem item) {
        if (item.getMaterialCategory() == MaterialCategory.WAFER) {
            String waferSize = item.getProperties().get("wafer_size").toString();
            return Arrays.asList("4_INCH", "6_INCH", "8_INCH", "12_INCH")
                         .contains(waferSize);
        }
        return true;
    }
}
```

### 2.4 版本控制引擎

#### 2.4.1 版本历史表
```sql
-- 主数据变更历史表
CREATE TABLE master_data_history (
    history_id VARCHAR(30) PRIMARY KEY,
    item_id VARCHAR(30),
    version VARCHAR(20),
    change_type ENUM('create','update','delete','activate','deactivate'),
    old_data JSON,                  -- 变更前数据
    new_data JSON,                  -- 变更后数据
    change_reason VARCHAR(500),     -- 变更原因
    changed_by VARCHAR(20),
    changed_at TIMESTAMP,
    
    INDEX idx_item_version (item_id, version),
    INDEX idx_change_time (changed_at)
);
```

#### 2.4.2 版本管理服务
```java
@Service
public class VersionControlService {
    
    public String generateNewVersion(String currentVersion) {
        // 版本号生成逻辑：主版本.次版本.修订版本
        return VersionUtils.incrementVersion(currentVersion);
    }
    
    public void saveHistory(MasterDataItem oldItem, MasterDataItem newItem, 
                           String changeType, String reason, String userId) {
        MasterDataHistory history = new MasterDataHistory();
        history.setItemId(newItem.getItemId());
        history.setVersion(newItem.getVersion());
        history.setChangeType(changeType);
        history.setOldData(JSON.toJSONString(oldItem));
        history.setNewData(JSON.toJSONString(newItem));
        history.setChangeReason(reason);
        history.setChangedBy(userId);
        history.setChangedAt(LocalDateTime.now());
        
        historyRepository.save(history);
    }
}
```

## 3. API接口设计

### 3.1 RESTful API接口

#### 3.1.1 主数据CRUD接口
```yaml
# 获取主数据列表
GET /api/master-data/{typeId}
  parameters:
    - name: typeId
      description: 数据类型ID
    - name: parentId
      description: 父级ID（可选）
    - name: status
      description: 状态过滤
    - name: keyword
      description: 关键词搜索
  response:
    type: array
    items: MasterDataItem

# 创建主数据
POST /api/master-data/{typeId}
  requestBody:
    type: MasterDataItem
  response:
    type: MasterDataItem

# 更新主数据
PUT /api/master-data/{typeId}/{itemId}
  parameters:
    - name: version
      description: 版本号（乐观锁）
  requestBody:
    type: MasterDataItem
  response:
    type: MasterDataItem

# 删除主数据
DELETE /api/master-data/{typeId}/{itemId}
  parameters:
    - name: reason
      description: 删除原因
```

#### 3.1.2 编码生成接口
```yaml
# 生成编码
POST /api/master-data/coding/generate
  requestBody:
    typeId: string
    customData: object
  response:
    code: string
    fullCode: string

# 验证编码唯一性
GET /api/master-data/coding/validate
  parameters:
    - name: typeId
    - name: code
  response:
    isValid: boolean
    message: string
```

### 3.2 事件通知接口

#### 3.2.1 数据变更事件
```java
// 数据创建事件
@Event("master-data.created")
public class MasterDataCreatedEvent {
    private String typeId;
    private String itemId;
    private MasterDataItem item;
    private String createdBy;
    private LocalDateTime createdAt;
}

// 数据更新事件
@Event("master-data.updated")
public class MasterDataUpdatedEvent {
    private String typeId;
    private String itemId;
    private MasterDataItem oldItem;
    private MasterDataItem newItem;
    private String updatedBy;
    private LocalDateTime updatedAt;
}
```

## 4. IC封测配置化支持

### 4.1 IC产品数据类型配置示例
```json
{
  "typeId": "IC_PRODUCT",
  "typeName": "IC产品信息",
  "category": "PRODUCT",
  "tablePrefix": "ic_product",
  "isHierarchical": true,
  "jedecStandard": "JEDEC JEP95",
  "semiStandard": "SEMI T7-0200",
  "codingRule": {
    "ruleId": "IC_PROD_001",
    "pattern": "IC-{package_type:3}-{date:YYYYMM}-{sequence:4}",
    "separator": "-"
  },
  "properties": [
    {
      "name": "packageType",
      "label": "封装类型",
      "type": "enum",
      "required": true,
      "options": ["QFP", "BGA", "CSP", "FC", "DIP", "SOP", "QFN", "LGA"],
      "jedecStandard": "JEDEC MO-220"
    },
    {
      "name": "pinCount",
      "label": "引脚数量",
      "type": "integer",
      "required": true,
      "range": {"min": 1, "max": 2000}
    },
    {
      "name": "dieSizeX",
      "label": "Die尺寸X(mm)",
      "type": "decimal",
      "precision": 3,
      "required": true
    },
    {
      "name": "moistureLevel",
      "label": "湿度敏感等级",
      "type": "enum",
      "required": true,
      "options": ["MSL1", "MSL2", "MSL3", "MSL4", "MSL5", "MSL6"],
      "jedecStandard": "J-STD-020"
    },
    {
      "name": "jedecOutline",
      "label": "JEDEC外形代码",
      "type": "string",
      "required": false,
      "format": "^MO-\\d+[A-Z]*$"
    }
  ],
  "validationRules": [
    {
      "field": "packageType",
      "type": "required",
      "message": "封装类型不能为空"
    },
    {
      "field": "pinCount",
      "type": "range",
      "message": "引脚数量必须在1-2000范围内"
    },
    {
      "field": "moistureLevel",
      "type": "jedec_std",
      "standard": "J-STD-020",
      "message": "湿度敏感等级必须符合JEDEC J-STD-020标准"
    }
  ]
}
```

### 4.2 晶圆物料数据类型配置示例
```json
{
  "typeId": "WAFER_MATERIAL",
  "typeName": "晶圆物料信息",
  "category": "MATERIAL",
  "tablePrefix": "wafer_material",
  "isHierarchical": false,
  "semiStandard": "SEMI M1-0302",
  "codingRule": {
    "ruleId": "WAF_MAT_001",
    "pattern": "WAF-{wafer_size:2}-{date:YYYYMM}-{sequence:4}",
    "separator": "-"
  },
  "properties": [
    {
      "name": "waferSize",
      "label": "晶圆尺寸",
      "type": "enum",
      "required": true,
      "options": ["4_INCH", "6_INCH", "8_INCH", "12_INCH"],
      "semiStandard": "SEMI M1-0302"
    },
    {
      "name": "waferThickness",
      "label": "晶圆厚度(um)",
      "type": "decimal",
      "precision": 2,
      "required": true,
      "range": {"min": 50, "max": 1000}
    },
    {
      "name": "crystalOrientation",
      "label": "晶向",
      "type": "enum",
      "required": true,
      "options": ["100", "111"]
    },
    {
      "name": "dopingType",
      "label": "掺杂类型",
      "type": "enum",
      "required": true,
      "options": ["N_TYPE", "P_TYPE"]
    },
    {
      "name": "diePerWafer",
      "label": "每片芯片数",
      "type": "integer",
      "required": true,
      "range": {"min": 1, "max": 100000}
    }
  ],
  "validationRules": [
    {
      "field": "waferSize",
      "type": "semi_std",
      "standard": "SEMI M1-0302",
      "message": "晶圆尺寸必须符合SEMI M1标准"
    },
    {
      "field": "waferThickness",
      "type": "range",
      "message": "晶圆厚度必须在50-1000um范围内"
    }
  ]
}
```

### 4.3 IC封测专业UI配置化
```json
{
  "formConfig": {
    "layout": "vertical",
    "columns": 3,
    "tabs": [
      {
        "title": "基本信息",
        "fields": [
          {
            "name": "itemCode",
            "label": "IC产品编码",
            "type": "input",
            "readonly": true,
            "autoGenerate": true,
            "tooltip": "符合JEDEC编码规范"
          },
          {
            "name": "itemName",
            "label": "IC产品名称",
            "type": "input",
            "required": true,
            "maxLength": 200
          },
          {
            "name": "packageType",
            "label": "封装类型",
            "type": "select",
            "required": true,
            "options": "PACKAGE_TYPE_OPTIONS",
            "tooltip": "参考JEDEC MO系列标准"
          },
          {
            "name": "pinCount",
            "label": "引脚数量",
            "type": "number",
            "required": true,
            "min": 1,
            "max": 2000
          }
        ]
      },
      {
        "title": "物理规格",
        "fields": [
          {
            "name": "dieSizeX",
            "label": "Die尺寸X (mm)",
            "type": "number",
            "precision": 3,
            "required": true,
            "tooltip": "芯片X方向尺寸"
          },
          {
            "name": "dieSizeY",
            "label": "Die尺寸Y (mm)",
            "type": "number",
            "precision": 3,
            "required": true,
            "tooltip": "芯片Y方向尺寸"
          },
          {
            "name": "packageHeight",
            "label": "封装高度 (mm)",
            "type": "number",
            "precision": 3,
            "required": true
          },
          {
            "name": "pitch",
            "label": "引脚间距 (mm)",
            "type": "number",
            "precision": 3,
            "required": true
          }
        ]
      },
      {
        "title": "可靠性规格",
        "fields": [
          {
            "name": "moistureLevel",
            "label": "湿度敏感等级",
            "type": "select",
            "required": true,
            "options": "MSL_LEVEL_OPTIONS",
            "tooltip": "符合JEDEC J-STD-020标准"
          },
          {
            "name": "operatingTempMin",
            "label": "最低工作温度 (°C)",
            "type": "number",
            "required": true
          },
          {
            "name": "operatingTempMax",
            "label": "最高工作温度 (°C)",
            "type": "number",
            "required": true
          },
          {
            "name": "jedecOutline",
            "label": "JEDEC外形代码",
            "type": "input",
            "pattern": "^MO-\\d+[A-Z]*$",
            "tooltip": "如：MO-220WGGD-1"
          }
        ]
      }
    ]
  },
  "listConfig": {
    "columns": [
      {"field": "itemCode", "label": "IC编码", "width": 140, "fixed": "left"},
      {"field": "itemName", "label": "产品名称", "width": 180},
      {"field": "packageType", "label": "封装类型", "width": 80},
      {"field": "pinCount", "label": "引脚数", "width": 80, "align": "center"},
      {"field": "dieSizeX", "label": "Die尺寸X", "width": 90, "precision": 3},
      {"field": "moistureLevel", "label": "MSL等级", "width": 80, "align": "center"},
      {"field": "jedecOutline", "label": "JEDEC外形", "width": 120},
      {"field": "status", "label": "状态", "width": 80, "align": "center"}
    ],
    "searchFields": ["itemCode", "itemName", "packageType", "jedecOutline"],
    "filterFields": ["packageType", "moistureLevel", "status"],
    "sortField": "itemCode",
    "pageSize": 50
  },
  "importConfig": {
    "supportedFormats": ["xlsx", "csv"],
    "templateColumns": [
      "IC产品编码", "产品名称", "封装类型", "引脚数量", 
      "Die尺寸X", "Die尺寸Y", "封装高度", "引脚间距",
      "湿度敏感等级", "最低工作温度", "最高工作温度", "JEDEC外形代码"
    ],
    "validationOnImport": true,
    "batchSize": 1000
  }
}
```

## 5. 缓存策略

### 5.1 多级缓存设计
```java
@Service
public class MasterDataCacheService {
    
    @Cacheable(value = "master-data", key = "#typeId + ':' + #itemId")
    public MasterDataItem getById(String typeId, String itemId) {
        return masterDataRepository.findByTypeIdAndItemId(typeId, itemId);
    }
    
    @Cacheable(value = "master-data-list", key = "#typeId + ':' + #parentId")
    public List<MasterDataItem> getByType(String typeId, String parentId) {
        return masterDataRepository.findByTypeIdAndParentId(typeId, parentId);
    }
    
    @CacheEvict(value = {"master-data", "master-data-list"}, allEntries = true)
    public void clearCache() {
        // 清除所有缓存
    }
}
```

### 5.2 缓存更新策略
- **创建数据**：清除列表缓存
- **更新数据**：清除对象缓存和列表缓存
- **删除数据**：清除对象缓存和列表缓存
- **状态变更**：清除相关缓存

## 6. 性能优化

### 6.1 数据库优化
- **索引策略**：复合索引优化查询性能
- **分区表**：按数据类型或时间分区
- **读写分离**：查询操作使用只读副本
- **连接池**：优化数据库连接管理

### 6.2 查询优化
- **分页查询**：大数据量分页处理
- **延迟加载**：层级数据按需加载
- **批量操作**：减少数据库交互次数
- **查询缓存**：热点数据缓存

## 7. 安全控制

### 7.1 数据权限
```java
@PreAuthorize("hasPermission(#typeId, 'MASTER_DATA', 'READ')")
public List<MasterDataItem> getByType(String typeId) {
    return masterDataService.getByType(typeId);
}

@PreAuthorize("hasPermission(#typeId, 'MASTER_DATA', 'WRITE')")
public MasterDataItem create(String typeId, MasterDataItem item) {
    return masterDataService.create(typeId, item);
}
```

### 7.2 数据脱敏
```java
@Component
public class DataMaskingProcessor {
    
    public MasterDataItem maskSensitiveData(MasterDataItem item, String userRole) {
        if (!"ADMIN".equals(userRole)) {
            // 非管理员隐藏敏感信息
            item.getProperties().remove("standardCost");
            item.getProperties().remove("supplierPrice");
        }
        return item;
    }
}
```

## 8. 部署架构

### 8.1 微服务部署
```yaml
services:
  master-data-service:
    image: cim/master-data-service:latest
    replicas: 3
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DATABASE_URL=**********************************
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_DATABASE=cim_master
      - MYSQL_ROOT_PASSWORD=password
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:6.2
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
```

## 9. IC封测专业监控指标

### 9.1 IC业务指标
- **IC产品数据量**：按封装类型、引脚数量分类统计
- **晶圆物料数据量**：按晶圆尺寸、厂商分类统计
- **封装物料数据量**：按物料类型（Lead Frame/Substrate/Wire）分类
- **测试设备数据量**：按ATE平台、Handler类型分类
- **JEDEC标准符合率**：各数据类型的标准符合度统计
- **SEMI标准符合率**：设备和工艺数据的标准符合度
- **数据质量评分**：按IC行业质量标准评分（IATF16949）

### 9.2 IC技术指标
- **API响应时间**：IC产品查询、晶圆数据导入响应时间
- **数据库查询性能**：复杂IC规格查询性能监控
- **JEDEC/SEMI验证性能**：标准验证执行时间统计
- **缓存命中率**：IC产品、封装类型等热点数据缓存效率
- **批量导入性能**：Wafer Map、测试数据批量导入性能
- **SECS/GEM集成延迟**：设备数据同步延迟监控

### 9.3 IC行业合规指标
- **JEDEC标准覆盖率**：支持的JEDEC标准数量和覆盖范围
- **SEMI标准覆盖率**：支持的SEMI标准数量和覆盖范围
- **IPC标准符合率**：封装工艺相关IPC标准符合情况
- **追溯完整性**：从Wafer到最终产品的追溯链完整性
- **质量数据一致性**：IC测试数据与质量标准的一致性

### 9.4 实时监控大屏
```javascript
// IC封测监控大屏配置
const ICMasterDataDashboard = {
  title: "IC封测基础数据管理监控中心",
  refreshInterval: 30000, // 30秒刷新
  charts: [
    {
      type: "gauge",
      title: "JEDEC标准符合率",
      dataSource: "/api/metrics/jedec-compliance",
      threshold: { warning: 95, error: 90 }
    },
    {
      type: "bar",
      title: "IC产品按封装类型分布",
      dataSource: "/api/metrics/ic-products-by-package",
      categories: ["QFP", "BGA", "CSP", "FC", "QFN"]
    },
    {
      type: "line",
      title: "数据验证错误趋势",
      dataSource: "/api/metrics/validation-errors-trend",
      timeRange: "24h"
    },
    {
      type: "table",
      title: "今日数据变更TOP10",
      dataSource: "/api/metrics/data-changes-top10",
      columns: ["数据类型", "变更数量", "变更人员", "验证通过率"]
    }
  ]
};
```

---

*该模块是IC封装测试CIM系统的专业基石，专门针对半导体后端制造工艺设计，其设计质量直接影响整个IC封测制造执行系统的可扩展性、行业标准符合性和数据质量管控能力*