<template>
  <div class="chart-widget" :class="widgetClass">
    <div v-if="title" class="chart-header">
      <h3 class="chart-title">
        {{ title }}
      </h3>
      <div class="chart-actions">
        <el-tooltip content="刷新数据" placement="top">
          <el-button
icon="Refresh" size="small"
circle :loading="loading" @click="handleRefresh"
/>
        </el-tooltip>
        <el-tooltip content="全屏显示" placement="top">
          <el-button
icon="FullScreen" size="small"
circle @click="handleFullscreen"
/>
        </el-tooltip>
        <el-dropdown trigger="click">
          <el-button icon="More" size="small" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item icon="Download"
@click="handleExport"
>
导出数据
</el-dropdown-item>
              <el-dropdown-item icon="Setting"
@click="handleSettings"
>
设置
</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <div class="chart-content" :style="chartContentStyle">
      <!-- KPI指标卡 -->
      <div v-if="chartType === 'kpi'" class="kpi-container">
        <div class="kpi-value">
          <span class="kpi-number">{{ formatKpiValue(kpiData.value) }}</span>
          <span class="kpi-unit">{{ kpiData.unit }}</span>
        </div>
        <div class="kpi-label">
          {{ kpiData.label }}
        </div>
        <div v-if="kpiData.change" class="kpi-change" :class="kpiData.changeType">
          <Icon :name="kpiData.changeType === 'increase' ? 'ArrowUp' : 'ArrowDown'" />
          <span>{{ Math.abs(kpiData.change) }}%</span>
        </div>
      </div>

      <!-- 表格显示 -->
      <div v-else-if="chartType === 'table'" class="table-container">
        <el-table
:data="tableData" :height="chartHeight - 20"
size="small" stripe
>
          <el-table-column
            v-for="column in tableColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :formatter="column.formatter"
            :align="column.align || 'left'"
          />
        </el-table>
      </div>

      <!-- 图表显示区域 -->
      <div v-else ref="chartContainer" class="echarts-container" :style="chartContainerStyle">
        <div v-if="loading" class="chart-loading">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span>加载中...</span>
        </div>
        <div v-else-if="error" class="chart-error">
          <el-icon><Warning /></el-icon>
          <span>{{ error }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import { Loading, Warning, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
  import * as echarts from 'echarts'
  import type { ChartType } from '@/types/analytics'

  interface Props {
    /** 图表标题 */
    title?: string
    /** 图表类型 */
    chartType: ChartType
    /** 图表数据 */
    data: any
    /** 图表配置 */
    options?: any
    /** 宽度 */
    width?: number | string
    /** 高度 */
    height?: number | string
    /** 是否加载中 */
    loading?: boolean
    /** 错误信息 */
    error?: string
    /** 是否显示标题 */
    showHeader?: boolean
    /** 主题 */
    theme?: 'light' | 'dark'
    /** 自动刷新间隔（秒）*/
    autoRefreshInterval?: number
  }

  interface Emits {
    (e: 'refresh'): void
    (e: 'fullscreen'): void
    (e: 'export'): void
    (e: 'settings'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    showHeader: true,
    theme: 'light',
    height: 300,
    loading: false
  })

  const emit = defineEmits<Emits>()

  // 引用
  const chartContainer = ref<HTMLElement>()
  const chartInstance = ref<echarts.ECharts>()

  // 状态
  const isFullscreen = ref(false)
  const autoRefreshTimer = ref<NodeJS.Timeout>()

  // 计算属性
  const widgetClass = computed(() => [
    'chart-widget',
    `chart-widget--${props.theme}`,
    { 'chart-widget--fullscreen': isFullscreen.value }
  ])

  const chartContentStyle = computed(() => ({
    height: typeof props.height === 'number' ? `${props.height}px` : props.height
  }))

  const chartContainerStyle = computed(() => ({
    width: '100%',
    height: '100%'
  }))

  const chartHeight = computed(() => {
    if (typeof props.height === 'number') {
      return props.showHeader ? props.height - 50 : props.height
    }
    return 250
  })

  // KPI数据处理
  const kpiData = computed(() => {
    if (props.chartType === 'kpi' && props.data) {
      return {
        value: props.data.value || 0,
        unit: props.data.unit || '',
        label: props.data.label || '',
        change: props.data.change || 0,
        changeType: props.data.change > 0 ? 'increase' : 'decrease'
      }
    }
    return {}
  })

  // 表格数据处理
  const tableData = computed(() => {
    if (props.chartType === 'table' && props.data) {
      return props.data.data || []
    }
    return []
  })

  const tableColumns = computed(() => {
    if (props.chartType === 'table' && props.data) {
      return props.data.columns || []
    }
    return []
  })

  // 格式化KPI值
  const formatKpiValue = (value: number): string => {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M'
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K'
    } else if (value % 1 === 0) {
      return value.toString()
    } else {
      return value.toFixed(2)
    }
  }

  // 初始化图表
  const initChart = async () => {
    if (!chartContainer.value || props.chartType === 'kpi' || props.chartType === 'table') {
      return
    }

    await nextTick()

    try {
      // 销毁已存在的图表实例
      if (chartInstance.value) {
        chartInstance.value.dispose()
      }

      // 创建新的图表实例
      chartInstance.value = echarts.init(chartContainer.value, props.theme)

      // 设置图表配置
      const option = generateChartOption()
      chartInstance.value.setOption(option)

      // 监听窗口大小变化
      const resizeObserver = new ResizeObserver(() => {
        chartInstance.value?.resize()
      })
      resizeObserver.observe(chartContainer.value)
    } catch (error) {
      console.error('图表初始化失败:', error)
      ElMessage.error('图表初始化失败')
    }
  }

  // 生成图表配置
  const generateChartOption = (): any => {
    const baseOption = {
      backgroundColor: 'transparent',
      animation: true,
      animationDuration: 1000,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(50,50,50,0.9)',
        borderColor: 'rgba(50,50,50,0.9)',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        textStyle: {
          color: props.theme === 'dark' ? '#ffffff' : '#333333'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      }
    }

    // 合并用户自定义配置
    if (props.options) {
      return { ...baseOption, ...props.options }
    }

    // 根据图表类型生成默认配置
    switch (props.chartType) {
      case 'line':
        return generateLineChartOption(baseOption)
      case 'bar':
        return generateBarChartOption(baseOption)
      case 'pie':
        return generatePieChartOption(baseOption)
      case 'area':
        return generateAreaChartOption(baseOption)
      default:
        return baseOption
    }
  }

  // 生成折线图配置
  const generateLineChartOption = (baseOption: any): any => {
    return {
      ...baseOption,
      xAxis: {
        type: 'category',
        data: props.data?.categories || [],
        axisLine: {
          lineStyle: {
            color: props.theme === 'dark' ? '#ffffff' : '#333333'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: props.theme === 'dark' ? '#ffffff' : '#333333'
          }
        }
      },
      series: [
        {
          data: props.data?.values || [],
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3
          },
          areaStyle: {
            opacity: 0.1
          }
        }
      ]
    }
  }

  // 生成柱状图配置
  const generateBarChartOption = (baseOption: any): any => {
    return {
      ...baseOption,
      xAxis: {
        type: 'category',
        data: props.data?.categories || [],
        axisLine: {
          lineStyle: {
            color: props.theme === 'dark' ? '#ffffff' : '#333333'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: props.theme === 'dark' ? '#ffffff' : '#333333'
          }
        }
      },
      series: [
        {
          data: props.data?.values || [],
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        }
      ]
    }
  }

  // 生成饼图配置
  const generatePieChartOption = (baseOption: any): any => {
    return {
      ...baseOption,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: props.title || '数据',
          type: 'pie',
          radius: '70%',
          center: ['50%', '50%'],
          data: props.data?.data || [],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  }

  // 生成面积图配置
  const generateAreaChartOption = (baseOption: any): any => {
    return {
      ...baseOption,
      xAxis: {
        type: 'category',
        data: props.data?.categories || []
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: props.data?.values || [],
          type: 'line',
          areaStyle: {
            opacity: 0.6
          },
          smooth: true
        }
      ]
    }
  }

  // 事件处理
  const handleRefresh = () => {
    emit('refresh')
  }

  const handleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
    emit('fullscreen')

    // 调整图表大小
    setTimeout(() => {
      chartInstance.value?.resize()
    }, 300)
  }

  const handleExport = () => {
    emit('export')
  }

  const handleSettings = () => {
    emit('settings')
  }

  // 监听数据变化，更新图表
  watch(
    () => [props.data, props.options],
    () => {
      if (chartInstance.value) {
        const option = generateChartOption()
        chartInstance.value.setOption(option, true)
      }
    },
    { deep: true }
  )

  // 监听主题变化
  watch(
    () => props.theme,
    () => {
      initChart()
    }
  )

  // 设置自动刷新
  const setupAutoRefresh = () => {
    if (props.autoRefreshInterval && props.autoRefreshInterval > 0) {
      autoRefreshTimer.value = setInterval(() => {
        handleRefresh()
      }, props.autoRefreshInterval * 1000)
    }
  }

  const clearAutoRefresh = () => {
    if (autoRefreshTimer.value) {
      clearInterval(autoRefreshTimer.value)
      autoRefreshTimer.value = undefined
    }
  }

  // 生命周期
  onMounted(() => {
    initChart()
    setupAutoRefresh()
  })

  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }
    clearAutoRefresh()
  })

  // 暴露方法
  defineExpose({
    refresh: initChart,
    resize: () => chartInstance.value?.resize(),
    getImage: () =>
      chartInstance.value?.getDataURL({
        type: 'png',
        backgroundColor: '#fff'
      })
  })
</script>

<style lang="scss" scoped>
  .chart-widget {
    overflow: hidden;
    background-color: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);

    &:hover {
      box-shadow: var(--shadow-md);
    }

    &--fullscreen {
      position: fixed;
      inset: 0;
      z-index: var(--z-modal);
      border-radius: 0;
    }

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      background-color: var(--color-bg-secondary);
      border-bottom: 1px solid var(--color-border-light);

      .chart-title {
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);
      }

      .chart-actions {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    .chart-content {
      position: relative;

      .echarts-container {
        position: relative;

        .chart-loading {
          position: absolute;
          top: 50%;
          left: 50%;
          display: flex;
          flex-direction: column;
          gap: var(--spacing-2);
          align-items: center;
          color: var(--color-text-secondary);
          transform: translate(-50%, -50%);

          .el-icon {
            font-size: 24px;
          }
        }

        .chart-error {
          position: absolute;
          top: 50%;
          left: 50%;
          display: flex;
          flex-direction: column;
          gap: var(--spacing-2);
          align-items: center;
          color: var(--color-danger);
          transform: translate(-50%, -50%);

          .el-icon {
            font-size: 24px;
          }
        }
      }
    }
  }

  // KPI指标卡样式
  .kpi-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: var(--spacing-4);

    .kpi-value {
      display: flex;
      align-items: baseline;
      margin-bottom: var(--spacing-2);

      .kpi-number {
        font-size: 2.5rem;
        font-weight: var(--font-weight-bold);
        line-height: 1;
        color: var(--color-primary);
      }

      .kpi-unit {
        margin-left: var(--spacing-1);
        font-size: var(--font-size-lg);
        color: var(--color-text-secondary);
      }
    }

    .kpi-label {
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-base);
      color: var(--color-text-primary);
      text-align: center;
    }

    .kpi-change {
      display: flex;
      gap: 4px;
      align-items: center;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);

      &.increase {
        color: var(--color-success);
      }

      &.decrease {
        color: var(--color-danger);
      }
    }
  }

  // 表格容器样式
  .table-container {
    height: 100%;
    padding: var(--spacing-2);

    :deep(.el-table) {
      .el-table__header th {
        background-color: var(--color-bg-tertiary);
      }
    }
  }

  // 暗色主题适配
  .chart-widget--dark {
    background-color: var(--color-bg-primary);
    border-color: var(--color-border-dark);

    .chart-header {
      background-color: var(--color-bg-secondary);
      border-color: var(--color-border-dark);

      .chart-title {
        color: var(--color-text-primary);
      }
    }
  }
</style>
