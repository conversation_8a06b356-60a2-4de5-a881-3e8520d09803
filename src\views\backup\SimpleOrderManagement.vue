<template>
  <div class="order-management">
    <div class="page-header">
      <h1>📋 订单管理</h1>
      <p>IC封测CIM系统 - 订单全生命周期管理</p>

      <div class="action-buttons">
        <button class="btn btn-primary"
@click="showCreateModal = true"
>
+ 新建订单
</button>
        <button class="btn btn-secondary"
@click="refreshData"
>
🔄 刷新数据
</button>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <div class="search-grid">
        <input
v-model="searchForm.orderNo" type="text"
placeholder="订单号" class="input-field"
/>
        <input
          v-model="searchForm.customerName"
          type="text"
          placeholder="客户名称"
          class="input-field"
        />
        <select v-model="searchForm.status" class="input-field">
          <option value="">全部状态</option>
          <option value="pending">待处理</option>
          <option value="processing">进行中</option>
          <option value="completed">已完成</option>
          <option value="cancelled">已取消</option>
        </select>
        <select v-model="searchForm.packageType" class="input-field">
          <option value="">全部封装</option>
          <option value="QFP">QFP</option>
          <option value="BGA">BGA</option>
          <option value="CSP">CSP</option>
          <option value="FC">FC</option>
        </select>
        <button class="btn btn-primary"
@click="searchOrders"
>
🔍 搜索
</button>
        <button class="btn btn-secondary"
@click="resetSearch"
>
重置
</button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-header">
        <h3>订单列表 ({{ filteredOrders.length }})</h3>
        <div class="pagination-info">
第 {{ currentPage }} 页，共 {{ totalPages }} 页
</div>
      </div>

      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>订单号</th>
              <th>客户名称</th>
              <th>封装类型</th>
              <th>数量</th>
              <th>金额</th>
              <th>状态</th>
              <th>当前阶段</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="order in paginatedOrders" :key="order.id">
              <td class="order-no">
                {{ order.orderNo }}
              </td>
              <td>{{ order.customerName }}</td>
              <td>
                <span class="package-type" :class="order.packageType.toLowerCase()">
                  {{ order.packageType }}
                </span>
              </td>
              <td class="number">
                {{ order.quantity.toLocaleString() }}
              </td>
              <td class="currency">¥{{ order.amount.toLocaleString() }}</td>
              <td>
                <span class="status-badge" :class="order.status">
                  {{ getStatusText(order.status) }}
                </span>
              </td>
              <td>
                <span class="stage-badge" :class="order.currentStage">
                  {{ getStageText(order.currentStage) }}
                </span>
              </td>
              <td>
                <button class="btn-small btn-info"
@click="viewOrder(order)"
>
查看
</button>
                <button class="btn-small btn-warning"
@click="editOrder(order)"
>
编辑
</button>
                <button class="btn-small btn-danger"
@click="deleteOrder(order)"
>
删除
</button>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-if="filteredOrders.length === 0" class="empty-state">
          <p>🔍 暂无订单数据</p>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button
          :disabled="currentPage === 1"
          class="btn btn-secondary"
          @click="currentPage = Math.max(1, currentPage - 1)"
        >
          上一页
        </button>
        <span class="page-numbers">
          <button
            v-for="page in pageNumbers"
            :key="page"
            :class="['page-btn', { active: currentPage === page }]"
            @click="currentPage = page"
          >
            {{ page }}
          </button>
        </span>
        <button
          :disabled="currentPage === totalPages"
          class="btn btn-secondary"
          @click="currentPage = Math.min(totalPages, currentPage + 1)"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 创建/编辑模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-backdrop" @click="closeModals">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showCreateModal ? '新建订单' : '编辑订单' }}</h3>
          <button class="modal-close"
@click="closeModals"
>
✕
</button>
        </div>

        <div class="modal-body">
          <div class="form-grid">
            <div class="form-group">
              <label>订单号</label>
              <input
                v-model="orderForm.orderNo"
                type="text"
                class="input-field"
                placeholder="自动生成"
                readonly
              />
            </div>
            <div class="form-group">
              <label>客户名称 *</label>
              <input
                v-model="orderForm.customerName"
                type="text"
                class="input-field"
                placeholder="输入客户名称"
              />
            </div>
            <div class="form-group">
              <label>封装类型 *</label>
              <select v-model="orderForm.packageType" class="input-field">
                <option value="">请选择</option>
                <option value="QFP">QFP - 四方扁平封装</option>
                <option value="BGA">BGA - 球栅阵列封装</option>
                <option value="CSP">CSP - 芯片尺寸封装</option>
                <option value="FC">FC - 倒装芯片</option>
              </select>
            </div>
            <div class="form-group">
              <label>数量 *</label>
              <input
                v-model="orderForm.quantity"
                type="number"
                class="input-field"
                placeholder="芯片数量"
              />
            </div>
            <div class="form-group">
              <label>单价 (元)</label>
              <input
                v-model="orderForm.unitPrice"
                type="number"
                step="0.01"
                class="input-field"
                placeholder="单价"
              />
            </div>
            <div class="form-group">
              <label>总金额</label>
              <input
                :value="calculateTotal()"
                type="text"
                class="input-field"
                placeholder="自动计算"
                readonly
              />
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-secondary"
@click="closeModals"
>
取消
</button>
          <button
class="btn btn-primary" @click="saveOrder"
>
            {{ showCreateModal ? '创建' : '保存' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'

  // 数据状态
  const orders = ref([
    {
      id: 'ORD001',
      orderNo: 'IC2024001',
      customerName: 'ABC半导体有限公司',
      packageType: 'QFP',
      quantity: 10000,
      amount: 50000,
      status: 'processing',
      currentStage: 'cp-test',
      createTime: '2024-01-15'
    },
    {
      id: 'ORD002',
      orderNo: 'IC2024002',
      customerName: 'XYZ电子科技',
      packageType: 'BGA',
      quantity: 5000,
      amount: 35000,
      status: 'completed',
      currentStage: 'delivery',
      createTime: '2024-01-10'
    },
    {
      id: 'ORD003',
      orderNo: 'IC2024003',
      customerName: 'DEF芯片公司',
      packageType: 'CSP',
      quantity: 15000,
      amount: 75000,
      status: 'pending',
      currentStage: 'pending',
      createTime: '2024-01-20'
    }
  ])

  const searchForm = ref({
    orderNo: '',
    customerName: '',
    status: '',
    packageType: ''
  })

  const orderForm = ref({
    orderNo: '',
    customerName: '',
    packageType: '',
    quantity: null,
    unitPrice: null
  })

  // 模态框状态
  const showCreateModal = ref(false)
  const showEditModal = ref(false)
  const editingOrder = ref(null)

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 计算属性
  const filteredOrders = computed(() => {
    let result = orders.value

    if (searchForm.value.orderNo) {
      result = result.filter(order =>
        order.orderNo.toLowerCase().includes(searchForm.value.orderNo.toLowerCase())
      )
    }

    if (searchForm.value.customerName) {
      result = result.filter(order =>
        order.customerName.toLowerCase().includes(searchForm.value.customerName.toLowerCase())
      )
    }

    if (searchForm.value.status) {
      result = result.filter(order => order.status === searchForm.value.status)
    }

    if (searchForm.value.packageType) {
      result = result.filter(order => order.packageType === searchForm.value.packageType)
    }

    return result
  })

  const totalPages = computed(() => Math.ceil(filteredOrders.value.length / pageSize.value))

  const paginatedOrders = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return filteredOrders.value.slice(start, end)
  })

  const pageNumbers = computed(() => {
    const pages = []
    const total = totalPages.value
    const current = currentPage.value

    let start = Math.max(1, current - 2)
    const end = Math.min(total, start + 4)

    if (end - start < 4) {
      start = Math.max(1, end - 4)
    }

    for (let i = start; i <= end; i++) {
      pages.push(i)
    }

    return pages
  })

  // 方法
  const getStatusText = (status: string) => {
    const statusMap = {
      pending: '待处理',
      processing: '进行中',
      completed: '已完成',
      cancelled: '已取消'
    }
    return statusMap[status] || status
  }

  const getStageText = (stage: string) => {
    const stageMap = {
      pending: '待开始',
      'cp-test': 'CP测试',
      assembly: '封装工艺',
      'ft-test': 'FT测试',
      delivery: '交付完成'
    }
    return stageMap[stage] || stage
  }

  const searchOrders = () => {
    currentPage.value = 1
    console.log('搜索订单:', searchForm.value)
  }

  const resetSearch = () => {
    searchForm.value = {
      orderNo: '',
      customerName: '',
      status: '',
      packageType: ''
    }
    currentPage.value = 1
  }

  const refreshData = () => {
    console.log('刷新数据')
  }

  const viewOrder = order => {
    console.log('查看订单:', order)
  }

  const editOrder = order => {
    editingOrder.value = order
    orderForm.value = { ...order }
    showEditModal.value = true
  }

  const deleteOrder = order => {
    if (confirm(`确定要删除订单 ${order.orderNo} 吗？`)) {
      const index = orders.value.findIndex(o => o.id === order.id)
      if (index > -1) {
        orders.value.splice(index, 1)
      }
    }
  }

  const closeModals = () => {
    showCreateModal.value = false
    showEditModal.value = false
    editingOrder.value = null
    orderForm.value = {
      orderNo: '',
      customerName: '',
      packageType: '',
      quantity: null,
      unitPrice: null
    }
  }

  const calculateTotal = () => {
    if (orderForm.value.quantity && orderForm.value.unitPrice) {
      return (orderForm.value.quantity * orderForm.value.unitPrice).toLocaleString()
    }
    return ''
  }

  const saveOrder = () => {
    // 简单验证
    if (
      !orderForm.value.customerName ||
      !orderForm.value.packageType ||
      !orderForm.value.quantity
    ) {
      alert('请填写必填字段')
      return
    }

    if (showCreateModal.value) {
      // 创建新订单
      const newOrder = {
        id: 'ORD' + Date.now(),
        orderNo: 'IC2024' + String(orders.value.length + 1).padStart(3, '0'),
        customerName: orderForm.value.customerName,
        packageType: orderForm.value.packageType,
        quantity: Number(orderForm.value.quantity),
        amount: Number(orderForm.value.quantity) * Number(orderForm.value.unitPrice || 5),
        status: 'pending',
        currentStage: 'pending',
        createTime: new Date().toISOString().split('T')[0]
      }
      orders.value.push(newOrder)
    } else {
      // 更新订单
      const index = orders.value.findIndex(o => o.id === editingOrder.value.id)
      if (index > -1) {
        orders.value[index] = {
          ...orders.value[index],
          ...orderForm.value,
          quantity: Number(orderForm.value.quantity),
          amount: Number(orderForm.value.quantity) * Number(orderForm.value.unitPrice || 5)
        }
      }
    }

    closeModals()
  }

  onMounted(() => {
    console.log('订单管理页面加载完成')
  })
</script>

<style scoped>
  .order-management {
    min-height: calc(100vh - 100px);
    padding: 1.5rem;
    background: #f9fafb;
  }

  .page-header {
    padding: 2rem;
    margin-bottom: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .page-header h1 {
    margin: 0 0 0.5rem;
    font-size: 2rem;
    color: #1f2937;
  }

  .page-header p {
    margin: 0 0 1.5rem;
    color: #6b7280;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
  }

  .search-section {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .search-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
  }

  .table-section {
    overflow: hidden;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .table-header h3 {
    margin: 0;
    color: #1f2937;
  }

  .pagination-info {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .table-container {
    overflow-x: auto;
  }

  .data-table {
    width: 100%;
    border-collapse: collapse;
  }

  .data-table th,
  .data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  .data-table th {
    font-weight: 600;
    color: #374151;
    background: #f9fafb;
  }

  .data-table tbody tr:hover {
    background: #f9fafb;
  }

  .order-no {
    font-family: monospace;
    font-weight: 600;
    color: #2563eb;
  }

  .number,
  .currency {
    text-align: right;
  }

  .package-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 4px;
  }

  .package-type.qfp {
    color: #1d4ed8;
    background: #dbeafe;
  }

  .package-type.bga {
    color: #166534;
    background: #dcfce7;
  }

  .package-type.csp {
    color: #92400e;
    background: #fef3c7;
  }

  .package-type.fc {
    color: #be185d;
    background: #fce7f3;
  }

  .status-badge,
  .stage-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
  }

  .status-badge.pending {
    color: #92400e;
    background: #fef3c7;
  }

  .status-badge.processing {
    color: #1d4ed8;
    background: #dbeafe;
  }

  .status-badge.completed {
    color: #166534;
    background: #dcfce7;
  }

  .status-badge.cancelled {
    color: #dc2626;
    background: #fee2e2;
  }

  .stage-badge.pending {
    color: #6b7280;
    background: #f3f4f6;
  }

  .stage-badge.cp-test {
    color: #1d4ed8;
    background: #dbeafe;
  }

  .stage-badge.assembly {
    color: #92400e;
    background: #fef3c7;
  }

  .stage-badge.ft-test {
    color: #166534;
    background: #dcfce7;
  }

  .stage-badge.delivery {
    color: #0369a1;
    background: #e0f2fe;
  }

  .btn,
  .btn-small {
    padding: 0.5rem 1rem;
    font-weight: 500;
    cursor: pointer;
    border: none;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .btn-small {
    padding: 0.25rem 0.5rem;
    margin-right: 0.25rem;
    font-size: 0.875rem;
  }

  .btn-primary {
    color: white;
    background: #2563eb;
  }

  .btn-primary:hover {
    background: #1d4ed8;
  }

  .btn-secondary {
    color: #374151;
    background: #e5e7eb;
  }

  .btn-secondary:hover {
    background: #d1d5db;
  }

  .btn-info {
    color: white;
    background: #06b6d4;
  }

  .btn-warning {
    color: white;
    background: #f59e0b;
  }

  .btn-danger {
    color: white;
    background: #dc2626;
  }

  .btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .input-field {
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    transition: border-color 0.2s ease;
  }

  .input-field:focus {
    border-color: #2563eb;
    outline: none;
    box-shadow: 0 0 0 3px rgb(37 99 235 / 10%);
  }

  .pagination {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .page-numbers {
    display: flex;
    gap: 0.25rem;
  }

  .page-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    cursor: pointer;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
  }

  .page-btn:hover,
  .page-btn.active {
    color: white;
    background: #2563eb;
    border-color: #2563eb;
  }

  .empty-state {
    padding: 3rem;
    color: #6b7280;
    text-align: center;
  }

  .modal-backdrop {
    position: fixed;
    inset: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgb(0 0 0 / 50%);
  }

  .modal-content {
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .modal-header h3 {
    margin: 0;
    color: #1f2937;
  }

  .modal-close {
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    background: none;
    border: none;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
  }

  .form-group label {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }

  .modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  @media (width <= 768px) {
    .order-management {
      padding: 1rem;
    }

    .search-grid {
      grid-template-columns: 1fr;
    }

    .action-buttons {
      flex-direction: column;
    }

    .table-header {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .pagination {
      flex-wrap: wrap;
    }

    .modal-content {
      width: 95%;
      margin: 1rem;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
