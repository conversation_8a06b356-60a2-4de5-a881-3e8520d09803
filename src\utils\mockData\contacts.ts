/**
 * IC封测CIM系统 - 客户联系人模拟数据
 * Customer Contact Mock Data for IC Packaging & Testing CIM System
 */

import type {
  Contact,
  ContactRole,
  ContactType,
  ContactStatus,
  ContactImportance,
  InfluenceLevel,
  CommunicationPreference,
  CommunicationRecord,
  CommunicationRecordType
} from '@/types/customer'

// IC设计行业专业联系人模拟数据
export const mockContacts: Contact[] = [
  // 华为海思半导体有限公司联系人
  {
    id: 'contact_001',
    customerId: 'customer_001',
    name: '张伟',
    englishName: '<PERSON>',
    position: '采购总监',
    department: '供应链管理部',
    level: '总监',
    email: '<EMAIL>',
    phone: '021-12345678',
    mobile: '***********',
    wechat: 'zw_hisilicon',
    dingTalk: 'zhang.wei_hs',
    role: 'PurchaseManager' as ContactRole,
    contactType: ['Procurement', 'Business', 'Decision'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 5 as ContactImportance,
    trustLevel: 4 as ContactImportance,
    influenceScore: 9 as InfluenceLevel,
    isDecisionMaker: true,
    isPrimaryContact: true,
    hireDate: '2018-03-01',
    reportTo: '供应链副总裁',
    industryExperience: 12,
    professionalDomain: ['供应链管理', '成本控制', '供应商开发'],
    languagePreference: ['中文', '英文'],
    communicationPreference: ['Email', 'Phone', 'InPerson'] as CommunicationPreference[],
    preferredContactTime: '工作日 9:00-18:00',
    timezone: 'GMT+8',
    lastContactDate: '2024-01-15',
    communicationFrequency: 8,
    totalCommunications: 45,
    specialties: ['供应链管理', '成本控制', '供应商管理', '合同谈判'],
    hobbies: ['高尔夫', '书法'],
    birthday: '1985-06-15',
    remarks: '重要决策者，负责供应商选择和合同谈判。对成本敏感，注重长期合作关系。',
    createdAt: '2023-01-15T08:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    createdBy: 'admin'
  },
  {
    id: 'contact_002',
    customerId: 'customer_001',
    name: '李明',
    englishName: 'Ming Li',
    position: '封装技术经理',
    department: '技术部',
    level: '经理',
    email: '<EMAIL>',
    phone: '021-12345679',
    mobile: '13800138002',
    wechat: 'liming_tech',
    role: 'PackageEngineer' as ContactRole,
    contactType: ['Technical'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 4 as ContactImportance,
    trustLevel: 5 as ContactImportance,
    influenceScore: 7 as InfluenceLevel,
    isDecisionMaker: false,
    isPrimaryContact: false,
    hireDate: '2019-08-15',
    reportTo: '技术总监',
    industryExperience: 8,
    professionalDomain: ['封装技术', 'BGA技术', 'FC技术'],
    languagePreference: ['中文', '英文'],
    communicationPreference: ['Email', 'WeChat', 'VideoConference'] as CommunicationPreference[],
    preferredContactTime: '工作日 10:00-17:00',
    timezone: 'GMT+8',
    lastContactDate: '2024-01-12',
    communicationFrequency: 6,
    totalCommunications: 28,
    specialties: ['封装技术', 'BGA封装', 'FC封装', '可靠性测试'],
    hobbies: ['摄影', '登山'],
    birthday: '1988-09-20',
    remarks: '技术专家，封装工艺方面的主要联系人。对新技术敏感，乐于分享技术见解。',
    createdAt: '2023-02-20T08:00:00Z',
    updatedAt: '2024-01-12T14:20:00Z',
    createdBy: 'admin'
  },
  {
    id: 'contact_003',
    customerId: 'customer_001',
    name: '王雪',
    englishName: 'Snow Wang',
    position: '质量工程师',
    department: '质量管理部',
    level: '高级工程师',
    email: '<EMAIL>',
    phone: '021-12345680',
    mobile: '13800138003',
    wechat: 'snowwang_qa',
    role: 'QualityEngineer' as ContactRole,
    contactType: ['Quality', 'Technical'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 4 as ContactImportance,
    trustLevel: 4 as ContactImportance,
    influenceScore: 6 as InfluenceLevel,
    isDecisionMaker: false,
    isPrimaryContact: false,
    hireDate: '2020-01-10',
    reportTo: '质量经理',
    industryExperience: 6,
    professionalDomain: ['质量管理', 'SPC控制', '失效分析'],
    languagePreference: ['中文'],
    communicationPreference: ['Email', 'Phone'] as CommunicationPreference[],
    preferredContactTime: '工作日 9:00-17:30',
    timezone: 'GMT+8',
    lastContactDate: '2024-01-08',
    communicationFrequency: 4,
    totalCommunications: 22,
    specialties: ['质量管理', 'SPC控制', '失效分析', 'IATF16949'],
    hobbies: ['瑜伽', '阅读'],
    birthday: '1990-03-12',
    remarks: '质量管理专家，严格按照标准执行，对质量问题反应迅速。',
    createdAt: '2023-03-15T08:00:00Z',
    updatedAt: '2024-01-08T16:45:00Z',
    createdBy: 'admin'
  },

  // 紫光展锐科技有限公司联系人
  {
    id: 'contact_004',
    customerId: 'customer_002',
    name: '王强',
    englishName: 'Qiang Wang',
    position: '供应链总监',
    department: '供应链管理中心',
    level: '总监',
    email: '<EMAIL>',
    phone: '021-87654321',
    mobile: '***********',
    wechat: 'wangqiang_unisoc',
    dingTalk: 'wang.qiang_unisoc',
    role: 'SupplyChainDirector' as ContactRole,
    contactType: ['Procurement', 'Business', 'Decision'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 5 as ContactImportance,
    trustLevel: 4 as ContactImportance,
    influenceScore: 8 as InfluenceLevel,
    isDecisionMaker: true,
    isPrimaryContact: true,
    hireDate: '2017-05-20',
    reportTo: '运营副总裁',
    industryExperience: 15,
    professionalDomain: ['供应链战略', '成本优化', '风险管控'],
    languagePreference: ['中文', '英文'],
    communicationPreference: ['Phone', 'InPerson', 'VideoConference'] as CommunicationPreference[],
    preferredContactTime: '工作日 9:00-18:00',
    timezone: 'GMT+8',
    lastContactDate: '2024-01-10',
    communicationFrequency: 10,
    totalCommunications: 52,
    specialties: ['供应链战略', '成本优化', '风险管控', '全球采购'],
    hobbies: ['网球', '收藏'],
    birthday: '1980-11-08',
    remarks: '战略合作主要决策者，对成本和质量要求很高。喜欢数据驱动的决策方式。',
    createdAt: '2023-03-10T08:00:00Z',
    updatedAt: '2024-01-10T16:40:00Z',
    createdBy: 'admin'
  },
  {
    id: 'contact_005',
    customerId: 'customer_002',
    name: '陈晓东',
    englishName: 'Xiaodong Chen',
    position: '射频技术经理',
    department: '射频技术部',
    level: '经理',
    email: '<EMAIL>',
    phone: '021-87654322',
    mobile: '13900139002',
    wechat: 'chenxd_rf',
    role: 'TechnicalDirector' as ContactRole,
    contactType: ['Technical'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 4 as ContactImportance,
    trustLevel: 5 as ContactImportance,
    influenceScore: 7 as InfluenceLevel,
    isDecisionMaker: false,
    isPrimaryContact: false,
    hireDate: '2018-09-01',
    reportTo: '技术副总监',
    industryExperience: 10,
    professionalDomain: ['射频电路', '5G技术', 'WiFi技术'],
    languagePreference: ['中文', '英文'],
    communicationPreference: ['Email', 'WeChat', 'Teams'] as CommunicationPreference[],
    preferredContactTime: '工作日 10:00-19:00',
    timezone: 'GMT+8',
    lastContactDate: '2024-01-05',
    communicationFrequency: 5,
    totalCommunications: 31,
    specialties: ['射频电路', '5G技术', 'WiFi技术', '天线设计'],
    hobbies: ['无线电', '电子制作'],
    birthday: '1987-07-22',
    remarks: '射频技术专家，对5G和WiFi技术有深入理解。技术导向，注重创新。',
    createdAt: '2023-04-15T08:00:00Z',
    updatedAt: '2024-01-05T11:20:00Z',
    createdBy: 'admin'
  },

  // 比亚迪半导体股份有限公司联系人
  {
    id: 'contact_006',
    customerId: 'customer_003',
    name: '陈志华',
    englishName: 'Zhihua Chen',
    position: '采购部经理',
    department: '供应链采购部',
    level: '经理',
    email: '<EMAIL>',
    phone: '0755-89888001',
    mobile: '***********',
    wechat: 'chenzhihua_byd',
    role: 'PurchaseManager' as ContactRole,
    contactType: ['Procurement', 'Business'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 4 as ContactImportance,
    trustLevel: 4 as ContactImportance,
    influenceScore: 6 as InfluenceLevel,
    isDecisionMaker: false,
    isPrimaryContact: true,
    hireDate: '2019-03-15',
    reportTo: '采购总监',
    industryExperience: 9,
    professionalDomain: ['汽车电子采购', '功率器件采购', '成本控制'],
    languagePreference: ['中文'],
    communicationPreference: ['Phone', 'WeChat', 'InPerson'] as CommunicationPreference[],
    preferredContactTime: '工作日 8:30-17:30',
    timezone: 'GMT+8',
    lastContactDate: '2024-01-03',
    communicationFrequency: 6,
    totalCommunications: 38,
    specialties: ['汽车电子采购', '功率器件采购', '成本控制', 'IATF16949'],
    hobbies: ['汽车', '篮球'],
    birthday: '1986-12-05',
    remarks: '汽车半导体采购专家，熟悉汽车行业质量要求。注重供应商资质和认证。',
    createdAt: '2023-05-20T08:00:00Z',
    updatedAt: '2024-01-03T14:15:00Z',
    createdBy: 'admin'
  },
  {
    id: 'contact_007',
    customerId: 'customer_003',
    name: '刘建强',
    englishName: 'Jianqiang Liu',
    position: '功率器件工程师',
    department: '功率器件技术部',
    level: '高级工程师',
    email: '<EMAIL>',
    phone: '0755-89888002',
    mobile: '13700137002',
    wechat: 'liujq_power',
    role: 'TestEngineer' as ContactRole,
    contactType: ['Technical'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 4 as ContactImportance,
    trustLevel: 5 as ContactImportance,
    influenceScore: 6 as InfluenceLevel,
    isDecisionMaker: false,
    isPrimaryContact: false,
    hireDate: '2020-06-01',
    reportTo: '技术经理',
    industryExperience: 7,
    professionalDomain: ['功率器件', 'IGBT技术', '电动汽车电控'],
    languagePreference: ['中文'],
    communicationPreference: ['Email', 'WeChat'] as CommunicationPreference[],
    preferredContactTime: '工作日 9:00-18:00',
    timezone: 'GMT+8',
    lastContactDate: '2023-12-28',
    communicationFrequency: 3,
    totalCommunications: 19,
    specialties: ['功率器件', 'IGBT技术', '电动汽车电控', '热管理'],
    hobbies: ['电动车', '编程'],
    birthday: '1989-04-18',
    remarks: '功率器件技术专家，专注于电动汽车电控系统。技术扎实，善于解决复杂问题。',
    createdAt: '2023-06-01T08:00:00Z',
    updatedAt: '2023-12-28T15:30:00Z',
    createdBy: 'admin'
  },

  // 兆易创新科技集团股份有限公司联系人
  {
    id: 'contact_008',
    customerId: 'customer_004',
    name: '刘建华',
    englishName: 'Jianhua Liu',
    position: '供应链总监',
    department: '运营管理中心',
    level: '总监',
    email: '<EMAIL>',
    phone: '010-82881001',
    mobile: '***********',
    wechat: 'liujianhua_gd',
    role: 'SupplyChainDirector' as ContactRole,
    contactType: ['Procurement', 'Business', 'Decision'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 5 as ContactImportance,
    trustLevel: 4 as ContactImportance,
    influenceScore: 8 as InfluenceLevel,
    isDecisionMaker: true,
    isPrimaryContact: true,
    hireDate: '2016-11-01',
    reportTo: '运营副总裁',
    industryExperience: 18,
    professionalDomain: ['存储器供应链', 'MCU供应链', '成本管控'],
    languagePreference: ['中文', '英文'],
    communicationPreference: ['Email', 'Phone', 'InPerson'] as CommunicationPreference[],
    preferredContactTime: '工作日 9:00-18:00',
    timezone: 'GMT+8',
    lastContactDate: '2023-12-25',
    communicationFrequency: 7,
    totalCommunications: 41,
    specialties: ['存储器供应链', 'MCU供应链', '成本管控', '产能规划'],
    hobbies: ['围棋', '古典音乐'],
    birthday: '1978-08-30',
    remarks: '资深供应链专家，在存储和MCU领域有丰富经验。决策谨慎，注重长期规划。',
    createdAt: '2023-07-10T08:00:00Z',
    updatedAt: '2023-12-25T17:20:00Z',
    createdBy: 'admin'
  },
  {
    id: 'contact_009',
    customerId: 'customer_004',
    name: '张丽娟',
    englishName: 'Lijuan Zhang',
    position: 'MCU产品经理',
    department: 'MCU产品部',
    level: '经理',
    email: '<EMAIL>',
    phone: '010-82881002',
    mobile: '***********',
    wechat: 'zhanglijuan_mcu',
    role: 'PMManager' as ContactRole,
    contactType: ['Business', 'Technical'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 4 as ContactImportance,
    trustLevel: 4 as ContactImportance,
    influenceScore: 6 as InfluenceLevel,
    isDecisionMaker: false,
    isPrimaryContact: false,
    hireDate: '2019-07-15',
    reportTo: '产品总监',
    industryExperience: 8,
    professionalDomain: ['MCU产品', '物联网应用', '市场分析'],
    languagePreference: ['中文', '英文'],
    communicationPreference: ['Email', 'VideoConference', 'WeChat'] as CommunicationPreference[],
    preferredContactTime: '工作日 9:30-18:30',
    timezone: 'GMT+8',
    lastContactDate: '2023-12-20',
    communicationFrequency: 5,
    totalCommunications: 26,
    specialties: ['MCU产品', '物联网应用', '市场分析', '客户需求'],
    hobbies: ['旅游', '美食'],
    birthday: '1988-02-14',
    remarks: 'MCU产品专家，对市场需求敏感，善于协调技术和商务资源。',
    createdAt: '2023-08-01T08:00:00Z',
    updatedAt: '2023-12-20T10:45:00Z',
    createdBy: 'admin'
  },

  // 汇顶科技股份有限公司联系人
  {
    id: 'contact_010',
    customerId: 'customer_005',
    name: '胡晓峰',
    englishName: 'Xiaofeng Hu',
    position: '采购经理',
    department: '供应链管理部',
    level: '经理',
    email: '<EMAIL>',
    phone: '0755-33338001',
    mobile: '13500135001',
    wechat: 'huxiaofeng_goodix',
    role: 'PurchaseManager' as ContactRole,
    contactType: ['Procurement'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 3 as ContactImportance,
    trustLevel: 3 as ContactImportance,
    influenceScore: 5 as InfluenceLevel,
    isDecisionMaker: false,
    isPrimaryContact: true,
    hireDate: '2020-04-01',
    reportTo: '采购总监',
    industryExperience: 6,
    professionalDomain: ['消费电子采购', '混合信号IC采购', '成本优化'],
    languagePreference: ['中文'],
    communicationPreference: ['Phone', 'WeChat', 'Email'] as CommunicationPreference[],
    preferredContactTime: '工作日 9:00-17:30',
    timezone: 'GMT+8',
    lastContactDate: '2023-12-15',
    communicationFrequency: 4,
    totalCommunications: 24,
    specialties: ['消费电子采购', '混合信号IC采购', '成本优化', '供应商管理'],
    hobbies: ['摄影', '健身'],
    birthday: '1990-10-25',
    remarks: '消费电子采购经验丰富，对成本控制要求严格，注重供应商长期合作。',
    createdAt: '2023-09-15T08:00:00Z',
    updatedAt: '2023-12-15T16:30:00Z',
    createdBy: 'admin'
  },
  {
    id: 'contact_011',
    customerId: 'customer_005',
    name: '苏雅琴',
    englishName: 'Yaqin Su',
    position: '混合信号IC工程师',
    department: '芯片设计部',
    level: '高级工程师',
    email: '<EMAIL>',
    phone: '0755-33338002',
    mobile: '13500135002',
    wechat: 'suyaqin_analog',
    role: 'DesignManager' as ContactRole,
    contactType: ['Technical'] as ContactType[],
    status: 'active' as ContactStatus,
    importance: 3 as ContactImportance,
    trustLevel: 4 as ContactImportance,
    influenceScore: 5 as InfluenceLevel,
    isDecisionMaker: false,
    isPrimaryContact: false,
    hireDate: '2021-01-10',
    reportTo: '设计总监',
    industryExperience: 5,
    professionalDomain: ['模拟电路设计', '触控技术', '传感器技术'],
    languagePreference: ['中文', '英文'],
    communicationPreference: ['Email', 'WeChat', 'VideoConference'] as CommunicationPreference[],
    preferredContactTime: '工作日 10:00-18:00',
    timezone: 'GMT+8',
    lastContactDate: '2023-12-10',
    communicationFrequency: 3,
    totalCommunications: 18,
    specialties: ['模拟电路设计', '触控技术', '传感器技术', '信号处理'],
    hobbies: ['钢琴', '绘画'],
    birthday: '1991-06-03',
    remarks: '年轻的模拟电路设计专家，对触控和传感器技术有深入研究。学习能力强。',
    createdAt: '2023-10-01T08:00:00Z',
    updatedAt: '2023-12-10T13:25:00Z',
    createdBy: 'admin'
  }
]

// 沟通记录模拟数据
export const mockCommunicationRecords: CommunicationRecord[] = [
  {
    id: 'comm_001',
    contactId: 'contact_001',
    customerId: 'customer_001',
    type: 'Meeting' as CommunicationRecordType,
    subject: '2024年封装服务供应商评审会议',
    date: '2024-01-15T14:00:00Z',
    duration: 120,
    content:
      '讨论了2024年封装服务供应商的评审标准和合作计划。重点关注成本控制和质量保证，要求供应商提供更有竞争力的报价和更严格的质量管控措施。',
    participants: ['张伟', '李明', '王雪', '销售经理-John'],
    followUpTasks: ['提供2024年封装服务报价方案', '完善质量管控体系文档', '安排技术交流会议'],
    importance: 5,
    nextContactDate: '2024-01-25',
    attachments: ['供应商评审标准.pdf', '质量要求清单.xlsx'],
    createdAt: '2024-01-15T16:30:00Z',
    createdBy: 'sales_john'
  },
  {
    id: 'comm_002',
    contactId: 'contact_002',
    customerId: 'customer_001',
    type: 'Email' as CommunicationRecordType,
    subject: 'BGA封装技术方案讨论',
    date: '2024-01-12T09:30:00Z',
    content:
      '李明提出了对新一代BGA封装技术的技术要求，包括球径、间距、基板厚度等具体参数。需要我们提供技术可行性分析和工艺开发计划。',
    participants: ['李明', '技术工程师-Mike'],
    followUpTasks: ['提供BGA技术可行性报告', '制定工艺开发时间表', '安排样品制作计划'],
    importance: 4,
    nextContactDate: '2024-01-20',
    attachments: ['BGA技术要求.docx'],
    createdAt: '2024-01-12T11:45:00Z',
    createdBy: 'tech_mike'
  },
  {
    id: 'comm_003',
    contactId: 'contact_004',
    customerId: 'customer_002',
    type: 'Call' as CommunicationRecordType,
    subject: '2024年第一季度订单计划沟通',
    date: '2024-01-10T10:00:00Z',
    duration: 45,
    content:
      '王强总监详细介绍了紫光展锐2024年Q1的封装需求计划，主要集中在5G芯片和IoT芯片的封装。对交期和质量都有较高要求。',
    participants: ['王强', '销售总监-David'],
    followUpTasks: ['准备Q1产能分配方案', '制定质量保证计划', '确认交期安排'],
    importance: 5,
    nextContactDate: '2024-01-18',
    createdAt: '2024-01-10T11:30:00Z',
    createdBy: 'sales_david'
  },
  {
    id: 'comm_004',
    contactId: 'contact_006',
    customerId: 'customer_003',
    type: 'Visit' as CommunicationRecordType,
    subject: '比亚迪半导体工厂参观与技术交流',
    date: '2024-01-03T09:00:00Z',
    duration: 240,
    content:
      '应陈志华经理邀请，参观了比亚迪半导体的生产工厂，深入了解了汽车级芯片的制造要求和质量标准。讨论了IATF16949认证要求和汽车级封装的特殊需求。',
    participants: ['陈志华', '刘建强', '销售经理-Tom', '质量工程师-Lisa'],
    followUpTasks: [
      '提供汽车级封装服务资质证明',
      '制定IATF16949合规方案',
      '安排汽车级封装工艺介绍'
    ],
    importance: 4,
    nextContactDate: '2024-01-15',
    attachments: ['IATF16949认证证书.pdf', '汽车级封装介绍.pptx'],
    createdAt: '2024-01-03T17:00:00Z',
    createdBy: 'sales_tom'
  },
  {
    id: 'comm_005',
    contactId: 'contact_008',
    customerId: 'customer_004',
    type: 'VideoCall' as CommunicationRecordType,
    subject: '兆易创新MCU封装需求评估',
    date: '2023-12-25T15:00:00Z',
    duration: 90,
    content:
      '与刘建华总监进行了深入的视频会议，讨论了兆易创新MCU产品的封装需求。重点关注成本控制和供应链稳定性，希望建立长期战略合作关系。',
    participants: ['刘建华', '张丽娟', '业务经理-Kevin'],
    followUpTasks: ['提供MCU封装成本分析报告', '制定长期合作方案', '安排供应链稳定性评估'],
    importance: 4,
    nextContactDate: '2024-01-08',
    attachments: ['MCU封装方案.pdf', '成本分析表.xlsx'],
    createdAt: '2023-12-25T17:30:00Z',
    createdBy: 'business_kevin'
  }
]

// 生成更多联系人数据的工具函数
export function generateMockContacts(count: number): Contact[] {
  const names = [
    { chinese: '赵明', english: 'Ming Zhao' },
    { chinese: '钱丽', english: 'Li Qian' },
    { chinese: '孙华', english: 'Hua Sun' },
    { chinese: '李洋', english: 'Yang Li' },
    { chinese: '周杰', english: 'Jie Zhou' },
    { chinese: '吴娜', english: 'Na Wu' },
    { chinese: '郑强', english: 'Qiang Zheng' },
    { chinese: '王磊', english: 'Lei Wang' },
    { chinese: '冯雪', english: 'Xue Feng' },
    { chinese: '陈刚', english: 'Gang Chen' }
  ]

  const positions = [
    { role: 'FAE' as ContactRole, position: '现场应用工程师', dept: '技术支持部' },
    { role: 'TestEngineer' as ContactRole, position: '测试工程师', dept: '测试部' },
    { role: 'QualityEngineer' as ContactRole, position: '质量工程师', dept: '质量部' },
    { role: 'PurchaseManager' as ContactRole, position: '采购经理', dept: '采购部' },
    { role: 'BusinessManager' as ContactRole, position: '商务经理', dept: '商务部' },
    { role: 'PMManager' as ContactRole, position: '项目经理', dept: '项目部' },
    { role: 'DesignManager' as ContactRole, position: '设计经理', dept: '设计部' },
    { role: 'PackageEngineer' as ContactRole, position: '封装工程师', dept: '封装技术部' }
  ]

  const customerIds = [
    'customer_001',
    'customer_002',
    'customer_003',
    'customer_004',
    'customer_005'
  ]

  const generatedContacts: Contact[] = []

  for (let i = 0; i < count && i < names.length; i++) {
    const name = names[i]
    const position = positions[i % positions.length]
    const customerId = customerIds[i % customerIds.length]

    generatedContacts.push({
      id: `contact_${String(mockContacts.length + i + 1).padStart(3, '0')}`,
      customerId,
      name: name.chinese,
      englishName: name.english,
      position: position.position,
      department: position.dept,
      level: ['专员', '工程师', '高级工程师', '经理'][Math.floor(Math.random() * 4)],
      email: `${name.english.toLowerCase().replace(' ', '.')}@company.com`,
      phone: `021-${String(Math.floor(Math.random() * 90000000) + 10000000)}`,
      mobile: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
      wechat: `${name.chinese.toLowerCase()}_wechat`,
      role: position.role,
      contactType: getRandomContactTypes(),
      status: 'active' as ContactStatus,
      importance: (Math.floor(Math.random() * 5) + 1) as ContactImportance,
      trustLevel: (Math.floor(Math.random() * 5) + 1) as ContactImportance,
      influenceScore: (Math.floor(Math.random() * 10) + 1) as InfluenceLevel,
      isDecisionMaker: Math.random() < 0.2,
      isPrimaryContact: Math.random() < 0.3,
      industryExperience: Math.floor(Math.random() * 15) + 2,
      communicationPreference: getRandomCommunicationPreferences(),
      preferredContactTime: '工作日 9:00-18:00',
      timezone: 'GMT+8',
      lastContactDate: getRandomRecentDate(),
      communicationFrequency: Math.floor(Math.random() * 8) + 1,
      totalCommunications: Math.floor(Math.random() * 50) + 5,
      specialties: getRandomSpecialties(),
      remarks: '系统生成的模拟联系人数据',
      createdAt: getRandomPastDate(),
      updatedAt: getRandomRecentDate(),
      createdBy: 'admin'
    })
  }

  return generatedContacts
}

// 辅助函数
function getRandomContactTypes(): ContactType[] {
  const types: ContactType[] = [
    'Technical',
    'Business',
    'Quality',
    'Procurement',
    'Decision',
    'Support'
  ]
  const count = Math.floor(Math.random() * 3) + 1
  const selected: ContactType[] = []

  for (let i = 0; i < count; i++) {
    const type = types[Math.floor(Math.random() * types.length)]
    if (!selected.includes(type)) {
      selected.push(type)
    }
  }

  return selected
}

function getRandomCommunicationPreferences(): CommunicationPreference[] {
  const prefs: CommunicationPreference[] = [
    'Email',
    'Phone',
    'WeChat',
    'InPerson',
    'VideoConference'
  ]
  const count = Math.floor(Math.random() * 3) + 1
  const selected: CommunicationPreference[] = []

  for (let i = 0; i < count; i++) {
    const pref = prefs[Math.floor(Math.random() * prefs.length)]
    if (!selected.includes(pref)) {
      selected.push(pref)
    }
  }

  return selected
}

function getRandomSpecialties(): string[] {
  const specialties = [
    '芯片设计',
    '模拟电路',
    '数字电路',
    '射频电路',
    '电源管理',
    '封装技术',
    '测试技术',
    '质量管理',
    '项目管理',
    '供应链管理'
  ]
  const count = Math.floor(Math.random() * 4) + 1
  const selected: string[] = []

  for (let i = 0; i < count; i++) {
    const specialty = specialties[Math.floor(Math.random() * specialties.length)]
    if (!selected.includes(specialty)) {
      selected.push(specialty)
    }
  }

  return selected
}

function getRandomRecentDate(): string {
  const now = new Date()
  const daysAgo = Math.floor(Math.random() * 30)
  const date = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000)
  return date.toISOString().split('T')[0]
}

function getRandomPastDate(): string {
  const now = new Date()
  const daysAgo = Math.floor(Math.random() * 365) + 30
  const date = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000)
  return date.toISOString()
}

// 导出所有联系人数据
export const allMockContacts = [...mockContacts, ...generateMockContacts(15)]

// 联系人统计数据
export const contactStats = {
  totalContacts: allMockContacts.length,
  activeContacts: allMockContacts.filter(c => c.status === 'active').length,
  decisionMakers: allMockContacts.filter(c => c.isDecisionMaker).length,
  primaryContacts: allMockContacts.filter(c => c.isPrimaryContact).length,
  highImportanceContacts: allMockContacts.filter(c => c.importance >= 4).length,
  roleDistribution: {
    FAE: allMockContacts.filter(c => c.role === 'FAE').length,
    PurchaseManager: allMockContacts.filter(c => c.role === 'PurchaseManager').length,
    TechnicalDirector: allMockContacts.filter(c => c.role === 'TechnicalDirector').length,
    QualityEngineer: allMockContacts.filter(c => c.role === 'QualityEngineer').length,
    TestEngineer: allMockContacts.filter(c => c.role === 'TestEngineer').length
  },
  contactTypeDistribution: {
    Technical: allMockContacts.filter(c => c.contactType.includes('Technical')).length,
    Business: allMockContacts.filter(c => c.contactType.includes('Business')).length,
    Procurement: allMockContacts.filter(c => c.contactType.includes('Procurement')).length,
    Quality: allMockContacts.filter(c => c.contactType.includes('Quality')).length,
    Decision: allMockContacts.filter(c => c.contactType.includes('Decision')).length
  }
}
