import { config } from '@vue/test-utils'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest'
import type { App } from 'vue'
import { nextTick } from 'vue'

// 全局Mock配置
beforeAll(() => {
  // Mock Window API
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn()
    }))
  })

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    key: vi.fn(),
    length: 0
  }
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  })

  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    key: vi.fn(),
    length: 0
  }
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock
  })

  // Mock URL.createObjectURL
  Object.defineProperty(URL, 'createObjectURL', {
    writable: true,
    value: vi.fn(() => 'mocked-url')
  })

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }))

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }))

  // Mock console.warn for cleaner test output
  vi.spyOn(console, 'warn').mockImplementation(() => {})

  // Mock performance API
  Object.defineProperty(window, 'performance', {
    value: {
      now: vi.fn(() => Date.now()),
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByName: vi.fn(() => []),
      getEntriesByType: vi.fn(() => [])
    }
  })
})

afterAll(() => {
  vi.restoreAllMocks()
})

beforeEach(() => {
  // 清除所有mock的调用记录
  vi.clearAllMocks()
})

afterEach(async () => {
  // 确保DOM更新完成
  await nextTick()
})

// Vue Test Utils 全局配置
config.global.plugins = [ElementPlus as any, createPinia()]

// 全局组件注册
config.global.components = {
  // 可以在这里注册全局测试组件
}

// 全局mixin
config.global.mixins = []

// 全局provide
config.global.provide = {}

// 全局stubs
config.global.stubs = {
  // RouterLink: true,
  // RouterView: true
}
