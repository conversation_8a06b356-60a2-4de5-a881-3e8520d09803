# IC封测CIM系统数据治理策略

## 1. 数据治理概述

### 1.1 数据治理目标
基于IC封装测试行业的严格质量要求，建立全面的数据治理体系，确保数据的准确性、完整性、一致性、时效性和安全性，满足IATF16949、ISO9001等质量管理体系要求。

### 1.2 数据治理原则
- **数据质量优先**: 确保数据准确性和完整性，支持质量追溯
- **合规性保障**: 严格遵循行业标准和法规要求
- **安全性管控**: 保护客户IP和商业机密
- **可追溯性**: 建立完整的数据变更审计链
- **自动化管理**: 减少人工干预，提高数据治理效率

## 2. 数据质量管理

### 2.1 数据质量标准

#### IC封测专业数据质量指标
| 质量维度 | 目标标准 | 检查方法 | 处理机制 |
|---------|---------|----------|----------|
| **准确性** | >99.95% | 自动校验规则 | 异常数据隔离 |
| **完整性** | >99.9% | 必填字段检查 | 缺失数据补录 |
| **一致性** | >99.8% | 跨表关联检查 | 数据同步修复 |
| **唯一性** | 100% | 重复数据检测 | 自动去重处理 |
| **时效性** | <1分钟 | 数据延迟监控 | 实时告警机制 |

#### 数据质量检查规则表
```sql
CREATE TABLE data_quality_rules (
    rule_id VARCHAR(30) PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_category ENUM('ACCURACY','COMPLETENESS','CONSISTENCY','UNIQUENESS','TIMELINESS') NOT NULL,
    table_name VARCHAR(100) NOT NULL COMMENT '目标表名',
    column_name VARCHAR(100) COMMENT '目标字段',
    rule_expression TEXT NOT NULL COMMENT '规则表达式',
    error_level ENUM('WARNING','ERROR','CRITICAL') DEFAULT 'ERROR',
    rule_description TEXT COMMENT '规则描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by VARCHAR(50) NOT NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_table_column (table_name, column_name),
    INDEX idx_category_active (rule_category, is_active)
) COMMENT='数据质量规则配置表';

-- IC封测专业数据质量规则示例
INSERT INTO data_quality_rules VALUES 
('DQ001', '测试温度范围检查', 'ACCURACY', 'ic_test_data', 'temperature', 
 'temperature IN (-40, 25, 85, 125, 150)', 'ERROR', '检查测试温度是否在标准温度点', TRUE, 'system', NOW()),
('DQ002', 'Wafer ID格式检查', 'ACCURACY', 'ic_wafer_lots', 'wafer_id', 
 'wafer_id REGEXP "^[A-Z0-9]{2}[0-9]{8}[A-Z0-9]{2}$"', 'ERROR', '检查Wafer ID格式符合性', TRUE, 'system', NOW()),
('DQ003', 'Die坐标范围检查', 'ACCURACY', 'ic_wafer_map', 'die_x,die_y',
 'die_x BETWEEN 1 AND 100 AND die_y BETWEEN 1 AND 100', 'ERROR', '检查Die坐标在合理范围内', TRUE, 'system', NOW());
```

### 2.2 自动化数据质量检查

#### 实时数据质量监控
```sql
-- 数据质量检查结果表
CREATE TABLE data_quality_check_results (
    check_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_id VARCHAR(30) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_count BIGINT NOT NULL COMMENT '检查记录数',
    error_count BIGINT NOT NULL COMMENT '错误记录数',
    warning_count BIGINT NOT NULL COMMENT '警告记录数',
    quality_score DECIMAL(5,2) COMMENT '质量得分(0-100)',
    check_start_time TIMESTAMP NOT NULL,
    check_end_time TIMESTAMP NOT NULL,
    check_duration_ms INT COMMENT '检查耗时(毫秒)',
    error_details JSON COMMENT '错误详情',
    
    INDEX idx_rule_time (rule_id, check_start_time),
    INDEX idx_table_time (table_name, check_start_time),
    FOREIGN KEY (rule_id) REFERENCES data_quality_rules(rule_id)
) COMMENT='数据质量检查结果表';

-- 数据质量检查存储过程
DELIMITER //
CREATE PROCEDURE sp_check_data_quality(IN target_table VARCHAR(100))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE rule_id_var VARCHAR(30);
    DECLARE rule_expr TEXT;
    DECLARE error_level_var VARCHAR(20);
    DECLARE check_count BIGINT DEFAULT 0;
    DECLARE error_count BIGINT DEFAULT 0;
    
    DECLARE rule_cursor CURSOR FOR
        SELECT rule_id, rule_expression, error_level 
        FROM data_quality_rules 
        WHERE table_name = target_table AND is_active = TRUE;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN rule_cursor;
    check_loop: LOOP
        FETCH rule_cursor INTO rule_id_var, rule_expr, error_level_var;
        IF done THEN
            LEAVE check_loop;
        END IF;
        
        -- 执行数据质量检查
        SET @check_sql = CONCAT('SELECT COUNT(*) FROM ', target_table, 
                               ' WHERE NOT (', rule_expr, ')');
        PREPARE stmt FROM @check_sql;
        EXECUTE stmt;
        -- 获取错误数量并记录结果
        
        DEALLOCATE PREPARE stmt;
    END LOOP;
    CLOSE rule_cursor;
    
    -- 插入检查结果
    INSERT INTO data_quality_check_results 
    (rule_id, table_name, record_count, error_count, check_start_time, check_end_time)
    VALUES (rule_id_var, target_table, check_count, error_count, NOW(), NOW());
END//
DELIMITER ;
```

#### 数据质量实时监控服务
```java
@Service
@Component
public class DataQualityMonitorService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private AlertService alertService;
    
    // 每5分钟执行一次数据质量检查
    @Scheduled(fixedDelay = 300000)
    public void performDataQualityCheck() {
        List<String> criticalTables = Arrays.asList(
            "ic_test_data", "ic_wafer_lots", "ic_product_specifications"
        );
        
        for (String tableName : criticalTables) {
            try {
                DataQualityResult result = checkTableDataQuality(tableName);
                recordQualityResult(result);
                
                // 质量得分低于95%时告警
                if (result.getQualityScore() < 95.0) {
                    alertService.sendAlert(
                        AlertLevel.HIGH,
                        String.format("数据质量异常: 表%s质量得分%.2f%%", 
                                    tableName, result.getQualityScore())
                    );
                }
            } catch (Exception e) {
                logger.error("数据质量检查失败: 表={}", tableName, e);
            }
        }
    }
    
    private DataQualityResult checkTableDataQuality(String tableName) {
        // 执行数据质量规则检查
        List<QualityRule> rules = getActiveRulesForTable(tableName);
        DataQualityResult result = new DataQualityResult();
        
        for (QualityRule rule : rules) {
            long totalCount = getTotalRecordCount(tableName);
            long errorCount = executeQualityRule(tableName, rule);
            
            double ruleScore = (double) (totalCount - errorCount) / totalCount * 100;
            result.addRuleResult(rule.getRuleId(), ruleScore, errorCount);
        }
        
        return result;
    }
}
```

## 3. 数据安全治理

### 3.1 数据分类分级

#### IC封测数据安全分类
```sql
CREATE TABLE data_classification (
    classification_id VARCHAR(20) PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    column_name VARCHAR(100),
    data_category ENUM('PUBLIC','INTERNAL','CONFIDENTIAL','SECRET') NOT NULL,
    sensitivity_level ENUM('LOW','MEDIUM','HIGH','CRITICAL') NOT NULL,
    encryption_required BOOLEAN DEFAULT FALSE COMMENT '是否需要加密',
    access_restrictions TEXT COMMENT '访问限制说明',
    retention_period_years INT COMMENT '数据保留年限',
    classification_reason TEXT COMMENT '分类依据',
    classified_by VARCHAR(50) NOT NULL,
    classification_date DATE NOT NULL,
    
    UNIQUE KEY uk_table_column (table_name, column_name),
    INDEX idx_category_level (data_category, sensitivity_level)
) COMMENT='数据分类分级表';

-- IC封测数据分类示例
INSERT INTO data_classification VALUES
('DC001', 'ic_customer_products', 'product_name', 'CONFIDENTIAL', 'HIGH', TRUE, 
 '仅客户相关人员可访问', 15, '客户商业机密', 'admin', '2025-01-01'),
('DC002', 'ic_test_data', 'test_result', 'CONFIDENTIAL', 'HIGH', TRUE,
 '仅授权工程师可访问', 15, '测试数据敏感', 'admin', '2025-01-01'),
('DC003', 'ic_wafer_lots', 'wafer_id', 'INTERNAL', 'MEDIUM', FALSE,
 '内部人员可访问', 7, '生产追溯需要', 'admin', '2025-01-01'),
('DC004', 'ic_equipment_specs', 'specification', 'SECRET', 'CRITICAL', TRUE,
 '仅设备工程师可访问', 20, '设备核心机密', 'admin', '2025-01-01');
```

### 3.2 数据脱敏策略

#### 脱敏规则配置
```sql
CREATE TABLE data_masking_rules (
    masking_id VARCHAR(20) PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    column_name VARCHAR(100) NOT NULL,
    masking_type ENUM('HASH','ENCRYPT','REPLACE','PARTIAL','RANDOM') NOT NULL,
    masking_pattern VARCHAR(200) COMMENT '脱敏模式',
    preserve_format BOOLEAN DEFAULT TRUE COMMENT '是否保持格式',
    masking_algorithm VARCHAR(50) COMMENT '脱敏算法',
    rule_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    
    UNIQUE KEY uk_table_column_mask (table_name, column_name),
    INDEX idx_masking_type (masking_type)
) COMMENT='数据脱敏规则表';

-- IC封测数据脱敏规则
INSERT INTO data_masking_rules VALUES
('DM001', 'ic_customers', 'customer_name', 'REPLACE', 'CUSTOMER_***', TRUE, 
 'PATTERN_REPLACE', '客户名称脱敏', TRUE),
('DM002', 'ic_products', 'product_code', 'PARTIAL', '***-****-##', TRUE,
 'PARTIAL_MASK', '产品编码部分脱敏', TRUE),
('DM003', 'ic_test_data', 'test_result', 'RANDOM', 'NORMAL_DISTRIBUTION', TRUE,
 'STATISTICAL_REPLACE', '测试数据随机化', TRUE);
```

#### 数据脱敏服务
```java
@Service
public class DataMaskingService {
    
    @Value("${data.masking.key}")
    private String maskingKey;
    
    public String maskSensitiveData(String originalData, String maskingType, String pattern) {
        switch (maskingType.toUpperCase()) {
            case "HASH":
                return DigestUtils.sha256Hex(originalData + maskingKey);
                
            case "ENCRYPT":
                return AESUtil.encrypt(originalData, maskingKey);
                
            case "REPLACE":
                return pattern.replace("***", generateRandomString(3));
                
            case "PARTIAL":
                return maskPartialData(originalData, pattern);
                
            case "RANDOM":
                return generateRandomValue(originalData, pattern);
                
            default:
                return "****";
        }
    }
    
    private String maskPartialData(String data, String pattern) {
        // 实现部分数据脱敏逻辑
        if (data.length() <= 4) return "****";
        
        String prefix = data.substring(0, 2);
        String suffix = data.substring(data.length() - 2);
        String middle = "*".repeat(data.length() - 4);
        
        return prefix + middle + suffix;
    }
}
```

### 3.3 访问控制与审计

#### 细粒度权限控制表
```sql
CREATE TABLE data_access_permissions (
    permission_id VARCHAR(30) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    role_id VARCHAR(30) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    column_name VARCHAR(100),
    access_type ENUM('SELECT','INSERT','UPDATE','DELETE') NOT NULL,
    row_filter_condition TEXT COMMENT '行级过滤条件',
    time_restrictions VARCHAR(200) COMMENT '时间限制',
    ip_restrictions TEXT COMMENT 'IP地址限制',
    granted_by VARCHAR(50) NOT NULL COMMENT '授权人',
    grant_date DATE NOT NULL,
    expire_date DATE COMMENT '过期日期',
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_user_table (user_id, table_name),
    INDEX idx_role_table (role_id, table_name),
    INDEX idx_expire_date (expire_date, is_active)
) COMMENT='数据访问权限表';

-- 行级安全策略示例
CREATE OR REPLACE VIEW ic_customer_data_secure AS
SELECT 
    product_id,
    CASE 
        WHEN CURRENT_USER_HAS_PERMISSION('CUSTOMER_A') THEN product_name
        ELSE '****'
    END AS product_name,
    specification
FROM ic_customer_products
WHERE customer_id = GET_USER_CUSTOMER_ID();
```

#### 数据访问审计日志
```sql
CREATE TABLE data_access_audit_logs (
    audit_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
    client_ip VARCHAR(45) NOT NULL COMMENT '客户端IP',
    access_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3),
    operation_type ENUM('SELECT','INSERT','UPDATE','DELETE','LOGIN','LOGOUT') NOT NULL,
    table_name VARCHAR(100) COMMENT '访问表名',
    record_count INT COMMENT '影响记录数',
    sql_statement TEXT COMMENT 'SQL语句',
    execution_time_ms INT COMMENT '执行时间(毫秒)',
    success BOOLEAN DEFAULT TRUE COMMENT '是否成功',
    error_message TEXT COMMENT '错误信息',
    sensitive_data_accessed BOOLEAN DEFAULT FALSE COMMENT '是否访问敏感数据',
    
    INDEX idx_user_time (user_id, access_time),
    INDEX idx_table_time (table_name, access_time),
    INDEX idx_sensitive_access (sensitive_data_accessed, access_time)
) COMMENT='数据访问审计日志表';

-- 审计日志触发器
DELIMITER //
CREATE TRIGGER tr_audit_customer_data_access
AFTER SELECT ON ic_customer_products
FOR EACH STATEMENT
BEGIN
    INSERT INTO data_access_audit_logs 
    (user_id, session_id, client_ip, operation_type, table_name, sensitive_data_accessed)
    VALUES 
    (USER(), CONNECTION_ID(), @@hostname, 'SELECT', 'ic_customer_products', TRUE);
END//
DELIMITER ;
```

## 4. 数据生命周期管理

### 4.1 数据保留策略

#### 数据保留规则表
```sql
CREATE TABLE data_retention_policies (
    policy_id VARCHAR(30) PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    data_category VARCHAR(50) NOT NULL COMMENT '数据类别',
    retention_period_years INT NOT NULL COMMENT '保留年限',
    archive_after_months INT COMMENT '归档时间(月)',
    delete_after_years INT COMMENT '删除时间(年)',
    retention_reason TEXT COMMENT '保留原因',
    compliance_requirement VARCHAR(200) COMMENT '合规要求',
    policy_status ENUM('ACTIVE','SUSPENDED','OBSOLETE') DEFAULT 'ACTIVE',
    created_by VARCHAR(50) NOT NULL,
    effective_date DATE NOT NULL,
    
    INDEX idx_table_category (table_name, data_category),
    INDEX idx_retention_period (retention_period_years)
) COMMENT='数据保留策略表';

-- IC封测行业数据保留策略
INSERT INTO data_retention_policies VALUES
('RP001', 'ic_test_data', '测试数据', 15, 12, 15, 'IATF16949要求', 'IATF16949:2016', 'ACTIVE', 'admin', '2025-01-01'),
('RP002', 'ic_quality_records', '质量记录', 15, 12, 15, '质量追溯需要', 'ISO9001:2015', 'ACTIVE', 'admin', '2025-01-01'),
('RP003', 'ic_customer_complaints', '客户投诉', 7, 6, 7, '客户服务需要', '公司政策', 'ACTIVE', 'admin', '2025-01-01'),
('RP004', 'ic_equipment_logs', '设备日志', 5, 3, 5, '设备维护需要', '行业最佳实践', 'ACTIVE', 'admin', '2025-01-01');
```

### 4.2 自动化数据归档

#### 数据归档任务
```sql
-- 数据归档表
CREATE TABLE data_archive_tasks (
    task_id VARCHAR(30) PRIMARY KEY,
    source_table VARCHAR(100) NOT NULL,
    archive_table VARCHAR(100) NOT NULL,
    archive_condition TEXT NOT NULL COMMENT '归档条件',
    archive_frequency ENUM('DAILY','WEEKLY','MONTHLY','QUARTERLY') DEFAULT 'MONTHLY',
    last_archive_time TIMESTAMP COMMENT '最后归档时间',
    next_archive_time TIMESTAMP COMMENT '下次归档时间',
    archived_records BIGINT DEFAULT 0 COMMENT '已归档记录数',
    task_status ENUM('ACTIVE','PAUSED','ERROR') DEFAULT 'ACTIVE',
    
    INDEX idx_next_archive (next_archive_time, task_status)
) COMMENT='数据归档任务表';

-- 自动归档存储过程
DELIMITER //
CREATE PROCEDURE sp_auto_archive_data()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE task_id_var VARCHAR(30);
    DECLARE source_table_var VARCHAR(100);
    DECLARE archive_table_var VARCHAR(100);
    DECLARE archive_condition_var TEXT;
    DECLARE archived_count BIGINT;
    
    DECLARE archive_cursor CURSOR FOR
        SELECT task_id, source_table, archive_table, archive_condition
        FROM data_archive_tasks
        WHERE task_status = 'ACTIVE' 
          AND next_archive_time <= NOW();
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN archive_cursor;
    archive_loop: LOOP
        FETCH archive_cursor INTO task_id_var, source_table_var, archive_table_var, archive_condition_var;
        IF done THEN
            LEAVE archive_loop;
        END IF;
        
        -- 执行数据归档
        SET @archive_sql = CONCAT(
            'INSERT INTO ', archive_table_var, 
            ' SELECT * FROM ', source_table_var,
            ' WHERE ', archive_condition_var
        );
        PREPARE stmt FROM @archive_sql;
        EXECUTE stmt;
        SET archived_count = ROW_COUNT();
        DEALLOCATE PREPARE stmt;
        
        -- 删除已归档数据
        SET @delete_sql = CONCAT(
            'DELETE FROM ', source_table_var,
            ' WHERE ', archive_condition_var
        );
        PREPARE stmt FROM @delete_sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 更新归档任务状态
        UPDATE data_archive_tasks 
        SET last_archive_time = NOW(),
            next_archive_time = DATE_ADD(NOW(), INTERVAL 1 MONTH),
            archived_records = archived_records + archived_count
        WHERE task_id = task_id_var;
        
    END LOOP;
    CLOSE archive_cursor;
END//
DELIMITER ;

-- 定时执行归档任务
CREATE EVENT evt_auto_archive
ON SCHEDULE EVERY 1 DAY
STARTS '2025-01-01 01:00:00'
DO
    CALL sp_auto_archive_data();
```

## 5. 数据标准化管理

### 5.1 数据标准定义

#### 数据标准字典
```sql
CREATE TABLE data_standards_dictionary (
    standard_id VARCHAR(30) PRIMARY KEY,
    standard_name VARCHAR(100) NOT NULL,
    standard_category ENUM('BUSINESS','TECHNICAL','REFERENCE','DERIVED') NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    data_format VARCHAR(100) COMMENT '数据格式',
    value_range VARCHAR(200) COMMENT '取值范围',
    default_value VARCHAR(100) COMMENT '默认值',
    validation_rules TEXT COMMENT '验证规则',
    business_definition TEXT NOT NULL COMMENT '业务定义',
    technical_definition TEXT COMMENT '技术定义',
    usage_examples TEXT COMMENT '使用示例',
    source_system VARCHAR(50) COMMENT '来源系统',
    owner_department VARCHAR(50) NOT NULL COMMENT '责任部门',
    standard_status ENUM('DRAFT','APPROVED','PUBLISHED','DEPRECATED') DEFAULT 'DRAFT',
    version_number VARCHAR(20) DEFAULT '1.0',
    effective_date DATE,
    created_by VARCHAR(50) NOT NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_category_status (standard_category, standard_status),
    INDEX idx_owner_dept (owner_department)
) COMMENT='数据标准字典表';

-- IC封测专业数据标准示例
INSERT INTO data_standards_dictionary VALUES
('STD001', 'IC产品编号', 'BUSINESS', 'VARCHAR(20)', 'XXX-YYYY-ZZZZ', '3-4-4格式', NULL,
 'LENGTH(product_code) = 12 AND product_code LIKE "___-____-____"',
 'IC产品的唯一标识编号，由客户代码-产品系列-版本号组成',
 '字符串类型，固定12位，用连字符分隔', 'ABC-1234-V001', 'PLM系统', '产品工程部', 
 'PUBLISHED', '1.0', '2025-01-01', 'admin', NOW());
```

### 5.2 数据标准化检查

#### 标准符合性检查
```java
@Service
public class DataStandardizationService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    public StandardizationResult checkDataStandards(String tableName) {
        List<DataStandard> standards = getTableStandards(tableName);
        StandardizationResult result = new StandardizationResult();
        
        for (DataStandard standard : standards) {
            long totalRecords = getTotalRecords(tableName);
            long nonStandardRecords = countNonStandardRecords(tableName, standard);
            
            double complianceRate = (double)(totalRecords - nonStandardRecords) / totalRecords * 100;
            result.addStandardResult(standard.getStandardId(), complianceRate, nonStandardRecords);
        }
        
        return result;
    }
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void performStandardizationCheck() {
        List<String> tables = getBusinessTables();
        
        for (String tableName : tables) {
            StandardizationResult result = checkDataStandards(tableName);
            recordStandardizationResult(tableName, result);
            
            // 合规率低于90%时告警
            if (result.getOverallComplianceRate() < 90.0) {
                alertService.sendAlert(
                    "数据标准化合规率过低",
                    String.format("表%s合规率%.2f%%", tableName, result.getOverallComplianceRate())
                );
            }
        }
    }
}
```

## 6. 元数据管理

### 6.1 元数据模型

#### 技术元数据
```sql
CREATE TABLE technical_metadata (
    metadata_id VARCHAR(30) PRIMARY KEY,
    object_type ENUM('TABLE','COLUMN','INDEX','VIEW','PROCEDURE','FUNCTION') NOT NULL,
    object_name VARCHAR(200) NOT NULL,
    parent_object VARCHAR(200) COMMENT '父对象名称',
    schema_name VARCHAR(100) NOT NULL,
    data_type VARCHAR(100),
    data_length INT,
    data_precision INT,
    data_scale INT,
    is_nullable BOOLEAN,
    default_value VARCHAR(200),
    is_primary_key BOOLEAN DEFAULT FALSE,
    is_foreign_key BOOLEAN DEFAULT FALSE,
    is_indexed BOOLEAN DEFAULT FALSE,
    object_description TEXT,
    creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_object_type_name (object_type, object_name),
    INDEX idx_schema_object (schema_name, object_name)
) COMMENT='技术元数据表';
```

#### 业务元数据
```sql
CREATE TABLE business_metadata (
    metadata_id VARCHAR(30) PRIMARY KEY,
    technical_metadata_id VARCHAR(30) NOT NULL,
    business_name VARCHAR(200) NOT NULL COMMENT '业务名称',
    business_definition TEXT NOT NULL COMMENT '业务定义',
    business_rules TEXT COMMENT '业务规则',
    data_owner VARCHAR(50) NOT NULL COMMENT '数据负责人',
    data_steward VARCHAR(50) COMMENT '数据管理员',
    business_importance ENUM('LOW','MEDIUM','HIGH','CRITICAL') DEFAULT 'MEDIUM',
    usage_frequency ENUM('RARELY','MONTHLY','WEEKLY','DAILY','REAL_TIME') DEFAULT 'DAILY',
    sensitive_level ENUM('PUBLIC','INTERNAL','CONFIDENTIAL','SECRET') DEFAULT 'INTERNAL',
    glossary_terms TEXT COMMENT '关联术语',
    related_processes TEXT COMMENT '相关业务流程',
    quality_requirements TEXT COMMENT '质量要求',
    
    FOREIGN KEY (technical_metadata_id) REFERENCES technical_metadata(metadata_id),
    INDEX idx_owner_steward (data_owner, data_steward),
    INDEX idx_importance_freq (business_importance, usage_frequency)
) COMMENT='业务元数据表';
```

### 6.2 数据血缘分析

#### 数据血缘关系表
```sql
CREATE TABLE data_lineage (
    lineage_id VARCHAR(30) PRIMARY KEY,
    source_object VARCHAR(200) NOT NULL COMMENT '源对象',
    target_object VARCHAR(200) NOT NULL COMMENT '目标对象',
    lineage_type ENUM('DIRECT','DERIVED','AGGREGATED','TRANSFORMED') NOT NULL,
    transformation_logic TEXT COMMENT '转换逻辑',
    dependency_type ENUM('STRONG','WEAK','OPTIONAL') DEFAULT 'STRONG',
    update_frequency ENUM('REAL_TIME','BATCH','DAILY','WEEKLY','MONTHLY'),
    lineage_depth INT COMMENT '血缘深度',
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50) NOT NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_source_target (source_object, target_object),
    INDEX idx_lineage_type (lineage_type, is_active)
) COMMENT='数据血缘关系表';

-- IC封测数据血缘示例
INSERT INTO data_lineage VALUES
('DL001', 'ic_test_data.test_result', 'ic_test_summary.pass_rate', 'AGGREGATED',
 'pass_rate = SUM(CASE WHEN test_result = "PASS" THEN 1 ELSE 0 END) / COUNT(*)',
 'STRONG', 'DAILY', 1, TRUE, 'system', NOW()),
('DL002', 'ic_wafer_lots.lot_id', 'ic_product_tracking.lot_id', 'DIRECT',
 'lot_id字段直接映射', 'STRONG', 'REAL_TIME', 1, TRUE, 'system', NOW());
```

## 7. 数据治理监控

### 7.1 治理指标监控

#### 数据治理KPI仪表板
```sql
CREATE TABLE data_governance_metrics (
    metric_id VARCHAR(30) PRIMARY KEY,
    metric_date DATE NOT NULL,
    data_quality_score DECIMAL(5,2) COMMENT '数据质量得分',
    data_completeness_rate DECIMAL(5,2) COMMENT '数据完整性',
    data_accuracy_rate DECIMAL(5,2) COMMENT '数据准确性',
    data_consistency_rate DECIMAL(5,2) COMMENT '数据一致性',
    standardization_compliance DECIMAL(5,2) COMMENT '标准化合规率',
    security_compliance_rate DECIMAL(5,2) COMMENT '安全合规率',
    metadata_coverage_rate DECIMAL(5,2) COMMENT '元数据覆盖率',
    data_lineage_coverage DECIMAL(5,2) COMMENT '血缘覆盖率',
    governance_maturity_level ENUM('INITIAL','DEVELOPING','DEFINED','MANAGED','OPTIMIZED'),
    total_data_volume_gb DECIMAL(12,2) COMMENT '数据总量(GB)',
    
    UNIQUE KEY uk_metric_date (metric_date),
    INDEX idx_quality_score (data_quality_score, metric_date)
) COMMENT='数据治理指标表';
```

### 7.2 治理成效评估

#### 治理成熟度评估
```java
@Component
public class DataGovernanceMaturityAssessment {
    
    public GovernanceMaturityLevel assessMaturityLevel() {
        // 评估各个维度的成熟度
        int qualityScore = assessDataQualityMaturity();
        int securityScore = assessDataSecurityMaturity();
        int standardScore = assessDataStandardMaturity();
        int metadataScore = assessMetadataMaturity();
        int lineageScore = assessLineageMaturity();
        
        double overallScore = (qualityScore + securityScore + standardScore + 
                             metadataScore + lineageScore) / 5.0;
        
        return determineMaturityLevel(overallScore);
    }
    
    private GovernanceMaturityLevel determineMaturityLevel(double score) {
        if (score >= 90) return GovernanceMaturityLevel.OPTIMIZED;
        if (score >= 75) return GovernanceMaturityLevel.MANAGED;
        if (score >= 60) return GovernanceMaturityLevel.DEFINED;
        if (score >= 40) return GovernanceMaturityLevel.DEVELOPING;
        return GovernanceMaturityLevel.INITIAL;
    }
}
```

---

*本文档定义了IC封测CIM系统的全面数据治理策略，确保数据资产的有效管理和价值最大化。*
*版本: V1.0 | 更新时间: 2025年*