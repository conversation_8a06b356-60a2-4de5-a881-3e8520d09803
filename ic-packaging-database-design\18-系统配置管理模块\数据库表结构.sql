-- ========================================
-- 系统配置管理模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- 系统参数配置表
CREATE TABLE system_parameters (
    param_id VARCHAR(32) PRIMARY KEY COMMENT '参数ID',
    param_code VARCHAR(50) NOT NULL UNIQUE COMMENT '参数编码',
    param_name VARCHAR(200) NOT NULL COMMENT '参数名称',
    param_category VARCHAR(50) NOT NULL COMMENT '参数分类',
    
    -- 参数值
    param_value TEXT COMMENT '参数值',
    default_value TEXT COMMENT '默认值',
    data_type VARCHAR(20) NOT NULL COMMENT '数据类型',
    
    -- 参数属性
    is_encrypted TINYINT(1) DEFAULT 0 COMMENT '是否加密',
    is_system_param TINYINT(1) DEFAULT 0 COMMENT '是否系统参数',
    is_editable TINYINT(1) DEFAULT 1 COMMENT '是否可编辑',
    is_visible TINYINT(1) DEFAULT 1 COMMENT '是否可见',
    
    -- 验证规则
    validation_rule TEXT COMMENT '验证规则',
    allowed_values TEXT COMMENT '允许值(JSON数组)',
    min_length INT COMMENT '最小长度',
    max_length INT COMMENT '最大长度',
    min_value DECIMAL(20,6) COMMENT '最小值',
    max_value DECIMAL(20,6) COMMENT '最大值',
    
    -- 参数描述
    param_description TEXT COMMENT '参数描述',
    usage_notes TEXT COMMENT '使用说明',
    
    -- 生效范围
    scope_type VARCHAR(30) DEFAULT 'GLOBAL' COMMENT '作用域类型',
    scope_value VARCHAR(100) COMMENT '作用域值',
    
    -- 生效信息
    effective_date DATE COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 状态管理
    param_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '参数状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version_no INT DEFAULT 1 COMMENT '版本号',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_param_code (param_code),
    INDEX idx_param_category (param_category),
    INDEX idx_param_status (param_status),
    INDEX idx_param_scope (scope_type, scope_value),
    INDEX idx_param_system (is_system_param),
    INDEX idx_param_effective (effective_date, expiry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统参数配置表';

-- 数据字典表
CREATE TABLE data_dictionary (
    dict_id VARCHAR(32) PRIMARY KEY COMMENT '字典ID',
    dict_code VARCHAR(50) NOT NULL COMMENT '字典编码',
    dict_name VARCHAR(200) NOT NULL COMMENT '字典名称',
    dict_type VARCHAR(50) NOT NULL COMMENT '字典类型',
    
    -- 字典项
    dict_key VARCHAR(100) NOT NULL COMMENT '字典键',
    dict_value VARCHAR(500) NOT NULL COMMENT '字典值',
    dict_label VARCHAR(200) NOT NULL COMMENT '字典标签',
    
    -- 显示属性
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    css_class VARCHAR(100) COMMENT 'CSS样式类',
    icon_class VARCHAR(100) COMMENT '图标样式类',
    
    -- 层级关系
    parent_id VARCHAR(32) COMMENT '父级ID',
    level_no INT DEFAULT 1 COMMENT '层级',
    is_leaf TINYINT(1) DEFAULT 1 COMMENT '是否叶子节点',
    
    -- 扩展属性
    extended_attributes JSON COMMENT '扩展属性',
    
    -- 多语言支持
    locale VARCHAR(10) DEFAULT 'zh_CN' COMMENT '语言区域',
    
    -- 字典描述
    dict_description TEXT COMMENT '字典描述',
    
    -- 状态管理
    dict_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '字典状态',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认值',
    is_system TINYINT(1) DEFAULT 0 COMMENT '是否系统字典',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_dict_code (dict_code),
    INDEX idx_dict_type (dict_type),
    INDEX idx_dict_key (dict_key),
    INDEX idx_dict_parent (parent_id),
    INDEX idx_dict_status (dict_status),
    INDEX idx_dict_sort (sort_order),
    INDEX idx_dict_system (is_system),
    UNIQUE KEY uk_dict_code_key (dict_code, dict_key, locale)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典表';

-- 编码规则配置表
CREATE TABLE code_generation_rules (
    rule_id VARCHAR(32) PRIMARY KEY COMMENT '规则ID',
    rule_code VARCHAR(50) NOT NULL UNIQUE COMMENT '规则编码',
    rule_name VARCHAR(200) NOT NULL COMMENT '规则名称',
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型',
    
    -- 编码规则
    code_prefix VARCHAR(20) COMMENT '编码前缀',
    code_suffix VARCHAR(20) COMMENT '编码后缀',
    date_format VARCHAR(50) COMMENT '日期格式',
    serial_length INT DEFAULT 4 COMMENT '流水号长度',
    serial_start INT DEFAULT 1 COMMENT '流水号起始值',
    
    -- 规则模板
    rule_pattern VARCHAR(200) NOT NULL COMMENT '规则模式',
    example_code VARCHAR(100) COMMENT '示例编码',
    
    -- 重置规则
    reset_frequency VARCHAR(20) DEFAULT 'NEVER' COMMENT '重置频率',
    reset_date DATE COMMENT '重置日期',
    current_serial INT DEFAULT 0 COMMENT '当前流水号',
    
    -- 分组规则
    group_by_fields JSON COMMENT '分组字段',
    
    -- 验证规则
    validation_regex VARCHAR(500) COMMENT '验证正则表达式',
    
    -- 规则描述
    rule_description TEXT COMMENT '规则描述',
    usage_scope VARCHAR(200) COMMENT '使用范围',
    
    -- 状态管理
    rule_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '规则状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_rule_code (rule_code),
    INDEX idx_rule_type (rule_type),
    INDEX idx_rule_status (rule_status),
    INDEX idx_rule_reset (reset_frequency, reset_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='编码规则配置表';

-- 编码生成历史表
CREATE TABLE code_generation_history (
    history_id VARCHAR(32) PRIMARY KEY COMMENT '历史ID',
    rule_id VARCHAR(32) NOT NULL COMMENT '规则ID',
    generated_code VARCHAR(100) NOT NULL COMMENT '生成的编码',
    
    -- 生成信息
    serial_number INT NOT NULL COMMENT '流水号',
    group_key VARCHAR(200) COMMENT '分组键',
    generation_date DATE NOT NULL COMMENT '生成日期',
    
    -- 使用信息
    business_type VARCHAR(50) COMMENT '业务类型',
    business_id VARCHAR(32) COMMENT '业务ID',
    used_by VARCHAR(32) COMMENT '使用人',
    
    -- 状态
    code_status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '编码状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (rule_id) REFERENCES code_generation_rules(rule_id) ON DELETE CASCADE,
    INDEX idx_history_rule (rule_id),
    INDEX idx_history_code (generated_code),
    INDEX idx_history_date (generation_date),
    INDEX idx_history_business (business_type, business_id),
    INDEX idx_history_used_by (used_by),
    UNIQUE KEY uk_generated_code (generated_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='编码生成历史表';

-- 消息模板配置表
CREATE TABLE message_templates (
    template_id VARCHAR(32) PRIMARY KEY COMMENT '模板ID',
    template_code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板编码',
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(30) NOT NULL COMMENT '模板类型',
    
    -- 消息渠道
    message_channel VARCHAR(30) NOT NULL COMMENT '消息渠道',
    
    -- 模板内容
    subject_template TEXT COMMENT '主题模板',
    content_template LONGTEXT NOT NULL COMMENT '内容模板',
    
    -- 模板变量
    template_variables JSON COMMENT '模板变量定义',
    
    -- 格式设置
    content_type VARCHAR(30) DEFAULT 'TEXT' COMMENT '内容类型',
    charset VARCHAR(20) DEFAULT 'UTF-8' COMMENT '字符集',
    
    -- 发送配置
    sender_config JSON COMMENT '发送者配置',
    priority_level VARCHAR(20) DEFAULT 'NORMAL' COMMENT '优先级',
    
    -- 触发条件
    trigger_events JSON COMMENT '触发事件',
    trigger_conditions JSON COMMENT '触发条件',
    
    -- 接收者配置
    recipient_config JSON COMMENT '接收者配置',
    
    -- 模板分类
    business_category VARCHAR(50) COMMENT '业务分类',
    functional_category VARCHAR(50) COMMENT '功能分类',
    
    -- 模板描述
    template_description TEXT COMMENT '模板描述',
    usage_notes TEXT COMMENT '使用说明',
    
    -- 状态管理
    template_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '模板状态',
    is_system_template TINYINT(1) DEFAULT 0 COMMENT '是否系统模板',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version_no INT DEFAULT 1 COMMENT '版本号',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_template_code (template_code),
    INDEX idx_template_type (template_type),
    INDEX idx_template_channel (message_channel),
    INDEX idx_template_status (template_status),
    INDEX idx_template_business (business_category),
    INDEX idx_template_system (is_system_template)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息模板配置表';

-- 系统日志配置表
CREATE TABLE system_log_config (
    config_id VARCHAR(32) PRIMARY KEY COMMENT '配置ID',
    logger_name VARCHAR(200) NOT NULL UNIQUE COMMENT '日志记录器名称',
    log_level VARCHAR(20) NOT NULL COMMENT '日志级别',
    
    -- 日志输出配置
    appender_config JSON COMMENT '输出器配置',
    file_path VARCHAR(500) COMMENT '日志文件路径',
    file_max_size VARCHAR(20) COMMENT '文件最大大小',
    max_file_count INT COMMENT '最大文件数',
    
    -- 日志格式
    log_pattern TEXT COMMENT '日志格式模式',
    date_pattern VARCHAR(100) COMMENT '日期格式模式',
    
    -- 过滤规则
    filter_rules JSON COMMENT '过滤规则',
    
    -- 轮转配置
    rotation_policy VARCHAR(30) COMMENT '轮转策略',
    rotation_size VARCHAR(20) COMMENT '轮转大小',
    rotation_time VARCHAR(50) COMMENT '轮转时间',
    
    -- 压缩配置
    compression_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用压缩',
    compression_format VARCHAR(20) COMMENT '压缩格式',
    
    -- 异步配置
    async_enabled TINYINT(1) DEFAULT 0 COMMENT '是否异步日志',
    buffer_size INT COMMENT '缓冲区大小',
    
    -- 配置描述
    config_description TEXT COMMENT '配置描述',
    
    -- 状态管理
    config_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '配置状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_log_config_logger (logger_name),
    INDEX idx_log_config_level (log_level),
    INDEX idx_log_config_status (config_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志配置表';

-- 定时任务配置表
CREATE TABLE scheduled_jobs (
    job_id VARCHAR(32) PRIMARY KEY COMMENT '任务ID',
    job_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码',
    job_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    job_group VARCHAR(100) DEFAULT 'DEFAULT' COMMENT '任务组',
    
    -- 任务类型
    job_type VARCHAR(30) NOT NULL COMMENT '任务类型',
    job_category VARCHAR(50) COMMENT '任务分类',
    
    -- 执行配置
    job_class VARCHAR(500) NOT NULL COMMENT '任务执行类',
    job_method VARCHAR(100) COMMENT '执行方法',
    job_parameters JSON COMMENT '任务参数',
    
    -- 调度配置
    cron_expression VARCHAR(200) COMMENT 'Cron表达式',
    trigger_type VARCHAR(30) NOT NULL COMMENT '触发器类型',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    repeat_interval BIGINT COMMENT '重复间隔(毫秒)',
    repeat_count INT COMMENT '重复次数',
    
    -- 并发控制
    concurrent_execution TINYINT(1) DEFAULT 1 COMMENT '是否允许并发执行',
    max_concurrent_instances INT DEFAULT 1 COMMENT '最大并发实例数',
    
    -- 重试配置
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    retry_interval INT DEFAULT 0 COMMENT '重试间隔(秒)',
    
    -- 超时配置
    timeout_seconds INT COMMENT '超时时间(秒)',
    
    -- 执行状态
    job_status VARCHAR(20) NOT NULL DEFAULT 'PAUSED' COMMENT '任务状态',
    last_execution_time TIMESTAMP COMMENT '上次执行时间',
    next_execution_time TIMESTAMP COMMENT '下次执行时间',
    execution_count INT DEFAULT 0 COMMENT '执行次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    failure_count INT DEFAULT 0 COMMENT '失败次数',
    
    -- 任务描述
    job_description TEXT COMMENT '任务描述',
    
    -- 负责人
    job_owner VARCHAR(32) COMMENT '任务负责人',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_job_code (job_code),
    INDEX idx_job_group (job_group),
    INDEX idx_job_type (job_type),
    INDEX idx_job_status (job_status),
    INDEX idx_job_next_execution (next_execution_time),
    INDEX idx_job_owner (job_owner)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务配置表';

-- 系统接口配置表
CREATE TABLE system_interfaces (
    interface_id VARCHAR(32) PRIMARY KEY COMMENT '接口ID',
    interface_code VARCHAR(50) NOT NULL UNIQUE COMMENT '接口编码',
    interface_name VARCHAR(200) NOT NULL COMMENT '接口名称',
    interface_type VARCHAR(30) NOT NULL COMMENT '接口类型',
    
    -- 接口基本信息
    protocol_type VARCHAR(30) NOT NULL COMMENT '协议类型',
    endpoint_url VARCHAR(500) COMMENT '接口地址',
    http_method VARCHAR(20) COMMENT 'HTTP方法',
    content_type VARCHAR(100) COMMENT '内容类型',
    
    -- 认证配置
    auth_type VARCHAR(30) COMMENT '认证类型',
    auth_config JSON COMMENT '认证配置',
    
    -- 请求配置
    request_headers JSON COMMENT '请求头配置',
    request_parameters JSON COMMENT '请求参数配置',
    request_timeout INT DEFAULT 30 COMMENT '请求超时(秒)',
    
    -- 响应配置
    response_format VARCHAR(30) COMMENT '响应格式',
    response_mapping JSON COMMENT '响应映射配置',
    
    -- 重试配置
    retry_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用重试',
    max_retry_count INT DEFAULT 3 COMMENT '最大重试次数',
    retry_interval INT DEFAULT 5 COMMENT '重试间隔(秒)',
    
    -- 限流配置
    rate_limit_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用限流',
    rate_limit_config JSON COMMENT '限流配置',
    
    -- 监控配置
    monitoring_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用监控',
    alert_config JSON COMMENT '告警配置',
    
    -- 接口分类
    business_category VARCHAR(50) COMMENT '业务分类',
    system_category VARCHAR(50) COMMENT '系统分类',
    
    -- 接口描述
    interface_description TEXT COMMENT '接口描述',
    usage_notes TEXT COMMENT '使用说明',
    
    -- 状态管理
    interface_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '接口状态',
    
    -- 版本信息
    interface_version VARCHAR(20) DEFAULT '1.0' COMMENT '接口版本',
    
    -- 负责人
    owner_id VARCHAR(32) COMMENT '负责人ID',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_interface_code (interface_code),
    INDEX idx_interface_type (interface_type),
    INDEX idx_interface_protocol (protocol_type),
    INDEX idx_interface_status (interface_status),
    INDEX idx_interface_business (business_category),
    INDEX idx_interface_owner (owner_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统接口配置表';

-- 系统配置变更历史表
CREATE TABLE config_change_history (
    change_id VARCHAR(32) PRIMARY KEY COMMENT '变更ID',
    config_type VARCHAR(50) NOT NULL COMMENT '配置类型',
    config_id VARCHAR(32) NOT NULL COMMENT '配置ID',
    config_code VARCHAR(50) COMMENT '配置编码',
    
    -- 变更信息
    change_type VARCHAR(20) NOT NULL COMMENT '变更类型',
    field_name VARCHAR(100) COMMENT '字段名',
    old_value LONGTEXT COMMENT '原值',
    new_value LONGTEXT COMMENT '新值',
    
    -- 变更描述
    change_description TEXT COMMENT '变更描述',
    change_reason TEXT COMMENT '变更原因',
    
    -- 影响分析
    impact_analysis TEXT COMMENT '影响分析',
    
    -- 变更人员
    changed_by VARCHAR(32) NOT NULL COMMENT '变更人',
    change_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
    
    -- 审批信息
    approval_required TINYINT(1) DEFAULT 0 COMMENT '是否需要审批',
    approved_by VARCHAR(32) COMMENT '审批人',
    approval_time TIMESTAMP COMMENT '审批时间',
    approval_status VARCHAR(20) COMMENT '审批状态',
    
    -- 回滚信息
    rollback_enabled TINYINT(1) DEFAULT 1 COMMENT '是否可回滚',
    
    INDEX idx_config_change_type (config_type),
    INDEX idx_config_change_id (config_id),
    INDEX idx_config_change_time (change_time),
    INDEX idx_config_changed_by (changed_by),
    INDEX idx_config_change_approval (approval_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置变更历史表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. system_parameters: 系统参数配置表，全局系统参数管理
2. data_dictionary: 数据字典表，系统码表和字典数据
3. code_generation_rules: 编码规则配置表，自动编码生成规则
4. code_generation_history: 编码生成历史表，编码生成记录
5. message_templates: 消息模板配置表，系统通知模板
6. system_log_config: 系统日志配置表，日志记录配置
7. scheduled_jobs: 定时任务配置表，系统定时任务管理
8. system_interfaces: 系统接口配置表，外部接口配置
9. config_change_history: 系统配置变更历史表，配置变更跟踪

核心特性:
- 全面的系统配置管理
- 灵活的参数化配置
- 完整的编码规则管理
- 多样化的消息通知支持
- 可配置的日志管理
- 强大的定时任务调度
- 统一的接口配置管理
- 完整的变更历史跟踪
*/