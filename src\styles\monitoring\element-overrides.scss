/**
 * Element Plus组件大屏优化样式
 * 针对监控大屏的Element Plus组件样式覆盖
 */

// ============ 监控页面容器 ============
.monitoring-center,
.production-dashboard,
.equipment-dashboard,
.quality-dashboard {
  // ============ 按钮组件优化 ============
  .el-button {
    font-weight: 500;
    color: #fff;
    background: rgb(24 144 255 / 10%);
    border-color: rgb(24 144 255 / 30%);
    
    &:hover, &:focus {
      color: #fff;
      background: rgb(24 144 255 / 20%);
      border-color: rgb(24 144 255 / 50%);
    }
    
    &.is-disabled {
      color: rgb(255 255 255 / 30%);
      background: rgb(255 255 255 / 5%);
      border-color: rgb(255 255 255 / 10%);
    }
    
    // 主要按钮样式
    &--primary {
      color: #fff;
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border-color: #1890ff;
      box-shadow: 0 4px 12px rgb(24 144 255 / 30%);
      
      &:hover, &:focus {
        background: linear-gradient(135deg, #40a9ff, #69c0ff);
        box-shadow: 0 6px 16px rgb(24 144 255 / 40%);
        transform: translateY(-2px);
      }
    }
    
    // 成功按钮样式
    &--success {
      background: linear-gradient(135deg, #52c41a, #73d13d);
      border-color: #52c41a;
      box-shadow: 0 4px 12px rgb(82 196 26 / 30%);
      
      &:hover, &:focus {
        background: linear-gradient(135deg, #73d13d, #95de64);
        box-shadow: 0 6px 16px rgb(82 196 26 / 40%);
      }
    }
    
    // 警告按钮样式
    &--warning {
      background: linear-gradient(135deg, #faad14, #ffc53d);
      border-color: #faad14;
      box-shadow: 0 4px 12px rgb(250 173 20 / 30%);
      
      &:hover, &:focus {
        background: linear-gradient(135deg, #ffc53d, #ffd666);
        box-shadow: 0 6px 16px rgb(250 173 20 / 40%);
      }
    }
    
    // 危险按钮样式
    &--danger {
      background: linear-gradient(135deg, #ff4d4f, #ff7875);
      border-color: #ff4d4f;
      box-shadow: 0 4px 12px rgb(255 77 79 / 30%);
      
      &:hover, &:focus {
        background: linear-gradient(135deg, #ff7875, #ffa39e);
        box-shadow: 0 6px 16px rgb(255 77 79 / 40%);
      }
    }
  }
  
  // ============ 卡片组件优化 ============
  .el-card {
    background: rgb(26 31 55 / 80%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(52 62 95 / 60%);
    border-radius: 12px;
    
    &:hover {
      border-color: rgb(24 144 255 / 60%);
      box-shadow: 
        0 8px 32px rgb(24 144 255 / 15%),
        inset 0 1px 0 rgb(255 255 255 / 10%);
    }
    
    .el-card__header {
      padding: 16px 24px;
      font-weight: 600;
      color: #fff;
      background: rgb(0 0 0 / 20%);
      border-bottom: 1px solid rgb(52 62 95 / 60%);
    }
    
    .el-card__body {
      padding: 24px;
      color: #fff;
    }
  }
  
  // ============ 进度条组件优化 ============
  .el-progress {
    .el-progress__text {
      font-weight: 600;
      color: #fff !important;
    }
    
    // 线性进度条
    .el-progress-bar {
      .el-progress-bar__outer {
        overflow: hidden;
        background: rgb(255 255 255 / 10%);
        border-radius: 6px;
      }
      
      .el-progress-bar__inner {
        position: relative;
        background: linear-gradient(90deg, #1890ff, #40a9ff);
        border-radius: 6px;
        
        &::after {
          position: absolute;
          inset: 0;
          content: '';
          background: linear-gradient(90deg, 
            transparent 0%, 
            rgb(255 255 255 / 30%) 50%, 
            transparent 100%
          );
          animation: shimmer 2s infinite;
        }
      }
    }
    
    // 圆形进度条
    &--circle {
      .el-progress-circle {
        .el-progress-circle__track {
          stroke: rgb(255 255 255 / 10%);
        }
        
        .el-progress-circle__path {
          stroke: url("#gradient");
        }
      }
    }
  }
  
  // ============ 表格组件优化 ============
  .el-table {
    color: #fff;
    background: transparent;
    
    // 表格头部
    .el-table__header {
      th {
        font-weight: 600;
        color: #b8c5d6;
        text-transform: uppercase;
        letter-spacing: 1px;
        background: rgb(0 0 0 / 30%);
        border-bottom: 2px solid rgb(52 62 95 / 60%);
      }
    }
    
    // 表格主体
    .el-table__body {
      tr {
        background: transparent;
        
        &:hover {
          background: rgb(24 144 255 / 10%) !important;
        }
        
        &:nth-child(even) {
          background: rgb(0 0 0 / 10%);
        }
      }
      
      td {
        color: #fff;
        border-bottom: 1px solid rgb(52 62 95 / 60%);
      }
    }
    
    // 边框优化
    &::before,
    &::after {
      display: none;
    }
    
    .el-table__border-left-patch {
      display: none;
    }
  }
  
  // ============ 选择器组件优化 ============
  .el-select {
    .el-input__wrapper {
      color: #fff;
      background: rgb(26 31 55 / 80%);
      border: 1px solid rgb(52 62 95 / 60%);
      
      &:hover,
      &.is-focus {
        border-color: rgb(24 144 255 / 60%);
        box-shadow: 0 0 0 2px rgb(24 144 255 / 10%);
      }
      
      .el-input__inner {
        color: #fff;
        
        &::placeholder {
          color: rgb(255 255 255 / 50%);
        }
      }
      
      .el-input__suffix {
        .el-input__suffix-inner {
          color: rgb(255 255 255 / 70%);
        }
      }
    }
  }
  
  // ============ 输入框组件优化 ============
  .el-input {
    .el-input__wrapper {
      background: rgb(26 31 55 / 80%);
      border: 1px solid rgb(52 62 95 / 60%);
      border-radius: 6px;
      
      &:hover,
      &.is-focus {
        border-color: rgb(24 144 255 / 60%);
        box-shadow: 0 0 0 2px rgb(24 144 255 / 10%);
      }
      
      .el-input__inner {
        color: #fff;
        
        &::placeholder {
          color: rgb(255 255 255 / 50%);
        }
      }
    }
  }
  
  // ============ 徽章组件优化 ============
  .el-badge {
    .el-badge__content {
      font-weight: 600;
      background: linear-gradient(135deg, #ff4d4f, #ff7875);
      border: 1px solid rgb(255 77 79 / 60%);
      box-shadow: 0 2px 8px rgb(255 77 79 / 30%);
    }
    
    &.is-dot {
      .el-badge__content {
        background: #ff4d4f;
        box-shadow: 0 0 0 2px rgb(255 77 79 / 30%);
      }
    }
  }
  
  // ============ 消息组件优化 ============
  .el-message {
    color: #fff;
    background: rgb(26 31 55 / 95%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(52 62 95 / 60%);
    box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
    
    .el-message__icon {
      color: #40a9ff;
    }
    
    &--success {
      background: rgb(26 31 55 / 95%);
      border-color: rgb(82 196 26 / 60%);
      
      .el-message__icon {
        color: #52c41a;
      }
    }
    
    &--warning {
      background: rgb(26 31 55 / 95%);
      border-color: rgb(250 173 20 / 60%);
      
      .el-message__icon {
        color: #faad14;
      }
    }
    
    &--error {
      background: rgb(26 31 55 / 95%);
      border-color: rgb(255 77 79 / 60%);
      
      .el-message__icon {
        color: #ff4d4f;
      }
    }
  }
  
  // ============ 对话框组件优化 ============
  .el-dialog {
    background: rgb(26 31 55 / 95%);
    backdrop-filter: blur(20px);
    border: 1px solid rgb(52 62 95 / 60%);
    box-shadow: 0 16px 64px rgb(0 0 0 / 50%);
    
    .el-dialog__header {
      color: #fff;
      background: rgb(0 0 0 / 20%);
      border-bottom: 1px solid rgb(52 62 95 / 60%);
      
      .el-dialog__title {
        font-weight: 600;
        color: #fff;
      }
      
      .el-dialog__headerbtn {
        .el-dialog__close {
          color: rgb(255 255 255 / 70%);
          
          &:hover {
            color: #ff4d4f;
          }
        }
      }
    }
    
    .el-dialog__body {
      color: #fff;
    }
    
    .el-dialog__footer {
      background: rgb(0 0 0 / 10%);
      border-top: 1px solid rgb(52 62 95 / 60%);
    }
  }
  
  // ============ 下拉菜单组件优化 ============
  .el-dropdown-menu {
    background: rgb(26 31 55 / 95%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(52 62 95 / 60%);
    box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
    
    .el-dropdown-menu__item {
      color: #fff;
      
      &:hover {
        color: #40a9ff;
        background: rgb(24 144 255 / 20%);
      }
      
      &.is-disabled {
        color: rgb(255 255 255 / 30%);
      }
    }
  }
  
  // ============ 工具提示组件优化 ============
  .el-tooltip__popper {
    color: #fff;
    background: rgb(26 31 55 / 95%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(52 62 95 / 60%);
    box-shadow: 0 8px 24px rgb(0 0 0 / 30%);
    
    .el-tooltip__arrow {
      &::before {
        border-top-color: rgb(26 31 55 / 95%);
      }
    }
  }
  
  // ============ 空状态组件优化 ============
  .el-empty {
    .el-empty__description {
      color: rgb(255 255 255 / 60%);
    }
  }
  
  // ============ 加载组件优化 ============
  .el-loading-mask {
    background: rgb(0 0 0 / 70%);
    backdrop-filter: blur(4px);
    
    .el-loading-spinner {
      .el-loading-text {
        color: #fff;
      }
      
      .circular {
        stroke: #40a9ff;
      }
    }
  }
  
  // ============ 图标组件优化 ============
  .el-icon {
    color: rgb(255 255 255 / 80%);
    
    &:hover {
      color: #40a9ff;
    }
  }
}

// ============ 全局滚动条优化 ============
.monitoring-center,
.production-dashboard,
.equipment-dashboard,
.quality-dashboard {
  * {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgb(255 255 255 / 10%);
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border-radius: 4px;
      
      &:hover {
        background: linear-gradient(135deg, #40a9ff, #69c0ff);
      }
    }
    
    &::-webkit-scrollbar-corner {
      background: rgb(255 255 255 / 10%);
    }
  }
}

// ============ 动画增强 ============
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

// ============ SVG渐变定义 ============
svg defs {
  linearGradient#gradient {
    stop:first-child {
      stop-color: #1890ff;
    }

    stop:last-child {
      stop-color: #40a9ff;
    }
  }
}