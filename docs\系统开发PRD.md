订单多维度调度系统PRD
本文档为起点学院www.qidianla.com学员分享提供
仅供内部学员交流参考，禁止转载、传播、商用

版本修订
版本号 修订章节 修订原因 修订日期 修订人
V1.0 初稿 根据业务需求编写 起点学院

目录

1. 项目概述 4
   1.1 项目背景 4
   1.2 项目目标 4
   1.3 功能需求 4
2. 权限管理 5
3. 调度作业 9
   3.1 订单调度主要逻辑 9
   3.1.1 调度机制 9
   3.1.2 订单建立和释放逻辑规则 11
   3.1.3 调度订单修改---张樱、戴智泽 12
   3.1.4 调度订单取消 16
   3.2 调度 17
   3.2.1 调度订单配置 17
   3.2.2 调度页面 19
   3.2.3 调度操作及查看 20
4. 待调度管理 22
5. 调度报表 23

6. 项目概述
   1.1 项目背景
    大宗订单在生产、配送前，没有和用户确认收货时间的沟通机制，导致大宗订单再投比例较高，再投率占总订单的9%，日均246单大宗订单需要二次运输配送，增加运输配送的运营成本。
    针对普通客户对大宗订单恶意拒收的行为，没有举报反馈机制，导致大宗订单的拒收率无法控制，仓储、运输、配送资源极大浪费。
   1.2 项目目标
    实现订单调度的生产计划时间安排，根据配送时间控制订单的生产、配送节奏。生产、配送前，与客户确认收货时间，减少因为客户原因变更收货时间导致订单再投。
    对恶单拒收的客户，调度人员可通过调度数据分析初步判断出恶意用户，并将此数据发送风控进行恶意用户的权限限制。
    给调度人员合理分配任务，合理安排调度人员的工作效率。
   1.3 功能需求
   模块 功能描述
   权限管理 可设置不同岗位角色，调度人员通过ERPID绑定岗位角色，并授予相应权限。
   调度作业 1、 订单调度配置。
   2、 调度人员进行订单的高效调度处理，并可进行登入处理订单及登出释放订单，以及手工领取订单等功能。
   待调度管理 可解决调度人员处于非操作状态时的订单释放功能。
   调度报表 可统计查看调度订单数据，分析恶意用户以及统计调度人员操作时效等数据。
7. 权限管理

简要说明：
1、 各分公司仅能查看和修改数据记录为本分公司数据，修改后系统记录修改人ERPID和修改时间；总公司人员可查看和修改所有公司的数据，修改后系统记录修改人ERPID和修改时间。
2、 总公司人员权限在履约系统中设置；调度人员权限由总公司人员在多维度调度系统中添加，调度人员资源在履约系统中设置。
界面原型：

图2-1 岗位查询界面

图2 -2 岗位新建界面
逻辑规则：
1、 查询
1）支持按照分公司、岗位名称（支持模糊查询）、变更人erp（支持拼音模糊查询）、变更时间查询活动列表。
2） 查询列表默认显示10条数据，可翻页显示。
3）列表以变更时间降序排列。
2、新建
1）岗位成员
 添加新成员时，如新添加成员已存在，则提示“该成员已存在。” 且不进行重复添加。
 岗位成员列表以操作时间降序排列，岗位成员列表默认显示10条数据，可进行翻页显示。
 删除时需有再次确认提示。
2） 配送中心绑定
 目前配送中心与分公司间系统无关联；但后续分配调度订单的时候，需要根据订单找到配送中心，再由配送中心找到分公司，这里的配送中心一定要该分公司下的所有配送中心都配置，否则会出现调度的订单无法进入调度队列中；如订单未找到配送中心，则订单直接下传。
 配送中心只能在所有分公司下配置一次，需有校验。
 此处配送中心做为与分公司绑定的必要条件。
3） 提交
需对页面进行依次校验，校验通过则提交保存成功，校验不通过则返回新建页面。
字段 必填 格式 校验点 错误提示语
岗位名称 是 字符串，len=20个汉字 必填
字符长度 岗位名称为空，请填写完整。
超过字符限制。
岗位备注 否 字符串 选填

3、 修改
分公司、岗位名称不可更改，其他可进行更改。3. 调度作业
3.1 订单调度主要逻辑
3.1.1 调度机制

6、进入调度系统订单说明
中小件订单且符合大宗设置条件订单进入该系统；
注意：
 自提订单不进入此调度系统；
 当订单时效为当日时效时不进入此调度系统（即promise时间为当天时间）；
 时效未算出来订单，暂不进入此调度系统；
 当调度页面当前计算的预约日历与展示给用户的预约日历不一致时，订单不进入调度系统。
3.1.2 订单建立和释放逻辑规则
1、登入后，当调度人员手动“领取订单”时，系统为调度人员分配10条订单； 调度人员调度完成订单后通过点击“领取订单”按钮进行新订单的领取。（要求：调度人员处理完之后才可以再次领取，限制业务操作过程中25分钟时重新领取，以免出现某个订单一直在系统重复被多人领取）
2、调度页面（范围覆盖图3.2.2-1、图3.2.3-1）有效期为30分钟（此时间设置为可配置），当调度人员进入调度页面“领取订单”时开始计算（页面显示倒计时），25分钟时提示用户“调度页面有效期为30分钟，已领取订单即将过期，请重新领取订单。”，30分钟时未调度订单释放到调度队列，同时个人队列删除。
3、待调度订单队列排序以下单时间排列。
4、调度人员登出系统时，个人队列中的订单自动释放到调度队列，个人队列下订单删除。

3.1.3 调度订单修改
调度系统可修改项有：收货人、手机号码、固定电话、预约日历
订单调度前后用户侧修改逻辑
简要说明：
调度后的订单禁止客户再在前台修改（前台不展示修改入口）；
调度前的订单，可允许客户在前台修改（前台有修改入口，一个月仅可修改一次）,且调度系统仍可对用户修改过的订单再次进行修改，但调度只可修改一次；
客户前台完成订单修改后，需在调度系统中对订单更新数据。
订单调度系统中，对订单的修改、取消操作 以调度系统修改(取消)的信息为最终订单信息。
流程图：

1）当用户在前台修改订单时，修改系统判断是否为调度订单。如果为调度订单，修改系统调用调度系统判断订单是否已经被调度过（此处已被调度过是指：订单已调度，包含调度成功和调度失败）。
2）调度系统返回是否被调度结果，如果已经被调度过，则订单修改页面不显示修改按钮，不可进行修改；如果订单未被调度过，则订单修改页面显示修改按钮。
3）未被调度过订单修改完成后，提交时，修改系统再次调用调度系统。判断是否调度系统也在同时进行调度，
如果调度系统也在进行调度，以调度信息为准，调度系统调用修改接口将修改后信息写入订单中间件，同时将正在调度的状态回传修改系统，修改系统在前台对用户进行不可修改的提示；
如果调度系统没有同时在进行调度，以用户修改的信息为准，调度系统调用修改接口将修改后信息写入订单中间件，同时将未进行调度状态回传修改系统，修改系统允许用户在前台修改。
3.1.4 调度订单取消
1、 取消功能调用取消接口。调度取消后，调取消接口，取消接口进行取消操作；如取消失败，同现有失败前台展示用户。
2、 订单调度固定取消原因“订单多维度调度系统与客户确认取消订单”
3.2 调度
3.2.1 调度订单配置
简要说明：
不是所有调度订单全部进入调度系统，可在“调度订单配置”中配置指定调度的订单范围，仅中小件订单且符合配置的调度订单进入调度系统。
界面原型：

图3.2.1-1 调度订单配置界面

图3.2.2-2 调度订单新增界面
逻辑规则：
1、 需校验配送中心与库房级联，必须选择配送中心才可显示相应库房，库房不可单独显示。
2、 点击每条数据的状态栏中的图标，“√”为开启，“空”为关闭 。
3、 数量配置默认均为“>=”，重量默认为“>”，体积默认为“>”，鼠标悬停某行数据时可进行数量和重量的参数修改设置，每次修改后需记录更新时间及更新人员。三种参数之间关系为“或”。
4、 查询结果支持数据导入、导出，格式如下。
配送中心ID 配送中心 库房ID 库房 订单的商品件数 订单的体积 订单的重量限制 创建时间 更新时间 更新人员
6 北京 0 3C仓A1库 500 300 50 2013/07/16 2013/08/16 Bjwangyi
1） 导入需有配送中心与对应库房级联校验；
2） 导出时需有创建时间、更新时间、更新人员字段；导入时无此三个字段。
3） 导入时，需进行库房校验，如改配送中心下已存在此库房信息，则不允许再次导入。
5、 选择列表中数据，可支持“批量开启”“批量关闭”功能。
6、 选择配送中心、库房及订单类型可新增调度订单设置。新增时需校验：

1. 校验配送中心与库房是否级联正确。
2. 校验是否新增库房已存在，如果已有，则提示“该库房配置已经存在！”
   7、此配置做为新的大宗订单标识的识别配置，现有大宗标的条件均按此配置打标。
   3.2.2 调度页面
   界面原型：

图3.2.2-1 订单调度界面

图3.2.2-2 订单调度查询界面
逻辑规则：
 图3.2.2-1 订单调度界面 主要逻辑：
1、 领取订单后，订单有效期倒计时开始计时，订单有效期30分钟；当倒计时到30分钟时，倒计时ICON显示“订单已过期请重新领取订单”。
2、 支持以订单号、下单时间（精确至秒）、配送中心、库房维度领取订单。
3、 调度处理中列表排序逻辑：先按照下单日期升序排序，下单日期相同按照时效类型排序，时效相同按照截单时间升序排序。
 图3.2.2-2 主要逻辑：
1、此查询页面数据为近一个月数据。
2、订单调度查询界面支持按列表字段导出。
3、调度完成时间和下单时间都精确至秒。
3.2.3 调度操作及查看
界面原型：

图3.2.3-1 调度操作界面

图3.2.3-2 调度查看界面
逻辑规则：
1、页面显示字段
调度页面：订单编号、下单时间、库房、支付方式、货物重量、货物体积、货物数量、客户账号、客户级别、收货人姓名、固定电话、手机号码、金额、收货地址、货物电梯、是否需要搬动、可通过车型、可选波次；
查询页面：除以上字段外，承诺送达日期、送达波次、进入调度时间、调度完成时间、调度停留时长；
2、预约日历展示规则
1）预约日历最长显示7天，展示长度与前台展示相同；
2）当调度页面展示预约日历异常时，调度人员点击“调度”后，直接提示“预约日历与当前显示不符，订单将直接下传。”；
3、调度运输备注
Erp中“订单查询”中“配送信息”中添加“多维度调度运输备注”，以接口调用方式返回展示信息。
接口描述：
入参：订单号
回参：订单运输备注信息
4、 进入调度时间：即点击调度列表页中“调度”时的时间；
调度完成时间：即调度详情页中点击“调度”时的时间；
调度停留时长：调度完成时间-进入调度时间4. 待调度管理
简要说明：
待调度管理可对未领取、未释放但紧急订单进行针对性调度操作。
界面原型：

图4-1 待调度管理界面
逻辑规则：
1、 待调度管理中订单数据源：待调度队列中的订单。
2、 支持按订单号查询订单，每次仅查询出一条订单。
3、 如订单已被领取，显示领取状态、领取人ID、领取时间、下单时间；
如订单未被领取，仅显示下单时间；
4、 已领取订单可进行释放及直接下传操作，释放后状态变为领取，可重新领取该订单；
未领取订单可进行调度和直接下传操作。
5、 对已领取订单，当需要释放重新领取时，需判断该订单是否已进入调度详情页，如已进入调度详情页，则提示“无法释放”。直接下传逻辑同样需判断该订单是否已进入调度详情页，如已进入，提示“无法直接下传”。5. 调度数据
界面原型：

图5-1 调度数据界面
逻辑规则：
所有数据均仅可查看昨日及之前数据，所有数据当日0点更新。
1、 支持按列表字段导出，最多可导出30天数据；
2、 进入调度时间：即点击调度列表页中“调度”时的时间；

调度完成时间：即调度详情页中点击“调度”时的时间；
调度停留时长：调度完成时间-进入调度时间
3、总公司可看全部区域数据，分公司仅可查看本公司数据。
