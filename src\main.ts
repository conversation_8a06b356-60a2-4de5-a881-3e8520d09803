import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'

// 全局样式导入
import './assets/styles/index.scss'

console.log('🚀 IC封测CIM系统启动中...')

// 创建应用实例
const app = createApp(App)

// 使用Pinia状态管理
const pinia = createPinia()
app.use(pinia)

// 使用Vue Router
app.use(router)

console.log('📱 Vue应用配置完成，开始挂载...')

// 挂载应用
app.mount('#app')

console.log('🎉 IC封测CIM系统启动完成！')
