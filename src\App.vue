<template>
  <div id="app" class="app-layout">
    <!-- 登录页面：全屏显示，不显示主应用布局 -->
    <template v-if="isLoginPage">
      <router-view v-slot="{ Component, route }">
        <transition
          name="page-fade"
          mode="out-in"
          @before-enter="onBeforeEnter"
          @after-enter="onAfterEnter"
        >
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </template>

    <!-- 主应用页面：显示完整的应用布局 -->
    <template v-else>
      <!-- 专业级顶部导航 -->
      <header class="app-header">
        <div class="app-header__content">
          <div class="app-header__left">
            <div class="app-logo">
              <div class="app-logo__icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path
                    d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"
                  />
                </svg>
              </div>
              <div class="app-logo__text">
                <div class="app-logo__title">IC封测CIM系统</div>
                <div class="app-logo__subtitle">专业制造执行系统</div>
              </div>
            </div>
          </div>

          <nav class="app-nav" @click="closeDropdown">
            <!-- 导航项 -->
            <div
              v-for="navItem in navigation"
              :key="navItem.path"
              class="app-nav__item-wrapper"
            >
              <!-- 有子菜单的导航项 -->
              <div
                v-if="navItem.children"
                class="app-nav__dropdown"
                :class="{ 'app-nav__dropdown--active': activeDropdown === navItem.name }"
              >
                <button
                  class="app-nav__item app-nav__item--dropdown"
                  :class="{
                    'app-nav__item--active': isNavItemActive(navItem.path)
                  }"
                  @click.stop="toggleDropdown(navItem.name)"
                >
                  <NavIcon :name="navItem.icon" class="app-nav__icon" />
                  <span class="app-nav__text">{{ navItem.name }}</span>
                  <svg
                    class="app-nav__arrow"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <polyline points="6,9 12,15 18,9" />
                  </svg>
                </button>

                <!-- 下拉菜单 -->
                <div
                  v-show="activeDropdown === navItem.name"
                  class="app-nav__dropdown-menu"
                >
                  <router-link
                    v-for="child in navItem.children"
                    :key="child.path"
                    :to="child.path"
                    class="app-nav__dropdown-item"
                    @click="closeDropdown"
                  >
                    {{ child.name }}
                  </router-link>
                </div>
              </div>

              <!-- 普通导航项 -->
              <router-link
                v-else
                :to="navItem.path"
                class="app-nav__item"
              >
                <NavIcon :name="navItem.icon" class="app-nav__icon" />
                <span class="app-nav__text">{{ navItem.name }}</span>
              </router-link>
            </div>
          </nav>

          <div class="app-header__right">
            <!-- 主题切换器 -->
            <!-- <ThemeToggle /> -->

            <!-- 用户下拉菜单 -->
            <div class="user-dropdown-wrapper" @click.stop>
              <div 
                class="user-info" 
                :class="{ 'user-info--active': activeDropdown === 'user' }"
                @click="toggleDropdown('user')"
              >
                <div class="user-avatar">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
                    />
                  </svg>
                </div>
                <div class="user-details">
                  <div class="user-name">{{ currentUser?.realName || '管理员' }}</div>
                  <div class="user-role">{{ currentUser?.position || '系统管理员' }}</div>
                </div>
                <svg class="user-dropdown-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="6,9 12,15 18,9" />
                </svg>
              </div>

              <!-- 用户下拉菜单 -->
              <div 
                v-show="activeDropdown === 'user'" 
                class="user-dropdown-menu"
                @click.stop
              >
                <div class="user-dropdown-header">
                  <div class="user-dropdown-avatar">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
                    </svg>
                  </div>
                  <div class="user-dropdown-info">
                    <div class="user-dropdown-name">{{ currentUser?.realName || '管理员' }}</div>
                    <div class="user-dropdown-email">{{ currentUser?.email || '<EMAIL>' }}</div>
                  </div>
                </div>
                
                <div class="user-dropdown-divider"></div>
                
                <router-link 
                  to="/system/profile" 
                  class="user-dropdown-item"
                  @click="closeDropdown"
                >
                  <svg class="user-dropdown-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
                  </svg>
                  个人资料
                </router-link>
                
                <router-link 
                  to="/system/settings" 
                  class="user-dropdown-item"
                  @click="closeDropdown"
                >
                  <svg class="user-dropdown-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
                  </svg>
                  系统设置
                </router-link>
                
                <div class="user-dropdown-divider"></div>
                
                <button 
                  class="user-dropdown-item user-dropdown-item--logout"
                  @click="handleLogout"
                >
                  <svg class="user-dropdown-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z" />
                  </svg>
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容区域 -->
      <main class="app-main">
        <div class="app-content">
          <router-view v-slot="{ Component, route }">
            <transition
              name="page-fade"
              mode="out-in"
              @before-enter="onBeforeEnter"
              @after-enter="onAfterEnter"
            >
              <!-- 增加KeepAlive缓存数量，减少重复初始化 -->
              <KeepAlive :max="8" :include="keepAliveComponents">
                <component :is="Component" :key="route.path" />
              </KeepAlive>
            </transition>
          </router-view>
        </div>
      </main>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watchEffect, shallowRef } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { debounce } from 'lodash-es'
  import { useAuthStore } from '@/stores/auth'
  import NavIcon from '@/components/base/NavIcon.vue'

  interface NavItem {
    name: string
    path: string
    icon: string
    children?: NavChild[]
  }

  interface NavChild {
    name: string
    path: string
  }

  const $route = useRoute()
  const $router = useRouter()
  const authStore = useAuthStore()
  
  // 当前用户信息
  const currentUser = computed(() => authStore.currentUser)
  
  // 暂时注释掉可能有问题的导入
  // import ThemeToggle from '@/components/layout/ThemeToggle.vue'
  // import { useTheme } from '@/composables/useTheme'

  // 初始化主题系统
  // useTheme()

  // 判断是否为登录页面 - 登录页面不显示主应用布局
  const isLoginPage = computed(() => {
    return $route.path === '/login'
  })

  // 下拉菜单状态管理 - 使用shallowRef优化性能
  const activeDropdown = shallowRef<string | null>(null)

  // 优化：使用更短的防抖时间，提升响应速度
  const toggleDropdown = debounce((navName: string) => {
    activeDropdown.value = activeDropdown.value === navName ? null : navName
  }, 50)

  // 高频页面KeepAlive缓存列表
  const keepAliveComponents = ref([
    'Home',
    'ComponentDemo',
    'CustomerList',
    'OrderManagement',
    'CPTesting',
    'Assembly',
    'FinalTest',
    'EquipmentStatus'
  ])

  // 关闭下拉菜单
  const closeDropdown = () => {
    activeDropdown.value = null
  }

  // 退出登录处理
  const handleLogout = async () => {
    try {
      closeDropdown()
      await authStore.logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  // 优化：计算当前活动的路由前缀，避免重复字符串处理
  const currentRoutePrefix = computed(() => {
    const path = $route.path
    const segments = path.split('/').filter(Boolean)
    return segments.length > 0 ? `/${segments[0]}` : '/'
  })

  // 优化：预计算导航项的激活状态
  const isNavItemActive = (navPath: string): boolean => {
    if (navPath === '/') {
      return $route.path === '/'
    }
    return $route.path.startsWith(navPath.substring(0, navPath.lastIndexOf('/')))
  }

  // 页面过渡性能优化钩子
  const onBeforeEnter = (el: Element) => {
    // 在动画开始前进行性能优化
    if (el instanceof HTMLElement) {
      el.style.willChange = 'opacity, transform'
    }
  }

  const onAfterEnter = (el: Element) => {
    // 动画结束后清理will-change，释放GPU资源
    if (el instanceof HTMLElement) {
      el.style.willChange = 'auto'
    }
  }

  // 导航配置 - 包含下拉菜单的完整导航结构
  const navigation: NavItem[] = [
    {
      name: '首页',
      path: '/',
      icon: 'home'
    },
    {
      name: '客户管理',
      path: '/customers',
      icon: 'users'
    },
    {
      name: '客户询价',
      path: '/inquiry/list',
      icon: 'clipboard'
    },
    {
      name: '订单管理',
      path: '/orders',
      icon: 'clipboard',
      children: [
        { name: '订单管理', path: '/orders' },
        { name: '订单跟踪', path: '/order/tracking' },
        { name: '合同管理', path: '/contract/management' }
      ]
    },
    {
      name: '生产计划',
      path: '/production',
      icon: 'chart'
    },
    {
      name: '物料库存',
      path: '/inventory/management',
      icon: 'box'
    },
    {
      name: '制造执行',
      path: '/manufacturing/cp-testing',
      icon: 'cpu',
      children: [
        { name: 'CP晶圆测试', path: '/manufacturing/cp-testing' },
        { name: '封装工艺控制', path: '/manufacturing/assembly' },
        { name: '最终测试', path: '/manufacturing/final-test' }
      ]
    },
    {
      name: '设备管理',
      path: '/equipment/status',
      icon: 'monitor',
      children: [
        { name: '设备状态监控', path: '/equipment/status' },
        { name: 'SECS/GEM通信', path: '/equipment/secs-gem' },
        { name: '预测性维护', path: '/equipment/maintenance' },
        { name: '设备配置管理', path: '/equipment/config' }
      ]
    },
    {
      name: '质量管理',
      path: '/quality/spc-control',
      icon: 'shield-check',
      children: [
        { name: 'SPC统计控制', path: '/quality/spc-control' },
        { name: '质量追溯系统', path: '/quality/traceability' },
        { name: 'IATF16949合规', path: '/quality/compliance' },
        { name: '质量检验管理', path: '/quality/inspection' },
        { name: '质量分析报告', path: '/quality/analytics' }
      ]
    },
    {
      name: '监控中心',
      path: '/monitoring/center',
      icon: 'bar-chart',
      children: [
        { name: '综合监控中心', path: '/monitoring/center' },
        { name: '生产监控大屏', path: '/monitoring/production' },
        { name: '设备监控大屏', path: '/monitoring/equipment' },
        { name: '质量监控大屏', path: '/monitoring/quality' }
      ]
    },
    {
      name: '组件演示',
      path: '/demo',
      icon: 'component'
    },
    {
      name: '系统管理',
      path: '/system/users',
      icon: 'setting',
      children: [
        { name: '用户管理', path: '/system/users' },
        { name: '角色管理', path: '/system/roles' },
        { name: '权限管理', path: '/system/permissions' },
        { name: '部门管理', path: '/system/departments' },
        { name: '系统日志', path: '/system/logs' }
      ]
    }
  ]

  console.log('🚀 IC封测CIM系统已启动')
</script>

<style lang="scss" scoped>
  // IC封测CIM系统 - 主应用样式
  // 严格遵循极简主义设计系统

  .app-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--color-bg-primary);
    transition: background-color var(--transition-normal);
  }

  // ===== 应用头部 =====
  .app-header {
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    flex-shrink: 0;
    height: var(--header-height);
    overflow: visible; // 关键修复：允许下拉菜单超出header边界显示
    background-color: var(--color-nav-bg);
    border-bottom: 1px solid var(--color-border-light);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);

    &__content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 1600px; // 增加最大宽度给导航更多空间
      height: 100%;
      padding: 0 var(--spacing-4); // 减少左右内边距
      margin: 0 auto;
      overflow: visible; // 修复：允许下拉菜单超出内容区域

      @media (width <= 768px) {
        padding: 0 var(--spacing-3);
      }
    }

    &__left {
      display: flex;
      flex-shrink: 0; // 防止logo区域被压缩
      align-items: center;
      margin-right: var(--spacing-6); // 给导航留出更多空间
    }

    &__right {
      display: flex;
      flex-shrink: 0; // 防止右侧区域被压缩
      gap: var(--spacing-3); // 稍微减少右侧间距
      align-items: center;
      margin-left: var(--spacing-4); // 与导航保持间距
    }
  }

  // ===== 应用Logo =====
  .app-logo {
    display: flex;
    gap: var(--spacing-3);
    align-items: center;

    &__icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      color: var(--color-text-inverse);
      background-color: var(--color-primary);
      border-radius: var(--radius-md);

      svg {
        width: 20px;
        height: 20px;
      }
    }

    &__text {
      display: flex;
      flex-direction: column;
      gap: 2px;

      @media (width <= 640px) {
        display: none;
      }
    }

    &__title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
      line-height: var(--line-height-tight);
      color: var(--color-text-primary);
    }

    &__subtitle {
      font-size: var(--font-size-xs);
      line-height: var(--line-height-tight);
      color: var(--color-text-secondary);
    }
  }

  // ===== 应用导航 =====
  .app-nav {
    display: flex;
    flex: 1;
    gap: var(--spacing-1);
    align-items: center;
    justify-content: center;
    overflow: visible; // 修复：允许下拉菜单显示，不使用overflow-x: auto

    @media (width <= 1200px) {
      gap: var(--spacing-1);
      justify-content: flex-start;
      overflow-x: auto; // 小屏幕时才使用横向滚动
    }

    @media (width <= 768px) {
      gap: 2px;
    }

    &__item-wrapper {
      position: relative;
    }

    &__item {
      display: flex;
      flex-shrink: 0;
      gap: var(--spacing-1);
      align-items: center;
      padding: var(--spacing-2) var(--spacing-2);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      color: var(--color-nav-text);
      text-decoration: none;
      white-space: nowrap;
      cursor: pointer;
      background: none;
      border: none;
      border-radius: var(--radius-base);

      // 性能优化：使用专门的导航过渡
      transition:
        background-color var(--transition-nav),
        color var(--transition-nav),
        transform var(--transition-nav);

      // 性能优化：启用GPU加速
      transform: translateZ(0);

      &:hover {
        color: var(--color-nav-text-active);
        background-color: var(--color-nav-item-hover);
      }

      &.router-link-active,
      &--active {
        color: var(--color-nav-text-active);
        background-color: var(--color-nav-item-active);
      }

      &--dropdown {
        position: relative;
      }

      @media (width <= 768px) {
        gap: 0;
        padding: var(--spacing-2);
      }

      @media (width <= 640px) {
        padding: var(--spacing-1);
      }
    }

    // 图标样式现在在NavIcon组件中处理

    &__arrow {
      width: 14px;
      height: 14px;
      margin-left: var(--spacing-1);

      // 性能优化：使用专门的下拉过渡和GPU加速
      transition: transform var(--transition-dropdown);
      transform: translateZ(0);
    }

    &__text {
      @media (width <= 640px) {
        display: none;
      }
    }

    // 下拉菜单样式 - 使用:deep()解决scoped作用域问题
    &__dropdown {
      position: relative;

      &--active :deep(.app-nav__arrow) {
        transform: rotate(180deg);
      }
    }

    // 使用:deep()确保下拉菜单样式在scoped环境中生效
    :deep(.app-nav__dropdown-menu) {
      position: absolute;
      top: 100%;
      left: 0;
      z-index: var(--z-dropdown);
      min-width: 160px;
      margin-top: var(--spacing-2);
      overflow: hidden;
      background-color: var(--color-bg-primary);
      border: 1px solid var(--color-border-light);
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-lg);

      @media (width <= 768px) {
        left: 50%;
        min-width: 140px;
        transform: translateX(-50%);
      }
    }

    :deep(.app-nav__dropdown-item) {
      display: block;
      padding: var(--spacing-3) var(--spacing-4);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-normal);
      color: var(--color-text-primary);
      text-decoration: none;
      background-color: transparent;
      border-bottom: 1px solid var(--color-border-light);
      transition: all var(--transition-fast);

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        color: var(--color-primary);
        background-color: var(--color-bg-hover);
      }

      &.router-link-active {
        font-weight: var(--font-weight-medium);
        color: var(--color-primary);
        background-color: var(--color-primary-light);
      }
    }
  }

  // ===== 用户下拉菜单 =====
  .user-dropdown-wrapper {
    position: relative;
  }

  .user-info {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    padding: var(--spacing-1) var(--spacing-2);
    cursor: pointer;
    border-radius: var(--radius-base);
    transition: all var(--transition-fast);

    &:hover,
    &--active {
      background-color: var(--color-bg-hover);
    }

    &--active .user-dropdown-arrow {
      transform: rotate(180deg);
    }

    @media (width <= 1024px) {
      gap: var(--spacing-1); // 减少间距
    }

    @media (width <= 768px) {
      display: none;
    }
  }

  .user-dropdown-arrow {
    width: 14px;
    height: 14px;
    margin-left: var(--spacing-1);
    color: var(--color-text-secondary);
    transition: transform var(--transition-fast);
  }

  .user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: var(--z-dropdown);
    min-width: 240px;
    margin-top: var(--spacing-2);
    background-color: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    
    @media (width <= 1024px) {
      min-width: 200px;
    }
  }

  .user-dropdown-header {
    display: flex;
    gap: var(--spacing-3);
    align-items: center;
    padding: var(--spacing-4);
    background-color: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-border-light);
  }

  .user-dropdown-avatar {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--color-text-secondary);
    background-color: var(--color-bg-tertiary);
    border-radius: var(--radius-full);

    svg {
      width: 22px;
      height: 22px;
    }
  }

  .user-dropdown-info {
    flex: 1;
    min-width: 0;
  }

  .user-dropdown-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    line-height: var(--line-height-tight);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .user-dropdown-email {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    line-height: var(--line-height-tight);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 2px;
  }

  .user-dropdown-divider {
    height: 1px;
    background-color: var(--color-border-light);
    margin: var(--spacing-1) 0;
  }

  .user-dropdown-item {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    text-decoration: none;
    background: none;
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);

    &:hover {
      color: var(--color-primary);
      background-color: var(--color-bg-hover);
    }

    &--logout {
      color: var(--color-danger);

      &:hover {
        color: var(--color-danger);
        background-color: var(--color-danger-light);
      }
    }
  }

  .user-dropdown-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  .user-avatar {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    color: var(--color-text-secondary);
    background-color: var(--color-bg-tertiary);
    border-radius: var(--radius-full);

    svg {
      width: 18px;
      height: 18px;
    }
  }

  .user-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .user-name {
    font-size: var(--font-size-xs); // 使用更小字体
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    color: var(--color-text-primary);
  }

  .user-role {
    font-size: 11px; // 更小的角色文字
    line-height: var(--line-height-tight);
    color: var(--color-text-secondary);
  }

  // ===== 主内容区域 =====
  .app-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    background-color: var(--color-bg-secondary);
    transition: background-color var(--transition-normal);
  }

  .app-content {
    flex: 1;
    padding: var(--spacing-6);
    overflow: hidden;

    @media (width <= 768px) {
      padding: var(--spacing-4);
    }
  }

  // ===== 页面过渡动画 - 极简高性能版本 =====
  .page-fade-enter-active,
  .page-fade-leave-active {
    transition: opacity var(--transition-nav);

    // 只使用opacity变化，避免transform引起的重排
  }

  .page-fade-enter-from,
  .page-fade-leave-to {
    opacity: 0;
  }

  .page-fade-enter-to,
  .page-fade-leave-from {
    opacity: 1;
  }

  // ===== 响应式优化 =====
  @media (width <= 1024px) {
    .app-header__content {
      gap: var(--spacing-4);
    }

    .app-nav {
      gap: var(--spacing-1);
    }
  }

  @media (width <= 768px) {
    .app-header {
      height: 56px;
    }

    .app-logo__icon {
      width: 28px;
      height: 28px;

      svg {
        width: 16px;
        height: 16px;
      }
    }

    .app-nav__item {
      gap: 0;
      padding: var(--spacing-2);
    }

    .app-content {
      padding: var(--spacing-4) var(--spacing-3);
    }
  }

  // ===== 主题切换动画 =====
  .theme-transition {
    transition:
      background-color var(--transition-normal),
      color var(--transition-normal),
      border-color var(--transition-normal),
      box-shadow var(--transition-normal);
  }
</style>
