/**
 * IC封测CIM系统 - 订单状态管理
 * Order Store Management
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  Order,
  OrderQueryParams,
  OrderListResponse,
  CreateOrderData,
  OrderSearchForm,
  OrderStats,
  OrderStatCard
} from '@/types/order'
import { OrderStatus, OrderPriority, PackageType } from '@/types/order'
import * as orderApi from '@/api/order'

interface OrderState {
  orders: Order[]
  currentOrder: Order | null
  loading: boolean
  searchForm: OrderSearchForm
  pagination: {
    current: number
    pageSize: number
    total: number
    showSizeChanger: boolean
    showQuickJumper: boolean
  }
  stats: OrderStats | null
  statCards: OrderStatCard[]
}

export const useOrderStore = defineStore('order', () => {
  // 状态
  const state = ref<OrderState>({
    orders: [],
    currentOrder: null,
    loading: false,
    searchForm: {
      orderNumber: '',
      customerId: '',
      customerName: '',
      status: [],
      priority: [],
      packageType: [],
      orderDateRange: null,
      deliveryDateRange: null
    },
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true
    },
    stats: null,
    statCards: []
  })

  // 计算属性
  const orderList = computed(() => state.value.orders)
  const currentOrder = computed(() => state.value.currentOrder)
  const isLoading = computed(() => state.value.loading)
  const searchForm = computed(() => state.value.searchForm)
  const pagination = computed(() => state.value.pagination)
  const orderStats = computed(() => state.value.stats)
  const statCards = computed(() => state.value.statCards)

  // 根据状态分组的订单
  const ordersByStatus = computed(() => {
    const groups: Record<OrderStatus, Order[]> = {
      [OrderStatus.PENDING]: [],
      [OrderStatus.CONFIRMED]: [],
      [OrderStatus.PROCESSING]: [],
      [OrderStatus.TESTING]: [],
      [OrderStatus.COMPLETED]: [],
      [OrderStatus.CANCELLED]: [],
      [OrderStatus.ON_HOLD]: []
    }

    state.value.orders.forEach(order => {
      groups[order.status].push(order)
    })

    return groups
  })

  // 急需处理的订单
  const urgentOrders = computed(() => {
    const now = new Date()
    return state.value.orders.filter(
      order =>
        order.priority === OrderPriority.URGENT ||
        (order.status === OrderStatus.PROCESSING &&
          new Date(order.schedule.deliveryDate).getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000)
    )
  })

  // 逾期订单
  const overdueOrders = computed(() => {
    const now = new Date()
    return state.value.orders.filter(order => {
      const deliveryDate = new Date(order.schedule.deliveryDate)
      return (
        deliveryDate < now &&
        order.status !== OrderStatus.COMPLETED &&
        order.status !== OrderStatus.CANCELLED
      )
    })
  })

  // Actions
  const setLoading = (loading: boolean) => {
    state.value.loading = loading
  }

  const setOrders = (orders: Order[]) => {
    state.value.orders = orders
  }

  const setCurrentOrder = (order: Order | null) => {
    state.value.currentOrder = order
  }

  const updateSearchForm = (form: Partial<OrderSearchForm>) => {
    Object.assign(state.value.searchForm, form)
  }

  const resetSearchForm = () => {
    state.value.searchForm = {
      orderNumber: '',
      customerId: '',
      customerName: '',
      status: [],
      priority: [],
      packageType: [],
      orderDateRange: null,
      deliveryDateRange: null
    }
  }

  const updatePagination = (pagination: Partial<typeof state.value.pagination>) => {
    Object.assign(state.value.pagination, pagination)
  }

  const setStats = (stats: OrderStats) => {
    state.value.stats = stats
  }

  const setStatCards = (cards: OrderStatCard[]) => {
    state.value.statCards = cards
  }

  // 获取订单列表
  const fetchOrders = async (params?: Partial<OrderQueryParams>) => {
    setLoading(true)
    try {
      const queryParams: OrderQueryParams = {
        page: state.value.pagination.current,
        pageSize: state.value.pagination.pageSize,
        orderNumber: state.value.searchForm.orderNumber || undefined,
        customerId: state.value.searchForm.customerId || undefined,
        customerName: state.value.searchForm.customerName || undefined,
        status:
          state.value.searchForm.status.length > 0 ? state.value.searchForm.status : undefined,
        priority:
          state.value.searchForm.priority.length > 0 ? state.value.searchForm.priority : undefined,
        packageType:
          state.value.searchForm.packageType.length > 0
            ? state.value.searchForm.packageType
            : undefined,
        orderDateRange: state.value.searchForm.orderDateRange || undefined,
        deliveryDateRange: state.value.searchForm.deliveryDateRange || undefined,
        ...params
      }

      const response: OrderListResponse = await orderApi.getOrders(queryParams)

      setOrders(response.data)
      updatePagination({
        current: response.page,
        total: response.total
      })

      return response
    } catch (error) {
      console.error('获取订单列表失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 获取订单详情
  const fetchOrderById = async (id: string) => {
    setLoading(true)
    try {
      const order = await orderApi.getOrderById(id)
      setCurrentOrder(order)
      return order
    } catch (error) {
      console.error('获取订单详情失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 创建订单
  const createOrder = async (data: CreateOrderData) => {
    setLoading(true)
    try {
      const newOrder = await orderApi.createOrder(data)
      // 重新获取订单列表
      await fetchOrders()
      return newOrder
    } catch (error) {
      console.error('创建订单失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 更新订单
  const updateOrder = async (
    id: string,
    data: Partial<CreateOrderData & { status: OrderStatus }>
  ) => {
    setLoading(true)
    try {
      const updatedOrder = await orderApi.updateOrder(id, data)

      // 更新本地状态
      const index = state.value.orders.findIndex(o => o.id === id)
      if (index !== -1) {
        state.value.orders[index] = updatedOrder
      }

      if (state.value.currentOrder?.id === id) {
        setCurrentOrder(updatedOrder)
      }

      return updatedOrder
    } catch (error) {
      console.error('更新订单失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 删除订单
  const deleteOrder = async (id: string) => {
    setLoading(true)
    try {
      const success = await orderApi.deleteOrder(id)
      if (success) {
        // 从本地状态中移除
        const index = state.value.orders.findIndex(o => o.id === id)
        if (index !== -1) {
          state.value.orders.splice(index, 1)
        }

        // 更新分页
        updatePagination({
          total: state.value.pagination.total - 1
        })
      }
      return success
    } catch (error) {
      console.error('删除订单失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 批量删除订单
  const batchDeleteOrders = async (ids: string[]) => {
    setLoading(true)
    try {
      const result = await orderApi.batchDeleteOrders(ids)

      // 重新获取订单列表以保持数据一致性
      await fetchOrders()

      return result
    } catch (error) {
      console.error('批量删除订单失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 更新订单状态
  const updateOrderStatus = async (id: string, status: OrderStatus) => {
    setLoading(true)
    try {
      const updatedOrder = await orderApi.updateOrderStatus(id, status)

      // 更新本地状态
      const index = state.value.orders.findIndex(o => o.id === id)
      if (index !== -1) {
        state.value.orders[index] = updatedOrder
      }

      return updatedOrder
    } catch (error) {
      console.error('更新订单状态失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 获取统计信息
  const fetchOrderStats = async () => {
    try {
      const [stats, statCards] = await Promise.all([
        orderApi.getOrderStats(),
        orderApi.getOrderStatCards()
      ])

      setStats(stats)
      setStatCards(statCards)

      return { stats, statCards }
    } catch (error) {
      console.error('获取订单统计失败:', error)
      throw error
    }
  }

  // 搜索订单建议
  const searchOrderSuggestions = async (keyword: string) => {
    try {
      return await orderApi.searchOrderSuggestions(keyword)
    } catch (error) {
      console.error('搜索订单建议失败:', error)
      return []
    }
  }

  // 导出订单数据
  const exportOrders = async (params?: OrderQueryParams) => {
    setLoading(true)
    try {
      return await orderApi.exportOrders(params)
    } catch (error) {
      console.error('导出订单数据失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 获取急需处理的订单
  const fetchUrgentOrders = async () => {
    try {
      return await orderApi.getUrgentOrders()
    } catch (error) {
      console.error('获取急需处理订单失败:', error)
      return []
    }
  }

  // 获取逾期订单
  const fetchOverdueOrders = async () => {
    try {
      return await orderApi.getOverdueOrders()
    } catch (error) {
      console.error('获取逾期订单失败:', error)
      return []
    }
  }

  // 重置状态
  const resetState = () => {
    state.value = {
      orders: [],
      currentOrder: null,
      loading: false,
      searchForm: {
        orderNumber: '',
        customerId: '',
        customerName: '',
        status: [],
        priority: [],
        packageType: [],
        orderDateRange: null,
        deliveryDateRange: null
      },
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true
      },
      stats: null,
      statCards: []
    }
  }

  return {
    // 状态
    orderList,
    currentOrder,
    isLoading,
    searchForm,
    pagination,
    orderStats,
    statCards,
    ordersByStatus,
    urgentOrders,
    overdueOrders,

    // Actions
    setLoading,
    setOrders,
    setCurrentOrder,
    updateSearchForm,
    resetSearchForm,
    updatePagination,
    setStats,
    setStatCards,
    fetchOrders,
    fetchOrderById,
    createOrder,
    updateOrder,
    deleteOrder,
    batchDeleteOrders,
    updateOrderStatus,
    fetchOrderStats,
    searchOrderSuggestions,
    exportOrders,
    fetchUrgentOrders,
    fetchOverdueOrders,
    resetState
  }
})
