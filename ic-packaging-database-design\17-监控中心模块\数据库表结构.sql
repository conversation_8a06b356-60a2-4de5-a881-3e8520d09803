-- ========================================
-- 监控中心模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- 监控指标配置表
CREATE TABLE monitoring_metrics (
    metric_id VARCHAR(32) PRIMARY KEY COMMENT '指标ID',
    metric_code VARCHAR(50) NOT NULL UNIQUE COMMENT '指标编码',
    metric_name VARCHAR(200) NOT NULL COMMENT '指标名称',
    metric_category VARCHAR(50) NOT NULL COMMENT '指标分类',
    
    -- 指标属性
    metric_type VARCHAR(30) NOT NULL COMMENT '指标类型',
    data_type VARCHAR(20) NOT NULL COMMENT '数据类型',
    measurement_unit VARCHAR(20) COMMENT '度量单位',
    
    -- 数据源配置
    data_source_type VARCHAR(30) NOT NULL COMMENT '数据源类型',
    data_source_config JSON COMMENT '数据源配置',
    collection_method VARCHAR(30) COMMENT '采集方法',
    collection_frequency INT NOT NULL COMMENT '采集频率(秒)',
    
    -- 阈值配置
    warning_threshold_upper DECIMAL(15,4) COMMENT '预警上限阈值',
    warning_threshold_lower DECIMAL(15,4) COMMENT '预警下限阈值',
    critical_threshold_upper DECIMAL(15,4) COMMENT '严重上限阈值',
    critical_threshold_lower DECIMAL(15,4) COMMENT '严重下限阈值',
    
    -- 告警配置
    alert_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用告警',
    alert_rules JSON COMMENT '告警规则',
    escalation_rules JSON COMMENT '升级规则',
    
    -- 聚合配置
    aggregation_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用聚合',
    aggregation_functions JSON COMMENT '聚合函数',
    aggregation_periods JSON COMMENT '聚合周期',
    
    -- 存储配置
    retention_period_days INT DEFAULT 365 COMMENT '数据保留天数',
    compression_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用压缩',
    
    -- 显示配置
    display_format VARCHAR(50) COMMENT '显示格式',
    chart_type VARCHAR(30) COMMENT '图表类型',
    color_scheme VARCHAR(50) COMMENT '色彩方案',
    
    -- 业务属性
    business_owner VARCHAR(32) COMMENT '业务负责人',
    technical_owner VARCHAR(32) COMMENT '技术负责人',
    criticality_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '重要程度',
    
    -- 指标描述
    metric_description TEXT COMMENT '指标描述',
    calculation_logic TEXT COMMENT '计算逻辑',
    
    -- 状态管理
    metric_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '指标状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_metric_code (metric_code),
    INDEX idx_metric_category (metric_category),
    INDEX idx_metric_type (metric_type),
    INDEX idx_metric_status (metric_status),
    INDEX idx_metric_business_owner (business_owner),
    INDEX idx_metric_criticality (criticality_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控指标配置表';

-- 实时监控数据表
CREATE TABLE real_time_monitoring_data (
    data_id VARCHAR(32) PRIMARY KEY COMMENT '数据ID',
    metric_id VARCHAR(32) NOT NULL COMMENT '指标ID',
    
    -- 时间戳
    timestamp_utc TIMESTAMP(3) NOT NULL COMMENT 'UTC时间戳(毫秒精度)',
    timestamp_local TIMESTAMP(3) NOT NULL COMMENT '本地时间戳(毫秒精度)',
    
    -- 数据值
    metric_value DECIMAL(15,6) NOT NULL COMMENT '指标值',
    formatted_value VARCHAR(100) COMMENT '格式化值',
    
    -- 维度标签
    dimension_labels JSON COMMENT '维度标签',
    
    -- 数据来源
    data_source VARCHAR(100) COMMENT '数据来源',
    collection_point VARCHAR(100) COMMENT '采集点',
    
    -- 数据质量
    data_quality_flag VARCHAR(20) DEFAULT 'GOOD' COMMENT '数据质量标识',
    confidence_score DECIMAL(3,2) DEFAULT 1.0 COMMENT '置信度',
    
    -- 异常检测
    anomaly_score DECIMAL(8,4) COMMENT '异常分数',
    is_anomaly TINYINT(1) DEFAULT 0 COMMENT '是否异常',
    
    -- 告警状态
    alert_level VARCHAR(20) DEFAULT 'NORMAL' COMMENT '告警级别',
    
    -- 审计字段
    created_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    
    FOREIGN KEY (metric_id) REFERENCES monitoring_metrics(metric_id) ON DELETE CASCADE,
    INDEX idx_rtm_metric (metric_id),
    INDEX idx_rtm_timestamp (timestamp_utc),
    INDEX idx_rtm_alert_level (alert_level),
    INDEX idx_rtm_anomaly (is_anomaly),
    INDEX idx_rtm_data_quality (data_quality_flag),
    -- 时间分区索引 - 按小时分区
    INDEX idx_rtm_hourly (metric_id, DATE_FORMAT(timestamp_utc, '%Y-%m-%d %H:00:00'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时监控数据表'
PARTITION BY RANGE (UNIX_TIMESTAMP(timestamp_utc)) (
    PARTITION p_current VALUES LESS THAN (UNIX_TIMESTAMP(NOW() + INTERVAL 1 DAY)),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 告警事件表
CREATE TABLE alert_events (
    alert_id VARCHAR(32) PRIMARY KEY COMMENT '告警ID',
    alert_code VARCHAR(50) NOT NULL UNIQUE COMMENT '告警编码',
    
    -- 关联信息
    metric_id VARCHAR(32) NOT NULL COMMENT '指标ID',
    monitoring_data_id VARCHAR(32) COMMENT '监控数据ID',
    
    -- 告警基本信息
    alert_title VARCHAR(200) NOT NULL COMMENT '告警标题',
    alert_level VARCHAR(20) NOT NULL COMMENT '告警级别',
    alert_type VARCHAR(30) NOT NULL COMMENT '告警类型',
    alert_category VARCHAR(50) COMMENT '告警分类',
    
    -- 触发信息
    trigger_time TIMESTAMP NOT NULL COMMENT '触发时间',
    trigger_value DECIMAL(15,6) NOT NULL COMMENT '触发值',
    threshold_value DECIMAL(15,6) COMMENT '阈值',
    trigger_condition VARCHAR(100) COMMENT '触发条件',
    
    -- 告警内容
    alert_description TEXT NOT NULL COMMENT '告警描述',
    alert_details JSON COMMENT '告警详情',
    
    -- 影响分析
    affected_systems JSON COMMENT '受影响系统',
    business_impact TEXT COMMENT '业务影响',
    estimated_impact_level VARCHAR(20) COMMENT '预估影响级别',
    
    -- 告警状态
    alert_status VARCHAR(20) NOT NULL DEFAULT 'OPEN' COMMENT '告警状态',
    
    -- 处理信息
    assigned_to VARCHAR(32) COMMENT '分配给',
    acknowledged_by VARCHAR(32) COMMENT '确认人',
    acknowledged_time TIMESTAMP COMMENT '确认时间',
    resolved_by VARCHAR(32) COMMENT '解决人',
    resolved_time TIMESTAMP COMMENT '解决时间',
    resolution_notes TEXT COMMENT '解决说明',
    
    -- 自动处理
    auto_resolution_enabled TINYINT(1) DEFAULT 0 COMMENT '是否自动解决',
    auto_resolution_config JSON COMMENT '自动解决配置',
    
    -- 通知配置
    notification_sent TINYINT(1) DEFAULT 0 COMMENT '是否已发送通知',
    notification_channels JSON COMMENT '通知渠道',
    notification_recipients JSON COMMENT '通知接收人',
    
    -- 升级信息
    escalation_level INT DEFAULT 0 COMMENT '升级级别',
    escalation_time TIMESTAMP COMMENT '升级时间',
    
    -- 重复告警
    is_duplicate TINYINT(1) DEFAULT 0 COMMENT '是否重复告警',
    parent_alert_id VARCHAR(32) COMMENT '父告警ID',
    occurrence_count INT DEFAULT 1 COMMENT '出现次数',
    
    -- 告警周期
    first_occurrence TIMESTAMP COMMENT '首次出现',
    last_occurrence TIMESTAMP COMMENT '最后出现',
    duration_seconds INT COMMENT '持续时间(秒)',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (metric_id) REFERENCES monitoring_metrics(metric_id) ON DELETE CASCADE,
    FOREIGN KEY (parent_alert_id) REFERENCES alert_events(alert_id) ON DELETE SET NULL,
    INDEX idx_alert_code (alert_code),
    INDEX idx_alert_metric (metric_id),
    INDEX idx_alert_level (alert_level),
    INDEX idx_alert_status (alert_status),
    INDEX idx_alert_trigger_time (trigger_time),
    INDEX idx_alert_assigned (assigned_to),
    INDEX idx_alert_acknowledged (acknowledged_by),
    INDEX idx_alert_resolved (resolved_by),
    INDEX idx_alert_parent (parent_alert_id),
    INDEX idx_alert_duplicate (is_duplicate)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警事件表';

-- 设备状态监控表
CREATE TABLE equipment_status_monitoring (
    status_id VARCHAR(32) PRIMARY KEY COMMENT '状态ID',
    equipment_id VARCHAR(32) NOT NULL COMMENT '设备ID',
    
    -- 时间戳
    status_timestamp TIMESTAMP NOT NULL COMMENT '状态时间戳',
    
    -- 设备状态
    overall_status VARCHAR(20) NOT NULL COMMENT '总体状态',
    operational_status VARCHAR(20) NOT NULL COMMENT '运行状态',
    communication_status VARCHAR(20) NOT NULL COMMENT '通信状态',
    
    -- 运行参数
    utilization_rate DECIMAL(5,2) COMMENT '利用率(%)',
    throughput_actual DECIMAL(10,2) COMMENT '实际产能',
    throughput_target DECIMAL(10,2) COMMENT '目标产能',
    efficiency_rate DECIMAL(5,2) COMMENT '效率(%)',
    
    -- 质量指标
    yield_rate DECIMAL(5,4) COMMENT '良率',
    first_pass_yield DECIMAL(5,4) COMMENT '首次通过良率',
    defect_rate DECIMAL(6,4) COMMENT '缺陷率',
    
    -- 设备健康度
    health_score DECIMAL(5,2) COMMENT '健康度评分',
    temperature DECIMAL(6,2) COMMENT '温度(℃)',
    vibration_level DECIMAL(8,4) COMMENT '振动水平',
    pressure DECIMAL(8,2) COMMENT '压力',
    
    -- 维护状态
    maintenance_status VARCHAR(20) COMMENT '维护状态',
    last_maintenance_date DATE COMMENT '上次维护日期',
    next_maintenance_due DATE COMMENT '下次维护到期',
    maintenance_overdue_days INT COMMENT '维护逾期天数',
    
    -- 告警统计
    active_alarms_count INT DEFAULT 0 COMMENT '活动告警数',
    critical_alarms_count INT DEFAULT 0 COMMENT '严重告警数',
    warning_alarms_count INT DEFAULT 0 COMMENT '预警告警数',
    
    -- 生产信息
    current_lot_id VARCHAR(32) COMMENT '当前批次ID',
    current_recipe VARCHAR(100) COMMENT '当前配方',
    processed_units_today INT COMMENT '今日处理数量',
    
    -- 环境信息
    cleanroom_class VARCHAR(20) COMMENT '洁净度等级',
    humidity DECIMAL(5,2) COMMENT '湿度(%)',
    
    -- 操作人员
    current_operator VARCHAR(32) COMMENT '当前操作员',
    shift VARCHAR(20) COMMENT '班次',
    
    -- 数据质量
    data_completeness DECIMAL(3,2) DEFAULT 1.0 COMMENT '数据完整性',
    
    -- 审计字段
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_equipment_status_equipment (equipment_id),
    INDEX idx_equipment_status_timestamp (status_timestamp),
    INDEX idx_equipment_status_overall (overall_status),
    INDEX idx_equipment_status_health (health_score),
    INDEX idx_equipment_status_maintenance (maintenance_status),
    INDEX idx_equipment_status_operator (current_operator)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备状态监控表';

-- 生产线监控仪表盘表
CREATE TABLE production_line_dashboards (
    dashboard_id VARCHAR(32) PRIMARY KEY COMMENT '仪表盘ID',
    line_id VARCHAR(32) NOT NULL COMMENT '生产线ID',
    dashboard_name VARCHAR(200) NOT NULL COMMENT '仪表盘名称',
    
    -- 刷新配置
    refresh_interval INT DEFAULT 30 COMMENT '刷新间隔(秒)',
    last_refresh_time TIMESTAMP COMMENT '上次刷新时间',
    
    -- 显示配置
    layout_config JSON COMMENT '布局配置',
    widget_config JSON COMMENT '控件配置',
    theme_config JSON COMMENT '主题配置',
    
    -- 总体KPI
    overall_oee DECIMAL(5,2) COMMENT '总体OEE(%)',
    overall_availability DECIMAL(5,2) COMMENT '总体可用性(%)',
    overall_performance DECIMAL(5,2) COMMENT '总体性能(%)',
    overall_quality DECIMAL(5,2) COMMENT '总体质量(%)',
    
    -- 产量信息
    planned_production BIGINT COMMENT '计划产量',
    actual_production BIGINT COMMENT '实际产量',
    production_achievement DECIMAL(5,2) COMMENT '产量达成率(%)',
    
    -- 质量信息
    total_tested BIGINT COMMENT '总测试数',
    total_passed BIGINT COMMENT '总通过数',
    total_failed BIGINT COMMENT '总失败数',
    current_yield DECIMAL(5,4) COMMENT '当前良率',
    
    -- 设备状态统计
    total_equipment_count INT COMMENT '设备总数',
    running_equipment_count INT COMMENT '运行设备数',
    idle_equipment_count INT COMMENT '空闲设备数',
    down_equipment_count INT COMMENT '故障设备数',
    maintenance_equipment_count INT COMMENT '维护设备数',
    
    -- 告警统计
    total_active_alerts INT DEFAULT 0 COMMENT '总活动告警',
    critical_alerts INT DEFAULT 0 COMMENT '严重告警',
    warning_alerts INT DEFAULT 0 COMMENT '预警告警',
    info_alerts INT DEFAULT 0 COMMENT '信息告警',
    
    -- 人员信息
    total_operators INT COMMENT '总操作员数',
    current_shift VARCHAR(20) COMMENT '当前班次',
    shift_start_time TIME COMMENT '班次开始时间',
    shift_end_time TIME COMMENT '班次结束时间',
    
    -- 环境监控
    average_temperature DECIMAL(6,2) COMMENT '平均温度(℃)',
    average_humidity DECIMAL(5,2) COMMENT '平均湿度(%)',
    cleanroom_status VARCHAR(20) COMMENT '洁净室状态',
    
    -- 能耗监控
    power_consumption DECIMAL(10,2) COMMENT '电力消耗(kWh)',
    water_consumption DECIMAL(10,2) COMMENT '用水消耗(L)',
    compressed_air_consumption DECIMAL(10,2) COMMENT '压缩空气消耗',
    
    -- 趋势数据
    hourly_production_trend JSON COMMENT '小时产量趋势',
    hourly_yield_trend JSON COMMENT '小时良率趋势',
    equipment_utilization_trend JSON COMMENT '设备利用率趋势',
    
    -- 状态管理
    dashboard_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '仪表盘状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_line_dashboard_line (line_id),
    INDEX idx_line_dashboard_status (dashboard_status),
    INDEX idx_line_dashboard_refresh (last_refresh_time),
    UNIQUE KEY uk_line_dashboard_name (line_id, dashboard_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产线监控仪表盘表';

-- 监控规则配置表
CREATE TABLE monitoring_rules (
    rule_id VARCHAR(32) PRIMARY KEY COMMENT '规则ID',
    rule_code VARCHAR(50) NOT NULL UNIQUE COMMENT '规则编码',
    rule_name VARCHAR(200) NOT NULL COMMENT '规则名称',
    rule_type VARCHAR(30) NOT NULL COMMENT '规则类型',
    
    -- 规则分类
    rule_category VARCHAR(50) NOT NULL COMMENT '规则分类',
    business_domain VARCHAR(50) COMMENT '业务域',
    
    -- 触发条件
    trigger_conditions JSON NOT NULL COMMENT '触发条件',
    evaluation_expression TEXT COMMENT '评估表达式',
    
    -- 时间窗口
    time_window_minutes INT COMMENT '时间窗口(分钟)',
    evaluation_frequency INT DEFAULT 60 COMMENT '评估频率(秒)',
    
    -- 动作配置
    actions JSON COMMENT '动作配置',
    notification_config JSON COMMENT '通知配置',
    escalation_config JSON COMMENT '升级配置',
    
    -- 抑制配置
    suppression_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用抑制',
    suppression_duration INT COMMENT '抑制时长(分钟)',
    
    -- 依赖关系
    dependency_rules JSON COMMENT '依赖规则',
    
    -- 生效时间
    effective_time_ranges JSON COMMENT '生效时间范围',
    
    -- 规则状态
    rule_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '规则状态',
    
    -- 优先级
    priority_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '优先级',
    
    -- 负责人
    rule_owner VARCHAR(32) COMMENT '规则负责人',
    
    -- 规则描述
    rule_description TEXT COMMENT '规则描述',
    business_justification TEXT COMMENT '业务理由',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version_no INT DEFAULT 1 COMMENT '版本号',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_monitoring_rule_code (rule_code),
    INDEX idx_monitoring_rule_type (rule_type),
    INDEX idx_monitoring_rule_category (rule_category),
    INDEX idx_monitoring_rule_status (rule_status),
    INDEX idx_monitoring_rule_owner (rule_owner),
    INDEX idx_monitoring_rule_priority (priority_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控规则配置表';

-- 系统性能监控表
CREATE TABLE system_performance_monitoring (
    performance_id VARCHAR(32) PRIMARY KEY COMMENT '性能ID',
    system_component VARCHAR(100) NOT NULL COMMENT '系统组件',
    
    -- 时间戳
    monitoring_timestamp TIMESTAMP NOT NULL COMMENT '监控时间戳',
    
    -- CPU监控
    cpu_usage_percent DECIMAL(5,2) COMMENT 'CPU使用率(%)',
    cpu_load_average DECIMAL(8,2) COMMENT 'CPU负载平均值',
    
    -- 内存监控
    memory_usage_percent DECIMAL(5,2) COMMENT '内存使用率(%)',
    memory_used_mb BIGINT COMMENT '已使用内存(MB)',
    memory_free_mb BIGINT COMMENT '空闲内存(MB)',
    memory_total_mb BIGINT COMMENT '总内存(MB)',
    
    -- 磁盘监控
    disk_usage_percent DECIMAL(5,2) COMMENT '磁盘使用率(%)',
    disk_used_gb BIGINT COMMENT '已使用磁盘(GB)',
    disk_free_gb BIGINT COMMENT '空闲磁盘(GB)',
    disk_io_read_rate DECIMAL(10,2) COMMENT '磁盘读取速率(MB/s)',
    disk_io_write_rate DECIMAL(10,2) COMMENT '磁盘写入速率(MB/s)',
    
    -- 网络监控
    network_in_rate DECIMAL(10,2) COMMENT '网络入流量(MB/s)',
    network_out_rate DECIMAL(10,2) COMMENT '网络出流量(MB/s)',
    network_latency_ms DECIMAL(8,2) COMMENT '网络延迟(ms)',
    
    -- 应用监控
    active_connections INT COMMENT '活动连接数',
    thread_count INT COMMENT '线程数',
    heap_memory_used_mb BIGINT COMMENT '堆内存使用(MB)',
    heap_memory_max_mb BIGINT COMMENT '堆内存最大值(MB)',
    
    -- 数据库监控
    db_connections_active INT COMMENT '数据库活动连接',
    db_connections_idle INT COMMENT '数据库空闲连接',
    db_query_avg_time_ms DECIMAL(8,2) COMMENT '数据库平均查询时间(ms)',
    db_slow_queries_count INT COMMENT '慢查询数量',
    
    -- 响应时间监控
    avg_response_time_ms DECIMAL(8,2) COMMENT '平均响应时间(ms)',
    max_response_time_ms DECIMAL(8,2) COMMENT '最大响应时间(ms)',
    min_response_time_ms DECIMAL(8,2) COMMENT '最小响应时间(ms)',
    
    -- 错误监控
    error_count INT DEFAULT 0 COMMENT '错误数量',
    warning_count INT DEFAULT 0 COMMENT '警告数量',
    
    -- 业务指标
    active_users_count INT COMMENT '活跃用户数',
    concurrent_sessions INT COMMENT '并发会话数',
    
    -- 审计字段
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_sys_perf_component (system_component),
    INDEX idx_sys_perf_timestamp (monitoring_timestamp),
    INDEX idx_sys_perf_cpu (cpu_usage_percent),
    INDEX idx_sys_perf_memory (memory_usage_percent),
    INDEX idx_sys_perf_disk (disk_usage_percent),
    INDEX idx_sys_perf_response (avg_response_time_ms)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统性能监控表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. monitoring_metrics: 监控指标配置表，定义所有监控指标
2. real_time_monitoring_data: 实时监控数据表，存储实时采集的监控数据
3. alert_events: 告警事件表，管理所有告警事件
4. equipment_status_monitoring: 设备状态监控表，专门监控设备状态
5. production_line_dashboards: 生产线监控仪表盘表，生产线整体视图
6. monitoring_rules: 监控规则配置表，定义监控和告警规则
7. system_performance_monitoring: 系统性能监控表，IT系统性能监控

核心特性:
- 全面的监控指标体系
- 实时数据采集和存储
- 智能告警和事件管理
- 设备状态全面监控
- 可视化仪表盘支持
- 灵活的监控规则引擎
- 系统性能全方位监控
- 支持时间序列数据分区
- 异常检测和预警机制
*/