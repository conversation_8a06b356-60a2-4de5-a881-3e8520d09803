/**
 * 客户主数据模拟数据
 * OSAT行业客户数据库
 */

import type {
  Customer,
  CustomerType,
  CustomerLevel,
  CustomerScale,
  CustomerStatus,
  CreditLevel,
  ApplicationField,
  ProcessNode,
  PackagePreference,
  Contact,
  ContactRole
} from '@/types/customer'
import type { ProductType } from '@/types/order'

// 生成客户编码
let customerCodeCounter = 1000

function generateCustomerCode(type: CustomerType): string {
  const prefix = {
    fabless: 'FAB',
    idm: 'IDM',
    foundry: 'FOU',
    distributor: 'DIS',
    broker: 'BRK',
    ems: 'EMS'
  }
  return `${prefix[type]}${(customerCodeCounter++).toString().padStart(4, '0')}`
}

// 生成联系人
function createContact(
  customerId: string,
  name: string,
  role: ContactRole,
  email: string,
  phone: string
): Contact {
  return {
    id: `contact_${Math.random().toString(36).substr(2, 9)}`,
    customerId,
    name,
    englishName: name,
    position: '总经理',
    department: '管理层',
    email,
    phone,
    mobile: phone,
    role,
    contactType: ['Business', 'Decision'],
    status: 'active',
    importance: 5,
    trustLevel: 4,
    influenceScore: 9,
    isDecisionMaker: true,
    isPrimaryContact: true,
    languagePreference: ['中文', 'English'],
    communicationPreference: ['Email', 'Phone', 'WeChat'],
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T09:00:00Z',
    createdBy: 'system'
  }
}

// 模拟客户数据
export const mockCustomers: Customer[] = [
  // 知名Fabless IC设计公司
  {
    id: 'cust_001',
    code: generateCustomerCode('fabless'),
    name: '紫光展锐科技有限公司',
    englishName: 'Unisoc Technologies Co., Ltd.',
    shortName: '紫光展锐',
    logo: '/logos/unisoc.png',
    website: 'https://www.unisoc.com',
    foundedYear: 2001,
    type: 'fabless',
    level: 'strategic',
    status: 'active',
    scale: 'enterprise',
    industryType: 'communication',
    creditLevel: 'A+',
    applicationFields: ['communication', 'ai', 'iot'],
    processNodes: ['7nm', '14nm', '22nm', '28nm'],
    packagePreferences: ['BGA', 'QFN', 'WLCSP'],
    qualityStandard: ['JEDEC', 'AEC-Q100', 'ISO9001'],
    complianceRequirements: ['ROHS', 'REACH', 'IATF16949'],
    contact: createContact(
      'cust_001',
      '李志强',
      'BusinessManager',
      '<EMAIL>',
      '+86-21-6888-8888'
    ),
    address: {
      country: '中国',
      province: '上海市',
      city: '上海市',
      district: '浦东新区',
      street: '张江高科技园区祖冲之路887号',
      postalCode: '201203'
    },
    businessInfo: {
      annualRevenue: 1*********0, // 150亿人民币
      employeeCount: 6000,
      mainProducts: ['5G芯片', 'AI芯片', '物联网芯片'],
      marketPosition: '全球领先的移动通信芯片设计企业',
      competitorInfo: ['联发科', '高通', '海思']
    },
    financialInfo: {
      creditLimit: 50000000, // 5000万
      paymentTerms: 'NET30',
      currency: 'CNY',
      taxId: '91310000MA1FL3E95X'
    },
    qualityCertifications: ['ISO9001:2015', 'IATF16949:2016', 'ISO14001:2015'],
    metrics: {
      totalOrders: 125,
      totalRevenue: *********,
      averageOrderValue: 2292000,
      onTimeDeliveryRate: 96.5,
      qualityScore: 9.2,
      satisfactionScore: 9.0
    },
    createdAt: '2023-06-15T10:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
    createdBy: 'admin',
    lastContactDate: '2024-01-10T16:20:00Z'
  },

  {
    id: 'cust_002',
    code: generateCustomerCode('fabless'),
    name: '瑞芯微电子股份有限公司',
    englishName: 'Rockchip Electronics Co., Ltd.',
    shortName: '瑞芯微',
    logo: '/logos/rockchip.png',
    website: 'https://www.rock-chips.com',
    foundedYear: 2001,
    type: 'fabless',
    level: 'important',
    status: 'active',
    scale: 'large',
    industryType: 'consumer',
    creditLevel: 'A',
    applicationFields: ['consumer', 'ai', 'industrial'],
    processNodes: ['7nm', '14nm', '22nm', '28nm', '40nm'],
    packagePreferences: ['BGA', 'QFN', 'CSP'],
    qualityStandard: ['JEDEC', 'ISO9001'],
    complianceRequirements: ['ROHS', 'REACH'],
    contact: createContact(
      'cust_002',
      '王晓明',
      'BusinessManager',
      '<EMAIL>',
      '+86-591-8750-9999'
    ),
    address: {
      country: '中国',
      province: '福建省',
      city: '福州市',
      district: '鼓楼区',
      street: '软件大道89号软件园A区',
      postalCode: '350003'
    },
    businessInfo: {
      annualRevenue: 8*********, // 85亿人民币
      employeeCount: 3500,
      mainProducts: ['处理器芯片', 'AI芯片', '电源管理芯片'],
      marketPosition: '国内领先的处理器芯片设计公司',
      competitorInfo: ['全志科技', '晶晨股份']
    },
    financialInfo: {
      creditLimit: 30000000, // 3000万
      paymentTerms: 'NET45',
      currency: 'CNY',
      taxId: '91350000MA2XL8E95Y'
    },
    qualityCertifications: ['ISO9001:2015', 'ISO14001:2015'],
    metrics: {
      totalOrders: 89,
      totalRevenue: *********,
      averageOrderValue: 1762921,
      onTimeDeliveryRate: 94.2,
      qualityScore: 8.8,
      satisfactionScore: 8.6
    },
    createdAt: '2023-08-20T11:00:00Z',
    updatedAt: '2024-01-12T09:15:00Z',
    createdBy: 'admin',
    lastContactDate: '2024-01-08T11:40:00Z'
  },

  {
    id: 'cust_003',
    code: generateCustomerCode('idm'),
    name: '三星半导体（中国）有限公司',
    englishName: 'Samsung Semiconductor (China) Co., Ltd.',
    shortName: '三星半导体',
    logo: '/logos/samsung.png',
    website: 'https://www.samsung.com/cn/business/semiconductor/',
    foundedYear: 1983,
    type: 'idm',
    level: 'strategic',
    status: 'active',
    scale: 'enterprise',
    industryType: 'consumer',
    creditLevel: 'A+',
    applicationFields: ['consumer', 'automotive', 'industrial', 'ai'],
    processNodes: ['3nm', '5nm', '7nm', '10nm', '14nm'],
    packagePreferences: ['BGA', 'CSP', 'WLCSP', 'FC'],
    qualityStandard: ['JEDEC', 'AEC-Q100', 'ISO9001', 'TS16949'],
    complianceRequirements: ['ROHS', 'REACH', 'IATF16949', 'WEEE'],
    contact: createContact(
      'cust_003',
      '金智勋',
      'BusinessManager',
      '<EMAIL>',
      '+86-10-8888-9999'
    ),
    address: {
      country: '中国',
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      street: '东三环中路39号建外SOHO A座',
      postalCode: '100022'
    },
    businessInfo: {
      annualRevenue: 4*********0, // 450亿人民币
      employeeCount: 15000,
      mainProducts: ['存储器', '处理器', '传感器', 'OLED驱动IC'],
      marketPosition: '全球领先的半导体制造商',
      competitorInfo: ['台积电', 'SK海力士', '美光科技']
    },
    financialInfo: {
      creditLimit: *********, // 2亿
      paymentTerms: 'NET30',
      currency: 'USD',
      taxId: '91110000MA001FL3E95Z'
    },
    qualityCertifications: ['ISO9001:2015', 'IATF16949:2016', 'ISO14001:2015', 'ISO45001:2018'],
    metrics: {
      totalOrders: 245,
      totalRevenue: 1250000000,
      averageOrderValue: 5102041,
      onTimeDeliveryRate: 98.5,
      qualityScore: 9.6,
      satisfactionScore: 9.4
    },
    createdAt: '2023-03-10T08:00:00Z',
    updatedAt: '2024-01-20T16:45:00Z',
    createdBy: 'admin',
    lastContactDate: '2024-01-18T14:30:00Z'
  },

  {
    id: 'cust_004',
    code: generateCustomerCode('foundry'),
    name: '中芯国际集成电路制造有限公司',
    englishName: 'Semiconductor Manufacturing International Corporation',
    shortName: 'SMIC',
    logo: '/logos/smic.png',
    website: 'https://www.smics.com',
    foundedYear: 2000,
    type: 'foundry',
    level: 'strategic',
    status: 'active',
    scale: 'enterprise',
    industryType: 'communication',
    creditLevel: 'A',
    applicationFields: ['communication', 'consumer', 'automotive', 'industrial'],
    processNodes: ['7nm', '14nm', '22nm', '28nm', '40nm', '55nm'],
    packagePreferences: ['BGA', 'QFP', 'QFN', 'TQFP'],
    qualityStandard: ['JEDEC', 'AEC-Q100', 'ISO9001'],
    complianceRequirements: ['ROHS', 'REACH', 'IATF16949'],
    contact: createContact(
      'cust_004',
      '赵海军',
      'BusinessManager',
      '<EMAIL>',
      '+86-21-3861-0000'
    ),
    address: {
      country: '中国',
      province: '上海市',
      city: '上海市',
      district: '浦东新区',
      street: '张江路18号',
      postalCode: '201203'
    },
    businessInfo: {
      annualRevenue: ***********, // 350亿人民币
      employeeCount: 18000,
      mainProducts: ['代工服务', '工艺开发', '封装测试服务'],
      marketPosition: '中国大陆规模最大的集成电路晶圆代工企业',
      competitorInfo: ['台积电', '联电', '格芯']
    },
    financialInfo: {
      creditLimit: *********, // 1亿
      paymentTerms: 'NET45',
      currency: 'CNY',
      taxId: '91310000733297052H'
    },
    qualityCertifications: ['ISO9001:2015', 'IATF16949:2016', 'ISO14001:2015'],
    metrics: {
      totalOrders: 178,
      totalRevenue: *********,
      averageOrderValue: 4775281,
      onTimeDeliveryRate: 95.8,
      qualityScore: 9.0,
      satisfactionScore: 8.8
    },
    createdAt: '2023-05-15T09:30:00Z',
    updatedAt: '2024-01-15T11:20:00Z',
    createdBy: 'admin',
    lastContactDate: '2024-01-12T10:15:00Z'
  },

  {
    id: 'cust_005',
    code: generateCustomerCode('fabless'),
    name: '汇顶科技股份有限公司',
    englishName: 'Goodix Technology Co., Ltd.',
    shortName: '汇顶科技',
    logo: '/logos/goodix.png',
    website: 'https://www.goodix.com',
    foundedYear: 2002,
    type: 'fabless',
    level: 'important',
    status: 'active',
    scale: 'large',
    industryType: 'consumer',
    creditLevel: 'A',
    applicationFields: ['consumer', 'iot', 'automotive'],
    processNodes: ['14nm', '22nm', '28nm', '40nm'],
    packagePreferences: ['BGA', 'QFN', 'CSP', 'WLCSP'],
    qualityStandard: ['JEDEC', 'ISO9001'],
    complianceRequirements: ['ROHS', 'REACH'],
    contact: createContact(
      'cust_005',
      '张帆',
      'BusinessManager',
      '<EMAIL>',
      '+86-755-3331-3131'
    ),
    address: {
      country: '中国',
      province: '广东省',
      city: '深圳市',
      district: '南山区',
      street: '学府路63号高新区联合总部大厦',
      postalCode: '518057'
    },
    businessInfo: {
      annualRevenue: 6800000000, // 68亿人民币
      employeeCount: 2800,
      mainProducts: ['指纹识别芯片', '触控芯片', '语音芯片'],
      marketPosition: '全球指纹识别芯片领导厂商',
      competitorInfo: ['新思科技', 'FPC', '思立微']
    },
    financialInfo: {
      creditLimit: 25000000, // 2500万
      paymentTerms: 'NET30',
      currency: 'CNY',
      taxId: '914403007338970526'
    },
    qualityCertifications: ['ISO9001:2015', 'ISO14001:2015'],
    metrics: {
      totalOrders: 67,
      totalRevenue: 98500000,
      averageOrderValue: 1470149,
      onTimeDeliveryRate: 92.5,
      qualityScore: 8.5,
      satisfactionScore: 8.3
    },
    createdAt: '2023-09-10T14:00:00Z',
    updatedAt: '2024-01-08T15:45:00Z',
    createdBy: 'admin',
    lastContactDate: '2024-01-05T09:30:00Z'
  },

  {
    id: 'cust_006',
    code: generateCustomerCode('distributor'),
    name: '深圳华强电子世界有限公司',
    englishName: 'Shenzhen Huaqiang Electronics World Co., Ltd.',
    shortName: '华强电子',
    logo: '/logos/huaqiang.png',
    website: 'https://www.hqew.com',
    foundedYear: 1998,
    type: 'distributor',
    level: 'standard',
    status: 'active',
    scale: 'medium',
    industryType: 'consumer',
    creditLevel: 'B+',
    applicationFields: ['consumer', 'industrial', 'automotive'],
    processNodes: ['28nm', '40nm', '55nm', '90nm', '130nm'],
    packagePreferences: ['QFP', 'SOP', 'TQFP', 'DIP'],
    qualityStandard: ['ISO9001'],
    complianceRequirements: ['ROHS'],
    contact: createContact(
      'cust_006',
      '陈建华',
      'BusinessManager',
      '<EMAIL>',
      '+86-755-8332-6688'
    ),
    address: {
      country: '中国',
      province: '广东省',
      city: '深圳市',
      district: '福田区',
      street: '华强北路1002号华强电子世界',
      postalCode: '518031'
    },
    businessInfo: {
      annualRevenue: 1*********, // 12亿人民币
      employeeCount: 800,
      mainProducts: ['电子元器件分销', '技术服务', '供应链管理'],
      marketPosition: '华强北知名电子元器件分销商',
      competitorInfo: ['科通芯城', '立创商城', '得捷电子']
    },
    financialInfo: {
      creditLimit: 8000000, // 800万
      paymentTerms: 'NET60',
      currency: 'CNY',
      taxId: '914403007338970527'
    },
    qualityCertifications: ['ISO9001:2015'],
    metrics: {
      totalOrders: 156,
      totalRevenue: 45600000,
      averageOrderValue: 292308,
      onTimeDeliveryRate: 88.5,
      qualityScore: 7.8,
      satisfactionScore: 7.9
    },
    createdAt: '2023-11-20T10:30:00Z',
    updatedAt: '2024-01-15T14:20:00Z',
    createdBy: 'admin',
    lastContactDate: '2024-01-14T16:45:00Z'
  },

  // 新增一些不同状态的客户
  {
    id: 'cust_007',
    code: generateCustomerCode('fabless'),
    name: '芯原微电子（上海）股份有限公司',
    englishName: 'VeriSilicon Holdings Co., Ltd.',
    shortName: '芯原微电子',
    logo: '/logos/verisilicon.png',
    website: 'https://www.verisilicon.com',
    foundedYear: 2001,
    type: 'fabless',
    level: 'potential',
    status: 'pending',
    scale: 'large',
    industryType: 'consumer',
    creditLevel: 'A',
    applicationFields: ['consumer', 'ai', 'automotive'],
    processNodes: ['7nm', '14nm', '22nm', '28nm'],
    packagePreferences: ['BGA', 'CSP', 'QFN'],
    qualityStandard: ['JEDEC', 'ISO9001'],
    complianceRequirements: ['ROHS', 'REACH'],
    contact: createContact(
      'cust_007',
      '戴伟民',
      'CTO',
      '<EMAIL>',
      '+86-21-6107-3388'
    ),
    address: {
      country: '中国',
      province: '上海市',
      city: '上海市',
      district: '浦东新区',
      street: '张江路1188号汤臣豪园',
      postalCode: '201203'
    },
    businessInfo: {
      annualRevenue: 4*********, // 45亿人民币
      employeeCount: 1800,
      mainProducts: ['GPU IP', 'ISP芯片', '显示芯片'],
      marketPosition: '国内领先的GPU IP和芯片设计公司',
      competitorInfo: ['ARM', 'Imagination', 'Cadence']
    },
    financialInfo: {
      creditLimit: 15000000, // 1500万
      paymentTerms: 'NET30',
      currency: 'CNY',
      taxId: '91310000MA1FL3E96A'
    },
    qualityCertifications: ['ISO9001:2015'],
    metrics: {
      totalOrders: 0,
      totalRevenue: 0,
      averageOrderValue: 0,
      onTimeDeliveryRate: 0,
      qualityScore: 0,
      satisfactionScore: 0
    },
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-10T09:00:00Z',
    createdBy: 'admin'
  },

  {
    id: 'cust_008',
    code: generateCustomerCode('ems'),
    name: '富士康科技集团',
    englishName: 'Foxconn Technology Group',
    shortName: '富士康',
    logo: '/logos/foxconn.png',
    website: 'https://www.foxconn.com',
    foundedYear: 1974,
    type: 'ems',
    level: 'important',
    status: 'suspended',
    scale: 'enterprise',
    industryType: 'consumer',
    creditLevel: 'A+',
    applicationFields: ['consumer', 'communication', 'automotive'],
    processNodes: ['14nm', '22nm', '28nm', '40nm', '55nm'],
    packagePreferences: ['BGA', 'QFP', 'QFN', 'SOP'],
    qualityStandard: ['JEDEC', 'AEC-Q100', 'ISO9001', 'TS16949'],
    complianceRequirements: ['ROHS', 'REACH', 'IATF16949'],
    contact: createContact(
      'cust_008',
      '郭台铭',
      'Chairman',
      '<EMAIL>',
      '+86-755-2878-5888'
    ),
    address: {
      country: '中国',
      province: '广东省',
      city: '深圳市',
      district: '龙华区',
      street: '观澜高新技术产业园',
      postalCode: '518110'
    },
    businessInfo: {
      annualRevenue: 180000000000, // 1800亿人民币
      employeeCount: 80000,
      mainProducts: ['电子制造服务', 'ODM服务', '系统集成'],
      marketPosition: '全球最大的电子制造服务商',
      competitorInfo: ['广达电脑', '和硕联合', '纬创资通']
    },
    financialInfo: {
      creditLimit: *********, // 5亿
      paymentTerms: 'NET45',
      currency: 'USD',
      taxId: '91440300MA5DA8E95B'
    },
    qualityCertifications: ['ISO9001:2015', 'IATF16949:2016', 'ISO14001:2015', 'ISO45001:2018'],
    metrics: {
      totalOrders: 89,
      totalRevenue: *********,
      averageOrderValue: 5123596,
      onTimeDeliveryRate: 97.2,
      qualityScore: 9.1,
      satisfactionScore: 8.9
    },
    createdAt: '2023-04-15T11:00:00Z',
    updatedAt: '2023-12-20T14:30:00Z',
    createdBy: 'admin',
    lastContactDate: '2023-12-15T10:20:00Z'
  }
]

// 导出常量
export const CUSTOMER_TYPE_OPTIONS = [
  { label: 'Fabless IC设计公司', value: 'fabless' },
  { label: 'IDM制造商', value: 'idm' },
  { label: 'Foundry代工厂', value: 'foundry' },
  { label: '分销商', value: 'distributor' },
  { label: '经纪商', value: 'broker' },
  { label: 'EMS服务商', value: 'ems' }
]

export const CUSTOMER_LEVEL_OPTIONS = [
  { label: '战略客户', value: 'strategic', color: '#f56c6c' },
  { label: '重要客户', value: 'important', color: '#e6a23c' },
  { label: '标准客户', value: 'standard', color: '#409eff' },
  { label: '潜在客户', value: 'potential', color: '#909399' }
]

export const CUSTOMER_STATUS_OPTIONS = [
  { label: '活跃', value: 'active', color: '#67c23a' },
  { label: '非活跃', value: 'inactive', color: '#909399' },
  { label: '审核中', value: 'pending', color: '#e6a23c' },
  { label: '暂停', value: 'suspended', color: '#f56c6c' },
  { label: '归档', value: 'archived', color: '#909399' }
]

export const CUSTOMER_SCALE_OPTIONS = [
  { label: '初创企业', value: 'startup' },
  { label: '小型企业', value: 'small' },
  { label: '中型企业', value: 'medium' },
  { label: '大型企业', value: 'large' },
  { label: '跨国企业', value: 'enterprise' }
]

export const APPLICATION_FIELD_OPTIONS = [
  { label: '汽车电子', value: 'automotive' },
  { label: '消费电子', value: 'consumer' },
  { label: '通信', value: 'communication' },
  { label: '工业控制', value: 'industrial' },
  { label: 'AI芯片', value: 'ai' },
  { label: '物联网', value: 'iot' },
  { label: '医疗电子', value: 'medical' },
  { label: '航空航天', value: 'aerospace' },
  { label: '电源管理', value: 'power' },
  { label: '安全芯片', value: 'security' }
]

export const PROCESS_NODE_OPTIONS = [
  { label: '3nm', value: '3nm' },
  { label: '5nm', value: '5nm' },
  { label: '7nm', value: '7nm' },
  { label: '10nm', value: '10nm' },
  { label: '14nm', value: '14nm' },
  { label: '16nm', value: '16nm' },
  { label: '22nm', value: '22nm' },
  { label: '28nm', value: '28nm' },
  { label: '40nm', value: '40nm' },
  { label: '55nm', value: '55nm' },
  { label: '65nm', value: '65nm' },
  { label: '90nm', value: '90nm' },
  { label: '130nm', value: '130nm' },
  { label: '180nm', value: '180nm' },
  { label: '180nm以上', value: 'above_180nm' }
]

export const PACKAGE_PREFERENCE_OPTIONS = [
  { label: 'QFP (四方扁平封装)', value: 'QFP' },
  { label: 'BGA (球栅阵列封装)', value: 'BGA' },
  { label: 'CSP (芯片尺寸封装)', value: 'CSP' },
  { label: 'QFN (四方扁平无引脚封装)', value: 'QFN' },
  { label: 'SOP (小外形封装)', value: 'SOP' },
  { label: 'TSOP (薄小外形封装)', value: 'TSOP' },
  { label: 'TQFP (薄四方扁平封装)', value: 'TQFP' },
  { label: 'LQFP (低剖面四方扁平封装)', value: 'LQFP' },
  { label: 'FC (倒装芯片)', value: 'FC' },
  { label: 'WLCSP (晶圆级芯片封装)', value: 'WLCSP' },
  { label: 'DIP (双列直插封装)', value: 'DIP' }
]

export const CREDIT_LEVEL_OPTIONS = [
  { label: 'A+', value: 'A+', color: '#67c23a' },
  { label: 'A', value: 'A', color: '#67c23a' },
  { label: 'B+', value: 'B+', color: '#e6a23c' },
  { label: 'B', value: 'B', color: '#e6a23c' },
  { label: 'C+', value: 'C+', color: '#f56c6c' },
  { label: 'C', value: 'C', color: '#f56c6c' },
  { label: 'D', value: 'D', color: '#909399' }
]

// 生成客户编码的工具函数
export function generateNewCustomerCode(type: CustomerType): string {
  return generateCustomerCode(type)
}
