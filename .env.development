# 开发环境配置
NODE_ENV=development

# 应用信息
VITE_APP_TITLE="IC封测CIM系统"
VITE_APP_VERSION="1.0.0"
VITE_APP_DESCRIPTION="IC Assembly & Testing CIM System"

# 服务端口 (IC封测CIM系统专用端口)
VITE_PORT=8851

# API配置
VITE_API_BASE_URL="http://localhost:8080"
VITE_API_PREFIX="/api/v1"

# WebSocket配置
VITE_WS_BASE_URL="ws://localhost:8080"
VITE_WS_PREFIX="/ws"

# 开发工具
VITE_OPEN_DEVTOOLS=true
VITE_SOURCEMAP=true

# 主题配置
VITE_DEFAULT_THEME="light"
VITE_THEME_STORAGE_KEY="ic-cim-theme"

# 调试开关
VITE_DEBUG=true
VITE_MOCK_DATA=true