/**
 * IC封测CIM系统 - 用户认证状态管理
 * User Authentication Store for IC Packaging & Testing CIM System
 */

import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
// Element Plus组件通过unplugin-auto-import自动导入
import type { ApiResponse } from '@/api/config'
import { apiClient } from '@/api'
import type {
  UserInfo,
  LoginRequest,
  LoginResponse,
  PermissionConfig,
  RoleConfig
} from '@/types/user'

/**
 * 用户认证Store
 */
export const useAuthStore = defineStore('auth', () => {
  // 状态管理
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const isAuthenticated = ref(false)
  const isLoggingIn = ref(false)
  const isLoggingOut = ref(false)
  const permissions = ref<PermissionConfig[]>([])
  const roles = ref<RoleConfig[]>([])
  const loginExpiry = ref<number>(0)
  
  // 错误状态管理
  const loginError = ref<string>('')
  const lastLoginAttempt = ref<number>(0)
  const loginAttempts = ref<number>(0)
  const isAccountLocked = ref(false)
  const lockoutExpiry = ref<number>(0)
  const validationErrors = ref<Record<string, string>>({})
  
  // 错误类型定义
  const ERROR_CODES = {
    INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
    ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
    USER_DISABLED: 'USER_DISABLED',
    SESSION_EXPIRED: 'SESSION_EXPIRED',
    CAPTCHA_REQUIRED: 'CAPTCHA_REQUIRED',
    CAPTCHA_INVALID: 'CAPTCHA_INVALID',
    NETWORK_ERROR: 'NETWORK_ERROR',
    SERVER_ERROR: 'SERVER_ERROR',
    TOO_MANY_ATTEMPTS: 'TOO_MANY_ATTEMPTS'
  } as const
  
  type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

  // 计算属性
  const isLoggedIn = computed(() => isAuthenticated.value && !!userInfo.value)
  const currentUser = computed(() => userInfo.value)
  const userPermissions = computed(() => userInfo.value?.permissions || [])
  const userRoles = computed(() => userInfo.value?.roles || [])
  const isTokenExpired = computed(() => {
    if (!loginExpiry.value) return false
    return Date.now() > loginExpiry.value
  })
  
  // 错误状态计算属性
  const hasLoginError = computed(() => !!loginError.value)
  const isLockoutActive = computed(() => {
    return isAccountLocked.value && Date.now() < lockoutExpiry.value
  })
  const lockoutTimeRemaining = computed(() => {
    if (!isLockoutActive.value) return 0
    return Math.ceil((lockoutExpiry.value - Date.now()) / 1000)
  })
  const canAttemptLogin = computed(() => {
    return !isLoggingIn.value && !isLockoutActive.value
  })
  const hasValidationErrors = computed(() => {
    return Object.keys(validationErrors.value).length > 0
  })

  /**
   * 清除错误状态
   */
  const clearErrors = (): void => {
    loginError.value = ''
    validationErrors.value = {}
  }

  /**
   * 设置验证错误
   */
  const setValidationError = (field: string, message: string): void => {
    validationErrors.value[field] = message
  }

  /**
   * 处理登录错误
   */
  const handleLoginError = (error: any): string => {
    loginAttempts.value++
    lastLoginAttempt.value = Date.now()

    // 根据错误类型返回不同的错误码和消息
    let errorCode: ErrorCode = 'SERVER_ERROR'
    let errorMessage = '登录失败，请重试'

    // 检查错误类型
    if (error.code) {
      errorCode = error.code
    } else if (error.message?.includes('网络')) {
      errorCode = 'NETWORK_ERROR'
      errorMessage = '网络连接失败，请检查网络设置'
    } else if (error.message?.includes('用户名') || error.message?.includes('密码')) {
      errorCode = 'INVALID_CREDENTIALS'
      errorMessage = '用户名或密码错误'
    } else if (error.message?.includes('验证码')) {
      errorCode = 'CAPTCHA_INVALID'
      errorMessage = '验证码错误，请重新输入'
    }

    // 检查是否需要锁定账户
    if (loginAttempts.value >= 5) {
      errorCode = 'TOO_MANY_ATTEMPTS'
      errorMessage = '登录失败次数过多，账户已被暂时锁定'
      isAccountLocked.value = true
      lockoutExpiry.value = Date.now() + 15 * 60 * 1000 // 15分钟锁定
    } else if (loginAttempts.value >= 3) {
      errorCode = 'CAPTCHA_REQUIRED'
      errorMessage = '登录失败次数过多，需要输入验证码'
    }

    loginError.value = errorMessage
    return errorCode
  }

  /**
   * 用户登录 - 增强版
   */
  const login = async (loginData: LoginRequest): Promise<boolean> => {
    // 预检查
    if (isLoggingIn.value || isLockoutActive.value) {
      if (isLockoutActive.value) {
        ElMessage.error(`账户已锁定，请${lockoutTimeRemaining.value}秒后重试`)
      }
      return false
    }

    try {
      isLoggingIn.value = true
      clearErrors()

      // 前端验证
      if (!validateLoginData(loginData)) {
        return false
      }

      // 调用登录API (模拟)
      const response = await mockLogin(loginData)

      if (response.success) {
        const loginResult = response.data

        // 重置错误状态
        loginAttempts.value = 0
        isAccountLocked.value = false
        lockoutExpiry.value = 0

        // 保存用户信息
        userInfo.value = loginResult.user
        token.value = loginResult.token
        refreshToken.value = loginResult.refreshToken
        isAuthenticated.value = true
        loginExpiry.value = Date.now() + loginResult.expiresIn * 1000

        // 持久化到localStorage
        saveToStorage()

        // 获取用户权限
        await loadUserPermissions()

        ElMessage.success({
          message: `欢迎回来，${loginResult.user.realName || loginResult.user.username}！`,
          duration: 3000
        })
        return true
      } else {
        const errorCode = handleLoginError(response)
        return false
      }
    } catch (error: any) {
      console.error('Login failed:', error)
      const errorCode = handleLoginError(error)
      
      // 根据错误码显示不同的错误提示
      if (errorCode === 'NETWORK_ERROR') {
        ElMessage.error({
          message: '网络连接失败，请检查网络设置后重试',
          duration: 5000
        })
      } else if (errorCode === 'TOO_MANY_ATTEMPTS') {
        ElMessage.error({
          message: `登录失败次数过多，账户已锁定${Math.ceil(15)}分钟`,
          duration: 8000
        })
      } else {
        ElMessage.error({
          message: loginError.value,
          duration: 4000
        })
      }
      
      return false
    } finally {
      isLoggingIn.value = false
    }
  }

  /**
   * 前端表单验证
   */
  const validateLoginData = (loginData: LoginRequest): boolean => {
    let isValid = true

    // 用户名验证
    if (!loginData.username?.trim()) {
      setValidationError('username', '请输入用户名')
      isValid = false
    } else if (loginData.username.length < 3) {
      setValidationError('username', '用户名长度不能少于3个字符')
      isValid = false
    } else if (!/^[a-zA-Z0-9_-]{3,20}$/.test(loginData.username)) {
      setValidationError('username', '用户名只能包含字母、数字、下划线和连字符')
      isValid = false
    }

    // 密码验证
    if (!loginData.password?.trim()) {
      setValidationError('password', '请输入密码')
      isValid = false
    } else if (loginData.password.length < 6) {
      setValidationError('password', '密码长度不能少于6个字符')
      isValid = false
    }

    // 验证码验证（如果需要）
    if (loginAttempts.value >= 3 && !loginData.captcha?.trim()) {
      setValidationError('captcha', '请输入验证码')
      isValid = false
    }

    if (!isValid) {
      ElMessage.warning('请检查输入信息')
    }

    return isValid
  }

  /**
   * 用户登出
   */
  const logout = async (showConfirm = true): Promise<void> => {
    if (isLoggingOut.value) return

    try {
      if (showConfirm) {
        await ElMessageBox.confirm('确定要退出登录吗？', '退出确认', {
          confirmButtonText: '退出',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }

      isLoggingOut.value = true

      // 调用登出API (模拟)
      await mockLogout()

      // 清理状态
      clearAuthState()

      ElMessage.success('已退出登录')

      // 跳转到登录页
      await nextTick(() => {
        window.location.href = '/login'
      })
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('Logout failed:', error)
        ElMessage.error('退出失败，请重试')
      }
    } finally {
      isLoggingOut.value = false
    }
  }

  /**
   * 强制登出（不显示确认框）
   */
  const forceLogout = async (): Promise<void> => {
    await logout(false)
  }

  /**
   * 刷新Token
   */
  const refreshTokenIfNeeded = async (): Promise<boolean> => {
    if (!isTokenExpired.value) return true
    if (!refreshToken.value) return false

    try {
      // 调用刷新Token API (模拟)
      const response = await mockRefreshToken(refreshToken.value)

      if (response.success) {
        const tokenData = response.data
        token.value = tokenData.token
        refreshToken.value = tokenData.refreshToken
        loginExpiry.value = Date.now() + tokenData.expiresIn * 1000

        // 更新存储
        saveToStorage()
        return true
      } else {
        throw new Error('Token refresh failed')
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
      await forceLogout()
      return false
    }
  }

  /**
   * 加载用户权限
   */
  const loadUserPermissions = async (): Promise<void> => {
    if (!userInfo.value) return

    try {
      // 模拟加载权限数据
      const [permissionsRes, rolesRes] = await Promise.all([mockGetPermissions(), mockGetRoles()])

      if (permissionsRes.success) {
        permissions.value = permissionsRes.data
      }

      if (rolesRes.success) {
        roles.value = rolesRes.data
      }
    } catch (error) {
      console.error('Failed to load permissions:', error)
    }
  }

  /**
   * 检查用户权限
   */
  const hasPermission = (permission: string): boolean => {
    if (!userInfo.value) return false
    return userPermissions.value.includes(permission)
  }

  /**
   * 检查用户角色
   */
  const hasRole = (role: string): boolean => {
    if (!userInfo.value) return false
    return userRoles.value.includes(role)
  }

  /**
   * 检查多个权限（AND逻辑）
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }

  /**
   * 检查多个权限（OR逻辑）
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

  /**
   * 检查菜单权限
   */
  const canAccessMenu = (menuCode: string): boolean => {
    const menuPermission = permissions.value.find(p => p.type === 'menu' && p.code === menuCode)
    return !!menuPermission && hasPermission(menuCode)
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = async (updates: Partial<UserInfo>): Promise<boolean> => {
    if (!userInfo.value) return false

    try {
      // 调用更新用户信息API (模拟)
      const response = await mockUpdateUserInfo({
        ...userInfo.value,
        ...updates
      })

      if (response.success) {
        userInfo.value = response.data
        saveToStorage()
        ElMessage.success('用户信息更新成功')
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('Update user info failed:', error)
      ElMessage.error(error.message || '更新用户信息失败')
      return false
    }
  }

  /**
   * 修改密码
   */
  const changePassword = async (oldPassword: string, newPassword: string): Promise<boolean> => {
    if (!userInfo.value) return false

    try {
      // 调用修改密码API (模拟)
      const response = await mockChangePassword(oldPassword, newPassword)

      if (response.success) {
        ElMessage.success('密码修改成功，请重新登录')
        await forceLogout()
        return true
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      console.error('Change password failed:', error)
      ElMessage.error(error.message || '修改密码失败')
      return false
    }
  }

  /**
   * 从存储中恢复状态
   */
  const restoreFromStorage = (): void => {
    try {
      const savedUserInfo = localStorage.getItem('cim_user_info')
      const savedToken = localStorage.getItem('cim_auth_token')
      const savedRefreshToken = localStorage.getItem('cim_refresh_token')
      const savedExpiry = localStorage.getItem('cim_token_expiry')

      if (savedUserInfo && savedToken) {
        userInfo.value = JSON.parse(savedUserInfo)
        token.value = savedToken
        refreshToken.value = savedRefreshToken || ''
        loginExpiry.value = savedExpiry ? parseInt(savedExpiry) : 0
        isAuthenticated.value = true

        // 检查token是否过期
        if (isTokenExpired.value) {
          refreshTokenIfNeeded()
        } else {
          // 重新加载权限
          loadUserPermissions()
        }
      }
    } catch (error) {
      console.error('Failed to restore auth state:', error)
      clearAuthState()
    }
  }

  /**
   * 保存到存储
   */
  const saveToStorage = (): void => {
    try {
      if (userInfo.value) {
        localStorage.setItem('cim_user_info', JSON.stringify(userInfo.value))
      }
      if (token.value) {
        localStorage.setItem('cim_auth_token', token.value)
      }
      if (refreshToken.value) {
        localStorage.setItem('cim_refresh_token', refreshToken.value)
      }
      if (loginExpiry.value) {
        localStorage.setItem('cim_token_expiry', loginExpiry.value.toString())
      }
    } catch (error) {
      console.error('Failed to save auth state:', error)
    }
  }

  /**
   * 清理认证状态
   */
  const clearAuthState = (): void => {
    userInfo.value = null
    token.value = ''
    refreshToken.value = ''
    isAuthenticated.value = false
    permissions.value = []
    roles.value = []
    loginExpiry.value = 0

    // 清理存储
    localStorage.removeItem('cim_user_info')
    localStorage.removeItem('cim_auth_token')
    localStorage.removeItem('cim_refresh_token')
    localStorage.removeItem('cim_token_expiry')
  }

  // 模拟API方法
  const mockLogin = async (loginData: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络延迟

    // 简单验证
    if (loginData.username === 'admin' && loginData.password === '123456') {
      return {
        success: true,
        data: {
          user: {
            id: 'user_001',
            username: 'admin',
            email: '<EMAIL>',
            phone: '13800138000',
            realName: '系统管理员',
            avatar: '',
            department: 'IT部门',
            position: '系统管理员',
            roles: ['admin', 'operator'],
            permissions: [
              'system:read',
              'system:write',
              'system:delete',
              'order:read',
              'order:write',
              'order:delete',
              'production:read',
              'production:write',
              'quality:read',
              'quality:write',
              'equipment:read',
              'equipment:write',
              'report:read',
              'report:export'
            ],
            lastLoginTime: new Date().toISOString(),
            lastLoginIp: '*************',
            status: 'active',
            createTime: '2024-01-01T00:00:00.000Z',
            updateTime: new Date().toISOString()
          },
          token: `token_${Date.now()}`,
          refreshToken: `refresh_${Date.now()}`,
          expiresIn: 3600 // 1小时
        },
        message: '登录成功',
        code: 200,
        timestamp: new Date().toISOString()
      }
    } else {
      return {
        success: false,
        data: null,
        message: '用户名或密码错误',
        code: 401,
        timestamp: new Date().toISOString()
      }
    }
  }

  const mockLogout = async (): Promise<ApiResponse> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      success: true,
      data: null,
      message: '退出成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  const mockRefreshToken = async (
    refreshToken: string
  ): Promise<
    ApiResponse<{
      token: string
      refreshToken: string
      expiresIn: number
    }>
  > => {
    await new Promise(resolve => setTimeout(resolve, 500))

    if (Math.random() > 0.1) {
      // 90%成功率
      return {
        success: true,
        data: {
          token: `new_token_${Date.now()}`,
          refreshToken: `new_refresh_${Date.now()}`,
          expiresIn: 3600
        },
        message: 'Token刷新成功',
        code: 200,
        timestamp: new Date().toISOString()
      }
    } else {
      return {
        success: false,
        data: null,
        message: 'Token刷新失败',
        code: 401,
        timestamp: new Date().toISOString()
      }
    }
  }

  const mockGetPermissions = async (): Promise<ApiResponse<PermissionConfig[]>> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return {
      success: true,
      data: [
        { code: 'system:read', name: '系统查看', type: 'menu', description: '系统管理查看权限' },
        { code: 'system:write', name: '系统编辑', type: 'button', description: '系统管理编辑权限' },
        {
          code: 'system:delete',
          name: '系统删除',
          type: 'button',
          description: '系统管理删除权限'
        },
        { code: 'order:read', name: '订单查看', type: 'menu', description: '订单管理查看权限' },
        { code: 'order:write', name: '订单编辑', type: 'button', description: '订单管理编辑权限' },
        { code: 'order:delete', name: '订单删除', type: 'button', description: '订单管理删除权限' },
        {
          code: 'production:read',
          name: '生产查看',
          type: 'menu',
          description: '生产管理查看权限'
        },
        {
          code: 'production:write',
          name: '生产编辑',
          type: 'button',
          description: '生产管理编辑权限'
        },
        { code: 'quality:read', name: '质量查看', type: 'menu', description: '质量管理查看权限' },
        {
          code: 'quality:write',
          name: '质量编辑',
          type: 'button',
          description: '质量管理编辑权限'
        },
        { code: 'equipment:read', name: '设备查看', type: 'menu', description: '设备管理查看权限' },
        {
          code: 'equipment:write',
          name: '设备编辑',
          type: 'button',
          description: '设备管理编辑权限'
        },
        { code: 'report:read', name: '报表查看', type: 'menu', description: '报表查看权限' },
        { code: 'report:export', name: '报表导出', type: 'button', description: '报表导出权限' }
      ],
      message: '获取权限成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  const mockGetRoles = async (): Promise<ApiResponse<RoleConfig[]>> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return {
      success: true,
      data: [
        {
          code: 'admin',
          name: '系统管理员',
          permissions: [
            'system:read',
            'system:write',
            'system:delete',
            'order:read',
            'order:write',
            'order:delete',
            'production:read',
            'production:write',
            'quality:read',
            'quality:write',
            'equipment:read',
            'equipment:write',
            'report:read',
            'report:export'
          ],
          description: '系统管理员，拥有所有权限'
        },
        {
          code: 'operator',
          name: '操作员',
          permissions: [
            'order:read',
            'order:write',
            'production:read',
            'production:write',
            'quality:read',
            'quality:write',
            'equipment:read',
            'report:read'
          ],
          description: '操作员，拥有基本操作权限'
        }
      ],
      message: '获取角色成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  const mockUpdateUserInfo = async (userInfo: UserInfo): Promise<ApiResponse<UserInfo>> => {
    await new Promise(resolve => setTimeout(resolve, 800))
    return {
      success: true,
      data: {
        ...userInfo,
        updateTime: new Date().toISOString()
      },
      message: '更新成功',
      code: 200,
      timestamp: new Date().toISOString()
    }
  }

  const mockChangePassword = async (
    oldPassword: string,
    newPassword: string
  ): Promise<ApiResponse> => {
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 简单验证
    if (oldPassword === '123456') {
      return {
        success: true,
        data: null,
        message: '密码修改成功',
        code: 200,
        timestamp: new Date().toISOString()
      }
    } else {
      return {
        success: false,
        data: null,
        message: '原密码错误',
        code: 400,
        timestamp: new Date().toISOString()
      }
    }
  }

  // 初始化时恢复状态
  if (typeof window !== 'undefined') {
    restoreFromStorage()
  }

  return {
    // 状态
    userInfo,
    token,
    refreshToken,
    isAuthenticated,
    isLoggingIn,
    isLoggingOut,
    permissions,
    roles,
    loginExpiry,

    // 计算属性
    isLoggedIn,
    currentUser,
    userPermissions,
    userRoles,
    isTokenExpired,

    // 方法
    login,
    logout,
    forceLogout,
    refreshTokenIfNeeded,
    loadUserPermissions,
    hasPermission,
    hasRole,
    hasAllPermissions,
    hasAnyPermission,
    canAccessMenu,
    updateUserInfo,
    changePassword,
    restoreFromStorage,
    clearAuthState,

    // 错误状态
    loginError,
    lastLoginAttempt,
    loginAttempts,
    isAccountLocked,
    lockoutExpiry,
    validationErrors,
    
    // 错误状态计算属性
    hasLoginError,
    isLockoutActive,
    lockoutTimeRemaining,
    canAttemptLogin,
    hasValidationErrors,
    
    // 错误处理方法
    clearErrors,
    setValidationError,
    validateLoginData
  }
})
