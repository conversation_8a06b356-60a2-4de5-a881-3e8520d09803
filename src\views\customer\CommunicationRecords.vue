<template>
  <div class="communication-records">
    <!-- 页面头部 -->
    <div class="communication-records__header">
      <div class="communication-records__header-left">
        <h2 class="communication-records__title">客户沟通记录</h2>
        <p class="communication-records__subtitle">管理与IC设计客户的沟通历史，跟踪业务进展</p>
      </div>
      <div class="communication-records__header-right">
        <el-button type="primary"
:icon="Plus" @click="handleAddRecord"
>
新增沟通记录
</el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="communication-records__stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <div class="stat-content">
              <div class="stat-value">
                {{ statistics.totalRecords }}
              </div>
              <div class="stat-label">总沟通次数</div>
            </div>
            <el-icon class="stat-icon">
              <ChatDotRound />
            </el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <div class="stat-content">
              <div class="stat-value">
                {{ statistics.thisMonthRecords }}
              </div>
              <div class="stat-label">本月沟通</div>
            </div>
            <el-icon class="stat-icon">
              <Calendar />
            </el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <div class="stat-content">
              <div class="stat-value">
                {{ statistics.upcomingFollowUps }}
              </div>
              <div class="stat-label">待跟进</div>
            </div>
            <el-icon class="stat-icon">
              <Clock />
            </el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <div class="stat-content">
              <div class="stat-value">
                {{ statistics.overdueFollowUps }}
              </div>
              <div class="stat-label">逾期跟进</div>
            </div>
            <el-icon class="stat-icon">
              <Warning />
            </el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="communication-records__search-card" shadow="never">
      <el-form :model="searchForm" class="communication-records__search-form" inline>
        <el-form-item label="关键词搜索">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索主题、内容、参与人"
            :prefix-icon="Search"
            clearable
            style="width: 240px"
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-form-item>

        <el-form-item label="客户">
          <el-select
            v-model="searchForm.customerId"
            placeholder="选择客户"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="customer in customerOptions"
              :key="customer.id"
              :label="customer.name"
              :value="customer.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="联系人">
          <el-select
            v-model="searchForm.contactId"
            placeholder="选择联系人"
            clearable
            filterable
            style="width: 160px"
          >
            <el-option
              v-for="contact in contactOptions"
              :key="contact.id"
              :label="contact.name"
              :value="contact.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="沟通方式">
          <el-select
            v-model="searchForm.type"
            placeholder="选择方式"
            multiple
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="type in communicationTypeOptions"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="沟通场景">
          <el-select
            v-model="searchForm.scene"
            placeholder="选择场景"
            multiple
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="scene in communicationSceneOptions"
              :key="scene.value"
              :label="scene.label"
              :value="scene.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="沟通结果">
          <el-select
            v-model="searchForm.result"
            placeholder="选择结果"
            multiple
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="result in communicationResultOptions"
              :key="result.value"
              :label="result.label"
              :value="result.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary"
:icon="Search" @click="handleSearch"
>
搜索
</el-button>
          <el-button :icon="Refresh"
@click="handleReset"
>
重置
</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 主要内容区域 -->
    <el-card class="communication-records__content-card" shadow="never">
      <!-- 工具栏 -->
      <div class="communication-records__toolbar">
        <div class="communication-records__toolbar-left">
          <el-radio-group v-model="viewMode" @change="handleViewModeChange">
            <el-radio-button label="timeline">时间线视图</el-radio-button>
            <el-radio-button label="table">表格视图</el-radio-button>
          </el-radio-group>
        </div>
        <div class="communication-records__toolbar-right">
          <el-tooltip content="导出记录">
            <el-button :icon="Download" circle @click="handleExport" />
          </el-tooltip>
          <el-tooltip content="刷新列表">
            <el-button :icon="Refresh" circle @click="fetchRecords" />
          </el-tooltip>
        </div>
      </div>

      <!-- 时间线视图 -->
      <div v-if="viewMode === 'timeline'" class="communication-records__timeline">
        <el-timeline>
          <el-timeline-item
            v-for="record in records"
            :key="record.id"
            :timestamp="formatDate(record.date)"
            :type="getTimelineType(record.result)"
            :size="record.importance >= 4 ? 'large' : 'normal'"
            placement="top"
          >
            <div class="timeline-card" @click="handleViewRecord(record)">
              <div class="timeline-card__header">
                <div class="timeline-card__title">
                  <span class="timeline-card__subject">{{ record.subject }}</span>
                  <div class="timeline-card__badges">
                    <el-tag :type="getCommunicationTypeTagType(record.type)" size="small">
                      {{ getCommunicationTypeLabel(record.type) }}
                    </el-tag>
                    <el-tag :type="getCommunicationSceneTagType(record.scene)" size="small">
                      {{ getCommunicationSceneLabel(record.scene) }}
                    </el-tag>
                    <el-rate
                      :model-value="record.importance"
                      disabled
                      size="small"
                      :max="5"
                      :colors="['#ff9900', '#ff9900', '#ff9900']"
                    />
                  </div>
                </div>
                <div class="timeline-card__meta">
                  <span>{{ getCustomerName(record.customerId) }}</span>
                  <span>{{ getContactName(record.contactId) }}</span>
                  <span v-if="record.duration">{{ record.duration }}分钟</span>
                </div>
              </div>

              <div class="timeline-card__content">
                <p>{{ truncateContent(record.content) }}</p>
                <div v-if="record.tags?.length" class="timeline-card__tags">
                  <el-tag
                    v-for="tag in record.tags.slice(0, 3)"
                    :key="tag"
                    size="small"
                    effect="plain"
                    type="info"
                  >
                    {{ tag }}
                  </el-tag>
                  <span v-if="record.tags.length > 3" class="more-tags">
                    +{{ record.tags.length - 3 }}
                  </span>
                </div>
              </div>

              <div
                v-if="record.followUpTasks?.length || record.nextContactDate"
                class="timeline-card__footer"
              >
                <div v-if="record.followUpTasks?.length" class="follow-up-tasks">
                  <el-icon><List /></el-icon>
                  <span>{{ record.followUpTasks.length }}个跟进任务</span>
                </div>
                <div v-if="record.nextContactDate" class="next-contact">
                  <el-icon><Clock /></el-icon>
                  <span>下次联系：{{ formatDate(record.nextContactDate) }}</span>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>

        <!-- 时间线空状态 -->
        <el-empty v-if="!records.length && !loading" description="暂无沟通记录" />
      </div>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="communication-records__table">
        <el-table
          v-loading="loading"
          :data="records"
          stripe
          row-key="id"
          @row-click="handleViewRecord"
        >
          <el-table-column label="沟通时间" width="120" sortable prop="date">
            <template #default="{ row }">
              <div class="table-date">
                <div>{{ formatDate(row.date) }}</div>
                <div
v-if="row.duration" class="duration">{{ row.duration }}分钟</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="沟通主题" min-width="250">
            <template #default="{ row }">
              <div class="table-subject">
                <div class="subject-title">
                  {{ row.subject }}
                </div>
                <div class="subject-meta">
                  <el-tag :type="getCommunicationTypeTagType(row.type)" size="small">
                    {{ getCommunicationTypeLabel(row.type) }}
                  </el-tag>
                  <el-tag :type="getCommunicationSceneTagType(row.scene)" size="small">
                    {{ getCommunicationSceneLabel(row.scene) }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="客户/联系人" width="200">
            <template #default="{ row }">
              <div class="table-contact">
                <div class="customer">
                  {{ getCustomerName(row.customerId) }}
                </div>
                <div class="contact">
                  {{ getContactName(row.contactId) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="重要程度" width="120" align="center">
            <template #default="{ row }">
              <el-rate
                :model-value="row.importance"
                disabled
                size="small"
                :max="5"
                :colors="['#ff9900', '#ff9900', '#ff9900']"
              />
            </template>
          </el-table-column>

          <el-table-column label="沟通结果" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getCommunicationResultTagType(row.result)" size="small">
                {{ getCommunicationResultLabel(row.result) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="跟进状态" width="120" align="center">
            <template #default="{ row }">
              <div v-if="row.followUpTasks?.length" class="follow-up-status">
                <el-badge :value="row.followUpTasks.length" type="primary">
                  <el-icon><List /></el-icon>
                </el-badge>
              </div>
              <div v-if="row.nextContactDate" class="next-contact-status">
                <el-tag
:type="isOverdue(row.nextContactDate) ? 'danger' : 'warning'" size="small"
>
                  {{ isOverdue(row.nextContactDate) ? '已逾期' : '待跟进' }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="创建人" width="100" prop="createdBy" />

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" text size="small" @click.stop="handleEditRecord(row)">
                编辑
              </el-button>
              <el-button type="danger" text size="small" @click.stop="handleDeleteRecord(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 表格空状态 -->
        <el-empty v-if="!records.length && !loading" description="暂无沟通记录" />
      </div>

      <!-- 分页 -->
      <div v-if="records.length" class="communication-records__pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange"
        />
      </div>
    </el-card>

    <!-- 沟通记录详情抽屉 -->
    <el-drawer
v-model="recordDrawerVisible" title="沟通记录详情"
size="800px" direction="rtl"
>
      <template v-if="selectedRecord">
        <div class="record-detail">
          <!-- 基本信息 -->
          <div class="record-detail__section">
            <h3>基本信息</h3>
            <el-descriptions :column="2" size="default" border>
              <el-descriptions-item label="沟通主题" :span="2">
                {{ selectedRecord.subject }}
              </el-descriptions-item>
              <el-descriptions-item label="沟通时间">
                {{ formatDateTime(selectedRecord.date, selectedRecord.startTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="沟通时长">
                {{ selectedRecord.duration ? selectedRecord.duration + '分钟' : '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="沟通方式">
                <el-tag :type="getCommunicationTypeTagType(selectedRecord.type)">
                  {{ getCommunicationTypeLabel(selectedRecord.type) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="沟通场景">
                <el-tag :type="getCommunicationSceneTagType(selectedRecord.scene)">
                  {{ getCommunicationSceneLabel(selectedRecord.scene) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item
v-if="selectedRecord.location" label="沟通地点"
>
                {{ selectedRecord.location }}
              </el-descriptions-item>
              <el-descriptions-item label="重要程度">
                <el-rate
                  :model-value="selectedRecord.importance"
                  disabled
                  :colors="['#ff9900', '#ff9900', '#ff9900']"
                />
              </el-descriptions-item>
              <el-descriptions-item label="客户公司">
                {{ getCustomerName(selectedRecord.customerId) }}
              </el-descriptions-item>
              <el-descriptions-item label="联系人">
                {{ getContactName(selectedRecord.contactId) }}
              </el-descriptions-item>
              <el-descriptions-item label="沟通结果">
                <el-tag :type="getCommunicationResultTagType(selectedRecord.result)">
                  {{ getCommunicationResultLabel(selectedRecord.result) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getCommunicationStatusTagType(selectedRecord.status)">
                  {{ getCommunicationStatusLabel(selectedRecord.status) }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 参与人员 -->
          <div v-if="selectedRecord.participants?.length" class="record-detail__section">
            <h3>参与人员</h3>
            <el-tag
              v-for="participant in selectedRecord.participants"
              :key="participant"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              {{ participant }}
            </el-tag>
          </div>

          <!-- 沟通内容 -->
          <div class="record-detail__section">
            <h3>沟通内容</h3>
            <div class="record-content">
              {{ selectedRecord.content }}
            </div>
          </div>

          <!-- 客户反馈 -->
          <div v-if="selectedRecord.customerFeedback" class="record-detail__section">
            <h3>客户反馈</h3>
            <div class="customer-feedback">
              {{ selectedRecord.customerFeedback }}
            </div>
          </div>

          <!-- 跟进安排 -->
          <div
            v-if="selectedRecord.nextContactDate || selectedRecord.nextContactPurpose"
            class="record-detail__section"
          >
            <h3>跟进安排</h3>
            <el-descriptions :column="1" size="default" border>
              <el-descriptions-item v-if="selectedRecord.nextContactDate" label="下次联系时间">
                <span :class="{ overdue: isOverdue(selectedRecord.nextContactDate) }">
                  {{ formatDate(selectedRecord.nextContactDate) }}
                  <el-tag
                    v-if="isOverdue(selectedRecord.nextContactDate)"
                    type="danger"
                    size="small"
                    style="margin-left: 8px"
                  >
                    已逾期
                  </el-tag>
                </span>
              </el-descriptions-item>
              <el-descriptions-item v-if="selectedRecord.nextContactPurpose" label="联系目的">
                {{ selectedRecord.nextContactPurpose }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 跟进任务 -->
          <div v-if="selectedRecord.followUpTasks?.length" class="record-detail__section">
            <h3>跟进任务</h3>
            <div class="follow-up-tasks">
              <div
v-for="task in selectedRecord.followUpTasks" :key="task.id"
class="task-item"
>
                <div class="task-header">
                  <div class="task-title">
                    {{ task.description }}
                  </div>
                  <div class="task-meta">
                    <el-tag :type="getTaskStatusTagType(task.status)" size="small">
                      {{ getTaskStatusLabel(task.status) }}
                    </el-tag>
                    <el-tag :type="getTaskPriorityTagType(task.priority)" size="small">
                      {{ getTaskPriorityLabel(task.priority) }}
                    </el-tag>
                  </div>
                </div>
                <div class="task-info">
                  <span>负责人：{{ task.assignee }}</span>
                  <span>截止时间：{{ formatDate(task.dueDate) }}</span>
                  <span v-if="isTaskOverdue(task)" class="overdue">已逾期</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 附件 -->
          <div v-if="selectedRecord.attachments?.length" class="record-detail__section">
            <h3>相关附件</h3>
            <div class="attachments">
              <div
                v-for="attachment in selectedRecord.attachments"
                :key="attachment.id"
                class="attachment-item"
                @click="handleDownloadAttachment(attachment)"
              >
                <el-icon class="attachment-icon">
                  <Document />
                </el-icon>
                <div class="attachment-info">
                  <div class="attachment-name">
                    {{ attachment.fileName }}
                  </div>
                  <div class="attachment-meta">
                    {{ formatFileSize(attachment.fileSize) }} ·
                    {{ formatDate(attachment.uploadTime) }}
                  </div>
                  <div v-if="attachment.description" class="attachment-desc">
                    {{ attachment.description }}
                  </div>
                </div>
                <el-icon class="download-icon">
                  <Download />
                </el-icon>
              </div>
            </div>
          </div>

          <!-- 标签 -->
          <div v-if="selectedRecord.tags?.length" class="record-detail__section">
            <h3>标签</h3>
            <el-tag
              v-for="tag in selectedRecord.tags"
              :key="tag"
              type="info"
              effect="plain"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              {{ tag }}
            </el-tag>
          </div>

          <!-- 创建信息 -->
          <div class="record-detail__section">
            <h3>创建信息</h3>
            <el-descriptions :column="2" size="default" border>
              <el-descriptions-item label="创建人">
                {{ selectedRecord.createdBy }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDateTime(selectedRecord.createdAt) }}
              </el-descriptions-item>
              <el-descriptions-item v-if="selectedRecord.updatedBy" label="更新人">
                {{ selectedRecord.updatedBy }}
              </el-descriptions-item>
              <el-descriptions-item v-if="selectedRecord.updatedAt" label="更新时间">
                {{ formatDateTime(selectedRecord.updatedAt) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </template>

      <template #footer>
        <div style="flex: auto">
          <el-button @click="recordDrawerVisible = false">关闭</el-button>
          <el-button type="primary"
@click="handleEditRecord(selectedRecord!)"
>
编辑记录
</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 沟通记录编辑对话框 -->
    <el-dialog
      v-model="recordDialogVisible"
      :title="isEditMode ? '编辑沟通记录' : '新增沟通记录'"
      width="1000px"
      @close="handleDialogClose"
    >
      <el-form
ref="recordFormRef" :model="recordForm"
:rules="recordFormRules" label-width="100px"
>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="客户公司" prop="customerId" required>
              <el-select
                v-model="recordForm.customerId"
                placeholder="选择客户公司"
                filterable
                style="width: 100%"
                @change="handleCustomerChange"
              >
                <el-option
                  v-for="customer in customerOptions"
                  :key="customer.id"
                  :label="customer.name"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactId" required>
              <el-select
                v-model="recordForm.contactId"
                placeholder="选择联系人"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="contact in filteredContactOptions"
                  :key="contact.id"
                  :label="`${contact.name} (${contact.position})`"
                  :value="contact.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="沟通方式" prop="type" required>
              <el-select v-model="recordForm.type" placeholder="选择沟通方式" style="width: 100%">
                <el-option
                  v-for="type in communicationTypeOptions"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="沟通场景" prop="scene" required>
              <el-select v-model="recordForm.scene" placeholder="选择沟通场景" style="width: 100%">
                <el-option
                  v-for="scene in communicationSceneOptions"
                  :key="scene.value"
                  :label="scene.label"
                  :value="scene.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="沟通主题" prop="subject" required>
          <el-input v-model="recordForm.subject" placeholder="请输入沟通主题" />
        </el-form-item>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="沟通日期" prop="date" required>
              <el-date-picker
                v-model="recordForm.date"
                type="date"
                placeholder="选择日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                v-model="recordForm.startTime"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                v-model="recordForm.endTime"
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="沟通地点" prop="location">
              <el-input v-model="recordForm.location" placeholder="请输入沟通地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重要程度" prop="importance" required>
              <el-rate
                v-model="recordForm.importance"
                :colors="['#ff9900', '#ff9900', '#ff9900']"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="参与人员" prop="participants">
          <el-select
            v-model="recordForm.participants"
            placeholder="选择或输入参与人员"
            multiple
            allow-create
            filterable
            style="width: 100%"
          >
            <el-option label="张工(我司技术)" value="张工(我司技术)" />
            <el-option label="陈工(我司质量)" value="陈工(我司质量)" />
            <el-option label="刘经理(我司销售)" value="刘经理(我司销售)" />
          </el-select>
        </el-form-item>

        <el-form-item label="沟通内容" prop="content" required>
          <el-input
            v-model="recordForm.content"
            type="textarea"
            :rows="6"
            placeholder="请详细描述沟通内容、讨论要点、达成的共识等"
          />
        </el-form-item>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="沟通结果" prop="result" required>
              <el-select v-model="recordForm.result" placeholder="选择沟通结果" style="width: 100%">
                <el-option
                  v-for="result in communicationResultOptions"
                  :key="result.value"
                  :label="result.label"
                  :value="result.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status" required>
              <el-select v-model="recordForm.status" placeholder="选择状态" style="width: 100%">
                <el-option
                  v-for="status in communicationStatusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="客户反馈" prop="customerFeedback">
          <el-input
            v-model="recordForm.customerFeedback"
            type="textarea"
            :rows="3"
            placeholder="请记录客户的反馈意见"
          />
        </el-form-item>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="下次联系时间" prop="nextContactDate">
              <el-date-picker
                v-model="recordForm.nextContactDate"
                type="date"
                placeholder="选择下次联系时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系目的" prop="nextContactPurpose">
              <el-input v-model="recordForm.nextContactPurpose" placeholder="下次联系的目的" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="标签" prop="tags">
          <el-select
            v-model="recordForm.tags"
            placeholder="添加标签"
            multiple
            allow-create
            filterable
            style="width: 100%"
          >
            <el-option value="技术讨论" />
            <el-option value="商务谈判" />
            <el-option value="质量问题" />
            <el-option value="交期协调" />
            <el-option value="新产品导入" />
            <el-option value="价格谈判" />
            <el-option value="合同签署" />
            <el-option value="客户拜访" />
          </el-select>
        </el-form-item>

        <el-form-item label="附件上传">
          <el-upload
            v-model:file-list="recordForm.attachments"
            :action="uploadUrl"
            :headers="uploadHeaders"
            multiple
            :limit="5"
            accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg"
            :before-upload="handleBeforeUpload"
          >
            <el-button :icon="Upload">上传附件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、Word、Excel、图片格式，单个文件不超过20MB，最多上传5个文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="recordDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitting" @click="handleSubmitRecord">
            {{ isEditMode ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import {
    ElMessage,
    ElMessageBox,
    type FormInstance,
    type FormRules,
    type UploadUserFile
  } from 'element-plus'
  import {
    Plus,
    Search,
    Refresh,
    Download,
    Calendar,
    Clock,
    Warning,
    ChatDotRound,
    List,
    Document,
    Upload
  } from '@element-plus/icons-vue'
  import type {
    CommunicationRecord,
    CommunicationRecordType,
    CommunicationScene,
    CommunicationStatus,
    CommunicationResult,
    CommunicationRecordQueryParams,
    CreateCommunicationRecordData,
    CommunicationStatistics,
    CommunicationAttachment,
    FollowUpTask
  } from '@/types/customer'
  import {
    allMockCommunicationRecords,
    communicationStatistics
  } from '@/utils/mockData/communicationRecords'
  import dayjs from 'dayjs'

  // 响应式数据
  const loading = ref(false)
  const submitting = ref(false)
  const recordDrawerVisible = ref(false)
  const recordDialogVisible = ref(false)
  const isEditMode = ref(false)
  const viewMode = ref<'timeline' | 'table'>('timeline')

  // 表单引用
  const recordFormRef = ref<FormInstance>()

  // 搜索表单
  const searchForm = reactive<CommunicationRecordQueryParams>({
    keyword: '',
    customerId: '',
    contactId: '',
    type: [],
    scene: [],
    result: [],
    status: [],
    dateRange: null,
    page: 1,
    pageSize: 20
  })

  // 分页信息
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 数据
  const records = ref<CommunicationRecord[]>([])
  const selectedRecord = ref<CommunicationRecord | null>(null)
  const statistics = ref<CommunicationStatistics>(communicationStatistics)

  // 选项数据
  const customerOptions = ref([
    { id: 'customer_001', name: '华为海思半导体有限公司' },
    { id: 'customer_002', name: '紫光展锐科技有限公司' },
    { id: 'customer_003', name: '比亚迪半导体股份有限公司' },
    { id: 'customer_004', name: '兆易创新科技集团股份有限公司' },
    { id: 'customer_005', name: '汇顶科技股份有限公司' }
  ])

  const contactOptions = ref([
    { id: 'contact_001_01', customerId: 'customer_001', name: '张伟', position: '采购总监' },
    { id: 'contact_001_02', customerId: 'customer_001', name: '李明', position: '技术经理' },
    { id: 'contact_002_01', customerId: 'customer_002', name: '王强', position: '供应链总监' },
    { id: 'contact_003_01', customerId: 'customer_003', name: '陈志华', position: '采购部经理' },
    { id: 'contact_004_01', customerId: 'customer_004', name: '刘建华', position: '供应链总监' },
    { id: 'contact_005_01', customerId: 'customer_005', name: '胡晓峰', position: '采购经理' }
  ])

  const communicationTypeOptions = [
    { value: 'Call', label: '电话沟通' },
    { value: 'Email', label: '邮件沟通' },
    { value: 'Meeting', label: '会议' },
    { value: 'Visit', label: '客户拜访' },
    { value: 'WeChat', label: '微信沟通' },
    { value: 'VideoCall', label: '视频会议' },
    { value: 'Exhibition', label: '展会接触' },
    { value: 'OnlineDemo', label: '在线演示' }
  ]

  const communicationSceneOptions = [
    { value: 'TechDiscussion', label: '技术讨论' },
    { value: 'BusinessNegotiation', label: '商务谈判' },
    { value: 'ProductIntro', label: '产品介绍' },
    { value: 'QualityIssue', label: '质量问题' },
    { value: 'DeliveryCoordination', label: '交期协调' },
    { value: 'ProjectReview', label: '项目评审' },
    { value: 'ContractDiscussion', label: '合同讨论' },
    { value: 'ProblemSolving', label: '问题解决' },
    { value: 'RelationshipBuilding', label: '关系维护' },
    { value: 'MarketInfo', label: '市场信息' }
  ]

  const communicationResultOptions = [
    { value: 'excellent', label: '非常成功' },
    { value: 'good', label: '良好' },
    { value: 'average', label: '一般' },
    { value: 'poor', label: '不理想' },
    { value: 'failed', label: '失败' }
  ]

  const communicationStatusOptions = [
    { value: 'completed', label: '已完成' },
    { value: 'pending', label: '待处理' },
    { value: 'cancelled', label: '已取消' },
    { value: 'postponed', label: '已延期' }
  ]

  // 表单数据
  const recordForm = reactive<CreateCommunicationRecordData>({
    customerId: '',
    contactId: '',
    type: 'Meeting' as CommunicationRecordType,
    scene: 'TechDiscussion' as CommunicationScene,
    subject: '',
    date: '',
    startTime: '',
    endTime: '',
    location: '',
    content: '',
    participants: [],
    importance: 3 as 1 | 2 | 3 | 4 | 5,
    result: 'good' as CommunicationResult,
    status: 'completed' as CommunicationStatus,
    customerFeedback: '',
    nextContactDate: '',
    nextContactPurpose: '',
    tags: [],
    attachments: [] as UploadUserFile[]
  })

  // 表单验证规则
  const recordFormRules: FormRules = {
    customerId: [{ required: true, message: '请选择客户公司', trigger: 'change' }],
    contactId: [{ required: true, message: '请选择联系人', trigger: 'change' }],
    type: [{ required: true, message: '请选择沟通方式', trigger: 'change' }],
    scene: [{ required: true, message: '请选择沟通场景', trigger: 'change' }],
    subject: [{ required: true, message: '请输入沟通主题', trigger: 'blur' }],
    date: [{ required: true, message: '请选择沟通日期', trigger: 'change' }],
    content: [{ required: true, message: '请输入沟通内容', trigger: 'blur' }],
    importance: [{ required: true, message: '请设置重要程度', trigger: 'change' }],
    result: [{ required: true, message: '请选择沟通结果', trigger: 'change' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }

  // 上传配置
  const uploadUrl = '/api/upload'
  const uploadHeaders = {
    Authorization: `Bearer ${localStorage.getItem('token')}`
  }

  // 计算属性
  const filteredContactOptions = computed(() => {
    if (!recordForm.customerId) return contactOptions.value
    return contactOptions.value.filter(contact => contact.customerId === recordForm.customerId)
  })

  // 方法
  const fetchRecords = async () => {
    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      records.value = allMockCommunicationRecords
      pagination.total = records.value.length
    } catch (error) {
      ElMessage.error('获取沟通记录失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const handleSearch = () => {
    pagination.page = 1
    fetchRecords()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      keyword: '',
      customerId: '',
      contactId: '',
      type: [],
      scene: [],
      result: [],
      status: [],
      dateRange: null,
      page: 1,
      pageSize: 20
    })
    handleSearch()
  }

  const handlePageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    fetchRecords()
  }

  const handleCurrentPageChange = (page: number) => {
    pagination.page = page
    fetchRecords()
  }

  const handleViewModeChange = () => {
    // 视图模式切换逻辑
  }

  const handleAddRecord = () => {
    isEditMode.value = false
    resetRecordForm()
    recordDialogVisible.value = true
  }

  const handleEditRecord = (record: CommunicationRecord) => {
    isEditMode.value = true

    // 填充表单数据
    Object.assign(recordForm, {
      customerId: record.customerId,
      contactId: record.contactId,
      type: record.type,
      scene: record.scene,
      subject: record.subject,
      date: record.date,
      startTime: record.startTime,
      endTime: record.endTime,
      location: record.location,
      content: record.content,
      participants: record.participants || [],
      importance: record.importance,
      result: record.result,
      status: record.status,
      customerFeedback: record.customerFeedback,
      nextContactDate: record.nextContactDate,
      nextContactPurpose: record.nextContactPurpose,
      tags: record.tags || [],
      attachments: []
    })

    selectedRecord.value = record
    recordDialogVisible.value = true
    recordDrawerVisible.value = false
  }

  const handleViewRecord = (record: CommunicationRecord) => {
    selectedRecord.value = record
    recordDrawerVisible.value = true
  }

  const handleDeleteRecord = async (record: CommunicationRecord) => {
    try {
      await ElMessageBox.confirm(`确定要删除沟通记录 "${record.subject}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // 模拟删除操作
      ElMessage.success('删除成功')
      fetchRecords()
    } catch {
      // 用户取消删除
    }
  }

  const handleSubmitRecord = async () => {
    if (!recordFormRef.value) return

    try {
      await recordFormRef.value.validate()

      submitting.value = true

      // 模拟提交操作
      await new Promise(resolve => setTimeout(resolve, 1000))

      ElMessage.success(isEditMode.value ? '更新成功' : '创建成功')
      recordDialogVisible.value = false
      fetchRecords()
    } catch (error) {
      console.error('表单验证失败:', error)
    } finally {
      submitting.value = false
    }
  }

  const handleDialogClose = () => {
    resetRecordForm()
    recordFormRef.value?.resetFields()
  }

  const handleCustomerChange = () => {
    // 客户变更时清空联系人选择
    recordForm.contactId = ''
  }

  const handleExport = () => {
    ElMessage.info('导出功能开发中...')
  }

  const handleDownloadAttachment = (attachment: CommunicationAttachment) => {
    // 模拟下载
    ElMessage.success(`正在下载 ${attachment.fileName}`)
  }

  const handleBeforeUpload = (file: File) => {
    const isLt20M = file.size / 1024 / 1024 < 20
    if (!isLt20M) {
      ElMessage.error('文件大小不能超过20MB!')
      return false
    }
    return true
  }

  const resetRecordForm = () => {
    Object.assign(recordForm, {
      customerId: '',
      contactId: '',
      type: 'Meeting' as CommunicationRecordType,
      scene: 'TechDiscussion' as CommunicationScene,
      subject: '',
      date: '',
      startTime: '',
      endTime: '',
      location: '',
      content: '',
      participants: [],
      importance: 3 as 1 | 2 | 3 | 4 | 5,
      result: 'good' as CommunicationResult,
      status: 'completed' as CommunicationStatus,
      customerFeedback: '',
      nextContactDate: '',
      nextContactPurpose: '',
      tags: [],
      attachments: []
    })
  }

  // 辅助方法
  const getCustomerName = (customerId: string) => {
    return customerOptions.value.find(customer => customer.id === customerId)?.name || '未知客户'
  }

  const getContactName = (contactId: string) => {
    return contactOptions.value.find(contact => contact.id === contactId)?.name || '未知联系人'
  }

  const getCommunicationTypeLabel = (type: CommunicationRecordType) => {
    return communicationTypeOptions.find(option => option.value === type)?.label || type
  }

  const getCommunicationSceneLabel = (scene: CommunicationScene) => {
    return communicationSceneOptions.find(option => option.value === scene)?.label || scene
  }

  const getCommunicationResultLabel = (result: CommunicationResult) => {
    return communicationResultOptions.find(option => option.value === result)?.label || result
  }

  const getCommunicationStatusLabel = (status: CommunicationStatus) => {
    return communicationStatusOptions.find(option => option.value === status)?.label || status
  }

  // 标签类型映射
  const getCommunicationTypeTagType = (type: CommunicationRecordType) => {
    const typeMap = {
      Call: 'success',
      Email: 'info',
      Meeting: 'primary',
      Visit: 'warning',
      WeChat: 'success',
      VideoCall: 'primary',
      Exhibition: 'danger',
      OnlineDemo: 'info'
    }
    return typeMap[type] || 'info'
  }

  const getCommunicationSceneTagType = (scene: CommunicationScene) => {
    const sceneMap = {
      TechDiscussion: 'primary',
      BusinessNegotiation: 'warning',
      ProductIntro: 'success',
      QualityIssue: 'danger',
      DeliveryCoordination: 'info',
      ProjectReview: 'primary',
      ContractDiscussion: 'warning',
      ProblemSolving: 'danger',
      RelationshipBuilding: 'success',
      MarketInfo: 'info'
    }
    return sceneMap[scene] || 'info'
  }

  const getCommunicationResultTagType = (result: CommunicationResult) => {
    const resultMap = {
      excellent: 'success',
      good: 'primary',
      average: 'info',
      poor: 'warning',
      failed: 'danger'
    }
    return resultMap[result] || 'info'
  }

  const getCommunicationStatusTagType = (status: CommunicationStatus) => {
    const statusMap = {
      completed: 'success',
      pending: 'warning',
      cancelled: 'info',
      postponed: 'danger'
    }
    return statusMap[status] || 'info'
  }

  const getTimelineType = (result: CommunicationResult) => {
    const typeMap = {
      excellent: 'success',
      good: 'primary',
      average: 'info',
      poor: 'warning',
      failed: 'danger'
    }
    return typeMap[result] || 'info'
  }

  const getTaskStatusLabel = (status: string) => {
    const statusMap = {
      pending: '待处理',
      in_progress: '进行中',
      completed: '已完成',
      overdue: '已逾期'
    }
    return statusMap[status] || status
  }

  const getTaskStatusTagType = (status: string) => {
    const typeMap = {
      pending: 'info',
      in_progress: 'primary',
      completed: 'success',
      overdue: 'danger'
    }
    return typeMap[status] || 'info'
  }

  const getTaskPriorityLabel = (priority: string) => {
    const priorityMap = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    }
    return priorityMap[priority] || priority
  }

  const getTaskPriorityTagType = (priority: string) => {
    const typeMap = {
      low: 'info',
      medium: 'primary',
      high: 'warning',
      urgent: 'danger'
    }
    return typeMap[priority] || 'info'
  }

  // 格式化方法
  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    return dayjs(dateString).format('YYYY-MM-DD')
  }

  const formatDateTime = (dateString: string, timeString?: string) => {
    if (!dateString) return '-'

    if (timeString) {
      return dayjs(`${dateString} ${timeString}`).format('YYYY-MM-DD HH:mm')
    }

    return dayjs(dateString).format('YYYY-MM-DD HH:mm')
  }

  const formatFileSize = (size: number) => {
    if (size < 1024) return size + 'B'
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
    return (size / (1024 * 1024)).toFixed(1) + 'MB'
  }

  const truncateContent = (content: string, maxLength = 100) => {
    if (content.length <= maxLength) return content
    return content.substring(0, maxLength) + '...'
  }

  const isOverdue = (dateString?: string) => {
    if (!dateString) return false
    return dayjs(dateString).isBefore(dayjs(), 'day')
  }

  const isTaskOverdue = (task: FollowUpTask) => {
    return task.status !== 'completed' && dayjs(task.dueDate).isBefore(dayjs(), 'day')
  }

  // 初始化
  onMounted(() => {
    fetchRecords()
  })
</script>

<style lang="scss" scoped>
  .communication-records {
    min-height: calc(100vh - 60px);
    padding: var(--spacing-4);
    background-color: var(--color-bg-base);

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-6);

      &-left {
        .communication-records__title {
          margin: 0 0 var(--spacing-1) 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .communication-records__subtitle {
          margin: 0;
          font-size: 14px;
          color: var(--color-text-regular);
        }
      }
    }

    &__stats {
      margin-bottom: var(--spacing-4);

      .stat-card {
        position: relative;
        overflow: hidden;

        :deep(.el-card__body) {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: var(--spacing-4);
        }

        .stat-content {
          .stat-value {
            margin-bottom: var(--spacing-1);
            font-size: 24px;
            font-weight: 600;
            color: var(--color-text-primary);
          }

          .stat-label {
            font-size: 13px;
            color: var(--color-text-regular);
          }
        }

        .stat-icon {
          font-size: 32px;
          color: var(--color-primary);
          opacity: 0.8;
        }
      }
    }

    &__search-card {
      margin-bottom: var(--spacing-4);

      .communication-records__search-form {
        .el-form-item {
          margin-bottom: var(--spacing-3);
        }
      }
    }

    &__content-card {
      .communication-records__toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-4);

        &-right {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
        }
      }
    }

    // 时间线视图样式
    &__timeline {
      .timeline-card {
        padding: var(--spacing-4);
        cursor: pointer;
        background: var(--color-bg-primary);
        border: 1px solid var(--color-border-light);
        border-radius: var(--radius-base);
        transition: all 0.2s;

        &:hover {
          border-color: var(--color-primary);
          box-shadow: 0 2px 8px rgb(64 158 255 / 10%);
        }

        &__header {
          .timeline-card__title {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: var(--spacing-2);

            .timeline-card__subject {
              flex: 1;
              margin-right: var(--spacing-3);
              font-size: 16px;
              font-weight: 500;
              color: var(--color-text-primary);
            }

            .timeline-card__badges {
              display: flex;
              flex-shrink: 0;
              gap: var(--spacing-2);
              align-items: center;
            }
          }

          .timeline-card__meta {
            display: flex;
            gap: var(--spacing-3);
            align-items: center;
            font-size: 13px;
            color: var(--color-text-regular);
          }
        }

        &__content {
          margin: var(--spacing-3) 0;

          p {
            margin: 0 0 var(--spacing-2) 0;
            line-height: 1.6;
            color: var(--color-text-secondary);
          }

          .timeline-card__tags {
            display: flex;
            gap: var(--spacing-1);
            align-items: center;

            .more-tags {
              font-size: 12px;
              color: var(--color-text-regular);
            }
          }
        }

        &__footer {
          display: flex;
          gap: var(--spacing-4);
          align-items: center;
          padding-top: var(--spacing-2);
          font-size: 12px;
          color: var(--color-text-regular);
          border-top: 1px solid var(--color-border-lighter);

          .follow-up-tasks,
          .next-contact {
            display: flex;
            gap: var(--spacing-1);
            align-items: center;
          }
        }
      }
    }

    // 表格视图样式
    &__table {
      .table-date {
        .duration {
          font-size: 12px;
          color: var(--color-text-regular);
        }
      }

      .table-subject {
        .subject-title {
          margin-bottom: var(--spacing-1);
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .subject-meta {
          display: flex;
          gap: var(--spacing-1);
        }
      }

      .table-contact {
        .customer {
          margin-bottom: var(--spacing-1);
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .contact {
          font-size: 12px;
          color: var(--color-text-regular);
        }
      }

      .follow-up-status {
        margin-bottom: var(--spacing-1);
      }

      :deep(.el-table__row) {
        cursor: pointer;

        &:hover {
          background-color: var(--color-bg-secondary);
        }
      }
    }

    &__pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: var(--spacing-4);
    }

    // 记录详情样式
    .record-detail {
      &__section {
        margin-bottom: var(--spacing-6);

        h3 {
          padding-bottom: var(--spacing-2);
          margin: 0 0 var(--spacing-3) 0;
          font-size: 16px;
          font-weight: 500;
          color: var(--color-text-primary);
          border-bottom: 1px solid var(--color-border-light);
        }
      }

      .record-content {
        padding: var(--spacing-4);
        line-height: 1.6;
        white-space: pre-wrap;
        background-color: var(--color-bg-secondary);
        border-radius: var(--radius-base);
      }

      .customer-feedback {
        padding: var(--spacing-3);
        font-style: italic;
        background-color: var(--color-bg-secondary);
        border-left: 4px solid var(--color-primary);
        border-radius: var(--radius-base);
      }

      .follow-up-tasks {
        .task-item {
          padding: var(--spacing-3);
          margin-bottom: var(--spacing-2);
          background-color: var(--color-bg-secondary);
          border-radius: var(--radius-base);

          &:last-child {
            margin-bottom: 0;
          }

          .task-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: var(--spacing-2);

            .task-title {
              flex: 1;
              font-weight: 500;
              color: var(--color-text-primary);
            }

            .task-meta {
              display: flex;
              gap: var(--spacing-1);
            }
          }

          .task-info {
            display: flex;
            gap: var(--spacing-3);
            align-items: center;
            font-size: 13px;
            color: var(--color-text-regular);

            .overdue {
              font-weight: 500;
              color: var(--color-danger);
            }
          }
        }
      }

      .attachments {
        .attachment-item {
          display: flex;
          gap: var(--spacing-3);
          align-items: center;
          padding: var(--spacing-3);
          margin-bottom: var(--spacing-2);
          cursor: pointer;
          border: 1px solid var(--color-border-light);
          border-radius: var(--radius-base);
          transition: all 0.2s;

          &:hover {
            background-color: var(--color-bg-secondary);
            border-color: var(--color-primary);
          }

          &:last-child {
            margin-bottom: 0;
          }

          .attachment-icon {
            font-size: 24px;
            color: var(--color-primary);
          }

          .attachment-info {
            flex: 1;

            .attachment-name {
              margin-bottom: var(--spacing-1);
              font-weight: 500;
              color: var(--color-text-primary);
            }

            .attachment-meta {
              margin-bottom: var(--spacing-1);
              font-size: 12px;
              color: var(--color-text-regular);
            }

            .attachment-desc {
              font-size: 12px;
              color: var(--color-text-secondary);
            }
          }

          .download-icon {
            color: var(--color-text-regular);
          }
        }
      }

      .overdue {
        font-weight: 500;
        color: var(--color-danger);
      }
    }

    // 响应式设计
    @media (width <= 768px) {
      padding: var(--spacing-3);

      &__header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: flex-start;

        &-right {
          width: 100%;
        }
      }

      &__stats {
        .el-col {
          margin-bottom: var(--spacing-3);
        }
      }

      &__search-form {
        .el-form-item {
          width: 100%;

          .el-input,
          .el-select,
          .el-date-picker {
            width: 100%;
          }
        }
      }

      &__toolbar {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: flex-start;

        &-left,
        &-right {
          justify-content: flex-start;
          width: 100%;
        }
      }

      .timeline-card {
        &__title {
          flex-direction: column;
          align-items: flex-start;

          .timeline-card__badges {
            flex-wrap: wrap;
            margin-top: var(--spacing-2);
          }
        }
      }
    }
  }

  // Element Plus 组件样式覆盖
  :deep(.el-timeline) {
    padding-left: 0;
  }

  :deep(.el-timeline-item) {
    padding-bottom: var(--spacing-6);
  }

  :deep(.el-timeline-item__timestamp) {
    font-weight: 500;
    color: var(--color-text-primary);
  }

  :deep(.el-drawer__body) {
    padding: var(--spacing-4);
  }

  :deep(.el-dialog) {
    .el-dialog__body {
      padding: var(--spacing-4);
    }
  }

  :deep(.el-upload) {
    .el-upload__tip {
      font-size: 12px;
      color: var(--color-text-regular);
    }
  }

  :deep(.el-descriptions) {
    .el-descriptions__body {
      .el-descriptions__table {
        .el-descriptions-item__cell {
          padding: 8px 12px;
        }
      }
    }
  }
</style>
