# IC封测CIM系统前端基础架构规划

## 🏗️ 项目技术架构设计

### 核心技术栈选型
```
前端技术栈 (2024年最新稳定版本)
├── 🎯 核心框架
│   ├── Vue.js 3.4+ (Composition API + <script setup>)
│   ├── TypeScript 5.3+ (类型安全开发)
│   ├── Vite 5.0+ (极速构建工具)
│   └── Node.js 20 LTS (运行环境)
├── 🎨 UI框架和组件
│   ├── Element Plus 2.5+ (PC端组件库)
│   ├── Vant 4.8+ (移动端组件库)  
│   ├── ECharts 5.4+ (图表可视化)
│   ├── D3.js 7.8+ (高级数据可视化)
│   └── 自定义组件库 (IC封测专业组件)
├── 🔧 状态管理和路由
│   ├── Pinia 2.1+ (轻量级状态管理)
│   ├── Vue Router 4.2+ (官方路由)
│   ├── VueUse 10.7+ (组合式工具库)
│   └── @vueuse/core (常用Hooks)
├── 🛠️ 开发工具链
│   ├── ESLint 8.56+ (代码规范检查)
│   ├── Prettier 3.2+ (代码格式化)
│   ├── Stylelint 16.2+ (样式规范)
│   ├── Husky 9.0+ (Git Hooks)
│   ├── lint-staged 15.2+ (暂存区检查)
│   └── commitizen (规范化提交)
├── 🧪 测试工具
│   ├── Vitest 1.2+ (单元测试)
│   ├── Cypress 13.6+ (E2E测试)
│   ├── @vue/test-utils 2.4+ (组件测试)
│   └── MSW 2.1+ (Mock Service Worker)
├── 📦 构建和部署
│   ├── Vite插件生态
│   ├── Docker容器化部署
│   ├── Nginx反向代理
│   └── CI/CD自动化流水线
└── 🎨 样式和主题
    ├── SCSS/Sass (CSS预处理器)
    ├── PostCSS (CSS后处理器)
    ├── CSS Variables (动态主题)
    ├── 极简主义设计系统
    └── 浅色/深色主题切换
```

## 🎨 极简主义设计系统

### 设计理念
```
极简主义设计原则
├── 📏 简洁至上
│   ├── 去除一切非必要装饰
│   ├── 专注内容和功能
│   ├── 大量留白空间
│   └── 清晰的信息层级
├── 🎯 功能导向
│   ├── 每个元素都有明确目的
│   ├── 减少用户认知负担
│   ├── 直观的交互方式
│   └── 一致的操作体验
├── 🎪 视觉统一
│   ├── 有限的颜色调色板
│   ├── 统一的字体系统
│   ├── 一致的间距规则
│   └── 规整的网格布局
└── ✨ 优雅细节
    ├── 微妙的阴影和过渡
    ├── 合适的动画反馈
    ├── 精确的像素对齐
    └── 舒适的视觉节奏
```

### 双主题色彩系统
```scss
// 浅色主题 (Light Theme)
$light-theme: (
  // 主色调 - 极简蓝色系
  primary: #2563eb,           // 主品牌色 (蓝色)
  primary-light: #3b82f6,     // 主色-浅
  primary-dark: #1d4ed8,      // 主色-深
  
  // 功能色彩 - 柔和版本
  success: #10b981,           // 成功色 (绿色)
  warning: #f59e0b,           // 警告色 (橙色) 
  error: #ef4444,             // 错误色 (红色)
  info: #6b7280,              // 信息色 (灰色)
  
  // 中性色阶 - 极简灰色系
  text-primary: #111827,      // 主要文字
  text-secondary: #6b7280,    // 次要文字
  text-tertiary: #9ca3af,     // 第三级文字
  text-disabled: #d1d5db,     // 禁用文字
  
  // 背景色 - 纯净白色系
  bg-primary: #ffffff,        // 主背景 (纯白)
  bg-secondary: #f9fafb,      // 次要背景
  bg-tertiary: #f3f4f6,       // 第三背景
  bg-hover: #f5f5f5,          // 悬停背景
  bg-active: #e5e7eb,         // 激活背景
  
  // 边框色 - 清淡边框
  border-light: #f3f4f6,      // 轻边框
  border-base: #e5e7eb,       // 基础边框
  border-dark: #d1d5db,       // 深边框
  
  // 阴影色
  shadow: rgba(0, 0, 0, 0.04), // 轻阴影
  shadow-heavy: rgba(0, 0, 0, 0.08) // 重阴影
);

// 深色主题 (Dark Theme) 
$dark-theme: (
  // 主色调 - 柔和蓝色系
  primary: #3b82f6,           // 主品牌色
  primary-light: #60a5fa,     // 主色-浅
  primary-dark: #2563eb,      // 主色-深
  
  // 功能色彩 - 深色优化版本
  success: #34d399,           // 成功色
  warning: #fbbf24,           // 警告色
  error: #f87171,             // 错误色
  info: #94a3b8,              // 信息色
  
  // 中性色阶 - 深色模式文字
  text-primary: #f8fafc,      // 主要文字
  text-secondary: #cbd5e1,    // 次要文字  
  text-tertiary: #94a3b8,     // 第三级文字
  text-disabled: #64748b,     // 禁用文字
  
  // 背景色 - 深色背景系
  bg-primary: #0f172a,        // 主背景 (深蓝黑)
  bg-secondary: #1e293b,      // 次要背景
  bg-tertiary: #334155,       // 第三背景
  bg-hover: #475569,          // 悬停背景
  bg-active: #64748b,         // 激活背景
  
  // 边框色 - 深色边框
  border-light: #334155,      // 轻边框
  border-base: #475569,       // 基础边框  
  border-dark: #64748b,       // 深边框
  
  // 阴影色
  shadow: rgba(0, 0, 0, 0.2),     // 轻阴影
  shadow-heavy: rgba(0, 0, 0, 0.4) // 重阴影
);

// IC封测专业色彩 (两个主题通用)
$professional-colors: (
  wafer: #e0f2fe,             // 晶圆色
  die-pass: #dcfce7,          // 良品die
  die-fail: #fee2e2,          // 不良die  
  cp-test: #f0f9ff,           // CP测试
  assembly: #f3e8ff,          // Assembly
  ft-test: #ecfdf5,           // FT测试
);
```

### 字体系统
```scss
// 极简字体系统
$font-system: (
  // 字体族 - 系统字体优先
  family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
               'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
  family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 
               'Source Code Pro', Menlo, Consolas, 'Courier New', monospace,
  
  // 字号系统 - 限制字号种类
  size-xs: 12px,    // 辅助信息
  size-sm: 14px,    // 标准文字 (默认)
  size-base: 16px,  // 正文内容
  size-lg: 18px,    // 小标题
  size-xl: 20px,    // 标题
  size-2xl: 24px,   // 大标题
  size-3xl: 32px,   // 页面标题
  
  // 字重 - 只使用3种字重
  weight-normal: 400,   // 正常
  weight-medium: 500,   // 中等
  weight-bold: 600,     // 加粗
  
  // 行高 - 黄金比例
  height-tight: 1.25,   // 紧凑
  height-base: 1.5,     // 标准
  height-loose: 1.75,   // 宽松
);
```

### 间距系统
```scss
// 基于4px网格的间距系统
$spacing: (
  0: 0,
  1: 4px,      // 0.25rem
  2: 8px,      // 0.5rem  
  3: 12px,     // 0.75rem
  4: 16px,     // 1rem (基础单位)
  5: 20px,     // 1.25rem
  6: 24px,     // 1.5rem
  8: 32px,     // 2rem
  10: 40px,    // 2.5rem
  12: 48px,    // 3rem
  16: 64px,    // 4rem
  20: 80px,    // 5rem
  24: 96px,    // 6rem
);

// 组件专用间距
$component-spacing: (
  // 按钮
  button-padding-x: 16px,
  button-padding-y: 8px,
  button-gap: 8px,
  
  // 表单
  form-item-margin: 16px,
  form-label-margin: 4px,
  
  // 卡片
  card-padding: 20px,
  card-margin: 16px,
  
  // 表格
  table-cell-padding: 12px,
  table-header-height: 48px,
  table-row-height: 40px,
);
```

### 圆角系统
```scss
// 极简圆角系统
$border-radius: (
  none: 0,
  sm: 4px,     // 小元素
  base: 6px,   // 标准元素 (默认)
  md: 8px,     // 卡片等
  lg: 12px,    // 大容器
  full: 9999px // 完全圆角
);
```

### 阴影系统
```scss
// 极简阴影 - 微妙层次
$shadows: (
  // 浅色主题阴影
  light: (
    none: none,
    sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05),
    base: 0 1px 3px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.04),
    md: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04),
    lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.04),
  ),
  
  // 深色主题阴影  
  dark: (
    none: none,
    sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2),
    base: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.15),
    md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.15),
    lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15),
  )
);
```

## 📁 项目目录结构设计

### 标准化目录结构
```
ic-cim-frontend/
├── 📄 项目配置文件
│   ├── package.json                    # 项目依赖和脚本
│   ├── vite.config.ts                  # Vite构建配置
│   ├── tsconfig.json                   # TypeScript配置
│   ├── tsconfig.node.json              # Node.js环境TS配置
│   ├── .eslintrc.cjs                   # ESLint规则配置
│   ├── .prettierrc.json                # Prettier格式化配置
│   ├── stylelint.config.cjs            # Stylelint样式规范
│   ├── tailwind.config.js              # Tailwind CSS配置
│   ├── postcss.config.js               # PostCSS配置
│   ├── vitest.config.ts                # 单元测试配置
│   ├── cypress.config.ts               # E2E测试配置
│   ├── .env.development                # 开发环境变量
│   ├── .env.production                 # 生产环境变量
│   ├── .env.staging                    # 测试环境变量
│   ├── .gitignore                      # Git忽略文件
│   ├── .dockerignore                   # Docker忽略文件
│   ├── Dockerfile                      # Docker构建文件
│   ├── nginx.conf                      # Nginx配置文件
│   └── README.md                       # 项目说明文档
├── 📁 public/                          # 静态资源目录
│   ├── favicon.ico                     # 网站图标
│   ├── logo-light.svg                  # 浅色主题Logo
│   ├── logo-dark.svg                   # 深色主题Logo
│   ├── icons/                          # 图标资源
│   ├── images/                         # 图片资源
│   └── locales/                        # 国际化资源
│       ├── zh-CN.json                  # 中文语言包
│       ├── en-US.json                  # 英文语言包
│       └── ja-JP.json                  # 日语语言包
├── 📁 src/                             # 源码目录
│   ├── 📁 api/                         # API接口目录
│   │   ├── index.ts                    # API统一导出
│   │   ├── request.ts                  # Axios封装和拦截器
│   │   ├── types.ts                    # API接口类型定义
│   │   ├── modules/                    # 按模块组织API
│   │   │   ├── auth.ts                 # 认证相关API
│   │   │   ├── customer.ts             # 客户管理API
│   │   │   ├── order.ts                # 订单管理API
│   │   │   ├── production.ts           # 生产管理API
│   │   │   ├── quality.ts              # 质量管理API
│   │   │   ├── material.ts             # 物料管理API
│   │   │   ├── equipment.ts            # 设备管理API
│   │   │   ├── report.ts               # 报表分析API
│   │   │   └── system.ts               # 系统管理API
│   │   └── mock/                       # Mock数据目录
│   ├── 📁 assets/                      # 资源文件目录
│   │   ├── styles/                     # 样式文件
│   │   │   ├── index.scss              # 样式统一入口
│   │   │   ├── themes/                 # 主题相关
│   │   │   │   ├── light.scss          # 浅色主题
│   │   │   │   ├── dark.scss           # 深色主题
│   │   │   │   └── variables.scss      # 主题变量
│   │   │   ├── base/                   # 基础样式
│   │   │   │   ├── reset.scss          # 样式重置
│   │   │   │   ├── typography.scss     # 字体排版
│   │   │   │   ├── layout.scss         # 布局样式
│   │   │   │   └── utilities.scss      # 工具类
│   │   │   ├── components/             # 组件样式
│   │   │   │   ├── button.scss         # 按钮样式
│   │   │   │   ├── form.scss           # 表单样式
│   │   │   │   ├── table.scss          # 表格样式
│   │   │   │   └── card.scss           # 卡片样式
│   │   │   └── pages/                  # 页面样式
│   │   ├── images/                     # 图片资源
│   │   ├── icons/                      # SVG图标
│   │   └── fonts/                      # 字体资源
│   ├── 📁 components/                  # 组件目录
│   │   ├── 📁 base/                    # 基础组件 (极简版)
│   │   │   ├── CButton/                # 极简按钮组件
│   │   │   │   ├── index.vue
│   │   │   │   ├── types.ts
│   │   │   │   └── style.scss
│   │   │   ├── CInput/                 # 极简输入框组件
│   │   │   ├── CSelect/                # 极简选择组件
│   │   │   ├── CTable/                 # 极简表格组件
│   │   │   ├── CCard/                  # 极简卡片组件
│   │   │   ├── CModal/                 # 极简模态框组件
│   │   │   ├── CLoading/               # 极简加载组件
│   │   │   └── index.ts                # 组件统一导出
│   │   ├── 📁 layout/                  # 布局组件 (极简版)
│   │   │   ├── AppHeader/              # 极简顶部导航
│   │   │   ├── AppSidebar/             # 极简侧边栏
│   │   │   ├── AppMain/                # 主内容区
│   │   │   ├── AppBreadcrumb/          # 极简面包屑
│   │   │   └── ThemeToggle/            # 主题切换组件
│   │   ├── 📁 business/                # 业务组件
│   │   │   ├── WaferMap/               # Wafer Map组件 (极简版)
│   │   │   ├── SPCChart/               # SPC图表 (极简版)
│   │   │   ├── ProcessFlow/            # 工艺流程图
│   │   │   ├── ProductSelector/        # 产品选择器
│   │   │   └── BOMEditor/              # BOM编辑器
│   │   └── 📁 charts/                  # 图表组件 (极简版)
│   │       ├── LineChart/              # 极简折线图
│   │       ├── BarChart/               # 极简柱状图
│   │       ├── PieChart/               # 极简饼图
│   │       └── GaugeChart/             # 极简仪表盘
│   ├── 📁 composables/                 # 组合式函数
│   │   ├── useTheme.ts                 # 主题切换Hook ⭐
│   │   ├── useAuth.ts                  # 认证相关Hook
│   │   ├── useTable.ts                 # 表格操作Hook
│   │   ├── useForm.ts                  # 表单操作Hook
│   │   ├── useRequest.ts               # 请求处理Hook
│   │   └── index.ts                    # Composables统一导出
│   ├── 📁 stores/                      # 状态管理
│   │   ├── index.ts                    # Store统一配置
│   │   ├── modules/                    # 状态模块
│   │   │   ├── app.ts                  # 应用状态 (包含主题)
│   │   │   ├── auth.ts                 # 认证状态
│   │   │   ├── user.ts                 # 用户状态
│   │   │   └── settings.ts             # 设置状态
│   │   └── types.ts                    # 状态类型定义
│   ├── 📁 views/                       # 页面视图 (极简版)
│   │   ├── 📁 auth/                    # 认证页面
│   │   │   ├── Login.vue               # 极简登录页面
│   │   │   └── components/             # 登录组件
│   │   ├── 📁 dashboard/               # 极简首页
│   │   │   ├── Index.vue               # 极简工作台
│   │   │   └── components/             # 首页组件
│   │   ├── 📁 customer/                # 客户管理 (极简版)
│   │   ├── 📁 order/                   # 订单管理 (极简版)
│   │   ├── 📁 production/              # 生产管理 (极简版)
│   │   ├── 📁 quality/                 # 质量管理 (极简版)
│   │   ├── 📁 material/                # 物料管理 (极简版)
│   │   ├── 📁 report/                  # 报表分析 (极简版)
│   │   └── 📁 mobile/                  # 移动端页面 (极简版)
│   ├── 📁 utils/                       # 工具函数
│   │   ├── theme.ts                    # 主题工具函数 ⭐
│   │   ├── common.ts                   # 通用工具函数
│   │   ├── date.ts                     # 日期处理工具
│   │   ├── format.ts                   # 格式化工具
│   │   └── constants.ts                # 常量定义
│   ├── App.vue                         # 根组件 (支持主题切换)
│   ├── main.ts                         # 应用入口
│   └── vite-env.d.ts                   # Vite类型声明
└── 📁 docs/                            # 文档目录
    ├── design-system.md                # 设计系统文档 ⭐
    ├── component-guide.md              # 组件使用指南
    └── theme-guide.md                  # 主题定制指南 ⭐
```

## ⚙️ 极简主题系统实现

### 主题切换Composable (useTheme.ts)
```typescript
// src/composables/useTheme.ts
import { ref, computed, watch } from 'vue'
import { useStorage } from '@vueuse/core'

export type Theme = 'light' | 'dark'

// 主题状态
const theme = useStorage<Theme>('cim-theme', 'light')

export function useTheme() {
  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
  }
  
  // 设置主题
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
  }
  
  // 是否为深色主题
  const isDark = computed(() => theme.value === 'dark')
  
  // 应用主题到DOM
  const applyTheme = () => {
    const root = document.documentElement
    root.setAttribute('data-theme', theme.value)
    
    // 移除之前的主题类
    root.classList.remove('light-theme', 'dark-theme')
    // 添加当前主题类
    root.classList.add(`${theme.value}-theme`)
  }
  
  // 监听主题变化
  watch(theme, applyTheme, { immediate: true })
  
  return {
    theme: readonly(theme),
    isDark,
    toggleTheme,
    setTheme,
    applyTheme
  }
}
```

### 极简主题变量 (themes/variables.scss)
```scss
// CSS Custom Properties for Theme System
:root {
  // 基础间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 基础圆角
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  
  // 字体
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  
  // 行高
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-loose: 1.75;
  
  // 过渡
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;
}

// 浅色主题
.light-theme {
  // 主色系
  --color-primary: #2563eb;
  --color-primary-hover: #3b82f6;
  --color-primary-active: #1d4ed8;
  
  // 功能色
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #6b7280;
  
  // 文字色
  --color-text-primary: #111827;
  --color-text-secondary: #6b7280;
  --color-text-tertiary: #9ca3af;
  --color-text-disabled: #d1d5db;
  
  // 背景色
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-tertiary: #f3f4f6;
  --color-bg-hover: #f5f5f5;
  --color-bg-active: #e5e7eb;
  
  // 边框色
  --color-border-light: #f3f4f6;
  --color-border-base: #e5e7eb;
  --color-border-dark: #d1d5db;
  
  // 阴影
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.04);
  
  // IC封测专业色
  --color-wafer: #e0f2fe;
  --color-die-pass: #dcfce7;
  --color-die-fail: #fee2e2;
}

// 深色主题
.dark-theme {
  // 主色系
  --color-primary: #3b82f6;
  --color-primary-hover: #60a5fa;
  --color-primary-active: #2563eb;
  
  // 功能色
  --color-success: #34d399;
  --color-warning: #fbbf24;
  --color-error: #f87171;
  --color-info: #94a3b8;
  
  // 文字色
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-disabled: #64748b;
  
  // 背景色
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-hover: #475569;
  --color-bg-active: #64748b;
  
  // 边框色
  --color-border-light: #334155;
  --color-border-base: #475569;
  --color-border-dark: #64748b;
  
  // 阴影
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
  
  // IC封测专业色 (深色优化)
  --color-wafer: #1e3a8a;
  --color-die-pass: #166534;
  --color-die-fail: #991b1b;
}
```

### 极简基础样式 (base/reset.scss)
```scss
// 极简样式重置
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
               'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  transition: background-color var(--transition-normal), 
              color var(--transition-normal);
}

// 移除默认样式
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

button,
[role="button"] {
  cursor: pointer;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

// 链接样式
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-hover);
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-dark);
  border-radius: 3px;
  
  &:hover {
    background: var(--color-text-tertiary);
  }
}
```

### 主题切换组件 (ThemeToggle.vue)
```vue
<template>
  <button 
    class="theme-toggle"
    @click="toggleTheme"
    :aria-label="isDark ? '切换到浅色主题' : '切换到深色主题'"
  >
    <svg 
      class="theme-icon sun"
      :class="{ active: !isDark }"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
    >
      <circle cx="12" cy="12" r="5"/>
      <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
    </svg>
    
    <svg 
      class="theme-icon moon"
      :class="{ active: isDark }"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
    >
      <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
    </svg>
  </button>
</template>

<script setup lang="ts">
import { useTheme } from '@/composables/useTheme'

const { isDark, toggleTheme } = useTheme()
</script>

<style lang="scss" scoped>
.theme-toggle {
  position: relative;
  width: 40px;
  height: 40px;
  border: 1px solid var(--color-border-base);
  border-radius: var(--radius-lg);
  background: var(--color-bg-primary);
  color: var(--color-text-secondary);
  transition: all var(--transition-fast);
  
  &:hover {
    border-color: var(--color-border-dark);
    color: var(--color-text-primary);
  }
  
  .theme-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    stroke-width: 1.5;
    opacity: 0;
    transform-origin: center;
    transition: all var(--transition-normal);
    
    &.active {
      opacity: 1;
      transform: translate(-50%, -50%) rotate(0deg);
    }
    
    &:not(.active) {
      transform: translate(-50%, -50%) rotate(45deg);
    }
  }
}
</style>
```

## 🎯 极简组件设计原则

### 按钮组件示例 (CButton.vue)
```vue
<template>
  <button 
    :class="buttonClass"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
interface Props {
  type?: 'primary' | 'secondary' | 'text'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'secondary',
  size: 'medium'
})

defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClass = computed(() => [
  'c-button',
  `c-button--${props.type}`,
  `c-button--${props.size}`,
  {
    'c-button--disabled': props.disabled
  }
])
</script>

<style lang="scss" scoped>
.c-button {
  // 基础样式
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  
  // 尺寸
  &--small {
    padding: 4px 12px;
    font-size: var(--font-size-xs);
    height: 28px;
  }
  
  &--medium {
    padding: 8px 16px;
    font-size: var(--font-size-sm);
    height: 36px;
  }
  
  &--large {
    padding: 12px 20px;
    font-size: var(--font-size-md);
    height: 44px;
  }
  
  // 类型
  &--primary {
    background: var(--color-primary);
    color: white;
    
    &:hover:not(.c-button--disabled) {
      background: var(--color-primary-hover);
    }
    
    &:active {
      background: var(--color-primary-active);
    }
  }
  
  &--secondary {
    background: var(--color-bg-primary);
    color: var(--color-text-primary);
    border-color: var(--color-border-base);
    
    &:hover:not(.c-button--disabled) {
      border-color: var(--color-primary);
      color: var(--color-primary);
    }
  }
  
  &--text {
    background: transparent;
    color: var(--color-text-secondary);
    
    &:hover:not(.c-button--disabled) {
      color: var(--color-primary);
      background: var(--color-bg-hover);
    }
  }
  
  // 禁用状态
  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}
</style>
```

这个极简主义设计系统具有以下特点：

1. **极简色彩**: 只使用必要的颜色，大量留白
2. **双主题支持**: 完整的浅色/深色主题切换
3. **一致性**: 统一的间距、字体、圆角系统  
4. **功能导向**: 每个设计元素都有明确目的
5. **响应式**: 适配各种设备和屏幕
6. **可访问性**: 良好的对比度和焦点管理

接下来我们可以开始创建具体的配置文件。您希望从哪个部分开始？