<template>
  <div class="customer-selector">
    <el-select
      v-model="selectedCustomerId"
      :placeholder="placeholder"
      :loading="loading"
      :clearable="clearable"
      :disabled="disabled"
      :multiple="multiple"
      :filterable="filterable"
      :remote="remote"
      :remote-method="handleRemoteSearch"
      :reserve-keyword="false"
      class="customer-selector__select"
      @change="handleChange"
      @clear="handleClear"
    >
      <template #empty>
        <div class="selector-empty">
          <svg
            class="empty-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
          >
            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
            <rect x="8" y="2" width="8" height="4" rx="1" ry="1" />
          </svg>
          <p class="empty-text">
            {{ loading ? '搜索中...' : '暂无客户数据' }}
          </p>
        </div>
      </template>

      <el-option
        v-for="customer in customers"
        :key="customer.id"
        :label="customer.name"
        :value="customer.id"
        class="customer-option"
      >
        <div class="customer-option__content">
          <div class="customer-option__main">
            <span class="customer-name">{{ customer.name }}</span>
            <span class="customer-code">{{ customer.code }}</span>
          </div>
          <div class="customer-option__meta">
            <el-tag :type="getCustomerLevelType(customer.level)"
size="small" class="level-tag">
              {{ getCustomerLevelText(customer.level) }}
            </el-tag>
            <el-tag
              :type="getIndustryType(customer.industryType)"
              size="small"
              class="industry-tag"
            >
              {{ getIndustryText(customer.industryType) }}
            </el-tag>
          </div>
        </div>
      </el-option>
    </el-select>

    <!-- 快速添加客户按钮 -->
    <el-button
      v-if="showAddButton && !disabled"
      type="primary"
      :icon="Plus"
      circle
      size="small"
      class="add-customer-btn"
      @click="handleAddCustomer"
    />

    <!-- 新建客户对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="新建客户"
      width="800px"
      :close-on-click-modal="false"
      class="add-customer-dialog"
    >
      <CustomerForm
        ref="customerFormRef"
        :loading="submitting"
        @submit="handleCreateCustomer"
        @cancel="showAddDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue'
  import { Plus } from '@element-plus/icons-vue'
  import type { Customer, CustomerLevel, CreateCustomerData } from '@/types/customer'
  import { ProductType } from '@/types/order'
  import { useCustomerStore } from '@/stores/customer'
  import CustomerForm from './CustomerForm.vue'

  interface Props {
    /** 当前选中的客户ID */
    modelValue?: string | string[]
    /** 占位符文本 */
    placeholder?: string
    /** 是否可清空 */
    clearable?: boolean
    /** 是否禁用 */
    disabled?: boolean
    /** 是否多选 */
    multiple?: boolean
    /** 是否可筛选 */
    filterable?: boolean
    /** 是否远程搜索 */
    remote?: boolean
    /** 是否显示添加按钮 */
    showAddButton?: boolean
    /** 客户类型筛选 */
    customerTypeFilter?: string[]
    /** 客户等级筛选 */
    customerLevelFilter?: string[]
    /** 行业筛选 */
    industryFilter?: string[]
  }

  interface Emits {
    (e: 'update:modelValue', value: string | string[]): void
    (e: 'change', customer: Customer | Customer[] | null): void
    (e: 'select', customer: Customer): void
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择客户',
    clearable: true,
    disabled: false,
    multiple: false,
    filterable: true,
    remote: true,
    showAddButton: false
  })

  const emit = defineEmits<Emits>()

  const customerStore = useCustomerStore()

  // 组件状态
  const loading = ref(false)
  const customers = ref<Customer[]>([])
  const showAddDialog = ref(false)
  const submitting = ref(false)
  const customerFormRef = ref()

  // 选中的客户ID
  const selectedCustomerId = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value || '')
  })

  // 远程搜索
  const handleRemoteSearch = async (query: string) => {
    if (!query || query.length < 2) {
      customers.value = []
      return
    }

    loading.value = true
    try {
      const suggestions = await customerStore.searchCustomerSuggestions(query)

      // 获取完整的客户信息
      const customerPromises = suggestions.map(s => customerStore.fetchCustomerById(s.id))
      const customerResults = await Promise.all(customerPromises)

      customers.value = customerResults.filter(Boolean) as Customer[]

      // 应用筛选条件
      applyFilters()
    } catch (error) {
      console.error('搜索客户失败:', error)
      customers.value = []
    } finally {
      loading.value = false
    }
  }

  // 应用筛选条件
  const applyFilters = () => {
    let filtered = [...customers.value]

    // 客户类型筛选
    if (props.customerTypeFilter && props.customerTypeFilter.length > 0) {
      filtered = filtered.filter(customer => props.customerTypeFilter!.includes(customer.type))
    }

    // 客户等级筛选
    if (props.customerLevelFilter && props.customerLevelFilter.length > 0) {
      filtered = filtered.filter(customer => props.customerLevelFilter!.includes(customer.level))
    }

    // 行业筛选
    if (props.industryFilter && props.industryFilter.length > 0) {
      filtered = filtered.filter(customer => props.industryFilter!.includes(customer.industryType))
    }

    customers.value = filtered
  }

  // 处理选择变化
  const handleChange = (value: string | string[]) => {
    const selectedCustomers = Array.isArray(value)
      ? customers.value.filter(c => value.includes(c.id))
      : customers.value.find(c => c.id === value) || null

    emit('change', selectedCustomers)

    // 发出select事件（单选时）
    if (!props.multiple && selectedCustomers && typeof selectedCustomers === 'object') {
      emit('select', selectedCustomers as Customer)
    }
  }

  // 处理清空
  const handleClear = () => {
    emit('change', null)
  }

  // 处理添加客户
  const handleAddCustomer = () => {
    showAddDialog.value = true
  }

  // 创建新客户
  const handleCreateCustomer = async (data: CreateCustomerData) => {
    submitting.value = true
    try {
      const newCustomer = await customerStore.createCustomer(data)

      // 添加到选项列表
      customers.value.unshift(newCustomer)

      // 自动选中新建的客户
      selectedCustomerId.value = props.multiple
        ? [...((selectedCustomerId.value as string[]) || []), newCustomer.id]
        : newCustomer.id

      // 触发change事件
      handleChange(selectedCustomerId.value!)

      showAddDialog.value = false

      ElMessage.success('客户创建成功')
    } catch (error) {
      console.error('创建客户失败:', error)
      ElMessage.error('客户创建失败')
    } finally {
      submitting.value = false
    }
  }

  // 获取客户等级类型
  const getCustomerLevelType = (level: CustomerLevel) => {
    const typeMap = {
      strategic: 'danger',
      important: 'warning',
      standard: 'info',
      potential: 'success'
    }
    return typeMap[level] || 'info'
  }

  // 获取客户等级文本
  const getCustomerLevelText = (level: CustomerLevel) => {
    const textMap = {
      strategic: '战略',
      important: '重要',
      standard: '标准',
      potential: '潜在'
    }
    return textMap[level] || level
  }

  // 获取行业类型
  const getIndustryType = (industry: ProductType) => {
    return 'info'
  }

  // 获取行业文本
  const getIndustryText = (industry: ProductType) => {
    const textMap = {
      automotive: '汽车',
      consumer: '消费电子',
      communication: '通信',
      industrial: '工控',
      computing: '计算机',
      medical: '医疗',
      aerospace: '航空航天'
    }
    return textMap[industry] || industry
  }

  // 初始化时加载热门客户
  onMounted(async () => {
    if (!props.remote) {
      loading.value = true
      try {
        await customerStore.fetchCustomers({ page: 1, pageSize: 20 })
        customers.value = customerStore.strategicCustomers.slice(0, 10)
      } catch (error) {
        console.error('加载客户列表失败:', error)
      } finally {
        loading.value = false
      }
    }
  })

  // 监听筛选条件变化
  watch(
    [() => props.customerTypeFilter, () => props.customerLevelFilter, () => props.industryFilter],
    () => {
      applyFilters()
    },
    { deep: true }
  )
</script>

<style lang="scss" scoped>
  .customer-selector {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;

    &__select {
      flex: 1;
      min-width: 200px;
    }
  }

  .customer-option {
    &__content {
      width: 100%;
    }

    &__main {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-1);
    }

    &__meta {
      display: flex;
      gap: var(--spacing-1);
    }
  }

  .customer-name {
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
  }

  .customer-code {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  .level-tag,
  .industry-tag {
    font-size: var(--font-size-xs);
  }

  .add-customer-btn {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
  }

  .selector-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-4);
    color: var(--color-text-secondary);

    .empty-icon {
      width: 32px;
      height: 32px;
      margin-bottom: var(--spacing-2);
      opacity: 0.5;
    }

    .empty-text {
      margin: 0;
      font-size: var(--font-size-sm);
    }
  }

  .add-customer-dialog {
    :deep(.el-dialog__body) {
      padding: var(--spacing-4) var(--spacing-6);
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .customer-selector {
      flex-direction: column;
      align-items: stretch;
    }

    .add-customer-btn {
      align-self: flex-end;
    }

    .customer-option__main {
      flex-direction: column;
      gap: var(--spacing-1);
      align-items: flex-start;
    }
  }
</style>
