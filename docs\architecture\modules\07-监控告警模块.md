# 监控告警模块设计

## 1. 模块概述

### 1.1 模块定位
监控告警模块为CIM系统提供统一的实时监控、智能告警、告警处理和升级机制，支持多维度监控指标、灵活的告警规则配置和多渠道告警通知，确保生产异常的及时发现和处理。

### 1.2 复用价值
- **规则引擎**：可配置的告警规则和阈值管理
- **智能分析**：基于历史数据的异常检测和预测告警
- **多级告警**：支持告警等级、升级和自动恢复机制
- **统一监控**：设备、工艺、质量、环境等全方位监控

### 1.3 应用场景覆盖
```
监控告警模块应用场景
├── 设备监控告警
│   ├── 设备状态异常告警
│   ├── 设备参数超限告警
│   ├── 设备故障预警
│   └── 设备维护提醒
├── 工艺过程监控
│   ├── 工艺参数偏离告警
│   ├── 生产节拍异常告警
│   ├── 工艺流程中断告警
│   └── SPC控制图告警
├── 质量监控告警
│   ├── 质量指标超标告警
│   ├── 不良品率异常告警
│   ├── 检验结果告警
│   └── 质量趋势预警
├── 环境监控告警
│   ├── 温湿度超限告警
│   ├── 洁净度异常告警
│   ├── 压力差告警
│   └── 能耗异常告警
├── 系统运行监控
│   ├── 服务器性能告警
│   ├── 数据库连接告警
│   ├── 接口通讯故障
│   └── 数据采集异常
└── 业务流程监控
    ├── 订单交期预警
    ├── 库存异常告警
    ├── 人员排班提醒
    └── 计划执行偏差
```

## 2. 技术架构

### 2.1 架构设计
```
监控告警模块架构
├── 数据采集层              # 多源数据采集
├── 规则引擎层              # 告警规则处理
├── 告警分析层              # 智能分析和判断
├── 告警管理层              # 告警生成和管理
├── 通知分发层              # 多渠道告警通知
├── 告警处理层              # 告警确认和处理
├── 统计分析层              # 告警统计和分析
└── 可视化展示层            # 监控大屏和面板
```

### 2.2 核心数据模型

#### 2.2.1 监控配置管理
```sql
-- 监控对象定义表
CREATE TABLE monitor_objects (
    object_id VARCHAR(30) PRIMARY KEY,
    object_name VARCHAR(100),               -- 监控对象名称
    object_type ENUM('equipment','process','quality','environment','system','business'), -- 对象类型
    object_code VARCHAR(100),               -- 对象编码
    business_id VARCHAR(100),               -- 业务关联ID
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_type_active (object_type, is_active),
    INDEX idx_business (business_id)
);

-- 监控指标定义表
CREATE TABLE monitor_metrics (
    metric_id VARCHAR(30) PRIMARY KEY,
    object_id VARCHAR(30),                  -- 监控对象ID
    metric_name VARCHAR(100),               -- 指标名称
    metric_code VARCHAR(100),               -- 指标编码
    metric_type ENUM('numeric','boolean','text','enum'), -- 指标类型
    data_source_type ENUM('realtime','database','api','calculation'), -- 数据源类型
    data_source_config JSON,                -- 数据源配置
    unit VARCHAR(20),                       -- 单位
    collect_interval INT DEFAULT 60,       -- 采集间隔(秒)
    retention_days INT DEFAULT 30,         -- 数据保留天数
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP,
    
    INDEX idx_object_active (object_id, is_active),
    INDEX idx_metric_code (metric_code)
);

-- 告警规则定义表
CREATE TABLE alert_rules (
    rule_id VARCHAR(30) PRIMARY KEY,
    rule_name VARCHAR(100),                 -- 规则名称
    rule_code VARCHAR(100),                 -- 规则编码
    object_id VARCHAR(30),                  -- 监控对象ID
    metric_id VARCHAR(30),                  -- 监控指标ID
    rule_type ENUM('threshold','trend','pattern','statistical','ml'), -- 规则类型
    alert_level ENUM('info','warning','error','critical'), -- 告警级别
    rule_config JSON,                       -- 规则配置
    threshold_config JSON,                  -- 阈值配置
    time_window INT DEFAULT 300,            -- 时间窗口(秒)
    trigger_condition TEXT,                 -- 触发条件表达式
    recovery_condition TEXT,                -- 恢复条件表达式
    suppress_duration INT DEFAULT 0,       -- 抑制持续时间(秒)
    escalation_rules JSON,                  -- 升级规则配置
    notification_config JSON,               -- 通知配置
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_object_metric (object_id, metric_id),
    INDEX idx_active_level (is_active, alert_level)
);
```

#### 2.2.2 告警事件管理
```sql
-- 告警事件表
CREATE TABLE alert_events (
    event_id VARCHAR(30) PRIMARY KEY,
    rule_id VARCHAR(30),                    -- 告警规则ID
    object_id VARCHAR(30),                  -- 监控对象ID
    metric_id VARCHAR(30),                  -- 监控指标ID
    alert_level ENUM('info','warning','error','critical'), -- 告警级别
    event_type ENUM('trigger','recovery','escalation','suppress'), -- 事件类型
    alert_title VARCHAR(200),               -- 告警标题
    alert_message TEXT,                     -- 告警消息
    trigger_value TEXT,                     -- 触发值
    threshold_value TEXT,                   -- 阈值
    trigger_time TIMESTAMP(3),              -- 触发时间
    recovery_time TIMESTAMP(3),             -- 恢复时间
    duration_seconds INT,                   -- 持续时间(秒)
    event_status ENUM('active','acknowledged','resolved','suppressed'), -- 事件状态
    acknowledge_user VARCHAR(20),           -- 确认用户
    acknowledge_time TIMESTAMP,             -- 确认时间
    acknowledge_comment TEXT,               -- 确认备注
    resolve_user VARCHAR(20),               -- 解决用户
    resolve_time TIMESTAMP,                 -- 解决时间
    resolve_comment TEXT,                   -- 解决备注
    escalation_level INT DEFAULT 0,        -- 升级级别
    escalation_time TIMESTAMP,              -- 升级时间
    additional_data JSON,                   -- 附加数据
    created_at TIMESTAMP,
    
    INDEX idx_rule_time (rule_id, trigger_time),
    INDEX idx_status_level (event_status, alert_level),
    INDEX idx_trigger_time (trigger_time),
    INDEX idx_object_status (object_id, event_status)
);

-- 告警通知记录表
CREATE TABLE alert_notifications (
    notification_id VARCHAR(30) PRIMARY KEY,
    event_id VARCHAR(30),                   -- 告警事件ID
    notification_type ENUM('email','sms','wechat','webhook','voice','app_push'), -- 通知类型
    recipient_type ENUM('user','role','group'), -- 接收者类型
    recipient_id VARCHAR(20),               -- 接收者ID
    recipient_address VARCHAR(200),         -- 接收地址
    notification_title VARCHAR(200),        -- 通知标题
    notification_content TEXT,              -- 通知内容
    send_time TIMESTAMP,                    -- 发送时间
    send_status ENUM('pending','sent','delivered','failed'), -- 发送状态
    delivery_time TIMESTAMP,                -- 送达时间
    failure_reason TEXT,                    -- 失败原因
    retry_count INT DEFAULT 0,              -- 重试次数
    
    INDEX idx_event_type (event_id, notification_type),
    INDEX idx_status_time (send_status, send_time),
    INDEX idx_recipient (recipient_type, recipient_id)
);

-- 监控数据历史表
CREATE TABLE monitor_data_history (
    data_id VARCHAR(30) PRIMARY KEY,
    metric_id VARCHAR(30),                  -- 指标ID
    object_id VARCHAR(30),                  -- 监控对象ID
    metric_value TEXT,                      -- 指标值
    numeric_value DECIMAL(15,4),            -- 数值型值
    collect_time TIMESTAMP(3),              -- 采集时间
    data_quality ENUM('good','uncertain','bad'), -- 数据质量
    source_info VARCHAR(100),               -- 数据来源信息
    
    INDEX idx_metric_time (metric_id, collect_time),
    INDEX idx_object_time (object_id, collect_time),
    INDEX idx_collect_time (collect_time)
) PARTITION BY RANGE (UNIX_TIMESTAMP(collect_time)) (
    PARTITION p_current VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 DAY))),
    PARTITION p_day1 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 2 DAY))),
    PARTITION p_day2 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 3 DAY))),
    PARTITION p_day3 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 4 DAY))),
    PARTITION p_day4 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 5 DAY))),
    PARTITION p_day5 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 6 DAY))),
    PARTITION p_day6 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 7 DAY))),
    PARTITION p_old VALUES LESS THAN MAXVALUE
);
```

## 3. 告警规则引擎

### 3.1 规则引擎核心实现
```java
@Service
public class AlertRuleEngine {
    
    @Autowired
    private AlertRuleRepository alertRuleRepository;
    
    @Autowired
    private MonitorDataService monitorDataService;
    
    @Autowired
    private AlertEventService alertEventService;
    
    @Autowired
    private ScriptEngine scriptEngine; // JavaScript引擎用于表达式计算
    
    private final Map<String, RuleProcessor> ruleProcessors = new HashMap<>();
    
    @PostConstruct
    public void initRuleProcessors() {
        ruleProcessors.put("threshold", new ThresholdRuleProcessor());
        ruleProcessors.put("trend", new TrendRuleProcessor());
        ruleProcessors.put("pattern", new PatternRuleProcessor());
        ruleProcessors.put("statistical", new StatisticalRuleProcessor());
        ruleProcessors.put("ml", new MachineLearningRuleProcessor());
    }
    
    /**
     * 处理监控数据，触发规则检查
     */
    @EventListener
    @Async
    public void onMonitorDataReceived(MonitorDataReceivedEvent event) {
        String metricId = event.getMetricId();
        
        // 获取该指标关联的告警规则
        List<AlertRule> rules = alertRuleRepository.findByMetricIdAndIsActiveTrue(metricId);
        
        for (AlertRule rule : rules) {
            try {
                processRule(rule, event.getData());
            } catch (Exception e) {
                log.error("告警规则处理失败: {}", rule.getRuleCode(), e);
            }
        }
    }
    
    private void processRule(AlertRule rule, MonitorData data) {
        RuleProcessor processor = ruleProcessors.get(rule.getRuleType().name().toLowerCase());
        if (processor == null) {
            log.warn("不支持的规则类型: {}", rule.getRuleType());
            return;
        }
        
        // 获取规则计算所需的历史数据
        List<MonitorData> historyData = getHistoryData(rule, data.getCollectTime());
        
        // 执行规则计算
        RuleEvaluationResult result = processor.evaluate(rule, data, historyData);
        
        // 处理规则计算结果
        handleRuleResult(rule, data, result);
    }
    
    private void handleRuleResult(AlertRule rule, MonitorData data, RuleEvaluationResult result) {
        if (result.isTriggered()) {
            // 检查是否需要触发新告警
            if (!isAlertSuppressed(rule, data)) {
                triggerAlert(rule, data, result);
            }
        } else if (result.isRecovered()) {
            // 检查是否需要恢复告警
            recoverAlert(rule, data, result);
        }
    }
    
    private void triggerAlert(AlertRule rule, MonitorData data, RuleEvaluationResult result) {
        // 创建告警事件
        AlertEvent event = new AlertEvent();
        event.setEventId(IdGenerator.generateId());
        event.setRuleId(rule.getRuleId());
        event.setObjectId(rule.getObjectId());
        event.setMetricId(rule.getMetricId());
        event.setAlertLevel(rule.getAlertLevel());
        event.setEventType(AlertEventType.TRIGGER);
        event.setAlertTitle(generateAlertTitle(rule, data, result));
        event.setAlertMessage(generateAlertMessage(rule, data, result));
        event.setTriggerValue(String.valueOf(data.getNumericValue()));
        event.setThresholdValue(result.getThresholdDescription());
        event.setTriggerTime(data.getCollectTime());
        event.setEventStatus(AlertEventStatus.ACTIVE);
        event.setAdditionalData(result.getAdditionalData());
        event.setCreatedAt(LocalDateTime.now());
        
        // 保存告警事件
        alertEventService.createAlertEvent(event);
        
        // 发送告警通知
        sendAlertNotification(event);
        
        log.info("告警触发: {} - {}", rule.getRuleName(), event.getAlertMessage());
    }
    
    private boolean isAlertSuppressed(AlertRule rule, MonitorData data) {
        if (rule.getSuppressDuration() <= 0) {
            return false;
        }
        
        // 检查抑制时间窗口内是否已有相同告警
        LocalDateTime suppressTime = data.getCollectTime().minusSeconds(rule.getSuppressDuration());
        List<AlertEvent> recentAlerts = alertEventService.findRecentAlerts(
            rule.getRuleId(), suppressTime, data.getCollectTime());
        
        return !recentAlerts.isEmpty();
    }
    
    private List<MonitorData> getHistoryData(AlertRule rule, LocalDateTime currentTime) {
        LocalDateTime startTime = currentTime.minusSeconds(rule.getTimeWindow());
        return monitorDataService.getHistoryData(rule.getMetricId(), startTime, currentTime);
    }
    
    private String generateAlertTitle(AlertRule rule, MonitorData data, RuleEvaluationResult result) {
        return String.format("[%s] %s - %s",
            rule.getAlertLevel().getDisplayName(),
            rule.getRuleName(),
            result.getDescription()
        );
    }
    
    private String generateAlertMessage(AlertRule rule, MonitorData data, RuleEvaluationResult result) {
        StringBuilder message = new StringBuilder();
        message.append("告警规则: ").append(rule.getRuleName()).append("\n");
        message.append("监控对象: ").append(rule.getObjectName()).append("\n");
        message.append("监控指标: ").append(rule.getMetricName()).append("\n");
        message.append("当前值: ").append(data.getNumericValue()).append(" ").append(rule.getUnit()).append("\n");
        message.append("阈值: ").append(result.getThresholdDescription()).append("\n");
        message.append("触发时间: ").append(data.getCollectTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        message.append("详细描述: ").append(result.getDescription());
        
        return message.toString();
    }
}

// 规则处理器接口
public interface RuleProcessor {
    RuleEvaluationResult evaluate(AlertRule rule, MonitorData currentData, List<MonitorData> historyData);
}

// 阈值规则处理器
@Component
public class ThresholdRuleProcessor implements RuleProcessor {
    
    @Override
    public RuleEvaluationResult evaluate(AlertRule rule, MonitorData currentData, List<MonitorData> historyData) {
        JSONObject thresholdConfig = rule.getThresholdConfig();
        
        // 解析阈值配置
        Double upperWarning = thresholdConfig.getDouble("upperWarning");
        Double upperError = thresholdConfig.getDouble("upperError");
        Double upperCritical = thresholdConfig.getDouble("upperCritical");
        Double lowerWarning = thresholdConfig.getDouble("lowerWarning");
        Double lowerError = thresholdConfig.getDouble("lowerError");
        Double lowerCritical = thresholdConfig.getDouble("lowerCritical");
        
        Double currentValue = currentData.getNumericValue().doubleValue();
        RuleEvaluationResult result = new RuleEvaluationResult();
        
        // 检查上限阈值
        if (upperCritical != null && currentValue > upperCritical) {
            result.setTriggered(true);
            result.setAlertLevel(AlertLevel.CRITICAL);
            result.setDescription("数值超过临界上限");
            result.setThresholdDescription("临界上限: " + upperCritical);
        } else if (upperError != null && currentValue > upperError) {
            result.setTriggered(true);
            result.setAlertLevel(AlertLevel.ERROR);
            result.setDescription("数值超过错误上限");
            result.setThresholdDescription("错误上限: " + upperError);
        } else if (upperWarning != null && currentValue > upperWarning) {
            result.setTriggered(true);
            result.setAlertLevel(AlertLevel.WARNING);
            result.setDescription("数值超过警告上限");
            result.setThresholdDescription("警告上限: " + upperWarning);
        }
        // 检查下限阈值
        else if (lowerCritical != null && currentValue < lowerCritical) {
            result.setTriggered(true);
            result.setAlertLevel(AlertLevel.CRITICAL);
            result.setDescription("数值低于临界下限");
            result.setThresholdDescription("临界下限: " + lowerCritical);
        } else if (lowerError != null && currentValue < lowerError) {
            result.setTriggered(true);
            result.setAlertLevel(AlertLevel.ERROR);
            result.setDescription("数值低于错误下限");
            result.setThresholdDescription("错误下限: " + lowerError);
        } else if (lowerWarning != null && currentValue < lowerWarning) {
            result.setTriggered(true);
            result.setAlertLevel(AlertLevel.WARNING);
            result.setDescription("数值低于警告下限");
            result.setThresholdDescription("警告下限: " + lowerWarning);
        } else {
            result.setTriggered(false);
            result.setRecovered(true);
            result.setDescription("数值恢复正常");
        }
        
        // 添加附加数据
        JSONObject additionalData = new JSONObject();
        additionalData.put("currentValue", currentValue);
        additionalData.put("thresholdConfig", thresholdConfig);
        result.setAdditionalData(additionalData);
        
        return result;
    }
}

// 趋势规则处理器
@Component
public class TrendRuleProcessor implements RuleProcessor {
    
    @Override
    public RuleEvaluationResult evaluate(AlertRule rule, MonitorData currentData, List<MonitorData> historyData) {
        if (historyData.size() < 3) {
            return RuleEvaluationResult.noTrigger("历史数据不足");
        }
        
        JSONObject ruleConfig = rule.getRuleConfig();
        String trendType = ruleConfig.getString("trendType"); // "increasing", "decreasing", "volatile"
        Double trendThreshold = ruleConfig.getDouble("trendThreshold");
        Integer minPoints = ruleConfig.getInteger("minPoints");
        
        RuleEvaluationResult result = new RuleEvaluationResult();
        
        // 计算趋势
        TrendAnalysisResult trendAnalysis = analyzeTrend(historyData, minPoints);
        
        boolean triggered = false;
        String description = "";
        
        switch (trendType) {
            case "increasing":
                if (trendAnalysis.getSlope() > trendThreshold) {
                    triggered = true;
                    description = String.format("数值持续上升，斜率: %.4f", trendAnalysis.getSlope());
                }
                break;
            case "decreasing":
                if (trendAnalysis.getSlope() < -trendThreshold) {
                    triggered = true;
                    description = String.format("数值持续下降，斜率: %.4f", trendAnalysis.getSlope());
                }
                break;
            case "volatile":
                if (trendAnalysis.getVariability() > trendThreshold) {
                    triggered = true;
                    description = String.format("数值波动过大，变异系数: %.4f", trendAnalysis.getVariability());
                }
                break;
        }
        
        result.setTriggered(triggered);
        result.setDescription(description);
        result.setThresholdDescription(trendType + "阈值: " + trendThreshold);
        
        // 添加趋势分析数据
        JSONObject additionalData = new JSONObject();
        additionalData.put("slope", trendAnalysis.getSlope());
        additionalData.put("correlation", trendAnalysis.getCorrelation());
        additionalData.put("variability", trendAnalysis.getVariability());
        additionalData.put("dataPoints", historyData.size());
        result.setAdditionalData(additionalData);
        
        return result;
    }
    
    private TrendAnalysisResult analyzeTrend(List<MonitorData> data, Integer minPoints) {
        if (data.size() < minPoints) {
            return new TrendAnalysisResult(0.0, 0.0, 0.0);
        }
        
        // 线性回归分析趋势
        double[] x = new double[data.size()];
        double[] y = new double[data.size()];
        
        for (int i = 0; i < data.size(); i++) {
            x[i] = i;
            y[i] = data.get(i).getNumericValue().doubleValue();
        }
        
        // 计算线性回归
        double sumX = Arrays.stream(x).sum();
        double sumY = Arrays.stream(y).sum();
        double sumXY = 0.0;
        double sumXX = 0.0;
        double sumYY = 0.0;
        
        for (int i = 0; i < data.size(); i++) {
            sumXY += x[i] * y[i];
            sumXX += x[i] * x[i];
            sumYY += y[i] * y[i];
        }
        
        int n = data.size();
        double slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        double correlation = (n * sumXY - sumX * sumY) / 
            Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
        
        // 计算变异系数
        double mean = sumY / n;
        double variance = Arrays.stream(y).map(val -> Math.pow(val - mean, 2)).sum() / n;
        double standardDeviation = Math.sqrt(variance);
        double variability = standardDeviation / Math.abs(mean);
        
        return new TrendAnalysisResult(slope, correlation, variability);
    }
}
```

## 4. 告警通知服务

### 4.1 多渠道通知实现
```java
@Service
public class AlertNotificationService {
    
    @Autowired
    private List<NotificationChannel> notificationChannels;
    
    @Autowired
    private AlertNotificationRepository notificationRepository;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private RoleService roleService;
    
    private final Map<NotificationType, NotificationChannel> channelMap = new HashMap<>();
    
    @PostConstruct
    public void initChannels() {
        for (NotificationChannel channel : notificationChannels) {
            channelMap.put(channel.getType(), channel);
        }
    }
    
    @EventListener
    @Async("alertNotificationExecutor")
    public void onAlertTriggered(AlertTriggeredEvent event) {
        AlertEvent alertEvent = event.getAlertEvent();
        AlertRule rule = event.getAlertRule();
        
        // 解析通知配置
        JSONObject notificationConfig = rule.getNotificationConfig();
        if (notificationConfig == null || notificationConfig.isEmpty()) {
            return;
        }
        
        // 获取通知接收者
        List<NotificationRecipient> recipients = getNotificationRecipients(notificationConfig);
        
        // 根据告警级别选择通知渠道
        List<NotificationType> channels = getNotificationChannels(rule.getAlertLevel(), notificationConfig);
        
        // 发送通知
        for (NotificationRecipient recipient : recipients) {
            for (NotificationType channelType : channels) {
                sendNotification(alertEvent, rule, recipient, channelType);
            }
        }
    }
    
    private void sendNotification(AlertEvent alertEvent, AlertRule rule, 
                                NotificationRecipient recipient, NotificationType channelType) {
        try {
            NotificationChannel channel = channelMap.get(channelType);
            if (channel == null) {
                log.warn("不支持的通知渠道: {}", channelType);
                return;
            }
            
            // 创建通知记录
            AlertNotification notification = createNotificationRecord(
                alertEvent, recipient, channelType);
            
            // 构建通知内容
            NotificationMessage message = buildNotificationMessage(alertEvent, rule, recipient, channelType);
            
            // 发送通知
            NotificationResult result = channel.send(message);
            
            // 更新通知状态
            updateNotificationStatus(notification, result);
            
        } catch (Exception e) {
            log.error("告警通知发送失败: {}", alertEvent.getEventId(), e);
            recordNotificationFailure(alertEvent.getEventId(), recipient, channelType, e.getMessage());
        }
    }
    
    private List<NotificationRecipient> getNotificationRecipients(JSONObject config) {
        List<NotificationRecipient> recipients = new ArrayList<>();
        
        // 解析用户接收者
        JSONArray users = config.getJSONArray("users");
        if (users != null) {
            for (Object userId : users) {
                User user = userService.findById(userId.toString());
                if (user != null) {
                    recipients.add(new NotificationRecipient(
                        RecipientType.USER, user.getUserId(), 
                        user.getEmail(), user.getPhone(), user.getWechatId()
                    ));
                }
            }
        }
        
        // 解析角色接收者
        JSONArray roles = config.getJSONArray("roles");
        if (roles != null) {
            for (Object roleId : roles) {
                List<User> roleUsers = roleService.getUsersByRole(roleId.toString());
                for (User user : roleUsers) {
                    recipients.add(new NotificationRecipient(
                        RecipientType.ROLE, user.getUserId(),
                        user.getEmail(), user.getPhone(), user.getWechatId()
                    ));
                }
            }
        }
        
        // 解析部门接收者
        JSONArray departments = config.getJSONArray("departments");
        if (departments != null) {
            for (Object deptId : departments) {
                List<User> deptUsers = userService.getUsersByDepartment(deptId.toString());
                for (User user : deptUsers) {
                    recipients.add(new NotificationRecipient(
                        RecipientType.DEPARTMENT, user.getUserId(),
                        user.getEmail(), user.getPhone(), user.getWechatId()
                    ));
                }
            }
        }
        
        return recipients.stream().distinct().collect(Collectors.toList());
    }
    
    private NotificationMessage buildNotificationMessage(AlertEvent alertEvent, AlertRule rule, 
                                                       NotificationRecipient recipient, 
                                                       NotificationType channelType) {
        NotificationMessage message = new NotificationMessage();
        message.setRecipient(recipient);
        message.setChannelType(channelType);
        message.setTitle(buildNotificationTitle(alertEvent, rule, channelType));
        message.setContent(buildNotificationContent(alertEvent, rule, channelType));
        message.setUrgent(rule.getAlertLevel() == AlertLevel.CRITICAL);
        
        return message;
    }
    
    private String buildNotificationTitle(AlertEvent alertEvent, AlertRule rule, NotificationType channelType) {
        String prefix = "";
        switch (rule.getAlertLevel()) {
            case CRITICAL:
                prefix = "【紧急告警】";
                break;
            case ERROR:
                prefix = "【错误告警】";
                break;
            case WARNING:
                prefix = "【警告告警】";
                break;
            case INFO:
                prefix = "【信息告警】";
                break;
        }
        
        return prefix + alertEvent.getAlertTitle();
    }
    
    private String buildNotificationContent(AlertEvent alertEvent, AlertRule rule, NotificationType channelType) {
        if (channelType == NotificationType.SMS) {
            // 短信内容要简洁
            return String.format("告警:%s,当前值:%s,时间:%s", 
                rule.getRuleName(),
                alertEvent.getTriggerValue(),
                alertEvent.getTriggerTime().format(DateTimeFormatter.ofPattern("HH:mm"))
            );
        } else {
            // 邮件、微信等详细内容
            StringBuilder content = new StringBuilder();
            content.append("告警详情：\n");
            content.append("===================\n");
            content.append("告警规则：").append(rule.getRuleName()).append("\n");
            content.append("监控对象：").append(rule.getObjectName()).append("\n");
            content.append("监控指标：").append(rule.getMetricName()).append("\n");
            content.append("告警级别：").append(rule.getAlertLevel().getDisplayName()).append("\n");
            content.append("当前值：").append(alertEvent.getTriggerValue()).append("\n");
            content.append("阈值：").append(alertEvent.getThresholdValue()).append("\n");
            content.append("触发时间：").append(alertEvent.getTriggerTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            content.append("告警描述：").append(alertEvent.getAlertMessage()).append("\n");
            content.append("===================\n");
            content.append("请及时处理并确认告警状态。");
            
            return content.toString();
        }
    }
}

// 通知渠道接口
public interface NotificationChannel {
    NotificationType getType();
    NotificationResult send(NotificationMessage message);
    boolean isEnabled();
}

// 邮件通知渠道
@Component
public class EmailNotificationChannel implements NotificationChannel {
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${alert.notification.email.from}")
    private String fromAddress;
    
    @Override
    public NotificationType getType() {
        return NotificationType.EMAIL;
    }
    
    @Override
    public NotificationResult send(NotificationMessage message) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            
            helper.setFrom(fromAddress);
            helper.setTo(message.getRecipient().getEmail());
            helper.setSubject(message.getTitle());
            helper.setText(message.getContent(), false);
            
            // 设置优先级
            if (message.isUrgent()) {
                mimeMessage.setHeader("X-Priority", "1");
                mimeMessage.setHeader("X-MSMail-Priority", "High");
            }
            
            mailSender.send(mimeMessage);
            
            return NotificationResult.success("邮件发送成功");
            
        } catch (Exception e) {
            log.error("邮件发送失败", e);
            return NotificationResult.failure("邮件发送失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean isEnabled() {
        return true; // 可以从配置中读取
    }
}

// 短信通知渠道
@Component
public class SmsNotificationChannel implements NotificationChannel {
    
    @Autowired
    private SmsService smsService;
    
    @Override
    public NotificationType getType() {
        return NotificationType.SMS;
    }
    
    @Override
    public NotificationResult send(NotificationMessage message) {
        try {
            String phone = message.getRecipient().getPhone();
            if (!StringUtils.hasText(phone)) {
                return NotificationResult.failure("手机号码为空");
            }
            
            // 发送短信
            SmsResult result = smsService.sendSms(phone, message.getContent());
            
            if (result.isSuccess()) {
                return NotificationResult.success("短信发送成功");
            } else {
                return NotificationResult.failure("短信发送失败: " + result.getMessage());
            }
            
        } catch (Exception e) {
            log.error("短信发送失败", e);
            return NotificationResult.failure("短信发送失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean isEnabled() {
        return smsService.isEnabled();
    }
}

// 微信通知渠道
@Component
public class WechatNotificationChannel implements NotificationChannel {
    
    @Autowired
    private WechatWorkService wechatWorkService;
    
    @Override
    public NotificationType getType() {
        return NotificationType.WECHAT;
    }
    
    @Override
    public NotificationResult send(NotificationMessage message) {
        try {
            String wechatId = message.getRecipient().getWechatId();
            if (!StringUtils.hasText(wechatId)) {
                return NotificationResult.failure("微信ID为空");
            }
            
            // 构建卡片消息
            WechatCardMessage cardMessage = WechatCardMessage.builder()
                .toUser(wechatId)
                .title(message.getTitle())
                .description(message.getContent())
                .url("http://cim.company.com/alerts") // 告警管理页面
                .color(getColorByUrgency(message.isUrgent()))
                .build();
            
            WechatResult result = wechatWorkService.sendCardMessage(cardMessage);
            
            if (result.isSuccess()) {
                return NotificationResult.success("微信消息发送成功");
            } else {
                return NotificationResult.failure("微信消息发送失败: " + result.getErrmsg());
            }
            
        } catch (Exception e) {
            log.error("微信消息发送失败", e);
            return NotificationResult.failure("微信消息发送失败: " + e.getMessage());
        }
    }
    
    private String getColorByUrgency(boolean urgent) {
        return urgent ? "#FF0000" : "#00AA00"; // 紧急用红色，普通用绿色
    }
    
    @Override
    public boolean isEnabled() {
        return wechatWorkService.isEnabled();
    }
}
```

## 5. 告警升级机制

### 5.1 智能升级服务
```java
@Service
public class AlertEscalationService {
    
    @Autowired
    private AlertEventRepository alertEventRepository;
    
    @Autowired
    private AlertNotificationService notificationService;
    
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void checkEscalation() {
        // 获取活跃状态的告警事件
        List<AlertEvent> activeAlerts = alertEventRepository.findByEventStatus(AlertEventStatus.ACTIVE);
        
        for (AlertEvent alert : activeAlerts) {
            try {
                processEscalation(alert);
            } catch (Exception e) {
                log.error("告警升级处理失败: {}", alert.getEventId(), e);
            }
        }
    }
    
    private void processEscalation(AlertEvent alert) {
        AlertRule rule = alertRuleRepository.findById(alert.getRuleId())
            .orElse(null);
        
        if (rule == null || rule.getEscalationRules() == null) {
            return;
        }
        
        JSONArray escalationRules = rule.getEscalationRules().getJSONArray("rules");
        if (escalationRules == null || escalationRules.isEmpty()) {
            return;
        }
        
        LocalDateTime now = LocalDateTime.now();
        Duration alertDuration = Duration.between(alert.getTriggerTime(), now);
        
        // 检查每个升级规则
        for (Object ruleObj : escalationRules) {
            JSONObject escalationRule = (JSONObject) ruleObj;
            
            int level = escalationRule.getIntValue("level");
            int delayMinutes = escalationRule.getIntValue("delayMinutes");
            
            // 检查是否达到升级时间且未升级过该级别
            if (alertDuration.toMinutes() >= delayMinutes && 
                alert.getEscalationLevel() < level) {
                
                executeEscalation(alert, escalationRule, level);
                break; // 只执行一个级别的升级
            }
        }
    }
    
    private void executeEscalation(AlertEvent alert, JSONObject escalationRule, int level) {
        try {
            // 更新告警升级级别
            alert.setEscalationLevel(level);
            alert.setEscalationTime(LocalDateTime.now());
            alertEventRepository.save(alert);
            
            // 发送升级通知
            sendEscalationNotification(alert, escalationRule, level);
            
            // 记录升级日志
            log.info("告警已升级: {} -> 级别 {}", alert.getEventId(), level);
            
        } catch (Exception e) {
            log.error("执行告警升级失败: {}", alert.getEventId(), e);
        }
    }
    
    private void sendEscalationNotification(AlertEvent alert, JSONObject escalationRule, int level) {
        // 获取升级通知配置
        JSONObject notificationConfig = escalationRule.getJSONObject("notification");
        if (notificationConfig == null) {
            return;
        }
        
        // 构建升级通知消息
        NotificationMessage message = buildEscalationMessage(alert, level);
        
        // 发送给升级接收者
        List<NotificationRecipient> recipients = getEscalationRecipients(notificationConfig);
        List<NotificationType> channels = getEscalationChannels(notificationConfig);
        
        for (NotificationRecipient recipient : recipients) {
            for (NotificationType channelType : channels) {
                notificationService.sendEscalationNotification(message, recipient, channelType);
            }
        }
    }
    
    private NotificationMessage buildEscalationMessage(AlertEvent alert, int level) {
        NotificationMessage message = new NotificationMessage();
        message.setTitle(String.format("【告警升级 L%d】%s", level, alert.getAlertTitle()));
        
        StringBuilder content = new StringBuilder();
        content.append("告警升级通知\n");
        content.append("====================\n");
        content.append("升级级别：L").append(level).append("\n");
        content.append("原始告警：").append(alert.getAlertTitle()).append("\n");
        content.append("触发时间：").append(alert.getTriggerTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        content.append("持续时间：").append(Duration.between(alert.getTriggerTime(), LocalDateTime.now()).toMinutes()).append("分钟\n");
        content.append("告警描述：").append(alert.getAlertMessage()).append("\n");
        content.append("====================\n");
        content.append("该告警已持续存在且未被处理，请高级管理人员立即关注并处理！");
        
        message.setContent(content.toString());
        message.setUrgent(true);
        
        return message;
    }
}
```

## 6. 监控大屏服务

### 6.1 实时监控面板
```java
@RestController
@RequestMapping("/api/monitor")
public class MonitorDashboardController {
    
    @Autowired
    private MonitorDashboardService dashboardService;
    
    @Autowired
    private AlertEventService alertEventService;
    
    /**
     * 获取监控概览数据
     */
    @GetMapping("/overview")
    public ResponseEntity<MonitorOverviewDTO> getMonitorOverview() {
        MonitorOverviewDTO overview = dashboardService.getMonitorOverview();
        return ResponseEntity.ok(overview);
    }
    
    /**
     * 获取告警统计数据
     */
    @GetMapping("/alerts/statistics")
    public ResponseEntity<AlertStatisticsDTO> getAlertStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(1);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }
        
        AlertStatisticsDTO statistics = alertEventService.getAlertStatistics(startTime, endTime);
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 获取活跃告警列表
     */
    @GetMapping("/alerts/active")
    public ResponseEntity<Page<AlertEventDTO>> getActiveAlerts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String level) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("triggerTime").descending());
        Page<AlertEvent> activeAlerts = alertEventService.getActiveAlerts(pageable, level);
        
        Page<AlertEventDTO> result = activeAlerts.map(this::convertToDTO);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取监控对象状态
     */
    @GetMapping("/objects/status")
    public ResponseEntity<List<MonitorObjectStatusDTO>> getObjectsStatus(
            @RequestParam(required = false) String objectType) {
        
        List<MonitorObjectStatusDTO> statuses = dashboardService.getObjectsStatus(objectType);
        return ResponseEntity.ok(statuses);
    }
    
    /**
     * 获取实时指标数据
     */
    @GetMapping("/metrics/realtime")
    public ResponseEntity<List<MetricRealtimeDataDTO>> getRealtimeMetrics(
            @RequestParam List<String> metricIds) {
        
        List<MetricRealtimeDataDTO> data = dashboardService.getRealtimeMetrics(metricIds);
        return ResponseEntity.ok(data);
    }
}

@Service
public class MonitorDashboardService {
    
    @Autowired
    private MonitorObjectRepository objectRepository;
    
    @Autowired
    private MonitorMetricRepository metricRepository;
    
    @Autowired
    private AlertEventRepository alertEventRepository;
    
    @Autowired
    private MonitorDataHistoryRepository dataHistoryRepository;
    
    @Cacheable(value = "monitor_overview", unless = "#result == null")
    public MonitorOverviewDTO getMonitorOverview() {
        MonitorOverviewDTO overview = new MonitorOverviewDTO();
        
        // 监控对象统计
        long totalObjects = objectRepository.countByIsActiveTrue();
        long normalObjects = objectRepository.countByIsActiveTrueAndStatus("normal");
        long warningObjects = objectRepository.countByIsActiveTrueAndStatus("warning");
        long errorObjects = objectRepository.countByIsActiveTrueAndStatus("error");
        
        overview.setTotalObjects((int) totalObjects);
        overview.setNormalObjects((int) normalObjects);
        overview.setWarningObjects((int) warningObjects);
        overview.setErrorObjects((int) errorObjects);
        
        // 今日告警统计
        LocalDateTime todayStart = LocalDate.now().atStartOfDay();
        LocalDateTime todayEnd = todayStart.plusDays(1);
        
        long todayAlerts = alertEventRepository.countByTriggerTimeBetween(todayStart, todayEnd);
        long criticalAlerts = alertEventRepository.countByTriggerTimeBetweenAndAlertLevel(
            todayStart, todayEnd, AlertLevel.CRITICAL);
        long activeAlerts = alertEventRepository.countByEventStatus(AlertEventStatus.ACTIVE);
        
        overview.setTodayAlerts((int) todayAlerts);
        overview.setCriticalAlerts((int) criticalAlerts);
        overview.setActiveAlerts((int) activeAlerts);
        
        // 系统健康度计算
        double healthScore = calculateSystemHealth(overview);
        overview.setSystemHealth(healthScore);
        
        return overview;
    }
    
    public List<MonitorObjectStatusDTO> getObjectsStatus(String objectType) {
        List<MonitorObject> objects;
        
        if (StringUtils.hasText(objectType)) {
            objects = objectRepository.findByObjectTypeAndIsActiveTrue(
                MonitorObjectType.valueOf(objectType.toUpperCase()));
        } else {
            objects = objectRepository.findByIsActiveTrueOrderByObjectName();
        }
        
        return objects.stream()
            .map(this::buildObjectStatusDTO)
            .collect(Collectors.toList());
    }
    
    public List<MetricRealtimeDataDTO> getRealtimeMetrics(List<String> metricIds) {
        List<MetricRealtimeDataDTO> result = new ArrayList<>();
        
        for (String metricId : metricIds) {
            MonitorMetric metric = metricRepository.findById(metricId).orElse(null);
            if (metric == null) continue;
            
            // 获取最新数据
            List<MonitorDataHistory> latestData = dataHistoryRepository
                .findTop1ByMetricIdOrderByCollectTimeDesc(metricId);
            
            if (!latestData.isEmpty()) {
                MonitorDataHistory data = latestData.get(0);
                
                MetricRealtimeDataDTO dto = new MetricRealtimeDataDTO();
                dto.setMetricId(metricId);
                dto.setMetricName(metric.getMetricName());
                dto.setObjectName(metric.getObjectName());
                dto.setValue(data.getNumericValue());
                dto.setUnit(metric.getUnit());
                dto.setCollectTime(data.getCollectTime());
                dto.setDataQuality(data.getDataQuality());
                
                // 计算状态
                dto.setStatus(calculateMetricStatus(metric, data));
                
                result.add(dto);
            }
        }
        
        return result;
    }
    
    private MonitorObjectStatusDTO buildObjectStatusDTO(MonitorObject object) {
        MonitorObjectStatusDTO dto = new MonitorObjectStatusDTO();
        dto.setObjectId(object.getObjectId());
        dto.setObjectName(object.getObjectName());
        dto.setObjectType(object.getObjectType());
        dto.setBusinessId(object.getBusinessId());
        
        // 获取对象的活跃告警
        List<AlertEvent> activeAlerts = alertEventRepository
            .findByObjectIdAndEventStatus(object.getObjectId(), AlertEventStatus.ACTIVE);
        
        // 计算对象状态
        if (activeAlerts.stream().anyMatch(a -> a.getAlertLevel() == AlertLevel.CRITICAL)) {
            dto.setStatus("critical");
            dto.setStatusColor("#FF4D4F");
        } else if (activeAlerts.stream().anyMatch(a -> a.getAlertLevel() == AlertLevel.ERROR)) {
            dto.setStatus("error");
            dto.setStatusColor("#FF7A45");
        } else if (activeAlerts.stream().anyMatch(a -> a.getAlertLevel() == AlertLevel.WARNING)) {
            dto.setStatus("warning");
            dto.setStatusColor("#FFA940");
        } else {
            dto.setStatus("normal");
            dto.setStatusColor("#52C41A");
        }
        
        dto.setActiveAlerts(activeAlerts.size());
        dto.setLastUpdateTime(LocalDateTime.now());
        
        return dto;
    }
    
    private double calculateSystemHealth(MonitorOverviewDTO overview) {
        // 系统健康度计算算法
        double objectHealth = (double) overview.getNormalObjects() / overview.getTotalObjects() * 100;
        double alertPenalty = overview.getActiveAlerts() * 2.0; // 每个活跃告警扣2分
        double criticalPenalty = overview.getCriticalAlerts() * 5.0; // 每个严重告警扣5分
        
        double health = objectHealth - alertPenalty - criticalPenalty;
        return Math.max(0, Math.min(100, health)); // 限制在0-100之间
    }
}
```

## 7. WebSocket实时推送

### 7.1 实时数据推送
```java
@Component
public class MonitorDataWebSocketHandler extends TextWebSocketHandler {
    
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> userSubscriptions = new ConcurrentHashMap<>();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String userId = getUserId(session);
        sessions.put(session.getId(), session);
        log.info("监控WebSocket连接建立: {}", userId);
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String sessionId = session.getId();
        sessions.remove(sessionId);
        
        // 清理订阅信息
        userSubscriptions.values().forEach(subs -> subs.remove(sessionId));
        
        log.info("监控WebSocket连接关闭: {}", sessionId);
    }
    
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        try {
            JSONObject request = JSON.parseObject(message.getPayload());
            String action = request.getString("action");
            
            switch (action) {
                case "subscribe":
                    handleSubscribe(session, request);
                    break;
                case "unsubscribe":
                    handleUnsubscribe(session, request);
                    break;
                case "heartbeat":
                    handleHeartbeat(session);
                    break;
                default:
                    log.warn("未知的WebSocket消息类型: {}", action);
            }
        } catch (Exception e) {
            log.error("WebSocket消息处理失败", e);
            sendErrorMessage(session, "消息处理失败: " + e.getMessage());
        }
    }
    
    private void handleSubscribe(WebSocketSession session, JSONObject request) throws IOException {
        String type = request.getString("type"); // "metrics", "alerts", "objects"
        JSONArray ids = request.getJSONArray("ids");
        
        Set<String> subscriptions = userSubscriptions.computeIfAbsent(
            session.getId(), k -> ConcurrentHashMap.newKeySet());
        
        for (Object id : ids) {
            String subscriptionKey = type + ":" + id.toString();
            subscriptions.add(subscriptionKey);
        }
        
        // 发送订阅确认
        JSONObject response = new JSONObject();
        response.put("type", "subscription_confirmed");
        response.put("subscriptionType", type);
        response.put("ids", ids);
        
        session.sendMessage(new TextMessage(response.toJSONString()));
    }
    
    private void handleUnsubscribe(WebSocketSession session, JSONObject request) throws IOException {
        String type = request.getString("type");
        JSONArray ids = request.getJSONArray("ids");
        
        Set<String> subscriptions = userSubscriptions.get(session.getId());
        if (subscriptions != null) {
            for (Object id : ids) {
                String subscriptionKey = type + ":" + id.toString();
                subscriptions.remove(subscriptionKey);
            }
        }
        
        // 发送取消订阅确认
        JSONObject response = new JSONObject();
        response.put("type", "unsubscription_confirmed");
        response.put("subscriptionType", type);
        response.put("ids", ids);
        
        session.sendMessage(new TextMessage(response.toJSONString()));
    }
    
    @EventListener
    @Async
    public void onMetricDataUpdate(MetricDataUpdateEvent event) {
        JSONObject message = new JSONObject();
        message.put("type", "metric_update");
        message.put("metricId", event.getMetricId());
        message.put("value", event.getValue());
        message.put("timestamp", event.getTimestamp());
        message.put("quality", event.getQuality());
        
        broadcastToSubscribers("metrics:" + event.getMetricId(), message.toJSONString());
    }
    
    @EventListener
    @Async
    public void onAlertUpdate(AlertUpdateEvent event) {
        JSONObject message = new JSONObject();
        message.put("type", "alert_update");
        message.put("eventType", event.getEventType());
        message.put("alertEvent", convertAlertToJson(event.getAlertEvent()));
        
        String subscriptionKey = "alerts:" + event.getAlertEvent().getObjectId();
        broadcastToSubscribers(subscriptionKey, message.toJSONString());
    }
    
    private void broadcastToSubscribers(String subscriptionKey, String message) {
        userSubscriptions.entrySet().stream()
            .filter(entry -> entry.getValue().contains(subscriptionKey))
            .forEach(entry -> {
                WebSocketSession session = sessions.get(entry.getKey());
                if (session != null && session.isOpen()) {
                    try {
                        session.sendMessage(new TextMessage(message));
                    } catch (IOException e) {
                        log.error("WebSocket消息发送失败", e);
                    }
                }
            });
    }
}

@Configuration
@EnableWebSocket
public class MonitorWebSocketConfig implements WebSocketConfigurer {
    
    @Autowired
    private MonitorDataWebSocketHandler webSocketHandler;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(webSocketHandler, "/ws/monitor")
                .setAllowedOriginPatterns("*")
                .withSockJS();
    }
}
```

---

*监控告警模块为CIM系统提供了智能化、多层级、全方位的监控告警解决方案，支持实时监控、智能分析、多渠道通知和自动升级处理*