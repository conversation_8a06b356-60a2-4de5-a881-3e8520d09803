<template>
  <div class="inquiry-list">
    <!-- 页面标题 -->
    <div class="inquiry-list__header">
      <div class="inquiry-list__title">
        <h2>客户询价管理</h2>
        <p class="inquiry-list__subtitle">
IC封装测试询价全流程管理 - 订单驱动的数字化基础
</p>
      </div>
      <div class="inquiry-list__actions">
        <el-button
type="primary" @click="handleCreateInquiry"
>
          <el-icon><Plus /></el-icon>
          新增询价
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="inquiry-list__stats">
      <div
v-for="stat in statsCards" :key="stat.key"
class="stats-card"
>
        <div
class="stats-card__icon" :style="{ backgroundColor: stat.color }"
>
          <el-icon :size="24">
            <component :is="stat.icon" />
          </el-icon>
        </div>
        <div class="stats-card__content">
          <div class="stats-card__value">
            {{ stat.value }}
          </div>
          <div class="stats-card__label">
            {{ stat.label }}
          </div>
          <div
class="stats-card__trend" :class="stat.trendClass"
>
            <el-icon :size="12">
              <component :is="stat.trendIcon" />
            </el-icon>
            {{ stat.changePercent }}
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="inquiry-list__search">
      <el-card>
        <el-form
:model="searchForm" inline
label-width="auto"
>
          <el-form-item label="询价编号">
            <el-input
              v-model="searchForm.inquiryNumber"
              placeholder="请输入询价编号"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="客户名称">
            <el-input
              v-model="searchForm.customerName"
              placeholder="请输入客户名称"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="询价状态">
            <el-select
              v-model="searchForm.status"
              placeholder="选择状态"
              multiple
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
                <span class="status-option">
                  <span
class="status-dot" :style="{ backgroundColor: option.color }"
/>
                  {{ option.label }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="优先级">
            <el-select
              v-model="searchForm.priority"
              placeholder="选择优先级"
              multiple
              clearable
              style="width: 150px"
            >
              <el-option
                v-for="option in priorityOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="产品类型">
            <el-select
              v-model="searchForm.productType"
              placeholder="选择产品类型"
              multiple
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="option in productTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="封装类型">
            <el-select
              v-model="searchForm.packageType"
              placeholder="选择封装类型"
              multiple
              clearable
              style="width: 150px"
            >
              <el-option
                v-for="option in packageTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="询价日期">
            <el-date-picker
              v-model="searchForm.inquiryDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button
type="primary" @click="handleSearch"
>
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="inquiry-list__table">
      <el-card>
        <template #header>
          <div class="table-header">
            <span>询价列表 (共 {{ pagination.total }} 条)</span>
            <div class="table-header__actions">
              <el-button-group>
                <el-button
                  v-if="selectedRows.length > 0"
                  type="warning"
                  @click="handleBatchEvaluate"
                >
                  批量评估 ({{ selectedRows.length }})
                </el-button>
                <el-button
v-if="selectedRows.length > 0" type="danger"
@click="handleBatchDelete"
>
                  批量删除 ({{ selectedRows.length }})
                </el-button>
              </el-button-group>
              <el-button
circle @click="handleRefresh"
>
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="tableData"
          stripe
          border
          height="600"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
        >
          <el-table-column
type="selection" width="50"
/>
          <el-table-column
            prop="inquiryNumber"
            label="询价编号"
            width="140"
            sortable="custom"
            fixed="left"
          >
            <template #default="{ row }">
              <el-link
type="primary" class="inquiry-number-link"
@click="handleViewDetail(row)"
>
                {{ row.inquiryNumber }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column
prop="customer.name" label="客户信息"
width="200" show-overflow-tooltip
>
            <template #default="{ row }">
              <div class="customer-info">
                <div class="customer-name">
                  {{ row.customer.name }}
                </div>
                <div class="customer-code">
                  {{ row.customer.code }}
                </div>
                <div class="customer-contact">
                  {{ row.customer.contact.name }} - {{ row.customer.contact.phone }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column
prop="productInfo" label="产品信息"
width="220"
>
            <template #default="{ row }">
              <div class="product-info">
                <div class="product-name">
                  {{ row.productInfo.productName }}
                </div>
                <div class="product-details">
                  <el-tag
size="small" type="info"
>
                    {{ row.productInfo.productType }}
                  </el-tag>
                  <el-tag size="small">
                    {{ row.productInfo.packageType }}
                  </el-tag>
                </div>
                <div class="product-quantity">
                  数量: {{ row.productInfo.quantity }}{{ row.productInfo.quantityLevel }} pcs
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column
prop="status" label="状态"
width="100" sortable="custom"
>
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                :color="getStatusColor(row.status)"
                effect="light"
              >
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
prop="priority" label="优先级"
width="80" sortable="custom"
>
            <template #default="{ row }">
              <el-tag
:type="getPriorityType(row.priority)" size="small"
>
                {{ getPriorityLabel(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
label="进度信息" width="150"
>
            <template #default="{ row }">
              <div class="progress-info">
                <div class="sales-manager">
销售: {{ row.assignedSalesManager || '未分配' }}
</div>
                <div class="engineer">
工程师: {{ row.assignedEngineer || '未分配' }}
</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column
label="报价信息" width="140"
>
            <template #default="{ row }">
              <div
v-if="row.quotation" class="quotation-info"
>
                <div class="quote-price">
¥{{ row.quotation.unitPrice }}/K pcs
</div>
                <div class="quote-total">
                  总额: ¥{{ (row.quotation.totalAmount / 10000).toFixed(0) }}万
                </div>
                <div class="quote-validity">
                  有效期至: {{ formatDate(row.quotation.validityPeriod) }}
                </div>
              </div>
              <div
v-else class="no-quotation"
>
                <el-text type="info">
未报价
</el-text>
              </div>
            </template>
          </el-table-column>

          <el-table-column
label="时间信息" width="180"
>
            <template #default="{ row }">
              <div class="time-info">
                <div class="inquiry-date">
询价: {{ formatDate(row.schedule.inquiryDate) }}
</div>
                <div class="expected-quote">
                  期望报价: {{ formatDate(row.schedule.expectedQuoteDate) }}
                </div>
                <div class="target-delivery">
                  目标交期: {{ formatDate(row.schedule.targetDeliveryDate) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column
label="操作" width="180"
fixed="right"
>
            <template #default="{ row }">
              <el-button-group size="small">
                <el-button type="primary"
@click="handleViewDetail(row)"
>
详情
</el-button>
                <el-button
                  v-if="row.status === 'pending'"
                  type="warning"
                  @click="handleStartEvaluation(row)"
                >
                  评估
                </el-button>
                <el-button
                  v-if="row.status === 'evaluating'"
                  type="success"
                  @click="handleQuote(row)"
                >
                  报价
                </el-button>
                <el-dropdown @command="command => handleDropdownCommand(command, row)">
                  <el-button size="small">
                    更多
                    <el-icon class="el-icon--right">
                      <ArrowDown />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">
编辑
</el-dropdown-item>
                      <el-dropdown-item command="duplicate">
复制
</el-dropdown-item>
                      <el-dropdown-item command="export">
导出
</el-dropdown-item>
                      <el-dropdown-item command="delete"
divided
>
删除
</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="inquiry-list__pagination">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="selectedInquiry?.inquiryNumber + ' - 询价详情'"
      size="60%"
      direction="rtl"
    >
      <div
v-if="selectedInquiry" class="inquiry-detail"
>
        <!-- 详情内容预留，后续实现 -->
        <el-descriptions
:column="2" border
>
          <el-descriptions-item label="询价编号">
            {{ selectedInquiry.inquiryNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="客户名称">
            {{ selectedInquiry.customer.name }}
          </el-descriptions-item>
          <el-descriptions-item label="产品名称">
            {{ selectedInquiry.productInfo.productName }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedInquiry.status)">
              {{ getStatusLabel(selectedInquiry.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    Plus,
    Download,
    Search,
    RefreshLeft,
    Refresh,
    ArrowDown,
    TrendCharts,
    User,
    Document,
    Clock
  } from '@element-plus/icons-vue'
  import type {
    CustomerInquiry,
    InquiryQueryParams,
    InquiryStatus,
    ProductType,
    PackageType,
    OrderPriority
  } from '@/types/order'
  import {
    mockGetInquiries,
    mockGetInquiryStats,
    mockUpdateInquiryStatus,
    mockDeleteInquiry,
    inquiryStatusOptions,
    productTypeOptions,
    packageTypeOptions,
    priorityOptions
  } from '@/utils/mockData/inquiry'

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<CustomerInquiry[]>([])
  const selectedRows = ref<CustomerInquiry[]>([])
  const drawerVisible = ref(false)
  const selectedInquiry = ref<CustomerInquiry | null>(null)

  // 搜索表单
  const searchForm = reactive<InquiryQueryParams>({
    inquiryNumber: '',
    customerName: '',
    status: [],
    priority: [],
    productType: [],
    packageType: [],
    inquiryDateRange: null
  })

  // 分页信息
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 统计数据
  const stats = ref({
    totalInquiries: 0,
    pendingInquiries: 0,
    quotedInquiries: 0,
    confirmedInquiries: 0,
    conversionRate: 0,
    averageQuoteTime: 0
  })

  // 选项数据
  const statusOptions = inquiryStatusOptions
  const productTypeOptions = productTypeOptions
  const packageTypeOptions = packageTypeOptions
  const priorityOptions = priorityOptions

  // 统计卡片数据
  const statsCards = computed(() => [
    {
      key: 'total',
      label: '总询价数',
      value: stats.value.totalInquiries,
      color: '#409EFF',
      icon: 'Document',
      trendIcon: 'ArrowUp',
      changePercent: '+12%',
      trendClass: 'trend-up'
    },
    {
      key: 'pending',
      label: '待评估',
      value: stats.value.pendingInquiries,
      color: '#E6A23C',
      icon: 'Clock',
      trendIcon: 'ArrowUp',
      changePercent: '+5%',
      trendClass: 'trend-up'
    },
    {
      key: 'quoted',
      label: '已报价',
      value: stats.value.quotedInquiries,
      color: '#67C23A',
      icon: 'TrendCharts',
      trendIcon: 'ArrowUp',
      changePercent: '+18%',
      trendClass: 'trend-up'
    },
    {
      key: 'conversion',
      label: '转化率',
      value: stats.value.conversionRate + '%',
      color: '#F56C6C',
      icon: 'User',
      trendIcon: 'ArrowDown',
      changePercent: '-2%',
      trendClass: 'trend-down'
    }
  ])

  // 初始化数据
  const loadData = async () => {
    try {
      loading.value = true
      const params: InquiryQueryParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...searchForm
      }
      const response = await mockGetInquiries(params)
      tableData.value = response.data
      pagination.total = response.total
    } catch (error) {
      ElMessage.error('数据加载失败')
      console.error('Load data error:', error)
    } finally {
      loading.value = false
    }
  }

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await mockGetInquiryStats()
      stats.value = response
    } catch (error) {
      console.error('Load stats error:', error)
    }
  }

  // 工具方法
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getStatusLabel = (status: InquiryStatus) => {
    const option = statusOptions.find(opt => opt.value === status)
    return option ? option.label : status
  }

  const getStatusType = (status: InquiryStatus) => {
    const typeMap: Record<InquiryStatus, string> = {
      pending: 'info',
      evaluating: 'warning',
      quoted: 'primary',
      confirmed: 'success',
      rejected: 'danger',
      expired: 'info'
    }
    return typeMap[status] || 'info'
  }

  const getStatusColor = (status: InquiryStatus) => {
    const option = statusOptions.find(opt => opt.value === status)
    return option ? option.color : '#909399'
  }

  const getPriorityLabel = (priority: OrderPriority) => {
    const option = priorityOptions.find(opt => opt.value === priority)
    return option ? option.label : priority
  }

  const getPriorityType = (priority: OrderPriority) => {
    const typeMap: Record<OrderPriority, string> = {
      low: 'info',
      medium: 'warning',
      high: 'danger',
      urgent: 'danger'
    }
    return typeMap[priority] || 'info'
  }

  // 事件处理
  const handleSearch = () => {
    pagination.current = 1
    loadData()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      inquiryNumber: '',
      customerName: '',
      status: [],
      priority: [],
      productType: [],
      packageType: [],
      inquiryDateRange: null
    })
    handleSearch()
  }

  const handleRefresh = () => {
    loadData()
    loadStats()
  }

  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.current = 1
    loadData()
  }

  const handleCurrentChange = (page: number) => {
    pagination.current = page
    loadData()
  }

  const handleSelectionChange = (selection: CustomerInquiry[]) => {
    selectedRows.value = selection
  }

  const handleSortChange = ({ column, prop, order }: any) => {
    // 排序逻辑
    console.log('Sort change:', { column, prop, order })
  }

  const handleCreateInquiry = () => {
    ElMessage.info('新增询价功能开发中...')
  }

  const handleExport = () => {
    ElMessage.info('导出功能开发中...')
  }

  const handleViewDetail = (inquiry: CustomerInquiry) => {
    selectedInquiry.value = inquiry
    drawerVisible.value = true
  }

  const handleStartEvaluation = async (inquiry: CustomerInquiry) => {
    try {
      await mockUpdateInquiryStatus(inquiry.id, 'evaluating' as InquiryStatus)
      ElMessage.success('已开始评估')
      loadData()
    } catch (error) {
      ElMessage.error('操作失败')
    }
  }

  const handleQuote = (inquiry: CustomerInquiry) => {
    // 跳转到报价单生成页面
    router.push(`/quotation/form/${inquiry.id}`)
  }

  const handleDropdownCommand = (command: string, inquiry: CustomerInquiry) => {
    switch (command) {
      case 'edit':
        ElMessage.info('编辑功能开发中...')
        break
      case 'duplicate':
        ElMessage.info('复制功能开发中...')
        break
      case 'export':
        ElMessage.info('导出功能开发中...')
        break
      case 'delete':
        handleDelete(inquiry)
        break
    }
  }

  const handleDelete = async (inquiry: CustomerInquiry) => {
    try {
      await ElMessageBox.confirm(`确定要删除询价 ${inquiry.inquiryNumber} 吗？`, '确认删除', {
        type: 'warning'
      })
      await mockDeleteInquiry(inquiry.id)
      ElMessage.success('删除成功')
      loadData()
      loadStats()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  const handleBatchEvaluate = async () => {
    const pendingIds = selectedRows.value.filter(row => row.status === 'pending').map(row => row.id)

    if (pendingIds.length === 0) {
      ElMessage.warning('请选择待评估的询价')
      return
    }

    try {
      await ElMessageBox.confirm(`确定要对 ${pendingIds.length} 条询价开始评估吗？`, '批量评估', {
        type: 'warning'
      })

      // 批量更新状态
      await Promise.all(
        pendingIds.map(id => mockUpdateInquiryStatus(id, 'evaluating' as InquiryStatus))
      )

      ElMessage.success(`已对 ${pendingIds.length} 条询价开始评估`)
      loadData()
      loadStats()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('批量评估失败')
      }
    }
  }

  const handleBatchDelete = async () => {
    const selectedIds = selectedRows.value.map(row => row.id)

    try {
      await ElMessageBox.confirm(`确定要删除选中的 ${selectedIds.length} 条询价吗？`, '批量删除', {
        type: 'warning'
      })

      // 批量删除
      await Promise.all(selectedIds.map(id => mockDeleteInquiry(id)))

      ElMessage.success(`已删除 ${selectedIds.length} 条询价`)
      loadData()
      loadStats()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('批量删除失败')
      }
    }
  }

  // 组件挂载时加载数据
  onMounted(() => {
    loadData()
    loadStats()
  })
</script>

<style lang="scss" scoped>
  .inquiry-list {
    min-height: 100vh;
    padding: var(--spacing-4);
    background-color: var(--color-bg-page);

    &__header {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      margin-bottom: var(--spacing-4);
    }

    &__title {
      h2 {
        margin: 0;
        font-size: var(--font-size-xl);
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }

    &__subtitle {
      margin: var(--spacing-1) 0 0;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    &__actions {
      display: flex;
      gap: var(--spacing-2);
    }

    &__stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
    }

    &__search {
      margin-bottom: var(--spacing-4);
    }

    &__table {
      margin-bottom: var(--spacing-4);
    }

    &__pagination {
      display: flex;
      justify-content: flex-end;
      padding-top: var(--spacing-4);
    }
  }

  // 统计卡片样式
  .stats-card {
    display: flex;
    align-items: center;
    padding: var(--spacing-4);
    background-color: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      margin-right: var(--spacing-3);
      color: white;
      border-radius: var(--radius-base);
    }

    &__content {
      flex: 1;
    }

    &__value {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      line-height: 1.2;
      color: var(--color-text-primary);
    }

    &__label {
      margin: var(--spacing-1) 0;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    &__trend {
      display: flex;
      align-items: center;
      font-size: var(--font-size-xs);
      font-weight: 500;

      &.trend-up {
        color: var(--color-success);
      }

      &.trend-down {
        color: var(--color-danger);
      }
    }
  }

  // 表格相关样式
  .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__actions {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;
    }
  }

  .status-option {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }
  }

  .inquiry-number-link {
    font-weight: 600;
    text-decoration: none;
  }

  .customer-info {
    .customer-name {
      margin-bottom: var(--spacing-1);
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .customer-code {
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
    }

    .customer-contact {
      font-size: var(--font-size-xs);
      color: var(--color-text-regular);
    }
  }

  .product-info {
    .product-name {
      margin-bottom: var(--spacing-1);
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .product-details {
      display: flex;
      gap: var(--spacing-1);
      margin-bottom: var(--spacing-1);
    }

    .product-quantity {
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
    }
  }

  .progress-info {
    font-size: var(--font-size-xs);

    .sales-manager,
    .engineer {
      margin-bottom: var(--spacing-1);
      color: var(--color-text-secondary);

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .quotation-info {
    font-size: var(--font-size-xs);

    .quote-price {
      margin-bottom: var(--spacing-1);
      font-weight: 600;
      color: var(--color-primary);
    }

    .quote-total,
    .quote-validity {
      margin-bottom: var(--spacing-1);
      color: var(--color-text-secondary);

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .no-quotation {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .time-info {
    font-size: var(--font-size-xs);

    .inquiry-date,
    .expected-quote,
    .target-delivery {
      margin-bottom: var(--spacing-1);
      color: var(--color-text-secondary);

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .inquiry-detail {
    padding: var(--spacing-4);
  }

  // 响应式设计
  @media (width <= 1200px) {
    .inquiry-list__stats {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (width <= 768px) {
    .inquiry-list {
      padding: var(--spacing-2);
    }

    .inquiry-list__header {
      flex-direction: column;
      gap: var(--spacing-3);
      align-items: flex-start;
    }

    .inquiry-list__stats {
      grid-template-columns: 1fr;
    }

    .stats-card {
      padding: var(--spacing-3);
    }
  }
</style>
