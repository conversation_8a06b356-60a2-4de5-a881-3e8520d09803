<template>
  <el-dialog
    :model-value="visible"
    title="批量导入客户"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:model-value="$emit('update:visible', $event)"
  >
    <div class="import-content">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" finish-status="success" class="import-steps">
        <el-step title="下载模板" description="下载Excel导入模板" />
        <el-step title="上传文件" description="上传填写好的Excel文件" />
        <el-step title="数据校验" description="校验数据格式和内容" />
        <el-step title="完成导入" description="确认导入客户数据" />
      </el-steps>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤1：下载模板 -->
        <div v-show="currentStep === 0" class="step-panel">
          <el-alert
title="导入说明" type="info"
:closable="false" show-icon
>
            <template #default>
              <ul class="import-tips">
                <li>请先下载客户数据导入模板，按照模板格式填写数据</li>
                <li>支持Excel文件格式（.xlsx, .xls）</li>
                <li>必填字段：客户名称、客户类型、联系人姓名、联系人邮箱、联系人电话</li>
                <li>客户编码如果为空，系统将自动生成</li>
                <li>单次最多支持导入500条客户记录</li>
              </ul>
            </template>
          </el-alert>

          <div class="template-download">
            <div class="download-info">
              <el-icon size="48" color="#409EFF">
                <DocumentAdd />
              </el-icon>
              <h4>客户数据导入模板</h4>
              <p>包含所有必填字段和可选字段的标准模板</p>
            </div>
            <el-button type="primary" size="large" @click="downloadTemplate">
              <el-icon><Download /></el-icon>
              下载模板
            </el-button>
          </div>

          <div class="field-reference">
            <h5>字段说明</h5>
            <el-table :data="fieldDescriptions" size="small" border>
              <el-table-column prop="field" label="字段名称" width="150" />
              <el-table-column prop="required" label="是否必填" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.required ? 'danger' : 'info'" size="small">
                    {{ row.required ? '必填' : '可选' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="说明" />
              <el-table-column prop="example" label="示例" width="120" />
            </el-table>
          </div>
        </div>

        <!-- 步骤2：上传文件 -->
        <div v-show="currentStep === 1" class="step-panel">
          <el-upload
            ref="uploadRef"
            class="import-upload"
            drag
            accept=".xlsx,.xls"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-icon class="el-icon--upload">
              <UploadFilled />
            </el-icon>
            <div class="el-upload__text">
              将Excel文件拖拽到此处，或
              <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
只能上传xlsx/xls文件，且不超过2MB
</div>
            </template>
          </el-upload>

          <div v-if="uploadFile" class="file-info">
            <el-descriptions title="文件信息" :column="2" border>
              <el-descriptions-item label="文件名">
                {{ uploadFile.name }}
              </el-descriptions-item>
              <el-descriptions-item label="文件大小">
                {{ formatFileSize(uploadFile.size) }}
              </el-descriptions-item>
              <el-descriptions-item label="上传时间">
                {{ formatDate(uploadFile.lastModified) }}
              </el-descriptions-item>
              <el-descriptions-item label="文件类型">
                {{ uploadFile.type || '未知' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 步骤3：数据校验 -->
        <div v-show="currentStep === 2" class="step-panel">
          <div v-if="validating" class="validation-loading">
            <el-loading-service v-loading="true" element-loading-text="正在校验数据...">
              <div style="height: 200px" />
            </el-loading-service>
          </div>

          <div v-else-if="validationResult" class="validation-result">
            <!-- 校验结果统计 -->
            <div class="validation-stats">
              <el-row :gutter="20">
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-value primary">
                      {{ validationResult.totalRows }}
                    </div>
                    <div class="stat-label">总记录数</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-value success">
                      {{ validationResult.validRows }}
                    </div>
                    <div class="stat-label">有效记录</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-value danger">
                      {{ validationResult.errorRows }}
                    </div>
                    <div class="stat-label">错误记录</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-value warning">
                      {{ validationResult.duplicateRows }}
                    </div>
                    <div class="stat-label">重复记录</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 错误详情 -->
            <div v-if="validationResult.errors.length > 0" class="error-details">
              <h5>
                <el-icon><WarnTriangleFilled /></el-icon>
                数据校验错误
              </h5>
              <el-table
:data="validationResult.errors" size="small"
max-height="300" border
>
                <el-table-column prop="row" label="行号" width="80" align="center" />
                <el-table-column prop="field" label="字段" width="120" />
                <el-table-column prop="value" label="错误值" width="150">
                  <template #default="{ row }">
                    <span class="error-value">{{ row.value || '(空)' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="message" label="错误描述" />
              </el-table>
            </div>

            <!-- 重复记录 -->
            <div v-if="validationResult.duplicates.length > 0" class="duplicate-details">
              <h5>
                <el-icon><Warning /></el-icon>
                重复记录处理
              </h5>
              <div class="duplicate-options">
                <el-radio-group v-model="duplicateHandling">
                  <el-radio label="skip">跳过重复记录</el-radio>
                  <el-radio label="update">更新已存在的记录</el-radio>
                  <el-radio label="create">创建为新记录（添加后缀）</el-radio>
                </el-radio-group>
              </div>
              <el-table
:data="validationResult.duplicates.slice(0, 5)" size="small"
border
>
                <el-table-column prop="row" label="行号" width="80" align="center" />
                <el-table-column prop="customerName" label="客户名称" width="200" />
                <el-table-column prop="existingCode" label="已存在编码" width="120" />
                <el-table-column prop="reason" label="重复原因" />
              </el-table>
              <div v-if="validationResult.duplicates.length > 5" class="more-duplicates">
                还有 {{ validationResult.duplicates.length - 5 }} 条重复记录...
              </div>
            </div>

            <!-- 预览数据 -->
            <div v-if="validationResult.preview.length > 0" class="data-preview">
              <h5>
                <el-icon><View /></el-icon>
                数据预览（前5条有效记录）
              </h5>
              <el-table
:data="validationResult.preview" size="small"
border max-height="300"
>
                <el-table-column prop="customerName" label="客户名称" width="200" />
                <el-table-column prop="customerType" label="客户类型" width="120" />
                <el-table-column prop="contactName" label="联系人" width="120" />
                <el-table-column prop="contactEmail" label="邮箱" width="180" />
                <el-table-column prop="contactPhone" label="电话" width="120" />
              </el-table>
            </div>
          </div>
        </div>

        <!-- 步骤4：完成导入 -->
        <div v-show="currentStep === 3" class="step-panel">
          <div v-if="importing" class="import-loading">
            <el-loading-service v-loading="true" element-loading-text="正在导入数据...">
              <div style="height: 200px" />
            </el-loading-service>
          </div>

          <div v-else-if="importResult" class="import-result">
            <div class="result-header">
              <el-result
                :icon="importResult.success ? 'success' : 'error'"
                :title="importResult.success ? '导入成功' : '导入失败'"
                :sub-title="importResult.message"
              />
            </div>

            <div v-if="importResult.success" class="success-stats">
              <el-descriptions title="导入统计" :column="2" border>
                <el-descriptions-item label="成功导入">
                  <span class="success-text">{{ importResult.successCount }} 条</span>
                </el-descriptions-item>
                <el-descriptions-item label="失败记录">
                  <span class="error-text">{{ importResult.failedCount }} 条</span>
                </el-descriptions-item>
                <el-descriptions-item label="跳过记录">
                  <span class="warning-text">{{ importResult.skippedCount }} 条</span>
                </el-descriptions-item>
                <el-descriptions-item label="总耗时">
                  {{ importResult.duration }}ms
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <div v-if="importResult.errors && importResult.errors.length > 0" class="import-errors">
              <h5>导入错误详情</h5>
              <el-table
:data="importResult.errors" size="small"
max-height="200" border
>
                <el-table-column prop="row" label="行号" width="80" align="center" />
                <el-table-column prop="customerName" label="客户名称" />
                <el-table-column prop="error" label="错误信息" />
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">
          {{ currentStep === 3 ? '关闭' : '取消' }}
        </el-button>

        <el-button v-if="currentStep > 0 && currentStep < 3"
@click="prevStep"
>
上一步
</el-button>

        <el-button
          v-if="currentStep < 3"
          type="primary"
          :disabled="!canNextStep"
          :loading="validating || importing"
          @click="nextStep"
        >
          {{ currentStep === 2 ? '开始导入' : '下一步' }}
        </el-button>

        <el-button
          v-if="currentStep === 3 && importResult?.success"
          type="success"
          @click="handleImportComplete"
        >
          完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  // Element Plus类型通过auto-import自动处理
  import type { CustomerImportData } from '@/types/customer'

  interface ValidationError {
    row: number
    field: string
    value: string
    message: string
  }

  interface DuplicateRecord {
    row: number
    customerName: string
    existingCode: string
    reason: string
  }

  interface ValidationResult {
    totalRows: number
    validRows: number
    errorRows: number
    duplicateRows: number
    errors: ValidationError[]
    duplicates: DuplicateRecord[]
    preview: CustomerImportData[]
  }

  interface ImportResult {
    success: boolean
    message: string
    successCount: number
    failedCount: number
    skippedCount: number
    duration: number
    errors?: Array<{
      row: number
      customerName: string
      error: string
    }>
  }

  interface Props {
    visible: boolean
  }

  interface Emits {
    (e: 'update:visible', visible: boolean): void
    (e: 'success', count: number): void
  }

  defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 上传相关
  const uploadRef = ref<any>()
  const fileList = ref<any[]>([])
  const uploadFile = ref<File | null>(null)

  // 步骤控制
  const currentStep = ref(0)
  const validating = ref(false)
  const importing = ref(false)
  const duplicateHandling = ref<'skip' | 'update' | 'create'>('skip')

  // 校验和导入结果
  const validationResult = ref<ValidationResult | null>(null)
  const importResult = ref<ImportResult | null>(null)

  // 字段说明
  const fieldDescriptions = [
    {
      field: '客户名称',
      required: true,
      description: '客户公司全称',
      example: '紫光展锐科技有限公司'
    },
    {
      field: '英文名称',
      required: false,
      description: '客户公司英文名称',
      example: 'Unisoc Technologies Co., Ltd.'
    },
    { field: '简称', required: false, description: '客户公司简称', example: '紫光展锐' },
    {
      field: '客户类型',
      required: true,
      description: 'Fabless IC设计公司/IDM制造商/Foundry代工厂等',
      example: 'Fabless IC设计公司'
    },
    {
      field: '客户等级',
      required: true,
      description: '战略客户/重要客户/标准客户/潜在客户',
      example: '重要客户'
    },
    {
      field: '客户规模',
      required: true,
      description: '初创企业/小型企业/中型企业/大型企业/跨国企业',
      example: '大型企业'
    },
    {
      field: '行业类型',
      required: true,
      description: '通信/消费电子/汽车电子/工业控制/AI芯片',
      example: '通信'
    },
    { field: '信用等级', required: true, description: 'A+/A/B+/B/C+/C/D', example: 'A' },
    {
      field: '应用领域',
      required: true,
      description: '主要应用领域，多个用逗号分隔',
      example: '通信,AI芯片,物联网'
    },
    {
      field: '工艺节点',
      required: true,
      description: '支持的工艺节点，多个用逗号分隔',
      example: '7nm,14nm,28nm'
    },
    {
      field: '封装偏好',
      required: true,
      description: '偏好的封装类型，多个用逗号分隔',
      example: 'BGA,QFN,WLCSP'
    },
    { field: '联系人姓名', required: true, description: '主要联系人姓名', example: '李志强' },
    {
      field: '联系人邮箱',
      required: true,
      description: '联系人邮箱地址',
      example: '<EMAIL>'
    },
    {
      field: '联系人电话',
      required: true,
      description: '联系人电话号码',
      example: '+86-21-6888-8888'
    },
    { field: '联系人职位', required: false, description: '联系人职位', example: '商务经理' },
    { field: '国家', required: true, description: '公司所在国家', example: '中国' },
    { field: '省份', required: true, description: '公司所在省份', example: '上海市' },
    { field: '城市', required: true, description: '公司所在城市', example: '上海市' },
    {
      field: '详细地址',
      required: true,
      description: '公司详细地址',
      example: '张江高科技园区祖冲之路887号'
    },
    {
      field: '官方网站',
      required: false,
      description: '公司官方网站',
      example: 'https://www.unisoc.com'
    },
    {
      field: '备注',
      required: false,
      description: '其他备注信息',
      example: '全球领先的移动通信芯片设计企业'
    }
  ]

  // 计算是否可以下一步
  const canNextStep = computed(() => {
    switch (currentStep.value) {
      case 0:
        return true // 第一步总是可以下一步
      case 1:
        return uploadFile.value !== null // 需要上传文件
      case 2:
        return validationResult.value && validationResult.value.validRows > 0 // 需要有有效数据
      default:
        return false
    }
  })

  // 下载模板
  const downloadTemplate = () => {
    ElMessage.info('模板下载功能开发中...')
  }

  // 处理文件变化
  const handleFileChange = (file: any, fileList: any[]) => {
    if (file.raw) {
      uploadFile.value = file.raw
    }
  }

  // 处理文件超出限制
  const handleExceed = () => {
    ElMessage.warning('只能上传一个文件')
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化日期
  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN')
  }

  // 上一步
  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--
    }
  }

  // 下一步
  const nextStep = async () => {
    if (currentStep.value === 1) {
      // 从步骤1到步骤2：开始数据校验
      await validateData()
    } else if (currentStep.value === 2) {
      // 从步骤2到步骤3：开始导入数据
      await importData()
    } else if (currentStep.value < 3) {
      currentStep.value++
    }
  }

  // 校验数据
  const validateData = async () => {
    if (!uploadFile.value) return

    validating.value = true
    currentStep.value = 2

    try {
      // 模拟读取和校验Excel文件
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 模拟校验结果
      const mockValidationResult: ValidationResult = {
        totalRows: 25,
        validRows: 20,
        errorRows: 3,
        duplicateRows: 2,
        errors: [
          {
            row: 3,
            field: '客户名称',
            value: '',
            message: '客户名称不能为空'
          },
          {
            row: 7,
            field: '联系人邮箱',
            value: 'invalid-email',
            message: '邮箱格式不正确'
          },
          {
            row: 15,
            field: '客户类型',
            value: '未知类型',
            message: '客户类型不在允许的值范围内'
          }
        ],
        duplicates: [
          {
            row: 10,
            customerName: '紫光展锐科技有限公司',
            existingCode: 'FAB1001',
            reason: '客户名称重复'
          },
          {
            row: 18,
            customerName: '瑞芯微电子股份有限公司',
            existingCode: 'FAB1002',
            reason: '客户名称重复'
          }
        ],
        preview: [
          {
            customerName: '全志科技股份有限公司',
            customerType: 'Fabless IC设计公司',
            customerLevel: '重要客户',
            customerScale: '大型企业',
            industryType: '通信电子',
            creditLevel: 'A',
            applicationFields: '移动设备,消费电子',
            processNodes: '28nm,40nm',
            packagePreferences: 'QFP,BGA',
            qualityStandard: 'JEDEC,IPC',
            complianceRequirements: 'RoHS,REACH',
            contactName: '张三',
            contactEmail: '<EMAIL>',
            contactPhone: '+86-756-3626-988',
            contactPosition: '销售经理',
            country: '中国',
            province: '广东省',
            city: '珠海市',
            address: '高新区创新海岸科技大厦'
          },
          {
            customerName: '晶晨半导体股份有限公司',
            customerType: 'Fabless IC设计公司',
            customerLevel: '标准客户',
            customerScale: '中型企业',
            industryType: '消费电子',
            creditLevel: 'B+',
            applicationFields: '智能电视,网络设备',
            processNodes: '22nm,28nm',
            packagePreferences: 'BGA,CSP',
            qualityStandard: 'JEDEC',
            complianceRequirements: 'RoHS',
            contactName: '李四',
            contactEmail: '<EMAIL>',
            contactPhone: '+86-21-6888-9999',
            contactPosition: '采购经理',
            country: '中国',
            province: '上海市',
            city: '上海市',
            address: '浦东新区张江高科技园区'
          }
          // ... 更多预览数据
        ]
      }

      validationResult.value = mockValidationResult
    } catch (error) {
      console.error('数据校验失败:', error)
      ElMessage.error('数据校验失败，请检查文件格式')
      currentStep.value = 1
    } finally {
      validating.value = false
    }
  }

  // 导入数据
  const importData = async () => {
    if (!validationResult.value) return

    importing.value = true
    currentStep.value = 3

    try {
      // 模拟导入过程
      await new Promise(resolve => setTimeout(resolve, 3000))

      // 模拟导入结果
      const mockImportResult: ImportResult = {
        success: true,
        message: '客户数据导入完成',
        successCount: 18,
        failedCount: 2,
        skippedCount: 2,
        duration: 3000,
        errors: [
          {
            row: 5,
            customerName: '测试客户A',
            error: '联系人邮箱已存在'
          },
          {
            row: 12,
            customerName: '测试客户B',
            error: '财务信息校验失败'
          }
        ]
      }

      importResult.value = mockImportResult
    } catch (error) {
      console.error('数据导入失败:', error)
      importResult.value = {
        success: false,
        message: '数据导入过程中发生错误',
        successCount: 0,
        failedCount: 0,
        skippedCount: 0,
        duration: 0
      }
    } finally {
      importing.value = false
    }
  }

  // 处理导入完成
  const handleImportComplete = () => {
    if (importResult.value?.success) {
      emit('success', importResult.value.successCount)
    }
    emit('update:visible', false)

    // 重置状态
    resetDialog()
  }

  // 重置对话框
  const resetDialog = () => {
    currentStep.value = 0
    fileList.value = []
    uploadFile.value = null
    validationResult.value = null
    importResult.value = null
    validating.value = false
    importing.value = false
    duplicateHandling.value = 'skip'
  }
</script>

<style lang="scss" scoped>
  .import-content {
    .import-steps {
      margin-bottom: 30px;
    }

    .step-content {
      min-height: 400px;

      .step-panel {
        .import-tips {
          padding-left: 20px;
          margin: 0;

          li {
            margin-bottom: 8px;
            color: var(--color-text-regular);
          }
        }

        .template-download {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20px;
          margin: 20px 0;
          border: 1px dashed var(--color-border-light);
          border-radius: 8px;

          .download-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;

            h4 {
              margin: 8px 0 4px;
              color: var(--color-text-primary);
            }

            p {
              margin: 0;
              font-size: 14px;
              color: var(--color-text-regular);
            }
          }
        }

        .field-reference {
          margin-top: 20px;

          h5 {
            margin: 0 0 12px;
            color: var(--color-text-primary);
          }
        }

        .import-upload {
          margin-bottom: 20px;
        }

        .file-info {
          margin-top: 20px;
        }

        .validation-stats {
          margin-bottom: 20px;

          .stat-item {
            padding: 16px;
            text-align: center;
            background: var(--color-bg-light);
            border-radius: 6px;

            .stat-value {
              margin-bottom: 4px;
              font-size: 24px;
              font-weight: 600;
              line-height: 1;

              &.primary {
                color: var(--color-primary);
              }

              &.success {
                color: var(--color-success);
              }

              &.danger {
                color: var(--color-danger);
              }

              &.warning {
                color: var(--color-warning);
              }
            }

            .stat-label {
              font-size: 12px;
              color: var(--color-text-secondary);
            }
          }
        }

        .error-details,
        .duplicate-details,
        .data-preview {
          margin-bottom: 20px;

          h5 {
            display: flex;
            gap: 8px;
            align-items: center;
            margin: 0 0 12px;
            color: var(--color-text-primary);
          }

          .error-value {
            font-style: italic;
            color: var(--color-danger);
          }

          .duplicate-options {
            margin-bottom: 12px;
          }

          .more-duplicates {
            margin-top: 8px;
            font-size: 12px;
            color: var(--color-text-secondary);
            text-align: center;
          }
        }

        .validation-loading,
        .import-loading {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .result-header {
          margin-bottom: 20px;
        }

        .success-stats {
          margin-bottom: 20px;

          .success-text {
            font-weight: 600;
            color: var(--color-success);
          }

          .error-text {
            font-weight: 600;
            color: var(--color-danger);
          }

          .warning-text {
            font-weight: 600;
            color: var(--color-warning);
          }
        }

        .import-errors {
          h5 {
            margin: 0 0 12px;
            color: var(--color-danger);
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }

  // 响应式调整
  @media (width <= 768px) {
    .import-content {
      .step-content {
        .step-panel {
          .template-download {
            flex-direction: column;
            gap: 16px;
          }

          .validation-stats {
            .el-col {
              margin-bottom: 12px;
            }
          }
        }
      }
    }
  }
</style>
