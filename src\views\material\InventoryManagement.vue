<template>
  <div class="inventory-management">
    <!-- 专业级页面头部 -->
    <div class="page-header">
      <div class="page-header__content">
        <div class="page-header__main">
          <h1 class="page-title">物料与库存管理</h1>
          <p class="page-subtitle">OSAT专业版 - 半导体专用仓库管理系统</p>
        </div>
        <div class="page-header__actions">
          <el-button :loading="materialLoading"
@click="handleRefresh"
>
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button
type="primary" @click="showTransactionModal = true"
>
            <el-icon><Plus /></el-icon>
            新建事务
          </el-button>
          <el-button
type="primary" @click="showInventoryAnalysis = true"
>
            <el-icon><TrendCharts /></el-icon>
            库存分析
          </el-button>
        </div>
      </div>
    </div>

    <!-- OSAT专业统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <!-- 库存总价值 -->
        <el-card class="stat-card stat-card--value">
          <div class="stat-header">
            <div class="stat-icon stat-icon--primary">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-trend stat-trend--up">
              <el-icon><ArrowUp /></el-icon>
              <span>+3.2%</span>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ formatCurrency(inventoryStats.totalValue) }}</div>
            <div class="stat-label">库存总价值</div>
            <div class="stat-detail">{{ inventoryStats.totalItems }}种物料在库</div>
          </div>
        </el-card>

        <!-- 库存预警 -->
        <el-card class="stat-card stat-card--warning">
          <div class="stat-header">
            <div class="stat-icon stat-icon--warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-trend stat-trend--stable">
              <el-icon><Minus /></el-icon>
              <span>持平</span>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ warningStats.totalWarnings }}
            </div>
            <div class="stat-label">库存预警项目</div>
            <div class="stat-detail">{{ warningStats.highUrgency }}项高优先级</div>
          </div>
        </el-card>

        <!-- ESD安全区域 -->
        <el-card class="stat-card stat-card--esd">
          <div class="stat-header">
            <div class="stat-icon stat-icon--info">
              <el-icon><Lightning /></el-icon>
            </div>
            <div class="stat-trend stat-trend--up">
              <el-icon><ArrowUp /></el-icon>
              <span>+1.5%</span>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatPercent(esdUtilization) }}%</div>
            <div class="stat-label">ESD安全区利用率</div>
            <div class="stat-detail">{{ activeZones }}个区域运行中</div>
          </div>
        </el-card>

        <!-- Die Bank管理 -->
        <el-card class="stat-card stat-card--die">
          <div class="stat-header">
            <div class="stat-icon stat-icon--success">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="stat-trend stat-trend--up">
              <el-icon><ArrowUp /></el-icon>
              <span>+2.8%</span>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ formatQuantity(dieBankStats.availableDies) }}
            </div>
            <div class="stat-label">可用Die数量</div>
            <div class="stat-detail">{{ dieBankStats.totalDieBanks }}个Die Bank</div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 筛选条件 -->
    <c-card class="filter-card">
      <div class="filter-form">
        <div class="filter-item">
          <label>物料分类</label>
          <c-select
            v-model="filterForm.category"
            :options="materialCategoryOptions"
            placeholder="选择分类"
          />
        </div>
        <div class="filter-item">
          <label>状态</label>
          <c-select
            v-model="filterForm.status"
            :options="materialStatusOptions"
            placeholder="选择状态"
          />
        </div>
        <div class="filter-item">
          <label>仓库区域</label>
          <c-select
            v-model="filterForm.warehouseZone"
            :options="warehouseZoneOptions"
            placeholder="选择区域"
          />
        </div>
        <div class="filter-item">
          <label>搜索</label>
          <c-input
            v-model="filterForm.search"
            placeholder="物料代码/名称/制造商"
            @keyup.enter="loadMaterials"
          />
        </div>
        <div class="filter-actions">
          <c-button type="primary"
@click="() => loadMaterials()"
>
查询
</c-button>
          <c-button @click="resetFilters">重置</c-button>
        </div>
      </div>

      <!-- 快速筛选 -->
      <div class="quick-filters">
        <c-button
          size="small"
          :type="filterForm.lowStock ? 'primary' : 'secondary'"
          @click="toggleFilter('lowStock')"
        >
          低库存预警
        </c-button>
        <c-button
          size="small"
          :type="filterForm.expired ? 'primary' : 'secondary'"
          @click="toggleFilter('expired')"
        >
          即将过期
        </c-button>
      </div>
    </c-card>

    <!-- 物料库存列表 -->
    <c-card class="inventory-card">
      <c-table
        v-model:current="pagination.current"
        v-model:page-size="pagination.pageSize"
        :data-source="[...materials]"
        :columns="materialColumns"
        :loading="materialLoading"
        :show-pagination="true"
        :total="pagination.total"
        class="inventory-table"
        @refresh="loadMaterials"
        @row-click="handleRowClick"
      >
        <!-- 物料信息列 -->
        <template #materialInfo="{ record }">
          <div class="material-info">
            <div class="material-code">
              {{ record.materialSpec.materialCode }}
            </div>
            <div class="material-name">
              {{ record.materialSpec.materialName }}
            </div>
            <div class="material-spec">
              {{ record.materialSpec.specification }}
            </div>
          </div>
        </template>

        <!-- 库存数量列 -->
        <template #stock="{ record }">
          <div class="stock-info">
            <div class="stock-current">
              <span class="stock-value">{{ record.currentStock }}</span>
              <span class="stock-unit">{{ record.unitOfMeasure }}</span>
            </div>
            <div class="stock-available">可用: {{ record.availableStock }}</div>
            <div v-if="record.reservedStock > 0"
class="stock-reserved"
>
              预留: {{ record.reservedStock }}
            </div>
          </div>
        </template>

        <!-- 库存状态列 -->
        <template #stockStatus="{ record }">
          <div class="stock-status">
            <el-tag :type="getStockStatusType(record)"
size="small"
>
              {{ getStockStatusText(record) }}
            </el-tag>
            <div class="stock-progress">
              <el-progress
                :percentage="getStockPercentage(record)"
                :color="getProgressColor(record)"
                :stroke-width="6"
                size="small"
              />
            </div>
          </div>
        </template>

        <!-- 存储区域列 -->
        <template #location="{ record }">
          <div class="location-info">
            <div class="zone-name">
              {{ record.warehouseZone.zoneName }}
            </div>
            <div class="zone-type">
              <el-tag
size="small" :type="getZoneTypeColor(record.warehouseZone.zoneType)"
>
                {{ getZoneTypeText(record.warehouseZone.zoneType) }}
              </el-tag>
            </div>
          </div>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <div class="table-actions">
            <el-button type="text"
size="small" @click="handleViewDetails(record)"
>
详情
</el-button>
            <el-button type="text"
size="small" @click="handleTransfer(record)"
>
调拨
</el-button>
            <el-button type="text"
size="small" @click="handleTransaction(record)"
>
事务
</el-button>
          </div>
        </template>
      </c-table>
    </c-card>

    <!-- 库存分析对话框 -->
    <el-dialog
      v-model="showInventoryAnalysis"
      title="库存分析报告"
      width="900px"
      :show-close="true"
    >
      <div class="analysis-content">
        <!-- 仓库利用率 -->
        <div class="analysis-section">
          <h4>仓库利用率分析</h4>
          <div class="warehouse-grid">
            <div v-for="zone in utilizationStats"
:key="zone.zoneId" class="warehouse-item"
>
              <div class="warehouse-header">
                <span class="warehouse-name">{{ zone.zoneName }}</span>
                <span class="warehouse-util">{{ zone.utilization }}%</span>
              </div>
              <el-progress
                :percentage="zone.utilization"
                :color="
                  zone.utilizationTrend === 'HIGH'
                    ? '#f56c6c'
                    : zone.utilizationTrend === 'MEDIUM'
                      ? '#e6a23c'
                      : '#67c23a'
                "
              />
              <div class="warehouse-recommend">
                {{ zone.recommendation }}
              </div>
            </div>
          </div>
        </div>

        <!-- Die Bank状态 -->
        <div class="analysis-section">
          <h4>Die Bank管理状态</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="die-stats">
                <div class="stat-item">
                  <span class="label">总Die Bank数:</span>
                  <span class="value">{{ dieBankStats.totalDieBanks }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">总Die数量:</span>
                  <span class="value">{{ formatQuantity(dieBankStats.totalDies) }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">可用Die数:</span>
                  <span class="value">{{ formatQuantity(dieBankStats.availableDies) }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">利用率:</span>
                  <span class="value">{{ dieBankStats.utilizationRate.toFixed(1) }}%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="die-banks">
                <div v-for="log in dieBankAccessLog"
:key="log.dieBankId" class="die-bank-item"
>
                  <div class="die-info">
                    <span class="die-id">{{ log.dieBankId }}</span>
                    <span class="die-type">{{ log.dieType }}</span>
                  </div>
                  <div class="die-status">
                    <el-tag
                      size="small"
                      :type="log.storageCondition.status === 'NORMAL' ? 'success' : 'warning'"
                    >
                      {{ log.storageCondition.status }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>

    <!-- 物料详情对话框 -->
    <el-dialog
      v-model="showDetailModal"
      :title="currentMaterial?.materialSpec.materialName || '物料详情'"
      width="800px"
    >
      <div
v-if="currentMaterial" class="material-detail"
>
        <el-descriptions
:column="2" border
>
          <el-descriptions-item label="物料代码">
            {{ currentMaterial.materialSpec.materialCode }}
          </el-descriptions-item>
          <el-descriptions-item label="物料名称">
            {{ currentMaterial.materialSpec.materialName }}
          </el-descriptions-item>
          <el-descriptions-item label="规格型号">
            {{ currentMaterial.materialSpec.specification }}
          </el-descriptions-item>
          <el-descriptions-item label="制造商">
            {{ currentMaterial.materialSpec.manufacturer }}
          </el-descriptions-item>
          <el-descriptions-item label="批次号">
            {{ currentMaterial.materialSpec.lotNumber || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="有效期">
            {{ currentMaterial.materialSpec.expiryDate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="当前库存">
            {{ currentMaterial.currentStock }} {{ currentMaterial.unitOfMeasure }}
          </el-descriptions-item>
          <el-descriptions-item label="可用库存">
            {{ currentMaterial.availableStock }} {{ currentMaterial.unitOfMeasure }}
          </el-descriptions-item>
          <el-descriptions-item label="预留库存">
            {{ currentMaterial.reservedStock }} {{ currentMaterial.unitOfMeasure }}
          </el-descriptions-item>
          <el-descriptions-item label="再订货点">
            {{ currentMaterial.reorderPoint }} {{ currentMaterial.unitOfMeasure }}
          </el-descriptions-item>
          <el-descriptions-item label="单位成本">
            ¥{{ currentMaterial.unitCost.toFixed(2) }}
          </el-descriptions-item>
          <el-descriptions-item label="总价值">
            ¥{{ currentMaterial.totalValue.toFixed(2) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import {
    useMaterialInventory,
    useWarehouseManagement,
    useDieBankManagement,
    useInventoryWarnings
  } from '@/composables/useMaterialManagement'
  import type { MaterialInventory, MaterialCategory, MaterialStatus } from '@/types/material'
  import { CButton, CCard, CInput, CSelect, CTable } from '@/components/base'
  import type { CTableColumn } from '@/components/base'

  // 组合函数
  const {
    loading: materialLoading,
    materials,
    totalCount,
    loadMaterials,
    getInventoryStats,
    transferMaterial
  } = useMaterialInventory()

  const { zones, getUtilizationStats } = useWarehouseManagement()

  const { dieBanks, getDieBankStats, getDieBankAccessLog } = useDieBankManagement()

  const { warnings, getWarningStats } = useInventoryWarnings()

  // 响应式数据
  const showInventoryAnalysis = ref(false)
  const showDetailModal = ref(false)
  const showTransactionModal = ref(false)
  const currentMaterial = ref<MaterialInventory | null>(null)

  // 筛选表单
  const filterForm = reactive({
    category: '',
    status: '',
    warehouseZone: '',
    search: '',
    lowStock: false,
    expired: false
  })

  // 分页
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0
  })

  // 计算属性
  const inventoryStats = computed(() => getInventoryStats())
  const warningStats = computed(() => getWarningStats())
  const dieBankStats = computed(() => getDieBankStats())
  const utilizationStats = computed(() => getUtilizationStats())
  const dieBankAccessLog = computed(() => getDieBankAccessLog())

  const esdUtilization = computed(() => {
    const esdZone = zones.value.find(z => z.zoneType === 'ESD_SAFE')
    return esdZone?.currentUtilization || 0
  })

  const activeZones = computed(() => {
    return zones.value.length
  })

  // 表格列配置
  const materialColumns: CTableColumn[] = [
    {
      key: 'materialInfo',
      title: '物料信息',
      width: 280,
      fixed: 'left'
    },
    {
      key: 'category',
      title: '分类',
      dataIndex: 'category',
      width: 120
    },
    {
      key: 'stock',
      title: '库存数量',
      width: 160
    },
    {
      key: 'stockStatus',
      title: '库存状态',
      width: 150
    },
    {
      key: 'location',
      title: '存储位置',
      width: 180
    },
    {
      key: 'unitCost',
      title: '单位成本',
      dataIndex: 'unitCost',
      width: 100,
      align: 'right'
    },
    {
      key: 'totalValue',
      title: '总价值',
      dataIndex: 'totalValue',
      width: 120,
      align: 'right'
    },
    {
      key: 'actions',
      title: '操作',
      width: 200,
      fixed: 'right'
    }
  ]

  // 选项数据
  const materialCategoryOptions = [
    { label: '全部', value: '' },
    { label: '晶圆', value: 'WAFER' },
    { label: 'Die Bank', value: 'DIE_BANK' },
    { label: '引脚框架', value: 'LEADFRAME' },
    { label: '基板', value: 'SUBSTRATE' },
    { label: '封装材料', value: 'PACKAGING_MATERIAL' },
    { label: '化学品', value: 'CHEMICAL' },
    { label: '易耗品', value: 'CONSUMABLE' },
    { label: '胶粘剂', value: 'ADHESIVE' },
    { label: '键合金线', value: 'WIRE_BOND' },
    { label: '塑封料', value: 'MOLD_COMPOUND' }
  ]

  const materialStatusOptions = [
    { label: '全部', value: '' },
    { label: '来料', value: 'INCOMING' },
    { label: '已检验', value: 'INSPECTED' },
    { label: '已放行', value: 'APPROVED' },
    { label: '使用中', value: 'IN_USE' },
    { label: '已预留', value: 'RESERVED' },
    { label: '隔离', value: 'QUARANTINE' },
    { label: '过期', value: 'EXPIRED' }
  ]

  const warehouseZoneOptions = computed(() => [
    { label: '全部', value: '' },
    ...zones.value.map(zone => ({
      label: zone.zoneName,
      value: zone.zoneId
    }))
  ])

  // 方法
  const handleRefresh = () => {
    loadMaterials()
  }

  const resetFilters = () => {
    Object.keys(filterForm).forEach(key => {
      const typedKey = key as keyof typeof filterForm
      if (typeof filterForm[typedKey] === 'boolean') {
        ;(filterForm[typedKey] as boolean) = false
      } else {
        ;(filterForm[typedKey] as string) = ''
      }
    })
    loadMaterials()
  }

  const toggleFilter = (key: keyof typeof filterForm) => {
    if (typeof filterForm[key] === 'boolean') {
      ;(filterForm[key] as boolean) = !(filterForm[key] as boolean)
    }
    loadMaterials()
  }

  const handleRowClick = (record: MaterialInventory) => {
    console.log('点击物料:', record)
  }

  const handleViewDetails = (record: MaterialInventory) => {
    currentMaterial.value = record
    showDetailModal.value = true
  }

  const handleTransfer = (record: MaterialInventory) => {
    console.log('调拨物料:', record)
  }

  const handleTransaction = (record: MaterialInventory) => {
    console.log('创建事务:', record)
  }

  // 辅助方法
  const formatCurrency = (value: number) => {
    return (value / 10000).toFixed(1) + '万'
  }

  const formatQuantity = (value: number) => {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M'
    } else if (value >= 1000) {
      return (value / 1000).toFixed(0) + 'K'
    }
    return value.toString()
  }

  const formatPercent = (value: number) => {
    return Math.round(value)
  }

  const getStockStatusType = (record: MaterialInventory) => {
    if (record.availableStock <= record.reorderPoint * 0.5) {
      return 'danger'
    } else if (record.availableStock <= record.reorderPoint) {
      return 'warning'
    }
    return 'success'
  }

  const getStockStatusText = (record: MaterialInventory) => {
    if (record.availableStock <= record.reorderPoint * 0.5) {
      return '紧急补货'
    } else if (record.availableStock <= record.reorderPoint) {
      return '低库存'
    }
    return '正常'
  }

  const getStockPercentage = (record: MaterialInventory) => {
    return Math.round((record.availableStock / record.maxStock) * 100)
  }

  const getProgressColor = (record: MaterialInventory) => {
    const percentage = (record.availableStock / record.maxStock) * 100
    if (percentage <= 25) return '#f56c6c'
    if (percentage <= 50) return '#e6a23c'
    return '#67c23a'
  }

  const getZoneTypeColor = (zoneType: string) => {
    switch (zoneType) {
      case 'ESD_SAFE':
        return 'primary'
      case 'TEMP_CONTROLLED':
        return 'warning'
      case 'CLEAN_ROOM':
        return 'success'
      case 'CHEMICAL':
        return 'danger'
      default:
        return 'info'
    }
  }

  const getZoneTypeText = (zoneType: string) => {
    switch (zoneType) {
      case 'ESD_SAFE':
        return 'ESD安全'
      case 'TEMP_CONTROLLED':
        return '温控区'
      case 'CLEAN_ROOM':
        return '洁净室'
      case 'CHEMICAL':
        return '化学品区'
      default:
        return zoneType
    }
  }

  // 生命周期
  onMounted(() => {
    loadMaterials()
  })
</script>

<style lang="scss" scoped>
  .inventory-management {
    padding: var(--spacing-6);
  }

  .page-header {
    @include flex-between;
    margin-bottom: var(--spacing-6);

    &__content {
      @include flex-between;
      width: 100%;
    }

    &__main {
      .page-title {
        margin: 0 0 var(--spacing-1) 0;
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
      }

      .page-subtitle {
        margin: 0;
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    &__actions {
      display: flex;
      gap: var(--spacing-3);
    }
  }

  // OSAT专业统计卡片
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
  }

  .stat-card {
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);

    &:hover {
      border-color: var(--color-primary);
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .stat-header {
      @include flex-between;
      margin-bottom: var(--spacing-3);
    }

    .stat-content {
      text-align: center;
    }

    .stat-value {
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
    }

    .stat-label {
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .stat-detail {
      font-size: var(--font-size-xs);
      color: var(--color-text-tertiary);
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      font-size: 20px;
      border-radius: var(--radius-lg);

      @include flex-center;

      &--primary {
        color: var(--color-primary);
        background: var(--color-primary-light);
      }

      &--warning {
        color: var(--color-warning);
        background: var(--color-warning-light);
      }

      &--info {
        color: var(--color-info);
        background: var(--color-info-light);
      }

      &--success {
        color: var(--color-success);
        background: var(--color-success-light);
      }
    }

    .stat-trend {
      @include flex-center;
      gap: 4px;
      font-size: var(--font-size-xs);

      &--up {
        color: var(--color-success);
      }

      &--down {
        color: var(--color-error);
      }

      &--stable {
        color: var(--color-text-secondary);
      }
    }
  }

  // 筛选卡片
  .filter-card {
    margin-bottom: var(--spacing-6);

    .filter-form {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);

      .filter-item {
        label {
          display: block;
          margin-bottom: var(--spacing-1);
          font-size: var(--font-size-sm);
          color: var(--color-text-secondary);
        }
      }

      .filter-actions {
        display: flex;
        gap: var(--spacing-2);
        align-items: end;
      }
    }

    .quick-filters {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
    }
  }

  // 库存表格
  .inventory-table {
    .material-info {
      .material-code {
        margin-bottom: var(--spacing-1);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
      }

      .material-name {
        margin-bottom: var(--spacing-1);
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }

      .material-spec {
        font-size: var(--font-size-xs);
        color: var(--color-text-tertiary);
      }
    }

    .stock-info {
      .stock-current {
        margin-bottom: var(--spacing-1);
        font-weight: var(--font-weight-bold);

        .stock-value {
          margin-right: var(--spacing-1);
          color: var(--color-text-primary);
        }

        .stock-unit {
          font-size: var(--font-size-xs);
          color: var(--color-text-secondary);
        }
      }

      .stock-available,
      .stock-reserved {
        font-size: var(--font-size-xs);
        color: var(--color-text-tertiary);
      }
    }

    .stock-status {
      .stock-progress {
        margin-top: var(--spacing-2);
      }
    }

    .location-info {
      .zone-name {
        margin-bottom: var(--spacing-1);
        font-size: var(--font-size-sm);
      }
    }
  }

  // 分析对话框
  .analysis-content {
    .analysis-section {
      margin-bottom: var(--spacing-6);

      h4 {
        padding-bottom: var(--spacing-2);
        margin: 0 0 var(--spacing-4) 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
        border-bottom: 2px solid var(--color-primary);
      }
    }

    .warehouse-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-4);
    }

    .warehouse-item {
      padding: var(--spacing-4);
      border: 1px solid var(--color-border-light);
      border-radius: var(--radius-base);

      .warehouse-header {
        @include flex-between;
        margin-bottom: var(--spacing-2);

        .warehouse-name {
          font-weight: var(--font-weight-medium);
        }

        .warehouse-util {
          font-weight: var(--font-weight-bold);
          color: var(--color-primary);
        }
      }

      .warehouse-recommend {
        margin-top: var(--spacing-2);
        font-size: var(--font-size-xs);
        color: var(--color-text-secondary);
      }
    }

    .die-stats {
      .stat-item {
        @include flex-between;
        padding: var(--spacing-2);
        border-bottom: 1px solid var(--color-border-light);

        .label {
          color: var(--color-text-secondary);
        }

        .value {
          font-weight: var(--font-weight-bold);
          color: var(--color-text-primary);
        }
      }
    }

    .die-banks {
      max-height: 200px;
      overflow-y: auto;

      .die-bank-item {
        @include flex-between;
        padding: var(--spacing-2);
        border-bottom: 1px solid var(--color-border-light);

        .die-info {
          .die-id {
            display: block;
            margin-bottom: var(--spacing-1);
            font-weight: var(--font-weight-medium);
          }

          .die-type {
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
          }
        }
      }
    }
  }

  // 响应式
  @media (width <= 768px) {
    .inventory-management {
      padding: var(--spacing-4);
    }

    .page-header__content {
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .filter-form {
      grid-template-columns: 1fr !important;
    }
  }
</style>
