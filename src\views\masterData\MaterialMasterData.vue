<template>
  <div class="material-master-data">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="page-title">
        <h2>物料主数据管理</h2>
        <p class="page-description">
          IC封装测试过程中使用的所有物料的标准化管理，支持BOM管理和采购计划
        </p>
      </div>
      <div class="page-actions">
        <el-button type="success" @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button type="info" @click="exportMaterials">
          <el-icon><Download /></el-icon>
          数据导出
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增物料
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索物料编码、名称、规格型号"
              clearable
              @clear="resetSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>

          <el-col :span="4">
            <el-select
              v-model="searchForm.category"
              placeholder="物料分类"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="option in MATERIAL_CATEGORY_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
                <div class="category-option">
                  <el-icon :style="{ color: option.color }">
                    <component :is="option.icon" />
                  </el-icon>
                  <span>{{ option.label }}</span>
                </div>
              </el-option>
            </el-select>
          </el-col>

          <el-col :span="4">
            <el-select
              v-model="searchForm.status"
              placeholder="物料状态"
              clearable
              multiple
              collapse-tags
            >
              <el-option
                v-for="option in MATERIAL_STATUS_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-col>

          <el-col :span="4">
            <el-select
              v-model="searchForm.manufacturer"
              placeholder="制造商"
              clearable
              multiple
              collapse-tags
              filterable
            >
              <el-option
v-for="mfr in manufacturerList" :key="mfr"
:label="mfr" :value="mfr"
/>
            </el-select>
          </el-col>

          <el-col :span="6">
            <el-button type="primary" @click="searchMaterials">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button
type="text" @click="showAdvancedSearch = !showAdvancedSearch"
>
              {{ showAdvancedSearch ? '收起' : '高级筛选' }}
              <el-icon>
                <component :is="showAdvancedSearch ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </el-col>
        </el-row>

        <!-- 高级搜索 -->
        <div v-show="showAdvancedSearch" class="advanced-search">
          <el-divider content-position="left">高级筛选</el-divider>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select
                v-model="searchForm.packageType"
                placeholder="封装类型"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="option in PACKAGE_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-col>

            <el-col :span="6">
              <el-select
                v-model="searchForm.waferSize"
                placeholder="晶圆尺寸"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="option in WAFER_SIZE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-col>

            <el-col :span="6">
              <el-select
                v-model="searchForm.wireType"
                placeholder="线材类型"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="option in WIRE_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                >
                  <div class="wire-option">
                    <span
class="wire-color" :style="{ backgroundColor: option.color }" />
                    <span>{{ option.label }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-col>

            <el-col :span="6">
              <div class="price-range">
                <el-input
                  v-model.number="searchForm.priceRange.min"
                  placeholder="最低价格"
                  type="number"
                  style="width: 48%"
                />
                <span style="margin: 0 2%">-</span>
                <el-input
                  v-model.number="searchForm.priceRange.max"
                  placeholder="最高价格"
                  type="number"
                  style="width: 48%"
                />
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 16px">
            <el-col :span="6">
              <el-checkbox v-model="searchForm.hasAlternatives">有替代料</el-checkbox>
            </el-col>
            <el-col :span="6">
              <el-checkbox v-model="searchForm.isLowStock">低库存预警</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-date-picker
                v-model="searchForm.createdDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="创建开始日期"
                end-placeholder="创建结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <!-- 数据统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-item">
              <div class="stats-icon primary">
                <el-icon><Box /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ materialStats.total }}
                </div>
                <div class="stats-label">总物料数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-item">
              <div class="stats-icon success">
                <el-icon><CircleCheckFilled /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ materialStats.active }}
                </div>
                <div class="stats-label">正常物料</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-item">
              <div class="stats-icon warning">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ materialStats.riskCount }}
                </div>
                <div class="stats-label">风险预警</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-item">
              <div class="stats-icon info">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ materialStats.thisMonth }}
                </div>
                <div class="stats-label">本月新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 物料列表表格 -->
    <el-card shadow="never" class="table-card">
      <template #header>
        <div class="table-header">
          <span class="table-title">物料列表 ({{ pagination.total }})</span>
          <div class="table-actions">
            <el-button-group>
              <el-button
                size="small"
                :type="tableView === 'table' ? 'primary' : ''"
                @click="tableView = 'table'"
              >
                <el-icon><Menu /></el-icon>
                列表视图
              </el-button>
              <el-button
                size="small"
                :type="tableView === 'category' ? 'primary' : ''"
                @click="tableView = 'category'"
              >
                <el-icon><Grid /></el-icon>
                分类视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-show="tableView === 'table'">
        <el-table
          v-loading="loading"
          :data="materialList"
          stripe
          border
          height="600"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="50" />

          <el-table-column prop="basicInfo.materialCode" label="物料编码" width="150" fixed="left">
            <template #default="{ row }">
              <div class="material-code">
                <span class="code-text">{{ row.basicInfo.materialCode }}</span>
                <el-tag
                  v-if="row.alternatives.length > 0"
                  size="small"
                  type="warning"
                  style="margin-left: 8px"
                >
                  有替代料
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="物料信息" width="300" fixed="left">
            <template #default="{ row }">
              <div class="material-info">
                <div class="material-name">
                  {{ row.basicInfo.materialName }}
                </div>
                <div class="material-english">
                  {{ row.basicInfo.englishName }}
                </div>
                <div class="material-spec">
                  {{ row.basicInfo.specification }}
                </div>
                <div class="material-manufacturer">
                  {{ row.basicInfo.manufacturer }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="分类" width="120" align="center">
            <template #default="{ row }">
              <el-tag
                size="small"
                :color="getCategoryColor(row.basicInfo.category)"
                style="color: white"
              >
                {{ getCategoryLabel(row.basicInfo.category) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag
:type="getStatusTagType(row.basicInfo.status)" size="small"
>
                {{ getStatusLabel(row.basicInfo.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="技术参数" width="200">
            <template #default="{ row }">
              <div class="tech-params">
                <!-- 晶圆信息 -->
                <div v-if="row.technicalParams.waferInfo">
                  <el-tag size="small" type="info">
                    {{ row.technicalParams.waferInfo.waferSize }}寸
                  </el-tag>
                  <span class="param-text">
                    厚度: {{ row.technicalParams.waferInfo.thickness }}μm
                  </span>
                </div>
                <!-- 封装信息 -->
                <div v-if="row.technicalParams.packageInfo">
                  <el-tag size="small" type="success">
                    {{ row.technicalParams.packageInfo.packageType?.toUpperCase() }}
                  </el-tag>
                  <span class="param-text">{{ row.technicalParams.packageInfo.pinCount }}Pin</span>
                </div>
                <!-- 线材信息 -->
                <div v-if="row.technicalParams.wirebondInfo">
                  <span
                    class="wire-indicator"
                    :style="{
                      backgroundColor: getWireColor(row.technicalParams.wirebondInfo.wireType)
                    }"
                  />
                  <span class="param-text">{{ row.technicalParams.wirebondInfo.diameter }}μm</span>
                </div>
                <!-- 尺寸信息 -->
                <div v-if="row.technicalParams.dimensions && !row.technicalParams.waferInfo">
                  <span class="param-text">
                    {{ formatDimensions(row.technicalParams.dimensions) }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="库存信息" width="150" align="center">
            <template #default="{ row }">
              <div class="stock-info">
                <div class="stock-value">
                  安全库存: {{ row.stockInfo.safetyStock }} {{ row.stockInfo.unitOfMeasure }}
                </div>
                <div class="stock-location">
                  {{ row.stockInfo.storageLocation }}
                </div>
                <div
                  v-if="row.stockInfo.shelfLife && row.stockInfo.shelfLife < 9999"
                  class="shelf-life"
                >
                  保质期: {{ row.stockInfo.shelfLife }}天
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="成本信息" width="150" align="center">
            <template #default="{ row }">
              <div class="cost-info">
                <div class="current-price">
${{ row.costInfo.currentPrice.toLocaleString() }}
</div>
                <div class="cost-unit">/ {{ row.costInfo.priceUnit }}</div>
                <div class="price-trend" :class="getPriceTrend(row.costInfo)">
                  {{ getPriceTrendText(row.costInfo) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="主供应商" width="180">
            <template #default="{ row }">
              <div class="supplier-info">
                <div class="supplier-name">
                  {{ getPrimarySupplier(row.suppliers)?.supplierName || '-' }}
                </div>
                <div v-if="getPrimarySupplier(row.suppliers)" class="supplier-details">
                  <span class="lead-time">
                    周期: {{ getPrimarySupplier(row.suppliers).leadTime }}天
                  </span>
                  <span class="quality-rating">
                    质量: {{ getPrimarySupplier(row.suppliers).qualityRating }}%
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="更新时间" width="160" align="center">
            <template #default="{ row }">
              {{ formatDate(row.auditInfo.updatedAt) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button size="small" @click.stop="viewMaterial(row)">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button size="small" @click.stop="editMaterial(row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button size="small" @click.stop="showDuplicateDialog(row)">
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
                <el-button size="small" @click.stop="viewAlternatives(row)">
                  <el-icon><Connection /></el-icon>
                </el-button>
                <el-button
size="small" type="danger"
@click.stop="deleteMaterial(row)"
>
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分类视图 -->
      <div v-show="tableView === 'category'" class="category-view">
        <div
          v-for="category in MATERIAL_CATEGORY_OPTIONS"
          :key="category.value"
          class="category-section"
        >
          <div class="category-header">
            <el-icon :style="{ color: category.color }">
              <component :is="category.icon" />
            </el-icon>
            <span class="category-title">{{ category.label }}</span>
            <el-tag size="small" type="info">
              {{ getCategoryCount(category.value) }}
            </el-tag>
          </div>

          <el-row :gutter="16" style="margin-top: 16px">
            <el-col
              v-for="material in getMaterialsByCategory(category.value)"
              :key="material.id"
              :span="8"
              style="margin-bottom: 16px"
            >
              <MaterialCard
                :material="material"
                @view="viewMaterial"
                @edit="editMaterial"
                @delete="deleteMaterial"
                @alternatives="viewAlternatives"
              />
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange"
        />
      </div>
    </el-card>

    <!-- 批量操作浮动菜单 -->
    <div v-show="selectedMaterials.length > 0" class="batch-actions">
      <div class="batch-info">
已选择 {{ selectedMaterials.length }} 个物料
</div>
      <div class="batch-buttons">
        <el-button
size="small" @click="batchExport">批量导出</el-button>
        <el-button
size="small" type="warning" @click="batchUpdateStatus">批量更新状态</el-button>
        <el-button
size="small" type="info" @click="batchUpdateSupplier">批量更新供应商</el-button>
        <el-button
size="small" type="danger" @click="batchDelete">批量删除</el-button>
        <el-button
size="small" @click="clearSelection">取消选择</el-button>
      </div>
    </div>

    <!-- 物料详情对话框 -->
    <MaterialDetailDialog
v-model:visible="showDetailDialog" :material="currentMaterial"
/>

    <!-- 新增/编辑物料对话框 -->
    <MaterialFormDialog
      v-model:visible="showCreateDialog"
      :material="currentMaterial"
      :mode="dialogMode"
      @save="handleSaveMaterial"
    />

    <!-- 替代料管理对话框 -->
    <AlternativeDialog
      v-model:visible="showAlternativeDialog"
      :material="currentMaterial"
      @save="handleSaveAlternatives"
    />

    <!-- 导入对话框 -->
    <MaterialImportDialog
v-model:visible="showImportDialog" @success="handleImportSuccess"
/>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import type {
    MaterialMaster,
    MaterialMasterQueryParams,
    CreateMaterialMasterData,
    UpdateMaterialMasterData,
    MaterialMasterCategory,
    MaterialStatus,
    PackageType,
    WaferSize,
    WireType,
    SupplierInfo
  } from '@/types/materialMaster'
  import {
    mockMaterialMasters,
    MATERIAL_CATEGORY_OPTIONS,
    MATERIAL_STATUS_OPTIONS,
    PACKAGE_TYPE_OPTIONS,
    WAFER_SIZE_OPTIONS,
    WIRE_TYPE_OPTIONS,
    mockMaterialRiskWarnings,
    generateNewMaterialCode,
    getMaterialCategoryStats
  } from '@/utils/mockData/materialMaster'
  import MaterialCard from '@/components/business/MaterialCard.vue'
  import MaterialDetailDialog from '@/components/business/MaterialDetailDialog.vue'
  import MaterialFormDialog from '@/components/business/MaterialFormDialog.vue'
  import AlternativeDialog from '@/components/business/AlternativeDialog.vue'
  import MaterialImportDialog from '@/components/business/MaterialImportDialog.vue'

  // 页面状态
  const loading = ref(false)
  const showAdvancedSearch = ref(false)
  const tableView = ref<'table' | 'category'>('table')

  // 搜索表单
  const searchForm = reactive<MaterialMasterQueryParams>({
    keyword: '',
    category: [],
    status: [],
    manufacturer: [],
    packageType: [],
    waferSize: [],
    wireType: [],
    priceRange: { min: 0, max: 0 },
    createdDateRange: undefined,
    hasAlternatives: false,
    isLowStock: false,
    page: 1,
    pageSize: 20
  })

  // 物料列表数据
  const materialList = ref<MaterialMaster[]>([])
  const selectedMaterials = ref<MaterialMaster[]>([])

  // 分页数据
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 对话框状态
  const showCreateDialog = ref(false)
  const showDetailDialog = ref(false)
  const showAlternativeDialog = ref(false)
  const showImportDialog = ref(false)
  const currentMaterial = ref<MaterialMaster | null>(null)
  const dialogMode = ref<'create' | 'edit'>('create')

  // 制造商列表
  const manufacturerList = computed(() => {
    return Array.from(new Set(mockMaterialMasters.map(m => m.basicInfo.manufacturer)))
  })

  // 物料统计数据
  const materialStats = computed(() => {
    const allMaterials = mockMaterialMasters
    return {
      total: allMaterials.length,
      active: allMaterials.filter(m => m.basicInfo.status === 'active').length,
      riskCount: mockMaterialRiskWarnings.length,
      thisMonth: allMaterials.filter(m => {
        const created = new Date(m.auditInfo.createdAt)
        const now = new Date()
        return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear()
      }).length
    }
  })

  // 获取分类颜色
  const getCategoryColor = (category: MaterialMasterCategory): string => {
    const option = MATERIAL_CATEGORY_OPTIONS.find(opt => opt.value === category)
    return option?.color || '#909399'
  }

  // 获取分类标签
  const getCategoryLabel = (category: MaterialMasterCategory): string => {
    const option = MATERIAL_CATEGORY_OPTIONS.find(opt => opt.value === category)
    return option?.label || category
  }

  // 获取状态标签类型
  const getStatusTagType = (status: MaterialStatus): string => {
    const option = MATERIAL_STATUS_OPTIONS.find(opt => opt.value === status)
    return option?.tagType || ''
  }

  // 获取状态标签文本
  const getStatusLabel = (status: MaterialStatus): string => {
    const option = MATERIAL_STATUS_OPTIONS.find(opt => opt.value === status)
    return option?.label || status
  }

  // 获取线材颜色
  const getWireColor = (wireType?: WireType): string => {
    if (!wireType) return '#909399'
    const option = WIRE_TYPE_OPTIONS.find(opt => opt.value === wireType)
    return option?.color || '#909399'
  }

  // 格式化尺寸
  const formatDimensions = (dimensions: any): string => {
    if (!dimensions) return '-'

    const parts = []
    if (dimensions.length && dimensions.width) {
      parts.push(`${dimensions.length}×${dimensions.width}mm`)
    } else if (dimensions.diameter) {
      parts.push(`φ${dimensions.diameter / 1000}mm`)
    }

    if (dimensions.thickness) {
      parts.push(`厚${dimensions.thickness}μm`)
    }

    return parts.join(' ')
  }

  // 获取主供应商
  const getPrimarySupplier = (suppliers: SupplierInfo[]): SupplierInfo | undefined => {
    return suppliers.find(s => s.isPrimary) || suppliers[0]
  }

  // 获取价格趋势
  const getPriceTrend = (costInfo: any): string => {
    const variance = costInfo.currentPrice - costInfo.standardCost
    if (variance > 0) return 'price-up'
    if (variance < 0) return 'price-down'
    return 'price-stable'
  }

  // 获取价格趋势文本
  const getPriceTrendText = (costInfo: any): string => {
    const variance = costInfo.currentPrice - costInfo.standardCost
    const percent = Math.abs((variance / costInfo.standardCost) * 100).toFixed(1)

    if (variance > 0) return `↑${percent}%`
    if (variance < 0) return `↓${percent}%`
    return '持平'
  }

  // 格式化日期
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  // 获取分类数量
  const getCategoryCount = (category: MaterialMasterCategory): number => {
    return materialList.value.filter(m => m.basicInfo.category === category).length
  }

  // 根据分类获取物料列表
  const getMaterialsByCategory = (category: MaterialMasterCategory): MaterialMaster[] => {
    return materialList.value.filter(m => m.basicInfo.category === category)
  }

  // 搜索物料
  const searchMaterials = async () => {
    loading.value = true
    try {
      // 模拟搜索
      await new Promise(resolve => setTimeout(resolve, 300))

      let filteredMaterials = mockMaterialMasters

      // 关键词搜索
      if (searchForm.keyword) {
        const keyword = searchForm.keyword.toLowerCase()
        filteredMaterials = filteredMaterials.filter(
          material =>
            material.basicInfo.materialCode.toLowerCase().includes(keyword) ||
            material.basicInfo.materialName.toLowerCase().includes(keyword) ||
            material.basicInfo.englishName?.toLowerCase().includes(keyword) ||
            material.basicInfo.specification.toLowerCase().includes(keyword) ||
            material.basicInfo.manufacturer.toLowerCase().includes(keyword)
        )
      }

      // 分类筛选
      if (searchForm.category && searchForm.category.length > 0) {
        filteredMaterials = filteredMaterials.filter(material =>
          searchForm.category!.includes(material.basicInfo.category)
        )
      }

      // 状态筛选
      if (searchForm.status && searchForm.status.length > 0) {
        filteredMaterials = filteredMaterials.filter(material =>
          searchForm.status!.includes(material.basicInfo.status)
        )
      }

      // 制造商筛选
      if (searchForm.manufacturer && searchForm.manufacturer.length > 0) {
        filteredMaterials = filteredMaterials.filter(material =>
          searchForm.manufacturer!.includes(material.basicInfo.manufacturer)
        )
      }

      // 封装类型筛选
      if (searchForm.packageType && searchForm.packageType.length > 0) {
        filteredMaterials = filteredMaterials.filter(
          material =>
            material.technicalParams.packageInfo?.packageType &&
            searchForm.packageType!.includes(material.technicalParams.packageInfo.packageType)
        )
      }

      // 晶圆尺寸筛选
      if (searchForm.waferSize && searchForm.waferSize.length > 0) {
        filteredMaterials = filteredMaterials.filter(
          material =>
            material.technicalParams.waferInfo?.waferSize &&
            searchForm.waferSize!.includes(material.technicalParams.waferInfo.waferSize)
        )
      }

      // 线材类型筛选
      if (searchForm.wireType && searchForm.wireType.length > 0) {
        filteredMaterials = filteredMaterials.filter(
          material =>
            material.technicalParams.wirebondInfo?.wireType &&
            searchForm.wireType!.includes(material.technicalParams.wirebondInfo.wireType)
        )
      }

      // 价格范围筛选
      if (
        searchForm.priceRange &&
        (searchForm.priceRange.min > 0 || searchForm.priceRange.max > 0)
      ) {
        filteredMaterials = filteredMaterials.filter(material => {
          const price = material.costInfo.currentPrice
          const min = searchForm.priceRange!.min || 0
          const max = searchForm.priceRange!.max || Number.MAX_VALUE
          return price >= min && price <= max
        })
      }

      // 替代料筛选
      if (searchForm.hasAlternatives) {
        filteredMaterials = filteredMaterials.filter(material => material.alternatives.length > 0)
      }

      // 创建日期筛选
      if (searchForm.createdDateRange) {
        const [startDate, endDate] = searchForm.createdDateRange
        filteredMaterials = filteredMaterials.filter(material => {
          const createdDate = new Date(material.auditInfo.createdAt)
          const start = new Date(startDate)
          const end = new Date(endDate)
          end.setHours(23, 59, 59)
          return createdDate >= start && createdDate <= end
        })
      }

      // 分页
      pagination.total = filteredMaterials.length
      const startIndex = (pagination.page - 1) * pagination.pageSize
      const endIndex = startIndex + pagination.pageSize
      materialList.value = filteredMaterials.slice(startIndex, endIndex)
    } catch (error) {
      console.error('搜索物料失败:', error)
      ElMessage.error('搜索物料失败，请重试')
    } finally {
      loading.value = false
    }
  }

  // 重置搜索
  const resetSearch = () => {
    Object.assign(searchForm, {
      keyword: '',
      category: [],
      status: [],
      manufacturer: [],
      packageType: [],
      waferSize: [],
      wireType: [],
      priceRange: { min: 0, max: 0 },
      createdDateRange: undefined,
      hasAlternatives: false,
      isLowStock: false
    })
    pagination.page = 1
    searchMaterials()
  }

  // 处理表格选择变化
  const handleSelectionChange = (selection: MaterialMaster[]) => {
    selectedMaterials.value = selection
  }

  // 处理行点击
  const handleRowClick = (row: MaterialMaster) => {
    viewMaterial(row)
  }

  // 查看物料详情
  const viewMaterial = (material: MaterialMaster) => {
    currentMaterial.value = material
    showDetailDialog.value = true
  }

  // 编辑物料
  const editMaterial = (material: MaterialMaster) => {
    currentMaterial.value = material
    dialogMode.value = 'edit'
    showCreateDialog.value = true
  }

  // 复制物料
  const showDuplicateDialog = (material: MaterialMaster) => {
    const duplicated = {
      ...material,
      id: `MAT-DUP-${Date.now()}`,
      basicInfo: {
        ...material.basicInfo,
        materialCode: generateNewMaterialCode(material.basicInfo.category),
        materialName: `${material.basicInfo.materialName} (副本)`
      },
      auditInfo: {
        ...material.auditInfo,
        createdBy: 'Current User',
        createdAt: new Date().toISOString(),
        updatedBy: 'Current User',
        updatedAt: new Date().toISOString(),
        version: 1
      }
    }

    currentMaterial.value = duplicated
    dialogMode.value = 'create'
    showCreateDialog.value = true
  }

  // 查看替代料
  const viewAlternatives = (material: MaterialMaster) => {
    currentMaterial.value = material
    showAlternativeDialog.value = true
  }

  // 删除物料
  const deleteMaterial = async (material: MaterialMaster) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除物料 "${material.basicInfo.materialName}" 吗？此操作不可恢复。`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 模拟删除操作
      const index = materialList.value.findIndex(m => m.id === material.id)
      if (index > -1) {
        materialList.value.splice(index, 1)
        pagination.total--
        ElMessage.success('删除成功')
      }
    } catch (error) {
      // 用户取消删除
    }
  }

  // 新增物料
  const createMaterial = () => {
    currentMaterial.value = null
    dialogMode.value = 'create'
    showCreateDialog.value = true
  }

  // 保存物料
  const handleSaveMaterial = async (
    materialData: CreateMaterialMasterData | UpdateMaterialMasterData
  ) => {
    try {
      if (dialogMode.value === 'create') {
        // 模拟创建
        const newMaterial: MaterialMaster = {
          id: `MAT-${Date.now()}`,
          basicInfo: materialData.basicInfo,
          technicalParams: materialData.technicalParams || {},
          suppliers: materialData.suppliers || [],
          qualityInfo: materialData.qualityInfo || {
            standard: '',
            specification: '',
            inspectionMethod: '',
            acceptanceCriteria: '',
            certifications: []
          },
          stockInfo: materialData.stockInfo || {
            unitOfMeasure: 'PCS',
            safetyStock: 0,
            maxStock: 0,
            reorderPoint: 0,
            leadTime: 30,
            storageRequirements: []
          },
          costInfo: materialData.costInfo || {
            standardCost: 0,
            currentPrice: 0,
            currency: 'USD',
            priceUnit: 'PCS',
            lastPriceUpdate: new Date().toISOString()
          },
          alternatives: materialData.alternatives || [],
          attachments: [],
          auditInfo: {
            createdBy: 'Current User',
            createdAt: new Date().toISOString(),
            updatedBy: 'Current User',
            updatedAt: new Date().toISOString(),
            version: 1
          }
        }

        materialList.value.unshift(newMaterial)
        pagination.total++
        ElMessage.success('物料创建成功')
      } else {
        // 模拟更新
        const index = materialList.value.findIndex(m => m.id === currentMaterial.value?.id)
        if (index > -1) {
          materialList.value[index] = {
            ...materialList.value[index],
            basicInfo: materialData.basicInfo,
            technicalParams:
              materialData.technicalParams || materialList.value[index].technicalParams,
            suppliers: materialData.suppliers || materialList.value[index].suppliers,
            qualityInfo: materialData.qualityInfo || materialList.value[index].qualityInfo,
            stockInfo: materialData.stockInfo || materialList.value[index].stockInfo,
            costInfo: materialData.costInfo || materialList.value[index].costInfo,
            alternatives: materialData.alternatives || materialList.value[index].alternatives,
            auditInfo: {
              ...materialList.value[index].auditInfo,
              updatedBy: 'Current User',
              updatedAt: new Date().toISOString(),
              version: materialList.value[index].auditInfo.version + 1
            }
          }
          ElMessage.success('物料更新成功')
        }
      }

      showCreateDialog.value = false
    } catch (error) {
      console.error('保存物料失败:', error)
      ElMessage.error('保存失败，请重试')
    }
  }

  // 保存替代料关系
  const handleSaveAlternatives = (alternatives: any[]) => {
    if (currentMaterial.value) {
      const index = materialList.value.findIndex(m => m.id === currentMaterial.value?.id)
      if (index > -1) {
        materialList.value[index].alternatives = alternatives
        ElMessage.success('替代料关系更新成功')
      }
    }
    showAlternativeDialog.value = false
  }

  // 导出物料数据
  const exportMaterials = () => {
    ElMessage.info('导出功能开发中...')
  }

  // 批量导出
  const batchExport = () => {
    ElMessage.info(`正在导出 ${selectedMaterials.value.length} 个物料数据...`)
  }

  // 批量更新状态
  const batchUpdateStatus = () => {
    ElMessage.info('批量更新状态功能开发中...')
  }

  // 批量更新供应商
  const batchUpdateSupplier = () => {
    ElMessage.info('批量更新供应商功能开发中...')
  }

  // 批量删除
  const batchDelete = async () => {
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedMaterials.value.length} 个物料吗？此操作不可恢复。`,
        '批量删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 模拟批量删除
      const deleteIds = selectedMaterials.value.map(m => m.id)
      materialList.value = materialList.value.filter(m => !deleteIds.includes(m.id))
      pagination.total -= selectedMaterials.value.length
      selectedMaterials.value = []

      ElMessage.success('批量删除成功')
    } catch (error) {
      // 用户取消
    }
  }

  // 清除选择
  const clearSelection = () => {
    selectedMaterials.value = []
  }

  // 处理导入成功
  const handleImportSuccess = (count: number) => {
    ElMessage.success(`成功导入 ${count} 个物料`)
    searchMaterials() // 刷新列表
  }

  // 分页处理
  const handlePageSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.page = 1
    searchMaterials()
  }

  const handleCurrentPageChange = (page: number) => {
    pagination.page = page
    searchMaterials()
  }

  // 页面初始化
  onMounted(() => {
    searchMaterials()
  })
</script>

<style lang="scss" scoped>
  .material-master-data {
    min-height: 100vh;
    padding: 20px;
    background: var(--color-bg-light);

    .page-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 20px;

      .page-title {
        h2 {
          margin: 0 0 8px;
          font-size: 24px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .page-description {
          margin: 0;
          font-size: 14px;
          color: var(--color-text-secondary);
        }
      }

      .page-actions {
        display: flex;
        gap: 12px;
      }
    }

    .search-card {
      margin-bottom: 20px;

      .search-form {
        .advanced-search {
          margin-top: 20px;
        }

        .category-option {
          display: flex;
          gap: 8px;
          align-items: center;
        }

        .wire-option {
          display: flex;
          gap: 8px;
          align-items: center;

          .wire-color {
            width: 12px;
            height: 12px;
            border: 1px solid var(--color-border-light);
            border-radius: 50%;
          }
        }

        .price-range {
          display: flex;
          align-items: center;
        }
      }
    }

    .stats-cards {
      margin-bottom: 20px;

      .stats-card {
        .stats-item {
          display: flex;
          align-items: center;

          .stats-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            margin-right: 16px;
            border-radius: 50%;

            &.primary {
              color: var(--color-primary);
              background: var(--color-primary-light);
            }

            &.success {
              color: var(--color-success);
              background: var(--color-success-light);
            }

            &.warning {
              color: var(--color-warning);
              background: var(--color-warning-light);
            }

            &.info {
              color: var(--color-info);
              background: var(--color-info-light);
            }
          }

          .stats-content {
            .stats-value {
              font-size: 24px;
              font-weight: 600;
              line-height: 1;
              color: var(--color-text-primary);
            }

            .stats-label {
              margin-top: 4px;
              font-size: 14px;
              color: var(--color-text-secondary);
            }
          }
        }
      }
    }

    .table-card {
      .table-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .table-title {
          font-size: 16px;
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .table-actions {
          display: flex;
          gap: 16px;
          align-items: center;
        }
      }

      .material-code {
        .code-text {
          font-family: Monaco, Consolas, monospace;
          font-weight: 500;
        }
      }

      .material-info {
        .material-name {
          margin-bottom: 2px;
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .material-english {
          margin-bottom: 2px;
          font-size: 12px;
          color: var(--color-text-secondary);
        }

        .material-spec {
          margin-bottom: 2px;
          font-size: 12px;
          color: var(--color-text-regular);
        }

        .material-manufacturer {
          font-size: 12px;
          font-style: italic;
          color: var(--color-text-secondary);
        }
      }

      .tech-params {
        .param-text {
          display: inline-block;
          margin-left: 8px;
          font-size: 12px;
          color: var(--color-text-secondary);
        }

        .wire-indicator {
          display: inline-block;
          width: 12px;
          height: 12px;
          margin-right: 4px;
          border: 1px solid var(--color-border-light);
          border-radius: 50%;
        }
      }

      .stock-info {
        .stock-value {
          margin-bottom: 2px;
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .stock-location {
          margin-bottom: 2px;
          font-size: 12px;
          color: var(--color-text-secondary);
        }

        .shelf-life {
          font-size: 12px;
          color: var(--color-warning);
        }
      }

      .cost-info {
        .current-price {
          margin-bottom: 2px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .cost-unit {
          margin-bottom: 2px;
          font-size: 12px;
          color: var(--color-text-secondary);
        }

        .price-trend {
          font-size: 12px;
          font-weight: 500;

          &.price-up {
            color: var(--color-danger);
          }

          &.price-down {
            color: var(--color-success);
          }

          &.price-stable {
            color: var(--color-text-secondary);
          }
        }
      }

      .supplier-info {
        .supplier-name {
          margin-bottom: 2px;
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .supplier-details {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .lead-time,
          .quality-rating {
            font-size: 12px;
            color: var(--color-text-secondary);
          }
        }
      }

      .category-view {
        .category-section {
          margin-bottom: 32px;

          .category-header {
            display: flex;
            gap: 12px;
            align-items: center;
            padding-bottom: 8px;
            margin-bottom: 16px;
            border-bottom: 1px solid var(--color-border-light);

            .category-title {
              font-size: 16px;
              font-weight: 500;
              color: var(--color-text-primary);
            }
          }
        }
      }

      .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }
    }

    .batch-actions {
      position: fixed;
      right: 50%;
      bottom: 20px;
      z-index: 1000;
      display: flex;
      gap: 20px;
      align-items: center;
      padding: 12px 20px;
      background: var(--color-white);
      border: 1px solid var(--color-border-light);
      border-radius: 24px;
      box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
      transform: translateX(50%);

      .batch-info {
        font-weight: 500;
        color: var(--color-text-primary);
      }

      .batch-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }

  // 响应式设计
  @media (width <= 1200px) {
    .material-master-data {
      padding: 16px;

      .page-header {
        flex-direction: column;
        gap: 16px;
      }

      .stats-cards .el-col {
        margin-bottom: 16px;
      }
    }
  }
</style>
