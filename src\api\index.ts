/**
 * IC封测CIM系统 - 统一API管理系统
 * Unified API Management System for IC Packaging & Testing CIM System
 */

import axios from 'axios'
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
  InternalAxiosRequestConfig
} from 'axios'
// Element Plus组件通过unplugin-auto-import自动导入
import type { ApiResponse, ApiPageResponse, ApiErrorCode } from './config'
import { ApiConfig, ApiErrorMessages, MockApiHelper } from './config'

/**
 * HTTP状态码枚举
 */
export enum HttpStatus {
  SUCCESS = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  VALIDATION_ERROR = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504
}

/**
 * 请求配置接口扩展
 */
export interface ApiRequestConfig extends AxiosRequestConfig {
  // UI控制选项
  showLoading?: boolean
  showError?: boolean
  showSuccess?: boolean
  successMessage?: string
  loadingText?: string

  // 重试配置
  retry?: boolean
  retryCount?: number
  retryDelay?: number

  // 缓存配置
  cache?: boolean
  cacheKey?: string
  cacheTimeout?: number

  // 业务配置
  skipAuth?: boolean
  skipInterceptor?: boolean
  mock?: boolean
}

/**
 * 重试配置接口
 */
interface RetryConfig {
  count: number
  delay: number
  backoff: number
}

/**
 * Loading管理器
 */
class LoadingManager {
  private loadingCount = 0
  private loadingInstance: any = null

  show(text = '加载中...'): void {
    this.loadingCount++
    if (this.loadingCount === 1) {
      this.loadingInstance = (window as any).ElLoading?.service({
        lock: true,
        text,
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'cim-loading'
      })
    }
  }

  hide(): void {
    this.loadingCount = Math.max(0, this.loadingCount - 1)
    if (this.loadingCount === 0 && this.loadingInstance) {
      this.loadingInstance.close()
      this.loadingInstance = null
    }
  }

  clear(): void {
    this.loadingCount = 0
    if (this.loadingInstance) {
      this.loadingInstance.close()
      this.loadingInstance = null
    }
  }
}

/**
 * Token管理器
 */
class TokenManager {
  private readonly TOKEN_KEY = 'cim_auth_token'
  private readonly REFRESH_TOKEN_KEY = 'cim_refresh_token'
  private readonly TOKEN_EXPIRE_KEY = 'cim_token_expire'

  private refreshPromise: Promise<string> | null = null

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY)
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY)
  }

  setToken(token: string, refreshToken?: string, expiresIn?: number): void {
    localStorage.setItem(this.TOKEN_KEY, token)

    if (refreshToken) {
      localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken)
    }

    if (expiresIn) {
      const expireTime = Date.now() + expiresIn * 1000
      localStorage.setItem(this.TOKEN_EXPIRE_KEY, expireTime.toString())
    }
  }

  clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.REFRESH_TOKEN_KEY)
    localStorage.removeItem(this.TOKEN_EXPIRE_KEY)
  }

  isTokenExpired(): boolean {
    const expireTime = localStorage.getItem(this.TOKEN_EXPIRE_KEY)
    if (!expireTime) return false

    return Date.now() > parseInt(expireTime)
  }

  async refreshTokenIfNeeded(): Promise<string | null> {
    if (!this.isTokenExpired()) {
      return this.getToken()
    }

    if (this.refreshPromise) {
      return this.refreshPromise
    }

    const refreshToken = this.getRefreshToken()
    if (!refreshToken) {
      this.clearToken()
      return null
    }

    this.refreshPromise = this.refreshToken(refreshToken)

    try {
      const newToken = await this.refreshPromise
      this.refreshPromise = null
      return newToken
    } catch (error) {
      this.refreshPromise = null
      this.clearToken()
      throw error
    }
  }

  private async refreshToken(refreshToken: string): Promise<string> {
    // 实际项目中调用刷新token的API
    // 这里模拟刷新逻辑
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) {
          // 90%成功率
          const newToken = `new_token_${Date.now()}`
          const newRefreshToken = `new_refresh_token_${Date.now()}`
          this.setToken(newToken, newRefreshToken, 3600) // 1小时
          resolve(newToken)
        } else {
          reject(new Error('Token refresh failed'))
        }
      }, 1000)
    })
  }
}

/**
 * 请求缓存管理器
 */
class RequestCacheManager {
  private cache = new Map<string, { data: any; timestamp: number; timeout: number }>()
  private readonly DEFAULT_TIMEOUT = 5 * 60 * 1000 // 5分钟

  get(key: string): any {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() - cached.timestamp > cached.timeout) {
      this.cache.delete(key)
      return null
    }

    return cached.data
  }

  set(key: string, data: any, timeout = this.DEFAULT_TIMEOUT): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      timeout
    })
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  generateKey(url: string, params?: any, data?: any): string {
    const paramsStr = params ? JSON.stringify(params) : ''
    const dataStr = data ? JSON.stringify(data) : ''
    return `${url}_${paramsStr}_${dataStr}`
  }
}

/**
 * 统一HTTP客户端类
 */
export class ApiClient {
  private instance: AxiosInstance
  private loadingManager = new LoadingManager()
  private tokenManager = new TokenManager()
  private cacheManager = new RequestCacheManager()

  constructor(config?: AxiosRequestConfig) {
    this.instance = this.createInstance(config)
    this.setupInterceptors()
  }

  /**
   * 创建axios实例
   */
  private createInstance(config?: AxiosRequestConfig): AxiosInstance {
    const instance = axios.create({
      baseURL: ApiConfig.BASE_URL,
      timeout: ApiConfig.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json'
      },
      ...config
    })

    return instance
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors(): void {
    this.setupRequestInterceptor()
    this.setupResponseInterceptor()
  }

  /**
   * 请求拦截器
   */
  private setupRequestInterceptor(): void {
    this.instance.interceptors.request.use(
      async (config: InternalAxiosRequestConfig) => {
        const apiConfig = config as any as ApiRequestConfig

        // 显示loading
        if (apiConfig.showLoading !== false) {
          this.loadingManager.show(apiConfig.loadingText)
        }

        // 添加认证token
        if (!apiConfig.skipAuth) {
          try {
            const token = await this.tokenManager.refreshTokenIfNeeded()
            if (token) {
              config.headers = config.headers || {}
              ;(config.headers as any).Authorization = `Bearer ${token}`
            }
          } catch (error) {
            console.error('Token refresh failed:', error)
            // 重定向到登录页面
            this.handleAuthError()
          }
        }

        // 添加请求ID用于追踪
        config.headers = config.headers || {}
        ;(config.headers as any)['X-Request-ID'] = this.generateRequestId()

        // 添加时间戳防缓存
        if (config.method?.toLowerCase() === 'get') {
          config.params = {
            ...config.params,
            _t: Date.now()
          }
        }

        console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
          params: config.params,
          data: config.data
        })

        return config
      },
      (error: AxiosError) => {
        this.loadingManager.hide()
        console.error('[API Request Error]', error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 响应拦截器
   */
  private setupResponseInterceptor(): void {
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        const config = response.config as ApiRequestConfig

        // 隐藏loading
        if (config.showLoading !== false) {
          this.loadingManager.hide()
        }

        console.log(
          `[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`,
          {
            status: response.status,
            data: response.data
          }
        )

        // 检查业务状态码
        const apiResponse = response.data as ApiResponse
        if (apiResponse && typeof apiResponse === 'object' && 'success' in apiResponse) {
          if (!apiResponse.success) {
            throw new Error(apiResponse.message || '请求失败')
          }

          // 显示成功消息
          if (config.showSuccess) {
            const message = config.successMessage || apiResponse.message
            if (message) {
              ElMessage.success(message)
            }
          }
        }

        return response
      },
      async (error: AxiosError) => {
        const config = error.config as ApiRequestConfig

        // 隐藏loading
        if (config?.showLoading !== false) {
          this.loadingManager.hide()
        }

        // 重试逻辑
        if (this.shouldRetry(error, config)) {
          return this.retryRequest(error)
        }

        console.error(`[API Response Error] ${config?.method?.toUpperCase()} ${config?.url}`, {
          status: error.response?.status,
          message: error.message,
          data: error.response?.data
        })

        // 处理不同类型的错误
        const handledError = this.handleResponseError(error, config)

        return Promise.reject(handledError)
      }
    )
  }

  /**
   * 处理响应错误
   */
  private handleResponseError(error: AxiosError, config?: ApiRequestConfig): any {
    let errorCode = HttpStatus.INTERNAL_SERVER_ERROR
    let errorMessage = '请求失败'

    if (error.response) {
      // HTTP错误响应
      errorCode = error.response.status
      const responseData = error.response.data as any
      errorMessage = responseData?.message || this.getHttpErrorMessage(errorCode)
    } else if (error.request) {
      // 网络错误
      errorCode = HttpStatus.SERVICE_UNAVAILABLE
      errorMessage = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    // 特殊错误处理
    this.handleSpecialErrors(errorCode)

    // 显示错误消息
    if (config?.showError !== false) {
      this.showErrorMessage(errorMessage, errorCode)
    }

    return {
      code: errorCode,
      message: errorMessage,
      originalError: error
    }
  }

  /**
   * 判断是否需要重试
   */
  private shouldRetry(error: AxiosError, config?: ApiRequestConfig): boolean {
    if (!config?.retry) return false
    if ((config as any).__retryCount >= (config.retryCount || ApiConfig.RETRY_COUNT)) return false

    // 只对特定错误进行重试
    const retryableStatuses = [408, 429, 500, 502, 503, 504]
    const status = error.response?.status

    return !status || retryableStatuses.includes(status)
  }

  /**
   * 重试请求
   */
  private async retryRequest(error: AxiosError): Promise<AxiosResponse> {
    const config = error.config as ApiRequestConfig & { __retryCount?: number }

    config.__retryCount = (config.__retryCount || 0) + 1

    const delay =
      (config.retryDelay || ApiConfig.RETRY_DELAY) * Math.pow(2, config.__retryCount - 1)

    console.log(`[API Retry] Attempt ${config.__retryCount} after ${delay}ms`, {
      url: config.url,
      method: config.method
    })

    await this.sleep(delay)

    return this.instance.request(config)
  }

  /**
   * 处理特殊错误
   */
  private handleSpecialErrors(code: number): void {
    switch (code) {
      case HttpStatus.UNAUTHORIZED:
        this.handleAuthError()
        break
      case HttpStatus.FORBIDDEN:
        this.handleForbiddenError()
        break
      case HttpStatus.TOO_MANY_REQUESTS:
        this.handleRateLimitError()
        break
    }
  }

  /**
   * 处理认证错误
   */
  private handleAuthError(): void {
    this.tokenManager.clearToken()

    // 发送全局事件
    window.dispatchEvent(
      new CustomEvent('auth:error', {
        detail: { code: HttpStatus.UNAUTHORIZED, message: '登录已过期，请重新登录' }
      })
    )

    // 实际项目中使用路由跳转
    if (window.location.pathname !== '/login') {
      ElMessage.error('登录已过期，请重新登录')
      setTimeout(() => {
        window.location.href = '/login'
      }, 2000)
    }
  }

  /**
   * 处理权限错误
   */
  private handleForbiddenError(): void {
    ElMessage.warning('权限不足，无法执行此操作')
  }

  /**
   * 处理限流错误
   */
  private handleRateLimitError(): void {
    ElMessage.warning('操作过于频繁，请稍后再试')
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(message: string, code: number): void {
    const messageType = code >= 500 ? 'error' : 'warning'

    ElMessage({
      type: messageType,
      message,
      duration: code >= 500 ? 5000 : 3000,
      showClose: true
    })
  }

  /**
   * 获取HTTP错误消息
   */
  private getHttpErrorMessage(status: number): string {
    const messages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权，请重新登录',
      403: '权限不足，无法访问',
      404: '请求的资源不存在',
      405: '请求方法不被允许',
      408: '请求超时',
      409: '请求冲突',
      422: '数据验证失败',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂时不可用',
      504: '网关超时'
    }

    return messages[status] || `HTTP错误 ${status}`
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
  }

  /**
   * 延时函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * GET请求
   */
  async get<T = any>(
    url: string,
    params?: any,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    // 检查缓存
    if (config?.cache) {
      const cacheKey = config.cacheKey || this.cacheManager.generateKey(url, params)
      const cached = this.cacheManager.get(cacheKey)
      if (cached) {
        console.log(`[API Cache Hit] GET ${url}`)
        return cached
      }
    }

    try {
      const response = await this.instance.get<ApiResponse<T> | ApiPageResponse<T>>(url, {
        params,
        ...config
      })

      // 设置缓存
      if (config?.cache) {
        const cacheKey = config.cacheKey || this.cacheManager.generateKey(url, params)
        this.cacheManager.set(cacheKey, response.data, config.cacheTimeout)
      }

      return response.data
    } catch (error) {
      throw error
    }
  }

  /**
   * POST请求
   */
  async post<T = any>(
    url: string,
    data?: any,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    const response = await this.instance.post<ApiResponse<T> | ApiPageResponse<T>>(url, data, {
      showSuccess: true,
      ...config
    })
    return response.data
  }

  /**
   * PUT请求
   */
  async put<T = any>(
    url: string,
    data?: any,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    const response = await this.instance.put<ApiResponse<T> | ApiPageResponse<T>>(url, data, {
      showSuccess: true,
      successMessage: '更新成功',
      ...config
    })
    return response.data
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(
    url: string,
    data?: any,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    const response = await this.instance.patch<ApiResponse<T> | ApiPageResponse<T>>(url, data, {
      showSuccess: true,
      successMessage: '更新成功',
      ...config
    })
    return response.data
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(
    url: string,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T> | ApiPageResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T> | ApiPageResponse<T>>(url, {
      showSuccess: true,
      successMessage: '删除成功',
      ...config
    })
    return response.data
  }

  /**
   * 上传文件
   */
  async upload<T = any>(
    url: string,
    formData: FormData,
    config?: ApiRequestConfig & {
      onUploadProgress?: (progressEvent: any) => void
    }
  ): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      showSuccess: true,
      successMessage: '上传成功',
      ...config
    })
    return response.data
  }

  /**
   * 下载文件
   */
  async download(
    url: string,
    params?: any,
    filename?: string,
    config?: ApiRequestConfig
  ): Promise<void> {
    const response = await this.instance.get(url, {
      params,
      responseType: 'blob',
      showLoading: true,
      loadingText: '正在下载...',
      ...config
    })

    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')

    link.href = downloadUrl
    link.download = filename || `download_${Date.now()}`
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    window.URL.revokeObjectURL(downloadUrl)

    ElMessage.success('下载完成')
  }

  /**
   * 批量请求
   */
  async batch<T = any>(requests: Array<() => Promise<ApiResponse<T>>>): Promise<ApiResponse<T>[]> {
    const results = await Promise.allSettled(requests.map(request => request()))

    const responses: ApiResponse<T>[] = []
    const errors: any[] = []

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        responses.push(result.value)
      } else {
        errors.push({ index, error: result.reason })
      }
    })

    if (errors.length > 0) {
      console.warn('[API Batch Errors]', errors)
    }

    return responses
  }

  /**
   * 取消请求
   */
  createCancelToken(): { token: any; cancel: (message?: string) => void } {
    const source = axios.CancelToken.source()
    return {
      token: source.token,
      cancel: source.cancel
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.loadingManager.clear()
    this.cacheManager.clear()
  }

  /**
   * 获取缓存管理器
   */
  get cache() {
    return this.cacheManager
  }

  /**
   * 获取token管理器
   */
  get token() {
    return this.tokenManager
  }
}

// 创建默认API客户端实例
export const apiClient = new ApiClient()

// 导出常用方法
export const { get, post, put, patch, delete: del, upload, download } = apiClient

// 导出类型
// ApiRequestConfig已在上面导出，此处仅导出其他类型
export type { ApiResponse, ApiPageResponse }

// 默认导出
export default {
  ApiClient,
  apiClient,
  get,
  post,
  put,
  patch,
  del,
  upload,
  download,
  HttpStatus
}
