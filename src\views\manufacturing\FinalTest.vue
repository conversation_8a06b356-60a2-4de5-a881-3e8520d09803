<template>
  <div class="final-test-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title">
        <h1>最终测试管理</h1>
        <p>Final Test Management</p>
      </div>
      <div class="page-actions">
        <CButton
:loading="loading" @click="refreshData"
>
          <el-icon><RefreshRight /></el-icon>
          刷新数据
        </CButton>
        <CButton type="primary" @click="openNewTestDialog">
          <el-icon><Plus /></el-icon>
          新建测试
        </CButton>
      </div>
    </div>

    <!-- 实时概览 -->
    <div class="overview-section">
      <div class="kpi-cards">
        <div class="kpi-card">
          <div class="kpi-value">
            {{ formatNumber(dashboardData.finalTest.testedUnits) }}
          </div>
          <div class="kpi-label">今日测试数量</div>
          <div class="kpi-trend up">+18.7%</div>
        </div>
        <div class="kpi-card">
          <div class="kpi-value">{{ dashboardData.finalTest.passRate.toFixed(1) }}%</div>
          <div class="kpi-label">通过率</div>
          <div class="kpi-trend up">+1.2%</div>
        </div>
        <div class="kpi-card">
          <div class="kpi-value">{{ dashboardData.finalTest.avgTestTime.toFixed(1) }}s</div>
          <div class="kpi-label">平均测试时间</div>
          <div class="kpi-trend down">-5.3%</div>
        </div>
        <div class="kpi-card">
          <div class="kpi-value">{{ dashboardData.finalTest.handlerEfficiency.toFixed(1) }}%</div>
          <div class="kpi-label">分选机效率</div>
          <div class="kpi-trend up">+2.8%</div>
        </div>
      </div>
    </div>

    <div class="main-content">
      <!-- 左侧：测试设备和分选机状态 -->
      <div class="equipment-section">
        <div class="section-header">
          <h2>测试设备状态</h2>
          <div class="equipment-filter">
            <el-radio-group v-model="equipmentFilter" size="small">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="running">运行中</el-radio-button>
              <el-radio-button label="idle">空闲</el-radio-button>
              <el-radio-button label="alarm">报警</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="equipment-grid">
          <ManufacturingEquipmentCard
            v-for="equipment in filteredTestEquipments"
            :key="equipment.id"
            :equipment="equipment"
            @view-details="viewEquipmentDetails"
            @control-equipment="controlEquipment"
          />
        </div>

        <!-- 分选机状态 -->
        <div class="handler-section">
          <h3>分选机状态</h3>
          <div class="handler-grid">
            <div
              v-for="handler in handlers"
              :key="handler.handlerId"
              class="handler-card"
              :class="`handler-card--${getHandlerStatus(handler)}`"
            >
              <div class="handler-header">
                <h4>{{ handler.handlerName }}</h4>
                <el-tag :type="getHandlerStatusType(handler)" size="small">
                  {{ getHandlerStatusText(handler) }}
                </el-tag>
              </div>

              <div class="handler-metrics">
                <div class="metric-row">
                  <span class="metric-label">产能:</span>
                  <span class="metric-value">{{ handler.throughput }} UPH</span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">效率:</span>
                  <span class="metric-value">{{ handler.efficiency }}%</span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">卡料次数:</span>
                  <span class="metric-value jam-count">{{ handler.jamCount }}</span>
                </div>
              </div>

              <div class="handler-actions">
                <CButton size="small"
@click="viewHandlerDetails(handler.handlerId)"
>
详情
</CButton>
                <CButton size="small" type="primary" @click="controlHandler(handler.handlerId)">
                  控制
                </CButton>
              </div>
            </div>
          </div>
        </div>

        <!-- 测试程序管理 -->
        <div class="test-program-section">
          <h3>测试程序管理</h3>
          <el-table :data="testPrograms" stripe size="small">
            <el-table-column prop="name" label="程序名称" />
            <el-table-column prop="version" label="版本" width="80" />
            <el-table-column prop="customerPN" label="客户料号" />
            <el-table-column
prop="testTime" label="测试时间"
width="80"
>
              <template #default="{ row }">
{{ row.testTime }}s
</template>
            </el-table-column>
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.isActive ? 'success' : 'info'" size="small">
                  {{ row.isActive ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <CButton size="small"
link @click="editTestProgram(row)"
>
编辑
</CButton>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 右侧：测试任务和结果 -->
      <div class="test-section">
        <!-- 当前测试任务 -->
        <div class="current-tests">
          <div class="section-header">
            <h2>当前测试任务</h2>
            <div class="test-filters">
              <el-select v-model="testStatusFilter" placeholder="状态筛选" size="small" clearable>
                <el-option label="运行中" value="running" />
                <el-option label="等待中" value="waiting" />
                <el-option label="已完成" value="completed" />
                <el-option label="暂停" value="hold" />
              </el-select>
              <el-select v-model="packageTypeFilter" placeholder="封装类型" size="small" clearable>
                <el-option label="QFP" value="QFP" />
                <el-option label="BGA" value="BGA" />
                <el-option label="CSP" value="CSP" />
              </el-select>
            </div>
          </div>

          <div class="test-list">
            <div
              v-for="test in filteredFinalTests"
              :key="test.id"
              class="test-item"
              :class="`test-item--${test.status}`"
              @click="selectTest(test)"
            >
              <div class="test-item__header">
                <div class="test-info">
                  <div class="lot-number">
                    {{ test.lotNumber }}
                  </div>
                  <div class="part-info">{{ test.packageType }} - {{ test.customerPN }}</div>
                </div>
                <el-tag :type="getTestStatusType(test.status)" size="small">
                  {{ getTestStatusText(test.status) }}
                </el-tag>
              </div>

              <div class="test-item__content">
                <div class="test-progress">
                  <div class="progress-info">
                    <span>测试进度: {{ test.testedQty }} / {{ test.testedQty + 5000 }}</span>
                    <span class="yield-info">良率: {{ test.yield.toFixed(2) }}%</span>
                  </div>
                  <el-progress
                    :percentage="(test.testedQty / (test.testedQty + 5000)) * 100"
                    :stroke-width="6"
                    :show-text="false"
                  />
                </div>

                <div class="test-metrics">
                  <div class="metric-item">
                    <span class="metric-label">通过数量:</span>
                    <span class="metric-value pass">{{ test.passedQty }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">测试时间:</span>
                    <span class="metric-value">
                      {{ getTestDuration(test.startTime, test.endTime) }}
                    </span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">操作员:</span>
                    <span class="metric-value">{{ test.operator }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试详情面板 -->
    <div
v-if="selectedTest" class="test-details-panel"
>
      <el-dialog
        v-model="detailsPanelVisible"
        :title="`${selectedTest.lotNumber} - 最终测试详情`"
        width="90%"
        top="5vh"
      >
        <el-tabs v-model="detailsActiveTab" type="border-card">
          <!-- 测试结果 -->
          <el-tab-pane label="测试结果" name="results">
            <div class="test-results-content">
              <div class="results-summary">
                <h3>测试汇总</h3>
                <div class="summary-grid">
                  <div class="summary-card">
                    <div class="summary-title">测试数量</div>
                    <div class="summary-value">
                      {{ selectedTest.testedQty }}
                    </div>
                  </div>
                  <div class="summary-card">
                    <div class="summary-title">通过数量</div>
                    <div class="summary-value pass">
                      {{ selectedTest.passedQty }}
                    </div>
                  </div>
                  <div class="summary-card">
                    <div class="summary-title">良率</div>
                    <div class="summary-value yield">{{ selectedTest.yield.toFixed(2) }}%</div>
                  </div>
                  <div class="summary-card">
                    <div class="summary-title">测试时间</div>
                    <div class="summary-value">{{ selectedTest.testProgram.testTime }}s</div>
                  </div>
                </div>
              </div>

              <!-- Bin汇总 -->
              <div class="bin-summary">
                <h3>Bin分布</h3>
                <div class="bin-chart">
                  <div
id="bin-distribution-chart" style="width: 100%; height: 300px"
/>
                </div>

                <div class="bin-table">
                  <el-table :data="binSummaryData" stripe>
                    <el-table-column prop="bin" label="Bin Code" width="100" />
                    <el-table-column prop="description" label="描述" />
                    <el-table-column prop="count" label="数量" width="100" />
                    <el-table-column label="百分比" width="100">
                      <template #default="{ row }">
                        {{ ((row.count / selectedTest.testedQty) * 100).toFixed(2) }}%
                      </template>
                    </el-table-column>
                    <el-table-column label="状态" width="80">
                      <template #default="{ row }">
                        <el-tag :type="getBinStatusType(row.bin)" size="small">
                          {{ getBinStatusText(row.bin) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 电测参数 -->
          <el-tab-pane label="电测参数" name="parameters">
            <div class="electrical-parameters">
              <ProcessParameterTable
                :parameters="testParametersData"
                :loading="parameterLoading"
                @refresh="refreshParameters"
                @edit-parameter="editParameter"
              />
            </div>
          </el-tab-pane>

          <!-- 老化测试 -->
          <el-tab-pane label="老化测试" name="burn-in" v-if="selectedTest.burnIn">
            <div class="burn-in-content">
              <div class="burn-in-summary">
                <h3>老化测试参数</h3>
                <div class="parameter-grid">
                  <div class="param-item">
                    <span class="param-label">老化温度:</span>
                    <span class="param-value">{{ selectedTest.burnIn.temperature }}°C</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">老化电压:</span>
                    <span class="param-value">{{ selectedTest.burnIn.voltage }}V</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">老化时间:</span>
                    <span class="param-value">{{ selectedTest.burnIn.duration }}h</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">测试座位数:</span>
                    <span class="param-value">{{ selectedTest.burnIn.socketCount }}</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">失效数量:</span>
                    <span class="param-value fail">{{ selectedTest.burnIn.failureCount }}</span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">失效率:</span>
                    <span
                      class="param-value"
                      :class="getFailureRateClass(selectedTest.burnIn.failureRate)"
                    >
                      {{ selectedTest.burnIn.failureRate.toFixed(3) }}%
                    </span>
                  </div>
                </div>
              </div>

              <!-- 老化曲线 -->
              <div class="burn-in-chart">
                <div
id="burn-in-curve-chart" style="width: 100%; height: 350px"
/>
              </div>
            </div>
          </el-tab-pane>

          <!-- 分选机操作 -->
          <el-tab-pane label="分选机操作" name="handler">
            <div class="handler-operation-content">
              <div class="handler-info">
                <h3>分选机信息</h3>
                <div class="handler-details">
                  <div class="detail-item">
                    <span class="detail-label">分选机ID:</span>
                    <span class="detail-value">{{ selectedTest.handler.handlerId }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">分选机名称:</span>
                    <span class="detail-value">{{ selectedTest.handler.handlerName }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">实际产能:</span>
                    <span class="detail-value">{{ selectedTest.handler.throughput }} UPH</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">运行效率:</span>
                    <span class="detail-value">{{ selectedTest.handler.efficiency }}%</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">卡料次数:</span>
                    <span class="detail-value">{{ selectedTest.handler.jamCount }}</span>
                  </div>
                </div>
              </div>

              <!-- Bin映射配置 -->
              <div class="bin-mapping">
                <h3>Bin映射配置</h3>
                <el-table :data="binMappingData" stripe>
                  <el-table-column prop="testBin" label="测试Bin" width="100" />
                  <el-table-column prop="physicalBin" label="物理Bin位" width="100" />
                  <el-table-column prop="description" label="描述" />
                  <el-table-column prop="count" label="当前数量" width="100" />
                  <el-table-column label="状态" width="80">
                    <template #default="{ row }">
                      <el-tag
:type="row.count > 0 ? 'success' : 'info'" size="small"
>
                        {{ row.count > 0 ? '有器件' : '空' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-dialog>
    </div>

    <!-- 新建测试弹窗 -->
    <el-dialog v-model="newTestDialogVisible" title="新建最终测试" width="60%">
      <el-form :model="newTestForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="批次号" required>
              <el-input v-model="newTestForm.lotNumber" placeholder="请输入批次号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户料号" required>
              <el-input v-model="newTestForm.customerPN" placeholder="请输入客户料号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="封装类型" required>
              <el-select v-model="newTestForm.packageType" placeholder="请选择封装类型">
                <el-option label="QFP" value="QFP" />
                <el-option label="BGA" value="BGA" />
                <el-option label="CSP" value="CSP" />
                <el-option label="FC" value="FC" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测试程序" required>
              <el-select v-model="newTestForm.testProgramId" placeholder="请选择测试程序">
                <el-option
                  v-for="program in activeTestPrograms"
                  :key="program.id"
                  :label="program.name"
                  :value="program.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分选机" required>
              <el-select v-model="newTestForm.handlerId" placeholder="请选择分选机">
                <el-option
                  v-for="handler in availableHandlers"
                  :key="handler.handlerId"
                  :label="handler.handlerName"
                  :value="handler.handlerId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作员">
              <el-input v-model="newTestForm.operator" placeholder="请输入操作员" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="是否需要老化">
          <el-switch v-model="newTestForm.requireBurnIn" />
        </el-form-item>

        <div v-if="newTestForm.requireBurnIn" class="burn-in-config">
          <h4>老化测试配置</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="老化温度">
                <el-input-number
                  v-model="newTestForm.burnInConfig.temperature"
                  :min="25"
                  :max="150"
                />
                <span style="margin-left: 8px">°C</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="老化电压">
                <el-input-number
                  v-model="newTestForm.burnInConfig.voltage"
                  :min="1"
                  :max="10"
                  :step="0.1"
                />
                <span style="margin-left: 8px">V</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="老化时间">
                <el-input-number v-model="newTestForm.burnInConfig.duration" :min="1" :max="168" />
                <span style="margin-left: 8px">h</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <template #footer>
        <CButton @click="newTestDialogVisible = false">取消</CButton>
        <CButton type="primary"
@click="createNewTest" :loading="creating"
>
创建测试
</CButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import type {
    FinalTestRecord,
    Equipment,
    TestProgram,
    HandlerOperation,
    ProcessParameter,
    DashboardData,
    ProcessStatus,
    PackageType
  } from '@/types/manufacturing'
  import CButton from '@/components/base/CButton.vue'
  import { ManufacturingEquipmentCard, ProcessParameterTable } from '@/components/manufacturing'
  import { RefreshRight, Plus } from '@element-plus/icons-vue'

  // 响应式数据
  const loading = ref(false)
  const creating = ref(false)
  const parameterLoading = ref(false)
  const newTestDialogVisible = ref(false)
  const detailsPanelVisible = ref(false)
  const equipmentFilter = ref<'all' | 'running' | 'idle' | 'alarm'>('all')
  const testStatusFilter = ref('')
  const packageTypeFilter = ref('')
  const detailsActiveTab = ref('results')

  // 模拟数据
  const dashboardData = ref<DashboardData>({
    totalEquipment: 12,
    runningEquipment: 8,
    overallOEE: 85.2,
    dailyOutput: 125000,
    cpTesting: {
      activeWafers: 0,
      avgYield: 0,
      totalTestedDie: 0,
      equipmentUtilization: 0
    },
    assembly: {
      activePackages: 0,
      avgCycleTime: 0,
      defectRate: 0,
      throughput: 0
    },
    finalTest: {
      testedUnits: 87500,
      passRate: 96.8,
      avgTestTime: 2.3,
      handlerEfficiency: 92.1
    }
  })

  const testEquipments = ref<Equipment[]>([
    {
      id: 'ft-tester-01',
      name: 'FT-001',
      model: 'Advantest T2000',
      station: 'FT-ST01',
      status: 'running',
      lastUpdated: new Date().toISOString(),
      utilization: 94.3,
      oee: 91.2
    },
    {
      id: 'ft-tester-02',
      name: 'FT-002',
      model: 'Advantest T2000',
      station: 'FT-ST02',
      status: 'idle',
      lastUpdated: new Date().toISOString(),
      utilization: 82.7,
      oee: 88.5
    }
  ])

  const handlers = ref<HandlerOperation[]>([
    {
      handlerId: 'handler-01',
      handlerName: 'HD-001',
      throughput: 12000,
      jamCount: 3,
      efficiency: 95.2,
      binMapping: {
        '1': 'Pass',
        '2': 'Fail-Electrical',
        '3': 'Fail-Functional'
      }
    },
    {
      handlerId: 'handler-02',
      handlerName: 'HD-002',
      throughput: 11800,
      jamCount: 1,
      efficiency: 97.1,
      binMapping: {
        '1': 'Pass',
        '2': 'Fail-Electrical',
        '3': 'Fail-Functional'
      }
    }
  ])

  const testPrograms = ref<TestProgram[]>([
    {
      id: 'tp-001',
      name: 'TP_ABC123_V1.5',
      version: 'V1.5',
      customerPN: 'ABC123',
      testItems: ['DC Parameters', 'AC Parameters', 'Functional Test'],
      testTime: 2.5,
      temperatureRange: { min: 25, max: 85 },
      isActive: true
    },
    {
      id: 'tp-002',
      name: 'TP_XYZ456_V2.1',
      version: 'V2.1',
      customerPN: 'XYZ456',
      testItems: ['DC Parameters', 'High Speed Test', 'Burn-in Test'],
      testTime: 3.2,
      temperatureRange: { min: -40, max: 125 },
      isActive: true
    }
  ])

  const finalTests = ref<FinalTestRecord[]>([])
  const selectedTest = ref<FinalTestRecord | null>(null)
  const testParametersData = ref<ProcessParameter[]>([])

  const newTestForm = reactive({
    lotNumber: '',
    customerPN: '',
    packageType: 'QFP' as PackageType,
    testProgramId: '',
    handlerId: '',
    operator: '',
    requireBurnIn: false,
    burnInConfig: {
      temperature: 125,
      voltage: 5.0,
      duration: 24
    }
  })

  // 计算属性
  const filteredTestEquipments = computed(() => {
    if (equipmentFilter.value === 'all') {
      return testEquipments.value
    }
    return testEquipments.value.filter(eq => eq.status === equipmentFilter.value)
  })

  const filteredFinalTests = computed(() => {
    let filtered = finalTests.value

    if (testStatusFilter.value) {
      filtered = filtered.filter(test => test.status === testStatusFilter.value)
    }

    if (packageTypeFilter.value) {
      filtered = filtered.filter(test => test.packageType === packageTypeFilter.value)
    }

    return filtered
  })

  const activeTestPrograms = computed(() => {
    return testPrograms.value.filter(tp => tp.isActive)
  })

  const availableHandlers = computed(() => {
    return handlers.value.filter(h => h.efficiency > 90)
  })

  const binSummaryData = computed(() => {
    if (!selectedTest.value) return []

    return Object.entries(selectedTest.value.binSummary).map(([bin, count]) => ({
      bin,
      count,
      description: getBinDescription(bin)
    }))
  })

  const binMappingData = computed(() => {
    if (!selectedTest.value) return []

    return Object.entries(selectedTest.value.handler.binMapping).map(([testBin, description]) => ({
      testBin,
      physicalBin: `Bin-${testBin}`,
      description,
      count: selectedTest.value?.binSummary[testBin] || 0
    }))
  })

  // 方法
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('zh-CN').format(num)
  }

  const getHandlerStatus = (handler: HandlerOperation): string => {
    if (handler.efficiency >= 95) return 'excellent'
    if (handler.efficiency >= 90) return 'good'
    if (handler.efficiency >= 80) return 'fair'
    return 'poor'
  }

  const getHandlerStatusType = (handler: HandlerOperation) => {
    const status = getHandlerStatus(handler)
    const typeMap = {
      excellent: 'success',
      good: 'primary',
      fair: 'warning',
      poor: 'danger'
    }
    return typeMap[status as keyof typeof typeMap] || 'info'
  }

  const getHandlerStatusText = (handler: HandlerOperation): string => {
    const status = getHandlerStatus(handler)
    const textMap = {
      excellent: '优秀',
      good: '良好',
      fair: '一般',
      poor: '较差'
    }
    return textMap[status as keyof typeof textMap] || '未知'
  }

  const getTestStatusType = (status: ProcessStatus) => {
    const typeMap = {
      planned: 'info',
      released: 'primary',
      running: 'success',
      completed: 'success',
      hold: 'warning'
    }
    return typeMap[status] || 'info'
  }

  const getTestStatusText = (status: ProcessStatus): string => {
    const textMap = {
      planned: '计划中',
      released: '已发布',
      running: '运行中',
      completed: '已完成',
      hold: '暂停'
    }
    return textMap[status] || '未知'
  }

  const getTestDuration = (startTime: string, endTime?: string): string => {
    const start = new Date(startTime)
    const end = endTime ? new Date(endTime) : new Date()
    const duration = Math.floor((end.getTime() - start.getTime()) / 60000)
    return `${duration} min`
  }

  const getBinStatusType = (bin: string) => {
    if (bin === '1') return 'success'
    if (bin === '0') return 'danger'
    return 'warning'
  }

  const getBinStatusText = (bin: string): string => {
    if (bin === '1') return '良品'
    if (bin === '0') return '废品'
    return '不良'
  }

  const getBinDescription = (bin: string): string => {
    const descriptions: Record<string, string> = {
      '1': '良品 - 全参数通过',
      '2': '不良 - 电参数失效',
      '3': '不良 - 功能失效',
      '0': '废品 - 多项失效'
    }
    return descriptions[bin] || '未定义'
  }

  const getFailureRateClass = (rate: number): string => {
    if (rate <= 0.1) return 'excellent'
    if (rate <= 0.5) return 'good'
    if (rate <= 1.0) return 'fair'
    return 'poor'
  }

  // 事件处理方法
  const refreshData = async () => {
    loading.value = true
    try {
      await loadFinalTests()
      ElMessage.success('数据已刷新')
    } finally {
      loading.value = false
    }
  }

  const openNewTestDialog = () => {
    newTestDialogVisible.value = true
  }

  const createNewTest = async () => {
    creating.value = true
    try {
      // 创建新测试逻辑
      ElMessage.success('测试已创建')
      newTestDialogVisible.value = false
      await refreshData()
    } finally {
      creating.value = false
    }
  }

  const selectTest = (test: FinalTestRecord) => {
    selectedTest.value = test
    detailsPanelVisible.value = true
    loadTestParameters(test.id)
  }

  const viewEquipmentDetails = (equipmentId: string) => {
    console.log('查看设备详情:', equipmentId)
  }

  const controlEquipment = (equipmentId: string) => {
    console.log('控制设备:', equipmentId)
  }

  const viewHandlerDetails = (handlerId: string) => {
    console.log('查看分选机详情:', handlerId)
  }

  const controlHandler = (handlerId: string) => {
    console.log('控制分选机:', handlerId)
  }

  const editTestProgram = (program: TestProgram) => {
    console.log('编辑测试程序:', program.id)
  }

  const refreshParameters = () => {
    if (selectedTest.value) {
      loadTestParameters(selectedTest.value.id)
    }
  }

  const editParameter = (parameter: ProcessParameter) => {
    console.log('编辑参数:', parameter.id)
  }

  // 数据加载方法
  const loadFinalTests = async () => {
    // 模拟API调用
    const mockTests: FinalTestRecord[] = [
      {
        id: 'ft-001',
        lotNumber: 'FT001',
        packageType: 'QFP',
        customerPN: 'ABC123',
        testProgram: testPrograms.value[0],
        testedQty: 45000,
        passedQty: 43560,
        yield: 96.8,
        burnIn: {
          temperature: 125,
          voltage: 5.0,
          duration: 24,
          socketCount: 512,
          failureCount: 23,
          failureRate: 0.051
        },
        handler: handlers.value[0],
        testResults: generateFinalTestResults(),
        binSummary: {
          '1': 43560,  // 良品
          '2': 980,    // 电气失效
          '3': 460,    // 功能失效
          '4': 120,    // 参数失效
          '5': 80,     // 老化失效
          '0': 50      // 废品
        },
        operator: 'op001',
        startTime: '2024-01-25T08:00:00Z',
        endTime: '',
        status: 'running'
      },
      {
        id: 'ft-002',
        lotNumber: 'FT002',
        packageType: 'BGA',
        customerPN: 'XYZ456',
        testProgram: testPrograms.value[1],
        testedQty: 38000,
        passedQty: 36860,
        yield: 97.0,
        burnIn: {
          temperature: 150,
          voltage: 3.3,
          duration: 48,
          socketCount: 256,
          failureCount: 15,
          failureRate: 0.039
        },
        handler: handlers.value[1],
        testResults: generateFinalTestResults(),
        binSummary: {
          '1': 36860,  // 良品
          '2': 684,    // 电气失效
          '3': 380,    // 功能失效
          '4': 76,     // 参数失效
          '0': 0       // 废品
        },
        operator: 'op002',
        startTime: '2024-01-25T06:00:00Z',
        endTime: '2024-01-25T14:30:00Z',
        status: 'completed'
      },
      {
        id: 'ft-003',
        lotNumber: 'FT003',
        packageType: 'CSP',
        customerPN: 'DEF789',
        testProgram: testPrograms.value[0],
        testedQty: 28000,
        passedQty: 27160,
        yield: 97.0,
        handler: handlers.value[0],
        testResults: generateFinalTestResults(),
        binSummary: {
          '1': 27160,  // 良品
          '2': 504,    // 电气失效
          '3': 280,    // 功能失效
          '4': 56,     // 参数失效
          '0': 0       // 废品
        },
        operator: 'op003',
        startTime: '2024-01-25T12:00:00Z',
        endTime: '2024-01-25T18:45:00Z',
        status: 'completed'
      }
    ]
    finalTests.value = mockTests
  }

  // 生成最终测试结果数据
  const generateFinalTestResults = () => {
    return [
      {
        parameterName: '输出高电平电压',
        measuredValue: 3.28 + Math.random() * 0.08,
        unit: 'V',
        minLimit: 3.15,
        maxLimit: 3.45,
        result: 'pass' as const,
        cpk: 1.33 + Math.random() * 0.4
      },
      {
        parameterName: '输出低电平电压',
        measuredValue: 0.42 + Math.random() * 0.06,
        unit: 'V',
        minLimit: 0.0,
        maxLimit: 0.5,
        result: 'pass' as const,
        cpk: 1.45 + Math.random() * 0.3
      },
      {
        parameterName: '静态电流',
        measuredValue: 8.5 + Math.random() * 1.5,
        unit: 'μA',
        minLimit: 1.0,
        maxLimit: 15.0,
        result: 'pass' as const,
        cpk: 1.67 + Math.random() * 0.25
      },
      {
        parameterName: '动态电流',
        measuredValue: 45.2 + Math.random() * 8.0,
        unit: 'mA',
        minLimit: 30.0,
        maxLimit: 65.0,
        result: 'pass' as const,
        cpk: 1.25 + Math.random() * 0.3
      },
      {
        parameterName: '建立时间',
        measuredValue: 12.5 + Math.random() * 2.0,
        unit: 'ns',
        minLimit: 8.0,
        maxLimit: 18.0,
        result: 'pass' as const,
        cpk: 1.15 + Math.random() * 0.4
      },
      {
        parameterName: '保持时间',
        measuredValue: 8.2 + Math.random() * 1.5,
        unit: 'ns',
        minLimit: 5.0,
        maxLimit: 12.0,
        result: 'pass' as const,
        cpk: 1.42 + Math.random() * 0.35
      },
      {
        parameterName: '传播延迟',
        measuredValue: 15.8 + Math.random() * 3.0,
        unit: 'ns',
        minLimit: 10.0,
        maxLimit: 25.0,
        result: 'pass' as const,
        cpk: 1.28 + Math.random() * 0.22
      },
      {
        parameterName: '输入阈值电压',
        measuredValue: 1.65 + Math.random() * 0.1,
        unit: 'V',
        minLimit: 1.5,
        maxLimit: 1.8,
        result: 'pass' as const,
        cpk: 1.55 + Math.random() * 0.3
      }
    ]
  }

  const loadTestParameters = async (testId: string) => {
    parameterLoading.value = true
    try {
      // 模拟加载测试参数
      testParametersData.value = [
        {
          id: 'ft-param-001',
          name: '输出高电平',
          unit: 'V',
          targetValue: 3.3,
          tolerance: 0.1,
          currentValue: 3.32,
          minLimit: 3.1,
          maxLimit: 3.5,
          status: 'normal'
        },
        {
          id: 'ft-param-002',
          name: '输出低电平',
          unit: 'V',
          targetValue: 0.4,
          tolerance: 0.1,
          currentValue: 0.38,
          minLimit: 0.0,
          maxLimit: 0.5,
          status: 'normal'
        }
      ]
    } finally {
      parameterLoading.value = false
    }
  }

  // 生命周期
  onMounted(() => {
    loadFinalTests()
  })
</script>

<style lang="scss" scoped>
  .final-test-page {
    min-height: 100vh;
    padding: var(--spacing-6);
    background: var(--color-bg-page);
  }

  .page-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);

    .page-title {
      h1 {
        margin: 0 0 var(--spacing-1) 0;
        font-size: var(--font-size-2xl);
        font-weight: 600;
        color: var(--color-text-primary);
      }

      p {
        margin: 0;
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    .page-actions {
      display: flex;
      gap: var(--spacing-2);
    }
  }

  .overview-section {
    margin-bottom: var(--spacing-6);
  }

  .kpi-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
  }

  .kpi-card {
    padding: var(--spacing-4);
    text-align: center;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);

    .kpi-value {
      margin-bottom: var(--spacing-2);
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-primary);
    }

    .kpi-label {
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .kpi-trend {
      font-size: var(--font-size-sm);
      font-weight: 600;

      &.up {
        color: var(--color-success);

        &::before {
          margin-right: 2px;
          content: '↑';
        }
      }

      &.down {
        color: var(--color-danger);

        &::before {
          margin-right: 2px;
          content: '↓';
        }
      }
    }
  }

  .main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-6);
  }

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);

    h2 {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--color-text-primary);
    }
  }

  .equipment-section {
    height: fit-content;
    padding: var(--spacing-4);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);
  }

  .equipment-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-6);
  }

  .handler-section {
    margin-bottom: var(--spacing-6);

    h3 {
      margin: 0 0 var(--spacing-3) 0;
      font-size: var(--font-size-base);
      font-weight: 600;
      color: var(--color-text-primary);
    }
  }

  .handler-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-3);
  }

  .handler-card {
    padding: var(--spacing-3);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);

    &--excellent {
      border-left: 4px solid var(--color-success);
    }

    &--good {
      border-left: 4px solid var(--color-primary);
    }

    &--fair {
      border-left: 4px solid var(--color-warning);
    }

    &--poor {
      border-left: 4px solid var(--color-danger);
    }

    .handler-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-3);

      h4 {
        margin: 0;
        color: var(--color-text-primary);
      }
    }

    .handler-metrics {
      margin-bottom: var(--spacing-3);
    }

    .metric-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-sm);

      .metric-label {
        color: var(--color-text-secondary);
      }

      .metric-value {
        font-weight: 500;
        color: var(--color-text-primary);

        &.jam-count {
          color: var(--color-warning);
        }
      }
    }

    .handler-actions {
      display: flex;
      gap: var(--spacing-2);
      padding-top: var(--spacing-2);
      border-top: 1px solid var(--color-border-lighter);
    }
  }

  .test-program-section {
    h3 {
      margin: 0 0 var(--spacing-3) 0;
      font-size: var(--font-size-base);
      font-weight: 600;
      color: var(--color-text-primary);
    }
  }

  .test-section {
    padding: var(--spacing-4);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);
  }

  .test-filters {
    display: flex;
    gap: var(--spacing-2);
  }

  .test-list {
    max-height: 600px;
    overflow-y: auto;
  }

  .test-item {
    padding: var(--spacing-3);
    margin-bottom: var(--spacing-3);
    cursor: pointer;
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--color-primary);
      box-shadow: var(--shadow-md);
    }

    &--running {
      border-left: 4px solid var(--color-success);
    }

    &--completed {
      border-left: 4px solid var(--color-primary);
    }

    &--hold {
      border-left: 4px solid var(--color-warning);
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-3);

      .lot-number {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .part-info {
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    &__content {
      .test-progress {
        margin-bottom: var(--spacing-3);

        .progress-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: var(--spacing-1);
          font-size: var(--font-size-sm);
          color: var(--color-text-secondary);

          .yield-info {
            font-weight: 600;
            color: var(--color-success);
          }
        }
      }

      .test-metrics {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-1);
      }

      .metric-item {
        display: flex;
        justify-content: space-between;
        font-size: var(--font-size-sm);

        .metric-label {
          color: var(--color-text-secondary);
        }

        .metric-value {
          font-weight: 500;
          color: var(--color-text-primary);

          &.pass {
            color: var(--color-success);
          }
        }
      }
    }
  }

  .test-results-content {
    padding: var(--spacing-4);
  }

  .results-summary {
    margin-bottom: var(--spacing-6);

    h3 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
  }

  .summary-card {
    padding: var(--spacing-4);
    text-align: center;
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);

    .summary-title {
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .summary-value {
      font-size: var(--font-size-xl);
      font-weight: 600;
      color: var(--color-text-primary);

      &.pass {
        color: var(--color-success);
      }

      &.yield {
        color: var(--color-primary);
      }
    }
  }

  .bin-summary {
    h3 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .bin-chart {
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);
  }

  .electrical-parameters {
    padding: var(--spacing-4);
  }

  .burn-in-content {
    padding: var(--spacing-4);
  }

  .burn-in-summary {
    margin-bottom: var(--spacing-6);

    h3 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .parameter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-3);
  }

  .param-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);

    .param-label {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .param-value {
      font-size: var(--font-size-base);
      font-weight: 500;
      color: var(--color-text-primary);

      &.fail {
        color: var(--color-danger);
      }

      &.excellent {
        color: var(--color-success);
      }

      &.good {
        color: var(--color-primary);
      }

      &.fair {
        color: var(--color-warning);
      }

      &.poor {
        color: var(--color-danger);
      }
    }
  }

  .burn-in-chart {
    padding: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);
  }

  .handler-operation-content {
    padding: var(--spacing-4);
  }

  .handler-info {
    margin-bottom: var(--spacing-6);

    h3 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .handler-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-3);
  }

  .detail-item {
    .detail-label {
      display: block;
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .detail-value {
      font-size: var(--font-size-base);
      font-weight: 500;
      color: var(--color-text-primary);
    }
  }

  .bin-mapping {
    h3 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .burn-in-config {
    padding: var(--spacing-4);
    margin-top: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);

    h4 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  @media (width <= 1200px) {
    .main-content {
      grid-template-columns: 1fr;
    }
  }

  @media (width <= 768px) {
    .final-test-page {
      padding: var(--spacing-4);
    }

    .page-header {
      flex-direction: column;
      gap: var(--spacing-3);
      align-items: stretch;

      .page-actions {
        justify-content: center;
      }
    }

    .kpi-cards {
      grid-template-columns: 1fr;
    }

    .test-filters {
      flex-direction: column;
    }

    .handler-grid {
      grid-template-columns: 1fr;
    }

    .summary-grid {
      grid-template-columns: 1fr;
    }

    .parameter-grid {
      grid-template-columns: 1fr;
    }

    .handler-details {
      grid-template-columns: 1fr;
    }
  }
</style>
