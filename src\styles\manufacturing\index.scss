// 制造执行管理专用样式
// Manufacturing Execution System Styles

// 公共页面样式
.manufacturing-page {
  min-height: 100vh;
  padding: var(--spacing-6);
  background: var(--color-bg-page);

  // 页面头部样式
  .page-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);
    
    .page-title {
      h1 {
        margin: 0 0 var(--spacing-1) 0;
        font-size: var(--font-size-2xl);
        font-weight: 600;
        color: var(--color-text-primary);
        
        // IC封装测试特色字体效果
        background: linear-gradient(135deg, var(--color-primary), var(--color-success));
        background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      
      p {
        margin: 0;
        font-size: var(--font-size-sm);
        font-style: italic;
        color: var(--color-text-secondary);
      }
    }
    
    .page-actions {
      display: flex;
      gap: var(--spacing-2);
    }
  }

  // 概览部分样式
  .overview-section {
    margin-bottom: var(--spacing-6);
    
    .kpi-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-4);
    }
  }

  // 主内容区域
  .main-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: var(--spacing-6);

    @media (width <= 1200px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-4);
    }
  }
}

// KPI卡片样式
.kpi-card {
  position: relative;
  padding: var(--spacing-4);
  overflow: hidden;
  text-align: center;
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-base);
  transition: all 0.3s ease;
  
  // 悬停效果
  &:hover {
    border-color: var(--color-primary);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }
  
  // 背景装饰
  &::before {
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    content: '';
    background: radial-gradient(circle, var(--color-primary-light), transparent);
    opacity: 0.1;
  }
  
  .kpi-value {
    position: relative;
    z-index: 1;
    margin-bottom: var(--spacing-2);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-primary);
  }
  
  .kpi-label {
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--color-text-secondary);
  }
  
  .kpi-trend {
    font-size: var(--font-size-sm);
    font-weight: 600;
    
    &.up {
      color: var(--color-success);
      
      &::before {
        margin-right: 2px;
        font-size: var(--font-size-base);
        content: '↗';
      }
    }
    
    &.down {
      color: var(--color-danger);
      
      &::before {
        margin-right: 2px;
        font-size: var(--font-size-base);
        content: '↘';
      }
    }
    
    &.flat {
      color: var(--color-warning);
      
      &::before {
        margin-right: 2px;
        font-size: var(--font-size-base);
        content: '→';
      }
    }
  }
}

// 状态指示器样式
.status-indicator {
  display: inline-flex;
  gap: var(--spacing-1);
  align-items: center;
  
  &::before {
    width: 8px;
    height: 8px;
    content: '';
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
  
  &.status-running::before {
    background: var(--color-success);
  }
  
  &.status-idle::before {
    background: var(--color-info);
  }
  
  &.status-alarm::before {
    background: var(--color-danger);
  }
  
  &.status-maintenance::before {
    background: var(--color-warning);
  }
}

// 设备卡片网格
.equipment-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  
  @media (width >= 1400px) {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
  }
}

// 工艺流程步骤指示器
.process-flow-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: var(--spacing-4) 0;
  
  .process-step {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    
    &:not(:last-child)::after {
      position: absolute;
      top: 16px;
      right: calc(-50% + 20px);
      left: calc(50% + 20px);
      z-index: 0;
      height: 2px;
      content: '';
      background: var(--color-border-light);
    }
    
    &.active::after {
      background: var(--color-primary);
    }
    
    .step-icon {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      font-weight: 600;
      color: var(--color-text-secondary);
      background: var(--color-bg-secondary);
      border: 2px solid var(--color-border-light);
      border-radius: 50%;
      
      &.completed {
        color: white;
        background: var(--color-success);
        border-color: var(--color-success);
      }
      
      &.active {
        color: white;
        background: var(--color-primary);
        border-color: var(--color-primary);
        animation: pulse 2s infinite;
      }
      
      &.pending {
        color: var(--color-text-secondary);
        background: var(--color-bg-secondary);
        border-color: var(--color-border-light);
      }
    }
    
    .step-label {
      margin-top: var(--spacing-2);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      text-align: center;
      
      &.active {
        font-weight: 600;
        color: var(--color-primary);
      }
    }
  }
}

// 测试结果状态样式
.test-result-status {
  &.result-pass {
    color: var(--color-success);
    
    &::before {
      margin-right: 4px;
      font-weight: 700;
      content: '✓';
    }
  }
  
  &.result-fail {
    color: var(--color-danger);
    
    &::before {
      margin-right: 4px;
      font-weight: 700;
      content: '✗';
    }
  }
  
  &.result-retest {
    color: var(--color-warning);
    
    &::before {
      margin-right: 4px;
      font-weight: 700;
      content: '⟲';
    }
  }
  
  &.result-pending {
    color: var(--color-text-secondary);
    
    &::before {
      margin-right: 4px;
      content: '⏳';
    }
  }
}

// 良率指示器
.yield-indicator {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  font-size: var(--font-size-sm);
  font-weight: 600;
  border-radius: var(--radius-sm);
  
  &.yield-excellent {
    color: var(--color-success);
    background: var(--color-success-light-9);
    border: 1px solid var(--color-success-light-7);
  }
  
  &.yield-good {
    color: var(--color-primary);
    background: var(--color-primary-light-9);
    border: 1px solid var(--color-primary-light-7);
  }
  
  &.yield-fair {
    color: var(--color-warning);
    background: var(--color-warning-light-9);
    border: 1px solid var(--color-warning-light-7);
  }
  
  &.yield-poor {
    color: var(--color-danger);
    background: var(--color-danger-light-9);
    border: 1px solid var(--color-danger-light-7);
  }
}

// Cpk指示器
.cpk-indicator {
  font-weight: 600;
  
  &.cpk-excellent {
    color: var(--color-success);
  }
  
  &.cpk-good {
    color: var(--color-primary);
  }
  
  &.cpk-fair {
    color: var(--color-warning);
  }
  
  &.cpk-poor {
    color: var(--color-danger);
  }
}

// SPC控制图样式
.spc-chart-container {
  padding: var(--spacing-4);
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-base);
  
  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);
    
    h3 {
      margin: 0;
      color: var(--color-text-primary);
    }
    
    .chart-controls {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;
    }
  }
  
  .control-lines-legend {
    display: flex;
    gap: var(--spacing-4);
    margin-top: var(--spacing-3);
    font-size: var(--font-size-sm);
    
    .legend-item {
      display: flex;
      gap: var(--spacing-1);
      align-items: center;
      
      &::before {
        width: 16px;
        height: 2px;
        content: '';
        border-radius: 1px;
      }
      
      &.ucl::before { background: var(--color-danger); }

      &.lcl::before { background: var(--color-danger); }

      &.usl::before { background: var(--color-warning); }

      &.lsl::before { background: var(--color-warning); }

      &.mean::before { background: var(--color-success); }
    }
  }
}

// 晶圆图特殊样式
.wafer-map-container {
  position: relative;
  padding: var(--spacing-4);
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-base);
  
  // 晶圆轮廓阴影效果
  .wafer-outline {
    filter: drop-shadow(2px 2px 4px rgb(0 0 0 / 10%));
  }
  
  // Die单元悬停效果
  .die-cell {
    transition: all 0.2s ease;
    
    &:hover {
      filter: brightness(1.2);
      stroke: var(--color-primary) !important;
      stroke-width: 2 !important;
    }
  }
}

// SECS消息样式
.secs-message-container {
  .message-item {
    padding: var(--spacing-2) var(--spacing-3);
    margin-bottom: var(--spacing-2);
    border-left: 4px solid transparent;
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
    
    &.host-to-equipment {
      background: var(--color-primary-light-9);
      border-left-color: var(--color-primary);
    }
    
    &.equipment-to-host {
      background: var(--color-success-light-9);
      border-left-color: var(--color-success);
    }
    
    &.error {
      background: var(--color-danger-light-9);
      border-left-color: var(--color-danger);
    }
    
    .message-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-1);
      
      .message-type {
        font-family: 'Courier New', monospace;
        font-weight: 600;
      }
      
      .message-time {
        font-size: var(--font-size-xs);
        color: var(--color-text-secondary);
      }
    }
    
    .message-content {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      word-break: break-all;
    }
  }
}

// 分选机Bin映射表格样式
.bin-mapping-table {
  .bin-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    
    &.pass { color: var(--color-success); }

    &.fail { color: var(--color-danger); }

    &.reject { color: var(--color-warning); }
  }
}

// 设备稼动率环形图样式
.oee-ring-chart {
  position: relative;
  
  .oee-center-text {
    position: absolute;
    top: 50%;
    left: 50%;
    text-align: center;
    transform: translate(-50%, -50%);
    
    .oee-value {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      color: var(--color-primary);
    }
    
    .oee-label {
      margin-top: var(--spacing-1);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
  }
}

// 实时数据流动效果
@keyframes dataFlow {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  50% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(20px);
  }
}

.realtime-data-indicator {
  position: relative;
  
  &::before {
    position: absolute;
    top: 50%;
    left: -10px;
    width: 4px;
    height: 4px;
    content: '';
    background: var(--color-success);
    border-radius: 50%;
    animation: dataFlow 2s infinite;
  }
}

// 工艺参数超限报警样式
.parameter-alarm {
  animation: alarmBlink 1s infinite alternate;
  
  @keyframes alarmBlink {
    0% {
      background: var(--color-danger-light-9);
      border-color: var(--color-danger);
    }

    100% {
      background: var(--color-danger-light-7);
      border-color: var(--color-danger-dark);
    }
  }
}

// 移动端适配
@media (width <= 768px) {
  .manufacturing-page {
    padding: var(--spacing-4);
    
    .page-header {
      flex-direction: column;
      gap: var(--spacing-3);
      align-items: stretch;
      
      .page-actions {
        justify-content: center;
      }
    }
    
    .main-content {
      grid-template-columns: 1fr;
    }
    
    .kpi-cards {
      grid-template-columns: 1fr;
    }
    
    .equipment-grid {
      display: flex;
      flex-direction: column;
    }
    
    .process-flow-indicator {
      flex-direction: column;
      gap: var(--spacing-3);
      
      .process-step::after {
        display: none;
      }
    }
  }
}

// 打印样式
@media print {
  .manufacturing-page {
    color: black;
    background: white;
    
    .page-actions {
      display: none;
    }
    
    .kpi-card {
      border: 1px solid #ddd;
      box-shadow: none;
      
      &::before {
        display: none;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .kpi-card {
    border-width: 2px;
  }
  
  .status-indicator {
    &::before {
      border: 1px solid currentcolor;
    }
  }
  
  .test-result-status {
    font-weight: 700;
  }
}

// 减少动效模式
@media (prefers-reduced-motion: reduce) {
  .kpi-card {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
  
  .status-indicator::before,
  .realtime-data-indicator::before {
    animation: none;
  }
  
  .parameter-alarm {
    background: var(--color-danger-light-9);
    border-color: var(--color-danger);
    animation: none;
  }
}