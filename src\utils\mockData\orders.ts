/**
 * IC封测CIM系统 - 订单模拟数据
 * Order Mock Data for IC Packaging & Testing CIM System
 */

import type { Order, OrderStats, OrderStatCard } from '@/types/order'
import { OrderStatus, OrderPriority, PackageType, CurrencyType } from '@/types/order'
import { allMockCustomers } from './customers'

// 生成订单编号
export const generateOrderNumber = (prefix = 'PO') => {
  const date = new Date()
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 999)
    .toString()
    .padStart(3, '0')
  return `${prefix}${year}${month}${day}${random}`
}

// IC产品名称列表
const icProductNames = [
  'ARM Cortex-M4微控制器',
  'WiFi射频芯片',
  '电源管理IC',
  'CAN总线控制器',
  'ADC模数转换器',
  'DAC数模转换器',
  'FLASH存储器',
  'SRAM存储器',
  '以太网PHY芯片',
  'USB控制器',
  'HDMI接口芯片',
  'GPU图形处理器',
  '音频编解码芯片',
  '传感器融合处理器',
  '马达驱动芯片',
  'LED驱动IC',
  '充电管理IC',
  'DC-DC转换器',
  'LDO稳压器',
  'MOSFET功率器件',
  '时钟发生器',
  'PLL锁相环',
  '运算放大器',
  '比较器',
  '射频功放',
  '混频器',
  '滤波器',
  '天线开关',
  'MEMS陀螺仪',
  '加速度传感器',
  '压力传感器',
  'CMOS图像传感器'
]

// 生成随机产品代码
const generateProductCode = () => {
  const prefixes = ['MT', 'ST', 'NX', 'RT', 'AT', 'KT', 'LT', 'HT']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const numbers = Math.floor(Math.random() * 9999)
    .toString()
    .padStart(4, '0')
  const suffix = String.fromCharCode(65 + Math.floor(Math.random() * 26)) // A-Z
  return `${prefix}${numbers}${suffix}`
}

// 生成模拟订单数据
export const generateMockOrder = (index: number): Order => {
  const customer = allMockCustomers[index % allMockCustomers.length]
  const packageType =
    Object.values(PackageType)[Math.floor(Math.random() * Object.values(PackageType).length)]
  const priority =
    Object.values(OrderPriority)[Math.floor(Math.random() * Object.values(OrderPriority).length)]
  const status =
    Object.values(OrderStatus)[Math.floor(Math.random() * Object.values(OrderStatus).length)]

  const quantity = Math.floor(Math.random() * 500 + 50) // 50-550 K pcs
  const unitPrice = Math.floor(Math.random() * 800 + 200) // 200-1000 元/K pcs
  const totalAmount = quantity * unitPrice

  // 根据状态生成不同的进度
  const generateProgress = () => {
    switch (status) {
      case OrderStatus.PENDING:
        return { overall: 0, cpTesting: 0, assembly: 0, ftTesting: 0, packaging: 0 }
      case OrderStatus.CONFIRMED:
        return { overall: 5, cpTesting: 0, assembly: 0, ftTesting: 0, packaging: 0 }
      case OrderStatus.PROCESSING:
        const cp = Math.floor(Math.random() * 30 + 20) // 20-50%
        const assembly = Math.max(0, cp - Math.floor(Math.random() * 20))
        const ft = Math.max(0, assembly - Math.floor(Math.random() * 15))
        const packaging = Math.max(0, ft - Math.floor(Math.random() * 10))
        return {
          overall: Math.floor((cp + assembly + ft + packaging) / 4),
          cpTesting: cp,
          assembly,
          ftTesting: ft,
          packaging
        }
      case OrderStatus.TESTING:
        return { overall: 85, cpTesting: 100, assembly: 100, ftTesting: 70, packaging: 0 }
      case OrderStatus.COMPLETED:
        return { overall: 100, cpTesting: 100, assembly: 100, ftTesting: 100, packaging: 100 }
      default:
        return {
          overall: Math.floor(Math.random() * 40),
          cpTesting: 0,
          assembly: 0,
          ftTesting: 0,
          packaging: 0
        }
    }
  }

  const orderDate = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000) // 最近90天内
  const deliveryDays = Math.floor(Math.random() * 30 + 15) // 15-45天交期
  const deliveryDate = new Date(orderDate.getTime() + deliveryDays * 24 * 60 * 60 * 1000)

  return {
    id: `order_${(index + 1).toString().padStart(3, '0')}`,
    orderNumber: generateOrderNumber(),
    customerId: customer.id,
    customer: {
      id: customer.id,
      name: customer.customerName,
      code: customer.customerCode,
      contact: customer.contacts[0]
        ? {
            name: customer.contacts[0].name,
            phone: customer.contacts[0].mobile,
            email: customer.contacts[0].email
          }
        : undefined
    },
    productInfo: {
      productName: icProductNames[Math.floor(Math.random() * icProductNames.length)],
      productCode: generateProductCode(),
      packageType,
      quantity,
      waferSize: [6, 8, 12][Math.floor(Math.random() * 3)], // 6寸/8寸/12寸
      dieSize: `${Math.random() * 5 + 2}mm x ${Math.random() * 5 + 2}mm`.replace(/\.\d{3,}/g, m =>
        m.substring(0, 4)
      ),
      leadCount:
        packageType === PackageType.BGA
          ? Math.floor(Math.random() * 400 + 100)
          : packageType === PackageType.QFP
            ? Math.floor(Math.random() * 200 + 64)
            : Math.floor(Math.random() * 100 + 32),
      specifications: `${packageType}封装，工作温度-40°C~+85°C，符合${['RoHS', 'REACH', 'IATF16949'][Math.floor(Math.random() * 3)]}标准`
    },
    pricing: {
      unitPrice,
      totalAmount,
      currency: Math.random() > 0.8 ? CurrencyType.USD : CurrencyType.CNY,
      paymentTerms: ['月结30天', '月结45天', '款到发货', 'T/T 30天'][Math.floor(Math.random() * 4)]
    },
    schedule: {
      orderDate: orderDate.toISOString(),
      confirmedDate:
        status !== OrderStatus.PENDING
          ? new Date(orderDate.getTime() + Math.random() * 3 * 24 * 60 * 60 * 1000).toISOString()
          : undefined,
      startDate: [OrderStatus.PROCESSING, OrderStatus.TESTING, OrderStatus.COMPLETED].includes(
        status
      )
        ? new Date(orderDate.getTime() + Math.random() * 5 * 24 * 60 * 60 * 1000).toISOString()
        : undefined,
      deliveryDate: deliveryDate.toISOString(),
      actualDeliveryDate:
        status === OrderStatus.COMPLETED
          ? new Date(
              deliveryDate.getTime() + (Math.random() - 0.5) * 5 * 24 * 60 * 60 * 1000
            ).toISOString()
          : undefined
    },
    status,
    priority,
    progress: generateProgress(),
    qualityInfo: {
      yieldRequirement: Math.floor(Math.random() * 10 + 95), // 95-99%
      currentYield:
        status === OrderStatus.COMPLETED
          ? Math.floor(Math.random() * 5 + 96)
          : [OrderStatus.PROCESSING, OrderStatus.TESTING].includes(status)
            ? Math.floor(Math.random() * 10 + 90)
            : undefined,
      defectRate:
        status === OrderStatus.COMPLETED ? Math.floor(Math.random() * 100 + 10) : undefined, // 10-110 ppm
      qualityLevel: ['A', 'B', 'C'][Math.floor(Math.random() * 3)] as 'A' | 'B' | 'C' | 'D'
    },
    createdAt: orderDate.toISOString(),
    updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    createdBy: 'system_user',
    notes:
      Math.random() > 0.7
        ? [
            '客户要求加急处理',
            '特殊包装要求，需要防静电包装',
            '样品确认中，等待客户反馈',
            '量产订单，后续还有追加可能',
            '新产品导入，需要严格控制质量'
          ][Math.floor(Math.random() * 5)]
        : undefined
  }
}

// 生成50个模拟订单
export const allMockOrders: Order[] = Array.from({ length: 50 }, (_, index) =>
  generateMockOrder(index)
)

// 订单统计数据
export const orderStats: OrderStats = {
  totalOrders: allMockOrders.length,
  processingOrders: allMockOrders.filter(order =>
    [OrderStatus.PROCESSING, OrderStatus.TESTING].includes(order.status)
  ).length,
  completedOrders: allMockOrders.filter(order => order.status === OrderStatus.COMPLETED).length,
  overdueOrders: allMockOrders.filter(order => {
    const deliveryDate = new Date(order.schedule.deliveryDate)
    const now = new Date()
    return deliveryDate < now && order.status !== OrderStatus.COMPLETED
  }).length,
  totalVolume: allMockOrders.reduce((sum, order) => sum + order.productInfo.quantity, 0),
  monthlyVolume: allMockOrders
    .filter(order => {
      const orderDate = new Date(order.schedule.orderDate)
      const now = new Date()
      return (
        orderDate.getMonth() === now.getMonth() && orderDate.getFullYear() === now.getFullYear()
      )
    })
    .reduce((sum, order) => sum + order.productInfo.quantity, 0),
  averageYield: Math.round(
    allMockOrders
      .filter(order => order.qualityInfo.currentYield)
      .reduce((sum, order) => sum + (order.qualityInfo.currentYield || 0), 0) /
      allMockOrders.filter(order => order.qualityInfo.currentYield).length
  ),
  onTimeDeliveryRate: Math.round(
    (allMockOrders
      .filter(order => order.status === OrderStatus.COMPLETED && order.schedule.actualDeliveryDate)
      .filter(order => {
        const deliveryDate = new Date(order.schedule.deliveryDate)
        const actualDate = new Date(order.schedule.actualDeliveryDate!)
        return actualDate <= deliveryDate
      }).length /
      allMockOrders.filter(order => order.status === OrderStatus.COMPLETED).length) *
      100
  )
}

// 订单统计卡片数据
export const orderStatCards: OrderStatCard[] = [
  {
    key: 'totalOrders',
    label: '订单总数',
    value: orderStats.totalOrders,
    detail: '全部订单',
    trend: 'up',
    changePercent: '+12.5%',
    trendClass: 'trend-up',
    iconClass: 'icon-orders',
    icon: `<path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2a1 1 0 0 0-2 0v2H8V2a1 1 0 0 0-2 0v2H5a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3z" fill="currentColor"/>`,
    color: '#3b82f6'
  },
  {
    key: 'processingOrders',
    label: '生产中',
    value: orderStats.processingOrders,
    detail: '正在生产处理',
    trend: 'up',
    changePercent: '+8.2%',
    trendClass: 'trend-up',
    iconClass: 'icon-processing',
    icon: `<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>`,
    color: '#f59e0b'
  },
  {
    key: 'completedOrders',
    label: '已完成',
    value: orderStats.completedOrders,
    detail: '成功交付',
    trend: 'up',
    changePercent: '+15.3%',
    trendClass: 'trend-up',
    iconClass: 'icon-completed',
    icon: `<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/>`,
    color: '#10b981'
  },
  {
    key: 'monthlyVolume',
    label: '月产能',
    value: `${Math.round(orderStats.monthlyVolume / 1000)}M`,
    detail: `${orderStats.monthlyVolume}K pcs`,
    trend: 'up',
    changePercent: '+23.1%',
    trendClass: 'trend-up',
    iconClass: 'icon-volume',
    icon: `<path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" fill="currentColor"/>`,
    color: '#8b5cf6'
  },
  {
    key: 'averageYield',
    label: '平均良率',
    value: `${orderStats.averageYield}%`,
    detail: '质量指标',
    trend: 'stable',
    changePercent: '+0.2%',
    trendClass: 'trend-stable',
    iconClass: 'icon-yield',
    icon: `<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>`,
    color: '#06b6d4'
  },
  {
    key: 'onTimeDelivery',
    label: '准时交付率',
    value: `${orderStats.onTimeDeliveryRate}%`,
    detail: '交期准确率',
    trend: 'up',
    changePercent: '+5.7%',
    trendClass: 'trend-up',
    iconClass: 'icon-delivery',
    icon: `<path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z" fill="currentColor"/>`,
    color: '#ef4444'
  }
]

// 根据状态获取订单列表
export const getOrdersByStatus = (status: OrderStatus): Order[] => {
  return allMockOrders.filter(order => order.status === status)
}

// 根据客户ID获取订单列表
export const getOrdersByCustomerId = (customerId: string): Order[] => {
  return allMockOrders.filter(order => order.customerId === customerId)
}

// 获取急需处理的订单
export const getUrgentOrders = (): Order[] => {
  return allMockOrders.filter(
    order =>
      order.priority === OrderPriority.URGENT ||
      (order.status === OrderStatus.PROCESSING &&
        new Date(order.schedule.deliveryDate).getTime() - Date.now() < 7 * 24 * 60 * 60 * 1000) // 一周内交付
  )
}

// 获取逾期订单
export const getOverdueOrders = (): Order[] => {
  const now = new Date()
  return allMockOrders.filter(order => {
    const deliveryDate = new Date(order.schedule.deliveryDate)
    return (
      deliveryDate < now &&
      order.status !== OrderStatus.COMPLETED &&
      order.status !== OrderStatus.CANCELLED
    )
  })
}
