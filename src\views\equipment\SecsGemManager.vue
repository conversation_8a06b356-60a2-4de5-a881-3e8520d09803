<template>
  <div class="secs-gem-manager">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">SECS/GEM通信管理</h1>
        <p class="page-description">设备SECS-II消息监控、GEM状态管理和Recipe参数配置</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 设备选择和状态概览 -->
    <div class="equipment-overview">
      <el-row :gutter="24">
        <!-- 设备选择 -->
        <el-col :span="8">
          <el-card>
            <template #header>
              <span class="card-title">设备选择</span>
            </template>
            <el-select
              v-model="selectedEquipmentId"
              placeholder="选择设备"
              style="width: 100%"
              @change="handleEquipmentChange"
            >
              <el-option
v-for="eq in equipmentList" :key="eq.id"
:label="eq.name" :value="eq.id"
>
                <div class="equipment-option">
                  <span>{{ eq.name }}</span>
                  <el-tag
                    :type="eq.isConnected ? 'success' : 'danger'"
                    size="small"
                    style="margin-left: 8px"
                  >
                    {{ eq.isConnected ? '已连接' : '未连接' }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-card>
        </el-col>

        <!-- GEM状态 -->
        <el-col :span="8">
          <el-card>
            <template #header>
              <span class="card-title">GEM状态</span>
            </template>
            <div v-if="selectedEquipment" class="gem-status">
              <div class="status-item">
                <span class="label">当前状态:</span>
                <el-tag :type="getGemStateType(selectedEquipment.gemState)">
                  {{ getGemStateText(selectedEquipment.gemState) }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="label">连接状态:</span>
                <el-tag :type="selectedEquipment.isConnected ? 'success' : 'danger'">
                  {{ selectedEquipment.isConnected ? '已连接' : '未连接' }}
                </el-tag>
              </div>
              <div class="status-actions">
                <el-button
                  size="small"
                  :disabled="!selectedEquipment.isConnected"
                  @click="handleGoOnline"
                >
                  上线
                </el-button>
                <el-button
                  size="small"
                  :disabled="!selectedEquipment.isConnected"
                  @click="handleGoOffline"
                >
                  离线
                </el-button>
              </div>
            </div>
            <div v-else class="no-equipment">
              <el-empty description="请先选择设备" />
            </div>
          </el-card>
        </el-col>

        <!-- 连接统计 -->
        <el-col :span="8">
          <el-card>
            <template #header>
              <span class="card-title">连接统计</span>
            </template>
            <div class="connection-stats">
              <div class="stat-item">
                <el-statistic title="在线设备" :value="onlineEquipmentCount">
                  <template #suffix>
                    <el-icon class="online-icon">
                      <Connection />
                    </el-icon>
                  </template>
                </el-statistic>
              </div>
              <div class="stat-item">
                <el-statistic title="离线设备" :value="offlineEquipmentCount">
                  <template #suffix>
                    <el-icon class="offline-icon">
                      <Disconnect />
                    </el-icon>
                  </template>
                </el-statistic>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Tab页签 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- SECS消息监控 -->
        <el-tab-pane label="SECS消息监控" name="messages">
          <SecsMessageMonitor
            :messages="secsMessages"
            :equipment-list="equipmentList"
            @send="handleSendMessage"
            @retry="handleRetryMessage"
            @refresh="refreshMessages"
          />
        </el-tab-pane>

        <!-- GEM状态管理 -->
        <el-tab-pane label="GEM状态管理" name="gem">
          <div class="gem-management">
            <el-row :gutter="24">
              <!-- 状态变量 -->
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <div class="card-header">
                      <span class="card-title">状态变量 (SV)</span>
                      <el-button size="small" @click="refreshStatusVariables">
                        <el-icon><Refresh /></el-icon>
                        刷新
                      </el-button>
                    </div>
                  </template>

                  <div class="variables-table">
                    <el-table :data="statusVariables" stripe max-height="400">
                      <el-table-column prop="id" label="ID" width="100" />
                      <el-table-column prop="name" label="变量名" width="200" />
                      <el-table-column prop="value" label="当前值" width="120" />
                      <el-table-column prop="unit" label="单位" width="80" />
                      <el-table-column prop="timestamp" label="更新时间" width="160">
                        <template #default="{ row }">
                          {{ formatTimestamp(row.timestamp) }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-card>
              </el-col>

              <!-- 设备事件 -->
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <div class="card-header">
                      <span class="card-title">设备事件 (CEID)</span>
                      <el-button size="small" @click="refreshEquipmentEvents">
                        <el-icon><Refresh /></el-icon>
                        刷新
                      </el-button>
                    </div>
                  </template>

                  <div class="events-table">
                    <el-table :data="equipmentEvents" stripe max-height="400">
                      <el-table-column prop="id" label="ID" width="100" />
                      <el-table-column prop="name" label="事件名" width="200" />
                      <el-table-column label="状态" width="100">
                        <template #default="{ row }">
                          <el-tag :type="row.enabled ? 'success' : 'info'">
                            {{ row.enabled ? '启用' : '禁用' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="120">
                        <template #default="{ row }">
                          <el-switch
                            :model-value="row.enabled"
                            @change="handleToggleEvent(row.id, $event)"
                          />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <!-- GEM状态转换图 -->
            <div class="gem-state-diagram">
              <el-card>
                <template #header>
                  <span class="card-title">GEM状态转换图</span>
                </template>

                <div class="state-diagram">
                  <div class="state-flow">
                    <div
                      v-for="state in gemStates"
                      :key="state.value"
                      :class="[
                        'state-node',
                        { current: selectedEquipment?.gemState === state.value }
                      ]"
                    >
                      <div class="state-label">
                        {{ state.label }}
                      </div>
                      <div class="state-value">
                        {{ state.value }}
                      </div>
                    </div>
                  </div>

                  <div class="state-transitions">
                    <h4>状态说明:</h4>
                    <ul>
                      <li>
                        <strong>EQUIPMENT_OFFLINE:</strong>
                        设备离线，未建立通信
                      </li>
                      <li>
                        <strong>ATTEMPT_ONLINE:</strong>
                        尝试上线，正在建立通信
                      </li>
                      <li>
                        <strong>HOST_OFFLINE:</strong>
                        主机离线，设备等待主机连接
                      </li>
                      <li>
                        <strong>ONLINE_LOCAL:</strong>
                        本地在线，设备可以接收命令但不受主机控制
                      </li>
                      <li>
                        <strong>ONLINE_REMOTE:</strong>
                        远程在线，设备完全受主机控制
                      </li>
                    </ul>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- Recipe管理 -->
        <el-tab-pane label="Recipe管理" name="recipe">
          <div class="recipe-management">
            <div class="recipe-toolbar">
              <el-row :gutter="16" align="middle">
                <el-col :span="8">
                  <el-select
                    v-model="recipeEquipmentFilter"
                    placeholder="选择设备筛选Recipe"
                    clearable
                    @change="refreshRecipes"
                  >
                    <el-option
                      v-for="eq in equipmentList"
                      :key="eq.id"
                      :label="eq.name"
                      :value="eq.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="16">
                  <div class="toolbar-actions">
                    <el-button type="primary" @click="showCreateRecipeDialog = true">
                      <el-icon><Plus /></el-icon>
                      新建Recipe
                    </el-button>
                    <el-button @click="refreshRecipes">
                      <el-icon><Refresh /></el-icon>
                      刷新
                    </el-button>
                  </div>
                </el-col>
              </el-row>
            </div>

            <div class="recipe-list">
              <el-table :data="recipes" stripe>
                <el-table-column prop="name" label="Recipe名称" width="200" />
                <el-table-column prop="version" label="版本" width="100" />
                <el-table-column label="设备" width="150">
                  <template #default="{ row }">
                    {{ getEquipmentName(row.equipmentId) }}
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.isActive ? 'success' : 'info'">
                      {{ row.isActive ? '激活' : '未激活' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createdBy" label="创建者" width="120" />
                <el-table-column prop="createdAt" label="创建时间" width="160">
                  <template #default="{ row }">
                    {{ formatTimestamp(row.createdAt) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      :disabled="row.isActive"
                      @click="handleActivateRecipe(row)"
                    >
                      激活
                    </el-button>
                    <el-button size="small"
@click="handleViewRecipe(row)"
>
查看
</el-button>
                    <el-button size="small"
@click="handleEditRecipe(row)"
>
编辑
</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- Equipment Constants -->
        <el-tab-pane label="设备常量" name="constants">
          <div class="constants-management">
            <div class="constants-toolbar">
              <el-alert
                v-if="!selectedEquipment"
                title="请先选择设备"
                type="warning"
                show-icon
                :closable="false"
              />
              <el-button
v-else type="primary"
@click="refreshConstants"
>
                <el-icon><Refresh /></el-icon>
                刷新常量
              </el-button>
            </div>

            <div v-if="selectedEquipment" class="constants-table">
              <el-table :data="constants" stripe>
                <el-table-column prop="name" label="常量名" width="200" />
                <el-table-column label="当前值" width="150">
                  <template #default="{ row }">
                    <el-input
                      v-if="!row.isReadOnly && editingConstantId === row.id"
                      v-model="editingConstantValue"
                      size="small"
                      @blur="handleSaveConstant(row)"
                      @keyup.enter="handleSaveConstant(row)"
                    />
                    <span v-else @dblclick="handleEditConstant(row)">
                      {{ row.value }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="unit" label="单位" width="80" />
                <el-table-column label="范围" width="120">
                  <template #default="{ row }">
                    <span v-if="row.min !== undefined && row.max !== undefined">
                      {{ row.min }} ~ {{ row.max }}
                    </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" show-overflow-tooltip />
                <el-table-column label="只读" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.isReadOnly ? 'info' : 'success'" size="small">
                      {{ row.isReadOnly ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="{ row }">
                    <el-button
                      v-if="!row.isReadOnly"
                      type="text"
                      size="small"
                      @click="handleEditConstant(row)"
                    >
                      编辑
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- Recipe详情/编辑弹窗 -->
    <el-dialog
v-model="showRecipeDialog" :title="recipeDialogTitle"
width="70%"
>
      <div v-if="selectedRecipe" class="recipe-detail">
        <el-form :model="recipeForm" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="Recipe名称">
                <el-input v-model="recipeForm.name" :readonly="!isEditingRecipe" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="版本">
                <el-input v-model="recipeForm.version" :readonly="!isEditingRecipe" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="设备">
                <el-select
                  v-model="recipeForm.equipmentId"
                  :disabled="!isEditingRecipe"
                  style="width: 100%"
                >
                  <el-option
                    v-for="eq in equipmentList"
                    :key="eq.id"
                    :label="eq.name"
                    :value="eq.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="创建者">
                <el-input v-model="recipeForm.createdBy" readonly />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="描述">
            <el-input
              v-model="recipeForm.description"
              type="textarea"
              :rows="3"
              :readonly="!isEditingRecipe"
            />
          </el-form-item>
        </el-form>

        <!-- Recipe参数 -->
        <div class="recipe-parameters">
          <div class="parameter-header">
            <h4>Recipe参数</h4>
            <el-button
v-if="isEditingRecipe" type="primary"
size="small" @click="addParameter"
>
              <el-icon><Plus /></el-icon>
              添加参数
            </el-button>
          </div>

          <el-table :data="recipeForm.parameters" stripe>
            <el-table-column prop="name" label="参数名" width="150">
              <template #default="{ row, $index }">
                <el-input
v-if="isEditingRecipe" v-model="row.name"
size="small"
/>
                <span v-else>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="参数值" width="120">
              <template #default="{ row, $index }">
                <el-input
v-if="isEditingRecipe" v-model="row.value"
size="small"
/>
                <span v-else>{{ row.value }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" width="80">
              <template #default="{ row, $index }">
                <el-input
v-if="isEditingRecipe" v-model="row.unit"
size="small"
/>
                <span v-else>{{ row.unit }}</span>
              </template>
            </el-table-column>
            <el-table-column label="范围" width="150">
              <template #default="{ row, $index }">
                <div v-if="isEditingRecipe" style="display: flex; gap: 8px">
                  <el-input-number
                    v-model="row.min"
                    size="small"
                    :controls="false"
                    style="width: 70px"
                  />
                  <span>~</span>
                  <el-input-number
                    v-model="row.max"
                    size="small"
                    :controls="false"
                    style="width: 70px"
                  />
                </div>
                <span v-else>
                  {{
                    row.min !== undefined && row.max !== undefined ? `${row.min} ~ ${row.max}` : '-'
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="category" label="分类" width="100">
              <template #default="{ row, $index }">
                <el-select
v-if="isEditingRecipe" v-model="row.category"
size="small"
>
                  <el-option label="电源" value="POWER" />
                  <el-option label="时序" value="TIMING" />
                  <el-option label="保护" value="PROTECTION" />
                  <el-option label="键合" value="BONDING" />
                  <el-option label="热控" value="THERMAL" />
                  <el-option label="其他" value="OTHER" />
                </el-select>
                <span v-else>{{ row.category }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" show-overflow-tooltip>
              <template #default="{ row, $index }">
                <el-input
v-if="isEditingRecipe" v-model="row.description"
size="small"
/>
                <span v-else>{{ row.description }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="isEditingRecipe" label="操作" width="80">
              <template #default="{ row, $index }">
                <el-button
type="text" size="small"
@click="removeParameter($index)"
>
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div v-if="isEditingRecipe">
          <el-button @click="cancelEditRecipe">取消</el-button>
          <el-button type="primary"
@click="saveRecipe"
>
保存
</el-button>
        </div>
        <div v-else>
          <el-button @click="showRecipeDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import { Refresh, Connection, Disconnect, Plus } from '@element-plus/icons-vue'
  import { useEquipmentStore } from '@/stores/equipment'
  import { SecsMessageMonitor } from '@/components/equipment'
  import type {
    Equipment,
    Recipe,
    RecipeParameter,
    EquipmentConstant,
    SecsMessage
  } from '@/types/equipment'
  import { GemState } from '@/types/equipment'

  // 状态管理
  const equipmentStore = useEquipmentStore()

  // 页面状态
  const selectedEquipmentId = ref('')
  const activeTab = ref('messages')
  const recipeEquipmentFilter = ref('')

  // Recipe相关状态
  const showRecipeDialog = ref(false)
  const showCreateRecipeDialog = ref(false)
  const selectedRecipe = ref<Recipe | null>(null)
  const isEditingRecipe = ref(false)
  const recipeForm = ref<Recipe>({
    id: '',
    name: '',
    version: '',
    equipmentId: '',
    equipmentType: 'ATE' as any,
    description: '',
    parameters: [],
    isActive: false,
    createdBy: '',
    createdAt: '',
    updatedAt: ''
  })

  // 设备常量编辑状态
  const editingConstantId = ref('')
  const editingConstantValue = ref('')

  // 模拟数据
  const statusVariables = ref([
    {
      id: 'SV001',
      name: 'TEMPERATURE',
      value: 25.6,
      unit: '°C',
      timestamp: new Date().toISOString()
    },
    {
      id: 'SV002',
      name: 'PRESSURE',
      value: 1.2,
      unit: 'bar',
      timestamp: new Date().toISOString()
    }
  ])

  const equipmentEvents = ref([
    {
      id: 'CEID001',
      name: 'PROCESS_START',
      enabled: true
    },
    {
      id: 'CEID002',
      name: 'PROCESS_END',
      enabled: true
    },
    {
      id: 'CEID003',
      name: 'ALARM_SET',
      enabled: false
    }
  ])

  const gemStates = [
    { value: 'EQUIPMENT_OFFLINE', label: '设备离线' },
    { value: 'ATTEMPT_ONLINE', label: '尝试上线' },
    { value: 'HOST_OFFLINE', label: '主机离线' },
    { value: 'ONLINE_LOCAL', label: '本地在线' },
    { value: 'ONLINE_REMOTE', label: '远程在线' }
  ]

  // 计算属性
  const equipmentList = computed(() => equipmentStore.equipment)
  const secsMessages = computed(() => equipmentStore.secsMessages)
  const recipes = computed(() => equipmentStore.recipes)
  const constants = computed(() => equipmentStore.constants)

  const selectedEquipment = computed(() =>
    equipmentList.value.find(eq => eq.id === selectedEquipmentId.value)
  )

  const onlineEquipmentCount = computed(
    () => equipmentList.value.filter(eq => eq.isConnected).length
  )

  const offlineEquipmentCount = computed(
    () => equipmentList.value.filter(eq => !eq.isConnected).length
  )

  const recipeDialogTitle = computed(() => {
    if (isEditingRecipe.value) {
      return selectedRecipe.value?.id ? '编辑Recipe' : '新建Recipe'
    }
    return 'Recipe详情'
  })

  // 工具方法
  const getGemStateText = (state: GemState): string => {
    const stateObj = gemStates.find(s => s.value === state)
    return stateObj?.label || state
  }

  const getGemStateType = (state: GemState): string => {
    const typeMap: Record<GemState, string> = {
      [GemState.EQUIPMENT_OFFLINE]: 'danger',
      [GemState.ATTEMPT_ONLINE]: 'warning',
      [GemState.HOST_OFFLINE]: 'warning',
      [GemState.ONLINE_LOCAL]: 'success',
      [GemState.ONLINE_REMOTE]: 'success'
    }
    return typeMap[state] || 'info'
  }

  const getEquipmentName = (equipmentId: string): string => {
    const equipment = equipmentList.value.find(eq => eq.id === equipmentId)
    return equipment?.name || equipmentId
  }

  const formatTimestamp = (timestamp: string): string => {
    return new Date(timestamp).toLocaleString('zh-CN')
  }

  // 事件处理
  const refreshData = () => {
    equipmentStore.fetchEquipmentList()
    if (selectedEquipmentId.value) {
      refreshMessages()
      refreshRecipes()
      refreshConstants()
    }
  }

  const handleEquipmentChange = (equipmentId: string) => {
    selectedEquipmentId.value = equipmentId
    refreshMessages()
    refreshConstants()
  }

  const handleTabChange = (tabName: string) => {
    switch (tabName) {
      case 'messages':
        refreshMessages()
        break
      case 'recipe':
        refreshRecipes()
        break
      case 'constants':
        refreshConstants()
        break
    }
  }

  const handleGoOnline = async () => {
    try {
      await ElMessageBox.confirm('确定要将设备切换为在线状态吗？', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })
      ElMessage.success('设备已切换为在线状态')
    } catch (error) {
      // 用户取消
    }
  }

  const handleGoOffline = async () => {
    try {
      await ElMessageBox.confirm('确定要将设备切换为离线状态吗？', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      ElMessage.success('设备已切换为离线状态')
    } catch (error) {
      // 用户取消
    }
  }

  const refreshMessages = () => {
    if (selectedEquipmentId.value) {
      equipmentStore.fetchSecsMessages(selectedEquipmentId.value)
    }
  }

  const refreshRecipes = () => {
    equipmentStore.fetchRecipes(recipeEquipmentFilter.value)
  }

  const refreshConstants = () => {
    if (selectedEquipmentId.value) {
      equipmentStore.fetchConstants(selectedEquipmentId.value)
    }
  }

  const refreshStatusVariables = () => {
    // 刷新状态变量
    ElMessage.success('状态变量已刷新')
  }

  const refreshEquipmentEvents = () => {
    // 刷新设备事件
    ElMessage.success('设备事件已刷新')
  }

  const handleSendMessage = (data: any) => {
    equipmentStore.sendSecsMessage(data.equipmentId, {
      stream: data.stream,
      function: data.function,
      data: data.data
    })
  }

  const handleRetryMessage = (message: SecsMessage) => {
    // 重试发送消息
    ElMessage.info('消息重试发送中...')
  }

  const handleToggleEvent = async (eventId: string, enabled: boolean) => {
    try {
      // 调用API切换事件状态
      const event = equipmentEvents.value.find(e => e.id === eventId)
      if (event) {
        event.enabled = enabled
      }
      ElMessage.success(`事件${enabled ? '启用' : '禁用'}成功`)
    } catch (error) {
      ElMessage.error(`事件状态切换失败`)
    }
  }

  const handleActivateRecipe = async (recipe: Recipe) => {
    try {
      await equipmentStore.activateRecipe(recipe.id, recipe.equipmentId)
    } catch (error) {
      ElMessage.error('Recipe激活失败')
    }
  }

  const handleViewRecipe = (recipe: Recipe) => {
    selectedRecipe.value = recipe
    recipeForm.value = { ...recipe }
    isEditingRecipe.value = false
    showRecipeDialog.value = true
  }

  const handleEditRecipe = (recipe: Recipe) => {
    selectedRecipe.value = recipe
    recipeForm.value = { ...recipe }
    isEditingRecipe.value = true
    showRecipeDialog.value = true
  }

  const addParameter = () => {
    recipeForm.value.parameters.push({
      name: '',
      value: '',
      unit: '',
      description: '',
      category: 'OTHER'
    })
  }

  const removeParameter = (index: number) => {
    recipeForm.value.parameters.splice(index, 1)
  }

  const saveRecipe = async () => {
    try {
      if (selectedRecipe.value?.id) {
        // 更新Recipe
        await equipmentStore.updateRecipe(selectedRecipe.value.id, recipeForm.value)
        ElMessage.success('Recipe更新成功')
      } else {
        // 创建新Recipe
        await equipmentStore.createRecipe(recipeForm.value)
        ElMessage.success('Recipe创建成功')
      }
      showRecipeDialog.value = false
      refreshRecipes()
    } catch (error) {
      ElMessage.error('Recipe保存失败')
    }
  }

  const cancelEditRecipe = () => {
    showRecipeDialog.value = false
    selectedRecipe.value = null
    isEditingRecipe.value = false
  }

  const handleEditConstant = (constant: EquipmentConstant) => {
    if (constant.isReadOnly) return

    editingConstantId.value = constant.id
    editingConstantValue.value = String(constant.value)
  }

  const handleSaveConstant = async (constant: EquipmentConstant) => {
    if (editingConstantId.value !== constant.id) return

    try {
      let newValue: string | number | boolean = editingConstantValue.value

      // 尝试转换为数字或布尔值
      if (!isNaN(Number(newValue))) {
        newValue = Number(newValue)
      } else if (newValue.toLowerCase() === 'true' || newValue.toLowerCase() === 'false') {
        newValue = newValue.toLowerCase() === 'true'
      }

      await equipmentStore.updateConstant(selectedEquipmentId.value, constant.id, newValue)
      ElMessage.success('常量更新成功')
    } catch (error) {
      ElMessage.error('常量更新失败')
    } finally {
      editingConstantId.value = ''
      editingConstantValue.value = ''
    }
  }

  // 生命周期
  onMounted(() => {
    equipmentStore.fetchEquipmentList()

    // 选择第一个设备
    if (equipmentList.value.length > 0) {
      selectedEquipmentId.value = equipmentList.value[0].id
      handleEquipmentChange(selectedEquipmentId.value)
    }
  })
</script>

<style lang="scss" scoped>
  .secs-gem-manager {
    padding: var(--spacing-4);

    .page-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: var(--spacing-6);

      .header-left {
        .page-title {
          margin: 0 0 var(--spacing-2);
          font-size: 1.75rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .page-description {
          margin: 0;
          color: var(--color-text-secondary);
        }
      }
    }

    .equipment-overview {
      margin-bottom: var(--spacing-6);

      .card-title {
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .equipment-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .gem-status {
        .status-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--spacing-3);

          .label {
            font-weight: 500;
            color: var(--color-text-secondary);
          }
        }

        .status-actions {
          display: flex;
          gap: var(--spacing-2);
          margin-top: var(--spacing-3);
        }
      }

      .no-equipment {
        padding: var(--spacing-4) 0;
        text-align: center;
      }

      .connection-stats {
        .stat-item {
          margin-bottom: var(--spacing-3);

          .online-icon {
            color: var(--color-success);
          }

          .offline-icon {
            color: var(--color-danger);
          }
        }
      }
    }

    .main-content {
      .gem-management {
        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .card-title {
            font-weight: 600;
            color: var(--color-text-primary);
          }
        }

        .variables-table,
        .events-table {
          margin-top: var(--spacing-3);
        }

        .gem-state-diagram {
          margin-top: var(--spacing-4);

          .state-diagram {
            .state-flow {
              display: flex;
              flex-wrap: wrap;
              gap: var(--spacing-3);
              align-items: center;
              justify-content: space-around;
              margin-bottom: var(--spacing-4);

              .state-node {
                min-width: 120px;
                padding: var(--spacing-3);
                text-align: center;
                background-color: var(--color-bg-soft);
                border: 2px solid var(--color-border-light);
                border-radius: var(--radius-base);
                transition: all 0.3s ease;

                &.current {
                  background-color: rgb(64 158 255 / 10%);
                  border-color: var(--color-primary);
                  transform: scale(1.05);
                }

                .state-label {
                  margin-bottom: var(--spacing-1);
                  font-weight: 500;
                  color: var(--color-text-primary);
                }

                .state-value {
                  font-family: var(--font-mono);
                  font-size: 0.75rem;
                  color: var(--color-text-secondary);
                }
              }
            }

            .state-transitions {
              padding: var(--spacing-4);
              background-color: var(--color-bg-soft);
              border-radius: var(--radius-base);

              h4 {
                margin-top: 0;
                margin-bottom: var(--spacing-3);
                color: var(--color-text-primary);
              }

              ul {
                padding-left: var(--spacing-4);
                margin: 0;

                li {
                  margin-bottom: var(--spacing-2);
                  color: var(--color-text-secondary);

                  strong {
                    font-family: var(--font-mono);
                    color: var(--color-text-primary);
                  }
                }
              }
            }
          }
        }
      }

      .recipe-management {
        .recipe-toolbar {
          padding: var(--spacing-3);
          margin-bottom: var(--spacing-4);
          background-color: var(--color-bg-soft);
          border-radius: var(--radius-base);

          .toolbar-actions {
            display: flex;
            gap: var(--spacing-2);
            justify-content: flex-end;
          }
        }

        .recipe-list {
          padding: var(--spacing-2);
          background-color: var(--color-bg-primary);
          border-radius: var(--radius-base);
        }
      }

      .constants-management {
        .constants-toolbar {
          margin-bottom: var(--spacing-4);
        }

        .constants-table {
          padding: var(--spacing-2);
          background-color: var(--color-bg-primary);
          border-radius: var(--radius-base);
        }
      }
    }

    .recipe-detail {
      .recipe-parameters {
        margin-top: var(--spacing-4);

        .parameter-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--spacing-3);

          h4 {
            margin: 0;
            color: var(--color-text-primary);
          }
        }
      }
    }
  }
</style>
