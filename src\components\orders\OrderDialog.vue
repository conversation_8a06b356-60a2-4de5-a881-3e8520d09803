<template>
  <el-dialog
    v-model="visible"
    :title="mode === 'create' ? '新建订单' : '编辑订单'"
    width="800px"
    destroy-on-close
    @close="handleClose"
  >
    <div class="order-dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <div class="form-grid">
          <el-form-item label="订单编号" prop="orderNumber">
            <el-input
              v-model="formData.orderNumber"
              placeholder="系统自动生成"
              :disabled="mode === 'edit'"
            />
          </el-form-item>

          <el-form-item label="客户名称" prop="customer">
            <el-select
              v-model="formData.customer"
              placeholder="请选择客户"
              filterable
              class="w-full"
            >
              <el-option
                v-for="customer in customerOptions"
                :key="customer.value"
                :label="customer.label"
                :value="customer.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="产品名称" prop="productName">
            <el-input
v-model="formData.productName" placeholder="请输入产品名称"
/>
          </el-form-item>

          <el-form-item label="封装类型" prop="packageType">
            <el-select
v-model="formData.packageType" placeholder="请选择封装类型"
class="w-full"
>
              <el-option
                v-for="pkg in packageTypes"
                :key="pkg.value"
                :label="pkg.label"
                :value="pkg.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="订单数量" prop="quantity">
            <el-input-number
              v-model="formData.quantity"
              :min="1"
              :max="10000"
              controls-position="right"
              class="w-full"
            />
            <span class="unit-text">K pcs</span>
          </el-form-item>

          <el-form-item label="优先级" prop="priority">
            <el-select
v-model="formData.priority" placeholder="请选择优先级"
class="w-full"
>
              <el-option label="普通" value="low" />
              <el-option label="一般" value="medium" />
              <el-option label="紧急" value="high" />
              <el-option label="特急" value="urgent" />
            </el-select>
          </el-form-item>

          <el-form-item label="交付日期" prop="deliveryDate">
            <el-date-picker
              v-model="formData.deliveryDate"
              type="date"
              placeholder="请选择交付日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
            />
          </el-form-item>

          <el-form-item label="备注" prop="remarks" class="full-width">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          {{ mode === 'create' ? '创建订单' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import type { FormInstance, FormRules } from 'element-plus'

  interface Props {
    modelValue: boolean
    orderData?: any
    mode: 'create' | 'edit'
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'confirm', data: any): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  const formRef = ref<FormInstance>()
  const loading = ref(false)

  const visible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  const formData = reactive({
    orderNumber: '',
    customer: '',
    productName: '',
    packageType: '',
    quantity: 100,
    priority: 'medium',
    deliveryDate: '',
    remarks: ''
  })

  const formRules: FormRules = {
    customer: [{ required: true, message: '请选择客户', trigger: 'change' }],
    productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
    packageType: [{ required: true, message: '请选择封装类型', trigger: 'change' }],
    quantity: [{ required: true, message: '请输入订单数量', trigger: 'blur' }],
    deliveryDate: [{ required: true, message: '请选择交付日期', trigger: 'change' }]
  }

  const customerOptions = [
    { label: 'Apple Inc.', value: 'apple' },
    { label: 'Samsung Electronics', value: 'samsung' },
    { label: 'TSMC', value: 'tsmc' },
    { label: 'Qualcomm', value: 'qualcomm' },
    { label: 'MediaTek', value: 'mediatek' }
  ]

  const packageTypes = [
    { label: 'QFP (Quad Flat Package)', value: 'QFP' },
    { label: 'BGA (Ball Grid Array)', value: 'BGA' },
    { label: 'CSP (Chip Scale Package)', value: 'CSP' },
    { label: 'FC (Flip Chip)', value: 'FC' },
    { label: 'SOP (Small Outline Package)', value: 'SOP' },
    { label: 'TSSOP (Thin Shrink Small Outline Package)', value: 'TSSOP' }
  ]

  const handleClose = () => {
    visible.value = false
    resetForm()
  }

  const handleConfirm = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      emit('confirm', { ...formData })
      handleClose()
    } catch (error) {
      ElMessage.error('请检查表单输入')
    } finally {
      loading.value = false
    }
  }

  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    Object.assign(formData, {
      orderNumber: '',
      customer: '',
      productName: '',
      packageType: '',
      quantity: 100,
      priority: 'medium',
      deliveryDate: '',
      remarks: ''
    })
  }

  // 监听数据变化
  watch(
    () => props.orderData,
    newData => {
      if (newData && props.modelValue) {
        Object.assign(formData, {
          orderNumber: newData.orderNumber || '',
          customer: newData.customer?.name || '',
          productName: newData.productInfo?.name || '',
          packageType: newData.productInfo?.packageType || '',
          quantity: newData.productInfo?.quantity || 100,
          priority: newData.priority || 'medium',
          deliveryDate: newData.deliveryDate || '',
          remarks: newData.remarks || ''
        })
      }
    },
    { immediate: true }
  )

  watch(
    () => props.modelValue,
    isVisible => {
      if (isVisible && props.mode === 'create') {
        resetForm()
        // 生成订单编号
        formData.orderNumber = `CIM${Date.now().toString().slice(-6)}`
      }
    }
  )
</script>

<style lang="scss" scoped>
  .order-dialog-content {
    padding: var(--spacing-4) 0;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);

    @media (width <= 768px) {
      grid-template-columns: 1fr;
    }

    .full-width {
      grid-column: 1 / -1;
    }
  }

  .unit-text {
    margin-left: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  .dialog-footer {
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
  }

  .w-full {
    width: 100%;
  }
</style>
