<template>
  <div class="home-page">
    <!-- 专业级英雄区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <div class="hero-badge">
            <svg class="hero-badge__icon" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"
              />
            </svg>
            <span>Professional IC CIM System</span>
          </div>
          <h1 class="hero-title">IC封测CIM系统</h1>
          <h2 class="hero-subtitle">专业制造执行系统</h2>
          <p class="hero-description">
            基于极简主义设计理念的半导体封装测试制造执行系统
            <br />
            提供从CP晶圆电测到Assembly封装再到FT成品测试的完整工艺流程管控
          </p>
          <div class="hero-actions">
            <el-button
              type="primary"
              size="large"
              class="hero-button"
              @click="$router.push('/inquiry/list')"
            >
              <svg
                class="button-icon"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
              >
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                <polyline points="14,2 14,8 20,8" />
                <line
x1="9" y1="15"
x2="15" y2="15"
/>
                <line
x1="9" y1="11"
x2="15" y2="11"
/>
              </svg>
              客户询价
            </el-button>
            <el-button
              size="large"
              class="hero-button hero-button--secondary"
              @click="$router.push('/production')"
            >
              <svg
                class="button-icon"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
              >
                <rect x="3" y="4" width="18" height="16" rx="2" />
                <path d="M7 8h10" />
                <path d="M7 12h7" />
              </svg>
              生产计划
            </el-button>
          </div>
          <div class="hero-metrics">
            <div
v-for="metric in quickMetrics" class="hero-metric"
:key="metric.key"
>
              <div class="hero-metric__value">
                {{ metric.value }}
              </div>
              <div class="hero-metric__label">
                {{ metric.label }}
              </div>
            </div>
          </div>
        </div>
        <div class="hero-visual">
          <div class="wafer-visualization">
            <div class="wafer-circle">
              <div class="wafer-dies">
                <div
                  v-for="n in 64"
                  :key="n"
                  class="wafer-die"
                  :class="{
                    'wafer-die--pass': Math.random() > 0.15,
                    'wafer-die--fail': Math.random() <= 0.15
                  }"
                />
              </div>
            </div>
            <div class="wafer-label">实时晶圆图监控</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心功能特性 -->
    <section class="features-section">
      <div class="section-container">
        <div class="section-header">
          <h3 class="section-title">核心功能特性</h3>
          <p class="section-subtitle">专业级IC封测制造执行系统的完整功能模块</p>
        </div>
        <div class="features-grid">
          <div
            v-for="feature in features"
            :key="feature.id"
            class="feature-card"
            @click="handleFeatureClick(feature)"
          >
            <div class="feature-card__header">
              <div class="feature-icon" :class="feature.iconClass">
                <svg viewBox="0 0 24 24"
v-html="feature.icon"
/>
              </div>
              <h4 class="feature-title">
                {{ feature.title }}
              </h4>
            </div>
            <p class="feature-description">
              {{ feature.description }}
            </p>
            <div class="feature-tags">
              <span
v-for="tag in feature.tags" :key="tag"
class="feature-tag"
>{{ tag }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 实时数据仪表板 -->
    <section class="dashboard-section">
      <div class="section-container">
        <div class="section-header">
          <h3 class="section-title">实时生产数据</h3>
          <p class="section-subtitle">关键绩效指标实时监控</p>
        </div>
        <div class="dashboard-grid">
          <div v-for="kpi in kpiData" :key="kpi.key" class="kpi-card">
            <div class="kpi-card__header">
              <div class="kpi-icon" :class="kpi.iconClass">
                <svg viewBox="0 0 24 24"
v-html="kpi.icon"
/>
              </div>
              <div class="kpi-trend" :class="kpi.trendClass">
                <svg
                  v-if="kpi.trend === 'up'"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path d="M7 14l5-5 5 5" />
                </svg>
                <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M7 10l5 5 5-5" />
                </svg>
                <span>{{ kpi.changePercent }}</span>
              </div>
            </div>
            <div class="kpi-value">
              {{ kpi.value }}
            </div>
            <div class="kpi-label">
              {{ kpi.label }}
            </div>
            <div class="kpi-detail">
              {{ kpi.detail }}
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 系统优势展示 -->
    <section class="advantages-section">
      <div class="section-container">
        <div class="advantages-content">
          <div class="advantages-text">
            <h3 class="section-title">专业级IC封测解决方案</h3>
            <div class="advantages-list">
              <div v-for="advantage in advantages" :key="advantage.title" class="advantage-item">
                <div class="advantage-icon">
                  <svg viewBox="0 0 24 24"
v-html="advantage.icon"
/>
                </div>
                <div class="advantage-content">
                  <h5 class="advantage-title">
                    {{ advantage.title }}
                  </h5>
                  <p class="advantage-description">
                    {{ advantage.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="advantages-visual">
            <div class="process-flow">
              <div
v-for="step in processSteps" class="process-step"
:key="step.id"
>
                <div class="process-step__icon" :class="step.iconClass">
                  <svg viewBox="0 0 24 24"
v-html="step.icon"
/>
                </div>
                <div class="process-step__label">
                  {{ step.label }}
                </div>
              </div>
              <svg class="process-flow-line" viewBox="0 0 300 60">
                <path
                  d="M20 30 L280 30"
                  stroke="var(--color-border-light)"
                  stroke-width="2"
                  fill="none"
                  stroke-dasharray="5,5"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  // 快速指标数据
  const quickMetrics = ref([
    { key: 'efficiency', value: '98.5%', label: '设备综合效率' },
    { key: 'yield', value: '99.2%', label: '产品良率' },
    { key: 'orders', value: '156', label: '今日订单' }
  ])

  // 核心功能特性
  const features = ref([
    {
      id: 1,
      title: '客户询价管理',
      description:
        'IC封装测试询价全流程管理，从客户需求分析到报价确认的数字化基础，支持汽车电子、消费电子等多领域IC产品询价',
      icon: '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="9" y1="15" x2="15" y2="15"/><line x1="9" y1="11" x2="15" y2="11"/>',
      iconClass: 'feature-icon--inquiry',
      tags: ['询价管理', '报价流程', '客户沟通'],
      route: '/inquiry/list'
    },
    {
      id: 2,
      title: '订单驱动制造',
      description:
        '完整的订单生命周期管理，从客户需求分析到产品交付的全流程数字化管控，支持多客户并行生产',
      icon: '<path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/><rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>',
      iconClass: 'feature-icon--primary',
      tags: ['订单管理', '生产计划', '客户服务'],
      route: '/orders'
    },
    {
      id: 3,
      title: 'CP晶圆电测',
      description: '专业的晶圆探针测试管理系统，支持多站点并行测试、实时良率统计和智能晶圆图分析',
      icon: '<circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>',
      iconClass: 'feature-icon--wafer',
      tags: ['电性测试', '良率分析', '晶圆图'],
      route: '/production'
    },
    {
      id: 4,
      title: 'Assembly封装',
      description: '精密封装工艺控制系统，支持QFP/BGA/CSP/FC等多种封装形式，确保微米级精度控制',
      icon: '<rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/>',
      iconClass: 'feature-icon--assembly',
      tags: ['封装工艺', 'QFP/BGA', '精度控制'],
      route: '/production'
    },
    {
      id: 5,
      title: 'FT成品测试',
      description: '完整的最终测试流程管理，包含功能测试、烧录程序、老化筛选和电性验证',
      icon: '<path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>',
      iconClass: 'feature-icon--test',
      tags: ['功能测试', '老化筛选', '质量验证'],
      route: '/production'
    },
    {
      id: 6,
      title: '质量追溯系统',
      description: '从晶圆批次到最终产品的完整质量追溯链，满足IATF16949和客户质量要求',
      icon: '<path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9z"/>',
      iconClass: 'feature-icon--trace',
      tags: ['质量追溯', 'IATF16949', '客户报告'],
      route: '/orders'
    },
    {
      id: 7,
      title: '设备协同平台',
      description: '基于SECS/GEM协议的设备集成平台，实现设备状态监控、参数管理和预测性维护',
      icon: '<rect x="4" y="2" width="16" height="16" rx="2"/><rect x="9" y="9" width="6" height="6"/><path d="M9 1v2m0 18v2M20 9h2M2 9h2m15.5-6.5 1.5 1.5M3 21l1.5-1.5M20.5 17.5l1.5 1.5M3 3l1.5 1.5"/>',
      iconClass: 'feature-icon--equipment',
      tags: ['SECS/GEM', '设备监控', '预测维护'],
      route: '/equipment'
    },
    {
      id: 8,
      title: '客户主数据管理',
      description:
        'IC设计公司客户标准化主数据维护，为业务流程提供准确数据源，支持批量导入和数据校验',
      icon: '<path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/><rect x="8" y="2" width="8" height="4" rx="1" ry="1"/><line x1="9" y1="9" x2="15" y2="9"/><line x1="9" y1="13" x2="15" y2="13"/>',
      iconClass: 'feature-icon--master-data',
      tags: ['主数据管理', '客户信息', '数据标准化'],
      route: '/master-data/customers'
    },
    {
      id: 9,
      title: '客户联系人管理',
      description: 'IC设计客户关键联系人全生命周期管理，支持多角色联系人维护和沟通历史跟踪',
      icon: '<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/><path d="M21 21v-2a4 4 0 0 0-3-3.85"/>',
      iconClass: 'feature-icon--contact',
      tags: ['客户管理', '联系人维护', '沟通跟踪'],
      route: '/customer/contacts'
    }
  ])

  // KPI数据
  const kpiData = ref([
    {
      key: 'yield',
      label: '整体良率',
      value: '99.2%',
      detail: '较昨日 +0.3%',
      trend: 'up',
      changePercent: '+0.3%',
      trendClass: 'kpi-trend--up',
      iconClass: 'kpi-icon--success',
      icon: '<path d="M22 12h-4l-3 9L9 3l-3 9H2"/>'
    },
    {
      key: 'efficiency',
      label: '设备综合效率(OEE)',
      value: '87.5%',
      detail: '较昨日 +2.1%',
      trend: 'up',
      changePercent: '+2.1%',
      trendClass: 'kpi-trend--up',
      iconClass: 'kpi-icon--primary',
      icon: '<circle cx="12" cy="12" r="10"/><path d="M8 14s1.5 2 4 2 4-2 4-2"/><line x1="9" y1="9" x2="9.01" y2="9"/><line x1="15" y1="9" x2="15.01" y2="9"/>'
    },
    {
      key: 'orders',
      label: '在制订单',
      value: '156',
      detail: '待完成 24 订单',
      trend: 'stable',
      changePercent: '0%',
      trendClass: 'kpi-trend--stable',
      iconClass: 'kpi-icon--info',
      icon: '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/>'
    },
    {
      key: 'throughput',
      label: '日产能达成率',
      value: '102%',
      detail: '超额完成 2%',
      trend: 'up',
      changePercent: '+5%',
      trendClass: 'kpi-trend--up',
      iconClass: 'kpi-icon--warning',
      icon: '<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,15 17,10"/><line x1="12" y1="15" x2="12" y2="3"/>'
    }
  ])

  // 系统优势
  const advantages = ref([
    {
      title: 'IATF16949质量体系',
      description: '完全符合汽车行业质量管理体系要求，确保产品质量和客户满意度',
      icon: '<path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>'
    },
    {
      title: '极简主义UI设计',
      description: '采用现代化极简设计理念，提供直观高效的用户体验和操作界面',
      icon: '<rect x="3" y="4" width="18" height="16" rx="2"/><path d="M7 8h10"/><path d="M7 12h4"/>'
    },
    {
      title: 'IC封测专业化',
      description: '专门针对半导体封装测试行业优化，支持各类封装工艺和测试流程',
      icon: '<circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>'
    },
    {
      title: '实时数据驱动',
      description: '基于实时数据采集和分析，提供智能决策支持和预测性维护',
      icon: '<polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>'
    }
  ])

  // 工艺流程步骤
  const processSteps = ref([
    {
      id: 1,
      label: 'CP电测',
      icon: '<circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>',
      iconClass: 'process-step__icon--cp'
    },
    {
      id: 2,
      label: 'Assembly',
      icon: '<rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/>',
      iconClass: 'process-step__icon--assembly'
    },
    {
      id: 3,
      label: 'FT测试',
      icon: '<path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>',
      iconClass: 'process-step__icon--ft'
    },
    {
      id: 4,
      label: '成品交付',
      icon: '<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,15 17,10"/>',
      iconClass: 'process-step__icon--delivery'
    }
  ])

  // 处理功能点击
  const handleFeatureClick = (feature: any) => {
    if (feature.route) {
      router.push(feature.route)
    }
  }

  // 组件挂载
  onMounted(() => {
    console.log('🏠 IC封测CIM系统主页已加载')
  })
</script>

<style lang="scss" scoped>
  // IC封测CIM系统主页 - 专业级样式
  // 遵循极简主义设计系统

  .home-page {
    min-height: calc(100vh - var(--header-height));
    background-color: var(--color-bg-primary);
  }

  // ===== 英雄区域 =====
  .hero-section {
    position: relative;
    padding: var(--spacing-12) 0 var(--spacing-16) 0;
    overflow: hidden;
    background: linear-gradient(
      135deg,
      var(--color-bg-primary) 0%,
      var(--color-bg-secondary) 50%,
      var(--color-wafer) 100%
    );
    border-bottom: 1px solid var(--color-border-light);

    &::before {
      position: absolute;
      inset: 0;
      content: '';
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23e2e8f0" opacity="0.3"/></svg>')
        repeat;
      background-size: 50px 50px;
      opacity: 0.5;
    }
  }

  .hero-container {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-12);
    align-items: center;
    max-width: 1200px;
    padding: 0 var(--spacing-6);
    margin: 0 auto;

    @media (width <= 1024px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-8);
      text-align: center;
    }
  }

  .hero-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
  }

  .hero-badge {
    display: inline-flex;
    gap: var(--spacing-2);
    align-items: center;
    width: fit-content;
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-inverse);
    background-color: var(--color-primary);
    border-radius: var(--radius-full);

    &__icon {
      width: 16px;
      height: 16px;
    }

    @media (width <= 1024px) {
      align-self: center;
    }
  }

  .hero-title {
    margin: 0;
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--color-text-primary);

    @media (width <= 768px) {
      font-size: var(--font-size-3xl);
    }
  }

  .hero-subtitle {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    color: var(--color-primary);

    @media (width <= 768px) {
      font-size: var(--font-size-lg);
    }
  }

  .hero-description {
    margin: 0;
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    color: var(--color-text-secondary);

    @media (width <= 768px) {
      font-size: var(--font-size-base);
    }
  }

  .hero-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4);

    @media (width <= 1024px) {
      justify-content: center;
    }

    @media (width <= 640px) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .hero-button {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    height: 48px;
    padding: 0 var(--spacing-6);
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);

    .button-icon {
      width: 20px;
      height: 20px;
    }

    &--secondary {
      color: var(--color-text-primary);
      background-color: var(--color-bg-tertiary);
      border: 1px solid var(--color-border-base);

      &:hover {
        background-color: var(--color-bg-hover);
        border-color: var(--color-primary);
      }
    }
  }

  .hero-metrics {
    display: flex;
    gap: var(--spacing-6);

    @media (width <= 1024px) {
      justify-content: center;
    }

    @media (width <= 640px) {
      gap: var(--spacing-4);
    }
  }

  .hero-metric {
    text-align: center;

    &__value {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      line-height: var(--line-height-tight);
      color: var(--color-primary);
    }

    &__label {
      margin-top: var(--spacing-1);
      font-size: var(--font-size-xs);
      color: var(--color-text-tertiary);
    }
  }

  // ===== 晶圆可视化 =====
  .hero-visual {
    display: flex;
    align-items: center;
    justify-content: center;

    @media (width <= 1024px) {
      order: -1;
    }
  }

  .wafer-visualization {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: center;
  }

  .wafer-circle {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 280px;
    height: 280px;
    background-color: var(--color-wafer);
    border: 3px solid var(--color-border-base);
    border-radius: 50%;
    box-shadow: var(--shadow-lg);

    @media (width <= 640px) {
      width: 240px;
      height: 240px;
    }
  }

  .wafer-dies {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 3px;
    width: 200px;
    height: 200px;

    @media (width <= 640px) {
      width: 160px;
      height: 160px;
    }
  }

  .wafer-die {
    width: 100%;
    aspect-ratio: 1;
    border-radius: 2px;
    transition: all var(--transition-fast);

    &--pass {
      background-color: var(--color-die-pass);
    }

    &--fail {
      background-color: var(--color-die-fail);
    }

    &:hover {
      z-index: 1;
      transform: scale(1.2);
    }
  }

  .wafer-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
    text-align: center;
  }

  // ===== 通用区域样式 =====
  .section-container {
    max-width: 1200px;
    padding: 0 var(--spacing-6);
    margin: 0 auto;

    @media (width <= 768px) {
      padding: 0 var(--spacing-4);
    }
  }

  .section-header {
    margin-bottom: var(--spacing-10);
    text-align: center;
  }

  .section-title {
    margin-bottom: var(--spacing-3);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);

    @media (width <= 768px) {
      font-size: var(--font-size-xl);
    }
  }

  .section-subtitle {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--color-text-secondary);

    @media (width <= 768px) {
      font-size: var(--font-size-base);
    }
  }

  // ===== 功能特性区域 =====
  .features-section {
    padding: var(--spacing-16) 0;
    background-color: var(--color-bg-secondary);
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-6);

    @media (width <= 640px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-4);
    }
  }

  .feature-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: var(--spacing-6);
    cursor: pointer;
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);

    &:hover {
      border-color: var(--color-primary);
      box-shadow: var(--shadow-lg);
      transform: translateY(-2px);
    }

    &__header {
      display: flex;
      gap: var(--spacing-4);
      align-items: center;
      margin-bottom: var(--spacing-4);
    }
  }

  .feature-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);

    svg {
      width: 24px;
      height: 24px;
      fill: none;
      stroke: currentcolor;
      stroke-width: 1.5;
    }

    &--primary {
      color: var(--color-primary);
      background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
    }

    &--wafer {
      color: var(--color-primary);
      background-color: color-mix(in srgb, var(--color-wafer) 80%, transparent);
    }

    &--assembly {
      color: var(--color-warning);
      background-color: color-mix(in srgb, var(--color-warning) 10%, transparent);
    }

    &--test {
      color: var(--color-success);
      background-color: color-mix(in srgb, var(--color-success) 10%, transparent);
    }

    &--trace {
      color: var(--color-info);
      background-color: color-mix(in srgb, var(--color-info) 10%, transparent);
    }

    &--equipment {
      color: #8b5cf6;
      background-color: color-mix(in srgb, #8b5cf6 10%, transparent);
    }

    &--master-data {
      color: #10b981;
      background-color: color-mix(in srgb, #10b981 10%, transparent);
    }

    &--contact {
      color: #f59e0b;
      background-color: color-mix(in srgb, #f59e0b 10%, transparent);
    }

    &--inquiry {
      color: var(--color-primary);
      background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
    }
  }

  .feature-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
  }

  .feature-description {
    flex-grow: 1;
    margin-bottom: var(--spacing-4);
    line-height: var(--line-height-relaxed);
    color: var(--color-text-secondary);
  }

  .feature-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
  }

  .feature-tag {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-tertiary);
    background-color: var(--color-bg-tertiary);
    border-radius: var(--radius-full);
  }

  // ===== 实时数据仪表板 =====
  .dashboard-section {
    padding: var(--spacing-16) 0;
    background-color: var(--color-bg-primary);
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);

    @media (width <= 640px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-4);
    }
  }

  .kpi-card {
    padding: var(--spacing-5);
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);

    &:hover {
      box-shadow: var(--shadow-md);
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-4);
    }
  }

  .kpi-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-base);

    svg {
      width: 20px;
      height: 20px;
      fill: none;
      stroke: currentcolor;
      stroke-width: 1.5;
    }

    &--success {
      color: var(--color-success);
      background-color: color-mix(in srgb, var(--color-success) 10%, transparent);
    }

    &--primary {
      color: var(--color-primary);
      background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
    }

    &--info {
      color: var(--color-info);
      background-color: color-mix(in srgb, var(--color-info) 10%, transparent);
    }

    &--warning {
      color: var(--color-warning);
      background-color: color-mix(in srgb, var(--color-warning) 10%, transparent);
    }
  }

  .kpi-trend {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);

    svg {
      width: 16px;
      height: 16px;
    }

    &--up {
      color: var(--color-success);
    }

    &--stable {
      color: var(--color-text-tertiary);
    }
  }

  .kpi-value {
    margin-bottom: var(--spacing-1);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
  }

  .kpi-label {
    margin-bottom: var(--spacing-1);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
  }

  .kpi-detail {
    font-size: var(--font-size-xs);
    color: var(--color-text-tertiary);
  }

  // ===== 系统优势区域 =====
  .advantages-section {
    padding: var(--spacing-16) 0;
    background-color: var(--color-bg-secondary);
  }

  .advantages-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-12);
    align-items: center;

    @media (width <= 1024px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-8);
    }
  }

  .advantages-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
  }

  .advantage-item {
    display: flex;
    gap: var(--spacing-4);
    align-items: flex-start;
  }

  .advantage-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--color-primary);
    background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
    border-radius: var(--radius-base);

    svg {
      width: 20px;
      height: 20px;
      fill: none;
      stroke: currentcolor;
      stroke-width: 1.5;
    }
  }

  .advantage-content {
    flex: 1;
  }

  .advantage-title {
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
  }

  .advantage-description {
    margin: 0;
    line-height: var(--line-height-relaxed);
    color: var(--color-text-secondary);
  }

  // ===== 工艺流程可视化 =====
  .advantages-visual {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .process-flow {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 400px;
  }

  .process-step {
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: center;

    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      background-color: var(--color-card-bg);
      border: 2px solid var(--color-border-light);
      border-radius: var(--radius-full);
      transition: all var(--transition-normal);

      svg {
        width: 24px;
        height: 24px;
        fill: none;
        stroke: currentcolor;
        stroke-width: 1.5;
      }

      &--cp {
        color: #06b6d4;
        background-color: color-mix(in srgb, #06b6d4 10%, transparent);
        border-color: #06b6d4;
      }

      &--assembly {
        color: var(--color-warning);
        background-color: color-mix(in srgb, var(--color-warning) 10%, transparent);
        border-color: var(--color-warning);
      }

      &--ft {
        color: var(--color-success);
        background-color: color-mix(in srgb, var(--color-success) 10%, transparent);
        border-color: var(--color-success);
      }

      &--delivery {
        color: var(--color-primary);
        background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
        border-color: var(--color-primary);
      }
    }

    &__label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-secondary);
      text-align: center;
    }
  }

  .process-flow-line {
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 2px;
    transform: translateY(-50%);
  }

  // ===== 响应式优化 =====
  @media (width <= 1024px) {
    .features-grid,
    .dashboard-grid {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
  }

  @media (width <= 768px) {
    .hero-section {
      padding: var(--spacing-8) 0 var(--spacing-12) 0;
    }

    .features-section,
    .dashboard-section,
    .advantages-section {
      padding: var(--spacing-12) 0;
    }

    .section-header {
      margin-bottom: var(--spacing-8);
    }
  }

  @media (width <= 640px) {
    .hero-container {
      padding: 0 var(--spacing-4);
    }

    .features-grid,
    .dashboard-grid {
      grid-template-columns: 1fr;
    }

    .process-flow {
      flex-direction: column;
      gap: var(--spacing-4);

      .process-flow-line {
        display: none;
      }
    }
  }
</style>
