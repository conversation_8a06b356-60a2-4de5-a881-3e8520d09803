/**
 * IC封测CIM系统 - 客户沟通记录模拟数据
 * Communication Records Mock Data for IC Packaging & Testing CIM System
 */

import type {
  CommunicationRecord,
  CommunicationRecordType,
  CommunicationScene,
  CommunicationStatus,
  CommunicationResult,
  CommunicationAttachment,
  FollowUpTask,
  CommunicationStatistics
} from '@/types/customer'

// 真实的IC封测行业沟通记录模拟数据
export const mockCommunicationRecords: CommunicationRecord[] = [
  {
    id: 'comm_001',
    contactId: 'contact_001_01',
    customerId: 'customer_001',
    type: 'Meeting' as CommunicationRecordType,
    scene: 'TechDiscussion' as CommunicationScene,
    subject: 'Kirin 9000 5G芯片封装方案技术讨论',
    date: '2024-01-15',
    startTime: '14:00',
    endTime: '16:30',
    duration: 150,
    location: '深圳华为坂田基地会议室B301',
    content: `会议主要讨论了Kirin 9000 5G芯片的封装技术方案：
    
1. 封装类型确认：采用FC-BGA封装，尺寸17x17mm
2. 基板要求：8层HDI基板，线宽/线距50μm/50μm
3. Bump规格：Cu Pillar + Solder Cap，间距130μm
4. 散热方案：集成散热垫，热阻<0.3°C/W
5. 可靠性测试：需通过JEDEC标准全套可靠性测试
6. 产能需求：月产能500K pcs，交期控制在4周内
7. 质量标准：Cpk≥1.33，零缺陷率<100PPM

技术难点讨论：
- 超薄Die(0.05mm)的处理工艺
- 高密度Bump的植球精度控制
- 封装过程中的翘曲控制

下一步行动：
1. 提供详细封装设计文件
2. 进行工艺能力验证
3. 制作工程样品进行验证`,
    participants: ['张伟(华为)', '李明(华为)', '王工(我司技术)', '陈工(我司质量)'],
    followUpTasks: [
      {
        id: 'task_001',
        description: '提交FC-BGA封装设计方案和工艺流程文件',
        assignee: '王工',
        dueDate: '2024-01-20',
        status: 'completed',
        priority: 'high',
        createdAt: '2024-01-15T16:30:00Z'
      },
      {
        id: 'task_002',
        description: '安排工艺能力验证和样品制作',
        assignee: '陈工',
        dueDate: '2024-01-25',
        status: 'in_progress',
        priority: 'high',
        createdAt: '2024-01-15T16:30:00Z'
      }
    ],
    importance: 5,
    result: 'excellent' as CommunicationResult,
    status: 'completed' as CommunicationStatus,
    customerFeedback: '对我司的技术方案和工艺能力表示满意，希望加快样品制作进度',
    nextContactDate: '2024-01-22',
    nextContactPurpose: '样品验证结果汇报',
    attachments: [
      {
        id: 'attach_001',
        fileName: 'Kirin9000_Package_Spec_v1.2.pdf',
        fileSize: 2048576,
        fileType: 'application/pdf',
        uploadTime: '2024-01-15T16:45:00Z',
        downloadUrl: '/files/comm_001/package_spec.pdf',
        description: '封装规格书'
      },
      {
        id: 'attach_002',
        fileName: '会议纪要_Kirin9000封装讨论_20240115.docx',
        fileSize: 512000,
        fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        uploadTime: '2024-01-15T17:00:00Z',
        downloadUrl: '/files/comm_001/meeting_minutes.docx',
        description: '会议纪要'
      }
    ],
    tags: ['5G芯片', 'FC-BGA', '高端封装', '技术讨论'],
    createdAt: '2024-01-15T17:00:00Z',
    updatedAt: '2024-01-16T09:00:00Z',
    createdBy: 'wang.engineer',
    updatedBy: 'wang.engineer'
  },

  {
    id: 'comm_002',
    contactId: 'contact_001_01',
    customerId: 'customer_001',
    type: 'Phone' as CommunicationRecordType,
    scene: 'DeliveryCoordination' as CommunicationScene,
    subject: 'Q1季度订单交期协调',
    date: '2024-01-18',
    startTime: '10:30',
    endTime: '11:15',
    duration: 45,
    content: `电话沟通Q1季度订单的交期安排：

1. 订单状态确认：
   - HW240101: 1M pcs，已完成80%，预计1月25日完成
   - HW240102: 500K pcs，已完成60%，预计2月1日完成
   - HW240103: 2M pcs，已完成30%，预计2月15日完成

2. 交期调整需求：
   - 客户希望HW240103订单提前到2月10日
   - 原因：下游客户Spring Festival前需要备货

3. 我司应对方案：
   - 增加夜班产能，每日增产20%
   - 优先调配原材料供应
   - 质量控制流程不变，确保品质

4. 协调结果：
   - 同意HW240103订单提前到2月12日
   - 客户需确认规格无变更
   - 加急费用：额外收取2%

客户对协调结果表示满意。`,
    participants: ['张伟(华为)', '刘经理(我司销售)'],
    importance: 4,
    result: 'good' as CommunicationResult,
    status: 'completed' as CommunicationStatus,
    customerFeedback: '感谢及时协调，同意加急费用和新交期',
    nextContactDate: '2024-01-25',
    nextContactPurpose: 'HW240101订单交付确认',
    tags: ['交期协调', 'Q1订单', '加急生产'],
    createdAt: '2024-01-18T11:20:00Z',
    updatedAt: '2024-01-18T11:20:00Z',
    createdBy: 'liu.sales'
  },

  {
    id: 'comm_003',
    contactId: 'contact_001_02',
    customerId: 'customer_001',
    type: 'Email' as CommunicationRecordType,
    scene: 'QualityIssue' as CommunicationScene,
    subject: '批次HW231215质量异常分析报告',
    date: '2024-01-12',
    content: `关于批次HW231215产品质量异常的分析报告：

异常描述：
- 客户反馈该批次产品在高温测试中出现功能失效
- 失效率：0.08%（800ppm），超出客户标准（<100ppm）
- 失效模式：高温下电流泄漏异常

根本原因分析（8D报告）：
1. 问题描述：高温测试中部分产品电流泄漏超标
2. 临时对策：已暂停该批次产品出货，全检后放行
3. 根本原因：封装材料供应商变更，新材料高温特性不稳定
4. 永久对策：
   - 更换回原供应商材料
   - 加强材料入厂检验
   - 增加高温筛选工序
5. 预防措施：建立供应商变更评估流程

赔偿方案：
- 全额退换异常产品
- 承担客户测试费用
- 提供8D分析报告

客户端验证：
- 重新测试合格品批次
- 确认改善措施有效性`,
    participants: ['李明(华为)', '陈工(我司质量)', '张工(我司技术)'],
    followUpTasks: [
      {
        id: 'task_003',
        description: '完成8D报告并提交客户审核',
        assignee: '陈工',
        dueDate: '2024-01-15',
        status: 'completed',
        priority: 'urgent',
        createdAt: '2024-01-12T14:00:00Z'
      },
      {
        id: 'task_004',
        description: '制作改善后样品供客户验证',
        assignee: '张工',
        dueDate: '2024-01-20',
        status: 'completed',
        priority: 'high',
        createdAt: '2024-01-12T14:00:00Z'
      }
    ],
    importance: 5,
    result: 'good' as CommunicationResult,
    status: 'completed' as CommunicationStatus,
    customerFeedback: '认可根本原因分析和改善措施，同意恢复供货',
    nextContactDate: '2024-01-30',
    nextContactPurpose: '改善措施执行效果确认',
    attachments: [
      {
        id: 'attach_003',
        fileName: '8D分析报告_HW231215_v2.1.pdf',
        fileSize: 1536000,
        fileType: 'application/pdf',
        uploadTime: '2024-01-12T16:30:00Z',
        downloadUrl: '/files/comm_003/8d_report.pdf',
        description: '质量异常8D分析报告'
      }
    ],
    tags: ['质量异常', '8D报告', '供应商管理', '改善措施'],
    createdAt: '2024-01-12T14:00:00Z',
    updatedAt: '2024-01-16T10:00:00Z',
    createdBy: 'chen.quality',
    updatedBy: 'chen.quality'
  },

  {
    id: 'comm_004',
    contactId: 'contact_002_01',
    customerId: 'customer_002',
    type: 'Visit' as CommunicationRecordType,
    scene: 'BusinessNegotiation' as CommunicationScene,
    subject: '2024年度合作协议商务谈判',
    date: '2024-01-10',
    startTime: '09:00',
    endTime: '17:00',
    duration: 480,
    location: '上海紫光展锐总部',
    content: `全天商务谈判，讨论2024年度合作框架协议：

上午议题（9:00-12:00）：
1. 2023年合作回顾
   - 总订单量：98批次，4.23亿元
   - 质量表现：优秀，客户满意度4.6/5.0
   - 交期达成率：96.8%

2. 2024年合作计划
   - 预计订单量：120批次，5.8亿元
   - 新产品导入：Tiger T820 5G芯片
   - 产能保障：月产能提升至2000K pcs

下午议题（14:00-17:00）：
3. 价格条款谈判
   - 客户期望：在2023年基础上降价5-8%
   - 我司回应：考虑到原材料涨价，可提供3%降价
   - 妥协方案：根据订单量阶梯定价

4. 质量条款
   - 质量标准：维持JEDEC标准，Cpk≥1.33
   - 质量保证：产品质量问题24小时响应
   - 赔付条款：按既有协议执行

5. 交期条款
   - 标准交期：4-6周
   - 急件加快：2-3周（加急费3%）
   - 产能预留：为客户预留30%产能

6. 技术支持
   - 专项技术支持团队
   - 新产品开发协作
   - 工艺优化持续改进

谈判结果：
- 价格：2024年综合降价4%，订单量达标后再降1%
- 质量：维持现有标准，增加月度质量评审
- 交期：承诺标准交期4周，急件3周
- 技术：建立联合技术团队

需要后续确认：
1. 合同条款最终版本
2. Tiger T820技术规格书
3. 产能保障具体措施`,
    participants: ['王强(紫光展锐)', '李总(紫光展锐)', '我司销售总监', '我司技术总监'],
    followUpTasks: [
      {
        id: 'task_005',
        description: '准备2024年度合作协议草案',
        assignee: '销售总监',
        dueDate: '2024-01-15',
        status: 'completed',
        priority: 'high',
        createdAt: '2024-01-10T17:30:00Z'
      },
      {
        id: 'task_006',
        description: 'Tiger T820技术规格书准备',
        assignee: '技术总监',
        dueDate: '2024-01-20',
        status: 'in_progress',
        priority: 'high',
        createdAt: '2024-01-10T17:30:00Z'
      }
    ],
    importance: 5,
    result: 'excellent' as CommunicationResult,
    status: 'completed' as CommunicationStatus,
    customerFeedback: '对谈判结果满意，期待2024年更深入的合作',
    nextContactDate: '2024-01-22',
    nextContactPurpose: '合同条款细节确认',
    attachments: [
      {
        id: 'attach_004',
        fileName: '2024年度合作谈判纪要_v1.0.pdf',
        fileSize: 1024000,
        fileType: 'application/pdf',
        uploadTime: '2024-01-10T18:00:00Z',
        downloadUrl: '/files/comm_004/negotiation_minutes.pdf',
        description: '商务谈判会议纪要'
      }
    ],
    tags: ['年度合作', '商务谈判', '框架协议', '价格谈判'],
    createdAt: '2024-01-10T18:00:00Z',
    updatedAt: '2024-01-11T09:00:00Z',
    createdBy: 'sales.director',
    updatedBy: 'sales.director'
  },

  {
    id: 'comm_005',
    contactId: 'contact_003_01',
    customerId: 'customer_003',
    type: 'VideoCall' as CommunicationRecordType,
    scene: 'ProductIntro' as CommunicationScene,
    subject: '车规级功率IC封装解决方案介绍',
    date: '2024-01-08',
    startTime: '15:00',
    endTime: '16:30',
    duration: 90,
    content: `通过Teams视频会议向比亚迪半导体介绍车规级功率IC封装方案：

1. 车规级封装技术优势：
   - IATF16949体系认证，满足汽车行业质量要求
   - AEC-Q100可靠性验证，-40°C~+150°C工作温度
   - 优秀的散热性能，热阻可达0.5°C/W以下
   - 抗振动、抗冲击设计

2. 封装形式推荐：
   - IGBT驱动IC：QFN-48封装，7x7mm
   - DC-DC电源IC：QFN-32封装，5x5mm  
   - 电池管理IC：TSSOP-20封装，适合高密度布局

3. 工艺特色：
   - 银烧结Die Attach，提升散热性能
   - 重金属线键合，提升载流能力
   - 低应力封装胶料，提升可靠性
   - 100%电测试，确保零缺陷

4. 质量保证：
   - Cpk>1.67，过程能力优秀
   - DPPM<50，质量水平行业领先
   - 完整可追溯系统，支持汽车行业要求

5. 产能及交期：
   - 车规级专线产能：月产500K pcs
   - 标准交期：6-8周
   - 支持小批量试样：100pcs起订

客户关注点：
- 散热性能是否满足150°C高温要求
- 可靠性测试数据及认证情况  
- 成本竞争力和批量生产能力

回应要点：
- 提供详细散热仿真报告
- 分享AEC-Q100认证证书
- 承诺提供有竞争力的报价`,
    participants: ['陈志华(比亚迪)', '技术经理(比亚迪)', '我司车规事业部总监', '我司技术专家'],
    followUpTasks: [
      {
        id: 'task_007',
        description: '提供车规级封装散热仿真报告',
        assignee: '技术专家',
        dueDate: '2024-01-12',
        status: 'completed',
        priority: 'medium',
        createdAt: '2024-01-08T16:30:00Z'
      },
      {
        id: 'task_008',
        description: '准备AEC-Q100认证证书和测试报告',
        assignee: '车规事业部总监',
        dueDate: '2024-01-15',
        status: 'completed',
        priority: 'medium',
        createdAt: '2024-01-08T16:30:00Z'
      }
    ],
    importance: 4,
    result: 'good' as CommunicationResult,
    status: 'completed' as CommunicationStatus,
    customerFeedback: '对技术方案感兴趣，希望看到更多技术细节和认证资料',
    nextContactDate: '2024-01-18',
    nextContactPurpose: '技术细节确认和报价讨论',
    tags: ['车规级', '功率IC', '产品介绍', 'AEC-Q100'],
    createdAt: '2024-01-08T16:45:00Z',
    updatedAt: '2024-01-08T16:45:00Z',
    createdBy: 'automotive.director'
  },

  {
    id: 'comm_006',
    contactId: 'contact_004_01',
    customerId: 'customer_004',
    type: 'WeChat' as CommunicationRecordType,
    scene: 'MarketInfo' as CommunicationScene,
    subject: '2024年存储器市场趋势交流',
    date: '2024-01-05',
    startTime: '20:30',
    endTime: '21:15',
    duration: 45,
    content: `微信沟通2024年存储器市场趋势和业务机会：

刘总分享的市场信息：
1. 2024年存储器市场预测
   - SPI NOR Flash需求预计增长15-20%
   - 主要驱动：IoT设备、汽车电子、工业控制
   - 价格趋势：Q1-Q2相对稳定，下半年可能小幅上涨

2. 兆易创新业务规划
   - 新产品导入：GD25WQ系列高速NOR Flash
   - 产能需求：月需求预计300K-500K pcs
   - 封装要求：主要是SOP-8和QFN-8

3. 供应链策略调整
   - 希望增加备选供应商，分散风险
   - 对我司的兴趣：希望评估我司产能和技术能力
   - 合作模式：小批量试样开始，逐步增量

我司回应：
- 表达合作意愿，有充足的SOP和QFN产能
- 愿意提供技术支持和工艺优化服务
- 可以提供有竞争力的价格和快速响应

下一步安排：
- 安排技术交流，了解具体产品要求
- 提供公司产能和技术能力介绍
- 讨论初步合作框架`,
    participants: ['刘建华(兆易创新)', '我司销售经理'],
    importance: 3,
    result: 'good' as CommunicationResult,
    status: 'completed' as CommunicationStatus,
    nextContactDate: '2024-01-15',
    nextContactPurpose: '技术交流和产能介绍',
    tags: ['市场趋势', 'NOR Flash', '新客户开发', '存储器'],
    createdAt: '2024-01-05T21:20:00Z',
    updatedAt: '2024-01-05T21:20:00Z',
    createdBy: 'sales.manager'
  }
]

// 生成更多模拟沟通记录的工具函数
export function generateMockCommunicationRecords(count: number): CommunicationRecord[] {
  const subjects = [
    '月度质量回顾会议',
    '新产品技术规格确认',
    '订单交期协调',
    '价格谈判',
    '质量问题分析',
    '产能规划讨论',
    '技术改进方案',
    '市场信息交流',
    '合同条款确认',
    '客户满意度反馈'
  ]

  const types: CommunicationRecordType[] = [
    'Call',
    'Email',
    'Meeting',
    'Visit',
    'WeChat',
    'VideoCall'
  ]
  const scenes: CommunicationScene[] = [
    'TechDiscussion',
    'BusinessNegotiation',
    'ProductIntro',
    'QualityIssue',
    'DeliveryCoordination'
  ]
  const results: CommunicationResult[] = ['excellent', 'good', 'average']
  const statuses: CommunicationStatus[] = ['completed', 'pending']

  const generatedRecords: CommunicationRecord[] = []
  const baseDate = new Date('2023-10-01')

  for (let i = 0; i < count; i++) {
    const recordDate = new Date(baseDate.getTime() + Math.random() * 90 * 24 * 60 * 60 * 1000)
    const recordId = `comm_${String(mockCommunicationRecords.length + i + 1).padStart(3, '0')}`

    generatedRecords.push({
      id: recordId,
      contactId: 'contact_001_01',
      customerId: 'customer_001',
      type: types[Math.floor(Math.random() * types.length)],
      scene: scenes[Math.floor(Math.random() * scenes.length)],
      subject: subjects[Math.floor(Math.random() * subjects.length)],
      date: recordDate.toISOString().split('T')[0],
      duration: Math.floor(Math.random() * 180) + 30,
      content: '这是自动生成的沟通记录内容，包含了基本的沟通要点和后续安排。',
      participants: ['客户代表', '我司销售'],
      importance: (Math.floor(Math.random() * 5) + 1) as 1 | 2 | 3 | 4 | 5,
      result: results[Math.floor(Math.random() * results.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      tags: ['日常沟通', '业务跟进'],
      createdAt: recordDate.toISOString(),
      updatedAt: recordDate.toISOString(),
      createdBy: 'sales.manager'
    })
  }

  return generatedRecords
}

// 导出所有沟通记录数据
export const allMockCommunicationRecords = [
  ...mockCommunicationRecords,
  ...generateMockCommunicationRecords(20)
]

// 沟通记录统计数据
export const communicationStatistics: CommunicationStatistics = {
  totalRecords: allMockCommunicationRecords.length,
  thisMonthRecords: allMockCommunicationRecords.filter(record => {
    const recordDate = new Date(record.date)
    const now = new Date()
    return (
      recordDate.getMonth() === now.getMonth() && recordDate.getFullYear() === now.getFullYear()
    )
  }).length,
  averagePerMonth: Math.round(allMockCommunicationRecords.length / 12),
  mostActiveCustomer: {
    customerId: 'customer_001',
    customerName: '华为海思半导体有限公司',
    count: allMockCommunicationRecords.filter(r => r.customerId === 'customer_001').length
  },
  typeDistribution: {
    Call: allMockCommunicationRecords.filter(r => r.type === 'Call').length,
    Email: allMockCommunicationRecords.filter(r => r.type === 'Email').length,
    Meeting: allMockCommunicationRecords.filter(r => r.type === 'Meeting').length,
    Visit: allMockCommunicationRecords.filter(r => r.type === 'Visit').length,
    WeChat: allMockCommunicationRecords.filter(r => r.type === 'WeChat').length,
    VideoCall: allMockCommunicationRecords.filter(r => r.type === 'VideoCall').length,
    Exhibition: allMockCommunicationRecords.filter(r => r.type === 'Exhibition').length,
    OnlineDemo: allMockCommunicationRecords.filter(r => r.type === 'OnlineDemo').length
  },
  sceneDistribution: {
    TechDiscussion: allMockCommunicationRecords.filter(r => r.scene === 'TechDiscussion').length,
    BusinessNegotiation: allMockCommunicationRecords.filter(r => r.scene === 'BusinessNegotiation')
      .length,
    ProductIntro: allMockCommunicationRecords.filter(r => r.scene === 'ProductIntro').length,
    QualityIssue: allMockCommunicationRecords.filter(r => r.scene === 'QualityIssue').length,
    DeliveryCoordination: allMockCommunicationRecords.filter(
      r => r.scene === 'DeliveryCoordination'
    ).length,
    ProjectReview: allMockCommunicationRecords.filter(r => r.scene === 'ProjectReview').length,
    ContractDiscussion: allMockCommunicationRecords.filter(r => r.scene === 'ContractDiscussion')
      .length,
    ProblemSolving: allMockCommunicationRecords.filter(r => r.scene === 'ProblemSolving').length,
    RelationshipBuilding: allMockCommunicationRecords.filter(
      r => r.scene === 'RelationshipBuilding'
    ).length,
    MarketInfo: allMockCommunicationRecords.filter(r => r.scene === 'MarketInfo').length
  },
  resultDistribution: {
    excellent: allMockCommunicationRecords.filter(r => r.result === 'excellent').length,
    good: allMockCommunicationRecords.filter(r => r.result === 'good').length,
    average: allMockCommunicationRecords.filter(r => r.result === 'average').length,
    poor: allMockCommunicationRecords.filter(r => r.result === 'poor').length,
    failed: allMockCommunicationRecords.filter(r => r.result === 'failed').length
  },
  upcomingFollowUps: allMockCommunicationRecords.filter(record => {
    if (!record.nextContactDate) return false
    const nextDate = new Date(record.nextContactDate)
    const now = new Date()
    const diffTime = nextDate.getTime() - now.getTime()
    return diffTime > 0 && diffTime <= 7 * 24 * 60 * 60 * 1000 // 一周内
  }).length,
  overdueFollowUps: allMockCommunicationRecords.filter(record => {
    if (!record.nextContactDate) return false
    const nextDate = new Date(record.nextContactDate)
    return nextDate < new Date()
  }).length
}
