// CSS Custom Properties for Theme System
:root {
  // 完整的间距系统
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-10: 40px;
  --spacing-12: 48px;
  --spacing-16: 64px;
  --spacing-20: 80px;
  --spacing-24: 96px;
  
  // 兼容旧版本
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 完整的圆角系统
  --radius-base: 4px;
  --radius-sm: 2px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 24px;
  --radius-full: 9999px;
  
  // 完整的字体系统
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  
  // 字重系统
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  // 行高系统
  --line-height-tight: 1.25;
  --line-height-base: 1.5;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 1.75;
  
  // 字体系列
  --font-family-base: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, 'Helvetica Neue', arial, sans-serif;
  --font-family-mono: 'SF Mono', monaco, inconsolata, 'Roboto Mono', consolas, 'Courier New', monospace;
  
  // 过渡动画 - 菜单切换优化版本 (统一时间，提升一致性)
  --transition-fast: 0.08s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  
  // 专门的导航过渡 (超快响应)
  --transition-nav: 0.08s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-dropdown: 0.12s cubic-bezier(0.4, 0, 0.6, 1);
  
  // 页面布局
  --header-height: 64px;
  --sidebar-width: 240px;
  --container-max-width: 1200px;
  
  // Z-index层级
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
  --z-toast: 1070;

  // 默认浅色主题颜色变量
  // 主色调 - 极简蓝色系
  --color-primary: #2563eb;
  --color-primary-hover: #3b82f6;
  --color-primary-active: #1d4ed8;
  --color-primary-light: #dbeafe;
  --color-primary-dark: #1e40af;

  // 功能色彩 - 柔和版本
  --color-success: #10b981;
  --color-success-hover: #34d399;
  --color-success-light: #d1fae5;
  --color-warning: #f59e0b;
  --color-warning-hover: #fbbf24;
  --color-warning-light: #fef3c7;
  --color-error: #ef4444;
  --color-error-hover: #f87171;
  --color-error-light: #fee2e2;
  --color-info: #6b7280;
  --color-info-hover: #9ca3af;
  --color-info-light: #f3f4f6;

  // 中性色阶 - 极简灰色系
  --color-text-primary: #111827;    // 主要文字
  --color-text-secondary: #6b7280;  // 次要文字
  --color-text-tertiary: #9ca3af;   // 第三级文字
  --color-text-disabled: #d1d5db;   // 禁用文字
  --color-text-placeholder: #9ca3af; // 占位符文字

  // 背景色 - 纯净白色系
  --color-bg-primary: #fff;      // 主背景 (纯白)
  --color-bg-secondary: #f9fafb;    // 次要背景
  --color-bg-tertiary: #f3f4f6;     // 第三背景
  --color-bg-hover: #f5f5f5;        // 悬停背景
  --color-bg-active: #e5e7eb;       // 激活背景
  --color-bg-disabled: #f9fafb;     // 禁用背景
  
  // 导航相关颜色
  --color-nav-bg: #fff;
  --color-nav-text: #64748b;
  --color-nav-text-active: #2563eb;
  --color-nav-item-hover: rgb(37 99 235 / 10%);
  --color-nav-item-active: rgb(37 99 235 / 15%);
  
  // 卡片相关颜色
  --color-card-bg: #fff;
  --color-card-border: #e5e7eb;
  
  // 反色文字
  --color-text-inverse: #fff;

  // 边框色 - 清淡边框
  --color-border-light: #f3f4f6;
  --color-border-base: #e5e7eb;
  --color-border-dark: #d1d5db;
  --color-border-focus: #2563eb;

  // 阴影色
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 5%);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 8%), 0 1px 2px 0 rgb(0 0 0 / 4%);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 8%), 0 2px 4px -1px rgb(0 0 0 / 4%);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 8%), 0 4px 6px -2px rgb(0 0 0 / 4%);

  // IC封测专业色彩
  --color-wafer: #e0f2fe;           // 晶圆色
  --color-die-pass: #dcfce7;        // 良品die
  --color-die-fail: #fee2e2;        // 不良die  
  --color-cp-test: #f0f9ff;         // CP测试
  --color-assembly: #f3e8ff;        // Assembly
  --color-ft-test: #ecfdf5;         // FT测试
}

// 浅色主题 (Light Theme) - 预留给主题切换功能
.light-theme {
  // 颜色变量已经定义在 :root 中作为默认主题
}

// 深色主题 (Dark Theme)
.dark-theme {
  // 主色调 - 柔和蓝色系
  --color-primary: #3b82f6;
  --color-primary-hover: #60a5fa;
  --color-primary-active: #2563eb;
  --color-primary-light: #1e3a8a;
  --color-primary-dark: #1d4ed8;

  // 功能色彩 - 深色优化版本
  --color-success: #34d399;
  --color-success-hover: #6ee7b7;
  --color-success-light: #064e3b;
  --color-warning: #fbbf24;
  --color-warning-hover: #fcd34d;
  --color-warning-light: #78350f;
  --color-error: #f87171;
  --color-error-hover: #fca5a5;
  --color-error-light: #7f1d1d;
  --color-info: #94a3b8;
  --color-info-hover: #cbd5e1;
  --color-info-light: #334155;

  // 中性色阶 - 深色模式文字
  --color-text-primary: #f8fafc;    // 主要文字
  --color-text-secondary: #cbd5e1;  // 次要文字  
  --color-text-tertiary: #94a3b8;   // 第三级文字
  --color-text-disabled: #64748b;   // 禁用文字
  --color-text-placeholder: #64748b; // 占位符文字

  // 背景色 - 深色背景系
  --color-bg-primary: #0f172a;      // 主背景 (深蓝黑)
  --color-bg-secondary: #1e293b;    // 次要背景
  --color-bg-tertiary: #334155;     // 第三背景
  --color-bg-hover: #475569;        // 悬停背景
  --color-bg-active: #64748b;       // 激活背景
  --color-bg-disabled: #1e293b;     // 禁用背景
  
  // 导航相关颜色
  --color-nav-bg: #1e293b;
  --color-nav-text: #94a3b8;
  --color-nav-text-active: #60a5fa;
  --color-nav-item-hover: rgb(96 165 250 / 10%);
  --color-nav-item-active: rgb(96 165 250 / 15%);
  
  // 卡片相关颜色
  --color-card-bg: #1e293b;
  --color-card-border: #334155;
  
  // 反色文字
  --color-text-inverse: #fff;

  // 边框色 - 深色边框
  --color-border-light: #334155;
  --color-border-base: #475569;
  --color-border-dark: #64748b;
  --color-border-focus: #3b82f6;

  // 阴影色
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 20%);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 30%), 0 1px 2px 0 rgb(0 0 0 / 15%);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 30%), 0 2px 4px -1px rgb(0 0 0 / 15%);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 30%), 0 4px 6px -2px rgb(0 0 0 / 15%);

  // IC封测专业色彩 (深色优化)
  --color-wafer: #1e3a8a;
  --color-die-pass: #166534;
  --color-die-fail: #991b1b;
  --color-cp-test: #0c4a6e;
  --color-assembly: #581c87;
  --color-ft-test: #14532d;
}