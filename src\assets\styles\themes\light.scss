// 浅色主题专用样式
// 主要包含Element Plus等组件库的主题定制

// Element Plus 主题变量覆盖
$--color-primary: #2563eb;
$--color-primary-light-1: #3b82f6;
$--color-primary-light-2: #60a5fa;
$--color-primary-light-3: #93c5fd;
$--color-primary-light-4: #bfdbfe;
$--color-primary-light-5: #dbeafe;
$--color-primary-light-6: #eff6ff;
$--color-primary-light-7: #f0f9ff;
$--color-primary-light-8: #f8fafc;
$--color-primary-light-9: #f8fafc;
$--color-primary-dark-1: #1d4ed8;
$--color-primary-dark-2: #1e40af;
$--color-success: #10b981;
$--color-warning: #f59e0b;
$--color-danger: #ef4444;
$--color-error: #ef4444;
$--color-info: #6b7280;

// 中性色
$--color-white: #fff;
$--color-black: #000;
$--color-text-primary: #111827;
$--color-text-regular: #374151;
$--color-text-secondary: #6b7280;
$--color-text-placeholder: #9ca3af;
$--color-text-disabled: #d1d5db;

// 背景色
$--background-color-base: #f9fafb;
$--background-color-page: #fff;
$--background-color-overlay: rgb(255 255 255 / 90%);

// 边框色
$--border-color-base: #e5e7eb;
$--border-color-light: #f3f4f6;
$--border-color-lighter: #fafafa;
$--border-color-extra-light: #f5f5f5;
$--border-color-dark: #d1d5db;
$--border-color-darker: #9ca3af;

// 填充色
$--fill-color-blank: #fff;
$--fill-color: #f0f2f5;
$--fill-color-light: #f5f7fa;
$--fill-color-lighter: #fafafa;
$--fill-color-extra-light: #fafcff;
$--fill-color-dark: #ebeef5;
$--fill-color-darker: #e6e8eb;

// 字体
$--font-family: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, 'Helvetica Neue', arial, 'Noto Sans', sans-serif;
$--font-size-extra-large: 20px;
$--font-size-large: 18px;
$--font-size-medium: 16px;
$--font-size-base: 14px;
$--font-size-small: 13px;
$--font-size-extra-small: 12px;

// 圆角
$--border-radius-base: 6px;
$--border-radius-small: 4px;
$--border-radius-round: 20px;
$--border-radius-circle: 100%;

// 阴影
$--box-shadow-base: 0 1px 3px 0 rgb(0 0 0 / 8%), 0 1px 2px 0 rgb(0 0 0 / 4%);
$--box-shadow-light: 0 1px 2px 0 rgb(0 0 0 / 5%);
$--box-shadow-lighter: 0 1px 1px 0 rgb(0 0 0 / 5%);
$--box-shadow-dark: 0 4px 6px -1px rgb(0 0 0 / 8%), 0 2px 4px -1px rgb(0 0 0 / 4%);

// 布局专用变量
$--header-height: 60px;
$--sidebar-width: 240px;
$--sidebar-collapsed-width: 64px;
$--footer-height: 40px;
$--breadcrumb-height: 48px;

// 极简设计专用变量
$--minimal-spacing: 16px;
$--minimal-border-width: 1px;
$--minimal-transition: 0.2s ease;

// 浅色主题专用 mixin
@mixin light-card {
  background: $--color-white;
  border: 1px solid $--border-color-light;
  border-radius: $--border-radius-base;
  box-shadow: $--box-shadow-light;
}

@mixin light-hover {
  transition: all $--minimal-transition;

  &:hover {
    background: $--fill-color-light;
    border-color: $--border-color-base;
  }
}

@mixin light-focus {
  outline: 2px solid rgb(37 99 235 / 20%);
  outline-offset: 2px;
}