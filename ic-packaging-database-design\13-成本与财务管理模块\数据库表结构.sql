-- ========================================
-- 成本与财务管理模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- 产品成本核算主表
CREATE TABLE product_cost_calculations (
    calculation_id VARCHAR(32) PRIMARY KEY COMMENT '核算ID',
    calculation_code VARCHAR(50) NOT NULL UNIQUE COMMENT '核算编码',
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL COMMENT '产品编码',
    
    -- 核算基本信息
    calculation_name VARCHAR(200) NOT NULL COMMENT '核算名称',
    calculation_type VARCHAR(30) NOT NULL COMMENT '核算类型(STANDARD/ACTUAL/TARGET)',
    calculation_period VARCHAR(20) NOT NULL COMMENT '核算期间',
    
    -- 核算范围
    calculation_scope VARCHAR(50) COMMENT '核算范围',
    volume_basis BIGINT NOT NULL COMMENT '核算数量基础',
    currency_code VARCHAR(10) DEFAULT 'CNY' COMMENT '币种',
    
    -- 直接材料成本
    die_cost DECIMAL(15,6) NOT NULL COMMENT 'Die成本',
    leadframe_cost DECIMAL(15,6) COMMENT '引线框架成本',
    substrate_cost DECIMAL(15,6) COMMENT '基板成本',
    wire_cost DECIMAL(15,6) COMMENT '键合线成本',
    molding_compound_cost DECIMAL(15,6) COMMENT '封装胶成本',
    other_material_cost DECIMAL(15,6) COMMENT '其他材料成本',
    total_material_cost DECIMAL(15,6) NOT NULL COMMENT '材料总成本',
    
    -- 直接人工成本
    cp_labor_cost DECIMAL(15,6) COMMENT 'CP测试人工成本',
    assembly_labor_cost DECIMAL(15,6) COMMENT '封装人工成本',
    ft_labor_cost DECIMAL(15,6) COMMENT 'FT测试人工成本',
    qa_labor_cost DECIMAL(15,6) COMMENT '质检人工成本',
    total_labor_cost DECIMAL(15,6) NOT NULL COMMENT '人工总成本',
    
    -- 制造费用
    equipment_depreciation DECIMAL(15,6) COMMENT '设备折旧费',
    utilities_cost DECIMAL(15,6) COMMENT '水电费',
    facility_cost DECIMAL(15,6) COMMENT '厂房费用',
    maintenance_cost DECIMAL(15,6) COMMENT '维修费',
    indirect_labor_cost DECIMAL(15,6) COMMENT '间接人工费',
    other_overhead_cost DECIMAL(15,6) COMMENT '其他制造费用',
    total_overhead_cost DECIMAL(15,6) NOT NULL COMMENT '制造费用总计',
    
    -- 管理费用
    admin_cost DECIMAL(15,6) COMMENT '管理费用',
    rd_cost DECIMAL(15,6) COMMENT '研发费用摊销',
    sales_cost DECIMAL(15,6) COMMENT '销售费用',
    finance_cost DECIMAL(15,6) COMMENT '财务费用',
    total_admin_cost DECIMAL(15,6) COMMENT '管理费用总计',
    
    -- 成本汇总
    manufacturing_cost DECIMAL(15,6) NOT NULL COMMENT '制造成本',
    full_cost DECIMAL(15,6) NOT NULL COMMENT '完全成本',
    
    -- 成本分析
    material_cost_percentage DECIMAL(5,2) COMMENT '材料成本占比',
    labor_cost_percentage DECIMAL(5,2) COMMENT '人工成本占比',
    overhead_cost_percentage DECIMAL(5,2) COMMENT '制造费用占比',
    
    -- 目标比较
    target_cost DECIMAL(15,6) COMMENT '目标成本',
    cost_variance DECIMAL(15,6) COMMENT '成本差异',
    cost_variance_percentage DECIMAL(5,2) COMMENT '成本差异百分比',
    
    -- 状态管理
    calculation_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '核算状态',
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    
    -- 核算信息
    calculated_by VARCHAR(32) NOT NULL COMMENT '核算人',
    calculation_date DATE NOT NULL COMMENT '核算日期',
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version_no INT DEFAULT 1 COMMENT '版本号',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_cost_calc_code (calculation_code),
    INDEX idx_cost_calc_product (product_id),
    INDEX idx_cost_calc_type (calculation_type),
    INDEX idx_cost_calc_period (calculation_period),
    INDEX idx_cost_calc_status (calculation_status),
    INDEX idx_cost_calc_date (calculation_date),
    INDEX idx_cost_calc_calculated_by (calculated_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品成本核算主表';

-- 成本要素明细表
CREATE TABLE cost_element_details (
    detail_id VARCHAR(32) PRIMARY KEY COMMENT '明细ID',
    calculation_id VARCHAR(32) NOT NULL COMMENT '核算ID',
    element_category VARCHAR(50) NOT NULL COMMENT '要素类别',
    element_code VARCHAR(50) NOT NULL COMMENT '要素编码',
    element_name VARCHAR(200) NOT NULL COMMENT '要素名称',
    
    -- 数量信息
    quantity DECIMAL(15,6) COMMENT '数量',
    unit VARCHAR(20) COMMENT '单位',
    unit_cost DECIMAL(15,6) COMMENT '单价',
    total_cost DECIMAL(15,6) NOT NULL COMMENT '总成本',
    
    -- 分摊信息
    allocation_method VARCHAR(50) COMMENT '分摊方法',
    allocation_base DECIMAL(15,6) COMMENT '分摊基础',
    allocation_rate DECIMAL(8,4) COMMENT '分摊比例',
    
    -- 来源信息
    source_type VARCHAR(30) COMMENT '来源类型',
    source_document VARCHAR(100) COMMENT '来源单据',
    supplier_id VARCHAR(32) COMMENT '供应商ID',
    
    -- 成本性质
    cost_behavior VARCHAR(20) COMMENT '成本性态(FIXED/VARIABLE/SEMI)',
    cost_controllability VARCHAR(20) COMMENT '成本可控性',
    
    -- 备注
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (calculation_id) REFERENCES product_cost_calculations(calculation_id) ON DELETE CASCADE,
    INDEX idx_cost_detail_calc (calculation_id),
    INDEX idx_cost_detail_category (element_category),
    INDEX idx_cost_detail_code (element_code),
    INDEX idx_cost_detail_supplier (supplier_id),
    INDEX idx_cost_detail_behavior (cost_behavior)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成本要素明细表';

-- 标准成本定义表
CREATE TABLE standard_costs (
    standard_id VARCHAR(32) PRIMARY KEY COMMENT '标准成本ID',
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL COMMENT '产品编码',
    cost_version VARCHAR(20) NOT NULL COMMENT '成本版本',
    
    -- 生效期间
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 标准成本构成
    standard_die_cost DECIMAL(15,6) NOT NULL COMMENT '标准Die成本',
    standard_material_cost DECIMAL(15,6) NOT NULL COMMENT '标准材料成本',
    standard_labor_cost DECIMAL(15,6) NOT NULL COMMENT '标准人工成本',
    standard_overhead_cost DECIMAL(15,6) NOT NULL COMMENT '标准制造费用',
    standard_total_cost DECIMAL(15,6) NOT NULL COMMENT '标准总成本',
    
    -- 工时标准
    standard_cp_time DECIMAL(8,4) COMMENT '标准CP测试工时(分钟)',
    standard_assembly_time DECIMAL(8,4) COMMENT '标准封装工时(分钟)',
    standard_ft_time DECIMAL(8,4) COMMENT '标准FT测试工时(分钟)',
    
    -- 产量标准
    standard_yield DECIMAL(6,4) NOT NULL COMMENT '标准良率',
    standard_throughput INT COMMENT '标准产能(件/小时)',
    
    -- 制定依据
    cost_basis TEXT COMMENT '制定依据',
    volume_assumption BIGINT COMMENT '产量假设',
    
    -- 状态管理
    standard_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '标准状态',
    is_current TINYINT(1) DEFAULT 0 COMMENT '是否当前标准',
    
    -- 制定信息
    developed_by VARCHAR(32) NOT NULL COMMENT '制定人',
    development_date DATE NOT NULL COMMENT '制定日期',
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_standard_product (product_id),
    INDEX idx_standard_version (cost_version),
    INDEX idx_standard_effective (effective_date, expiry_date),
    INDEX idx_standard_status (standard_status),
    INDEX idx_standard_current (is_current),
    INDEX idx_standard_developed_by (developed_by),
    UNIQUE KEY uk_product_version (product_id, cost_version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标准成本定义表';

-- 成本差异分析表
CREATE TABLE cost_variance_analysis (
    variance_id VARCHAR(32) PRIMARY KEY COMMENT '差异ID',
    calculation_id VARCHAR(32) NOT NULL COMMENT '核算ID',
    standard_id VARCHAR(32) COMMENT '标准成本ID',
    variance_type VARCHAR(30) NOT NULL COMMENT '差异类型',
    
    -- 差异分析
    element_category VARCHAR(50) NOT NULL COMMENT '差异要素类别',
    standard_cost DECIMAL(15,6) NOT NULL COMMENT '标准成本',
    actual_cost DECIMAL(15,6) NOT NULL COMMENT '实际成本',
    variance_amount DECIMAL(15,6) NOT NULL COMMENT '差异金额',
    variance_percentage DECIMAL(5,2) COMMENT '差异百分比',
    
    -- 数量差异(材料用量差异、工时差异)
    standard_quantity DECIMAL(15,6) COMMENT '标准数量',
    actual_quantity DECIMAL(15,6) COMMENT '实际数量',
    quantity_variance DECIMAL(15,6) COMMENT '数量差异',
    
    -- 价格差异(材料价格差异、工资率差异)
    standard_price DECIMAL(15,6) COMMENT '标准价格',
    actual_price DECIMAL(15,6) COMMENT '实际价格',
    price_variance DECIMAL(15,6) COMMENT '价格差异',
    
    -- 效率差异
    standard_efficiency DECIMAL(8,4) COMMENT '标准效率',
    actual_efficiency DECIMAL(8,4) COMMENT '实际效率',
    efficiency_variance DECIMAL(15,6) COMMENT '效率差异',
    
    -- 差异原因分析
    variance_reason TEXT COMMENT '差异原因',
    root_cause_analysis TEXT COMMENT '根本原因分析',
    improvement_actions TEXT COMMENT '改进措施',
    
    -- 责任归属
    responsible_department VARCHAR(100) COMMENT '责任部门',
    responsible_person VARCHAR(32) COMMENT '责任人',
    
    -- 分析人员
    analyst_id VARCHAR(32) NOT NULL COMMENT '分析人',
    analysis_date DATE NOT NULL COMMENT '分析日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (calculation_id) REFERENCES product_cost_calculations(calculation_id) ON DELETE CASCADE,
    FOREIGN KEY (standard_id) REFERENCES standard_costs(standard_id) ON DELETE SET NULL,
    INDEX idx_variance_calc (calculation_id),
    INDEX idx_variance_standard (standard_id),
    INDEX idx_variance_type (variance_type),
    INDEX idx_variance_category (element_category),
    INDEX idx_variance_analyst (analyst_id),
    INDEX idx_variance_date (analysis_date),
    INDEX idx_variance_responsible (responsible_person)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成本差异分析表';

-- 财务报表数据表
CREATE TABLE financial_reports (
    report_id VARCHAR(32) PRIMARY KEY COMMENT '报表ID',
    report_code VARCHAR(50) NOT NULL UNIQUE COMMENT '报表编码',
    report_name VARCHAR(200) NOT NULL COMMENT '报表名称',
    report_type VARCHAR(30) NOT NULL COMMENT '报表类型',
    
    -- 报表期间
    report_period VARCHAR(20) NOT NULL COMMENT '报表期间',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    
    -- 业务数据
    production_volume BIGINT COMMENT '生产数量',
    sales_volume BIGINT COMMENT '销售数量',
    
    -- 收入数据
    sales_revenue DECIMAL(18,2) COMMENT '销售收入',
    other_revenue DECIMAL(18,2) COMMENT '其他收入',
    total_revenue DECIMAL(18,2) COMMENT '总收入',
    
    -- 成本数据
    material_cost DECIMAL(18,2) COMMENT '材料成本',
    labor_cost DECIMAL(18,2) COMMENT '人工成本',
    manufacturing_overhead DECIMAL(18,2) COMMENT '制造费用',
    cost_of_goods_sold DECIMAL(18,2) COMMENT '销售成本',
    
    -- 费用数据
    selling_expense DECIMAL(18,2) COMMENT '销售费用',
    admin_expense DECIMAL(18,2) COMMENT '管理费用',
    rd_expense DECIMAL(18,2) COMMENT '研发费用',
    finance_expense DECIMAL(18,2) COMMENT '财务费用',
    
    -- 利润数据
    gross_profit DECIMAL(18,2) COMMENT '毛利润',
    operating_profit DECIMAL(18,2) COMMENT '营业利润',
    net_profit DECIMAL(18,2) COMMENT '净利润',
    
    -- 利润率
    gross_margin DECIMAL(5,2) COMMENT '毛利率',
    operating_margin DECIMAL(5,2) COMMENT '营业利润率',
    net_margin DECIMAL(5,2) COMMENT '净利率',
    
    -- 资产数据
    current_assets DECIMAL(18,2) COMMENT '流动资产',
    fixed_assets DECIMAL(18,2) COMMENT '固定资产',
    total_assets DECIMAL(18,2) COMMENT '总资产',
    
    -- 负债数据
    current_liabilities DECIMAL(18,2) COMMENT '流动负债',
    long_term_liabilities DECIMAL(18,2) COMMENT '长期负债',
    total_liabilities DECIMAL(18,2) COMMENT '总负债',
    
    -- 权益数据
    shareholders_equity DECIMAL(18,2) COMMENT '股东权益',
    
    -- 现金流数据
    operating_cash_flow DECIMAL(18,2) COMMENT '经营现金流',
    investing_cash_flow DECIMAL(18,2) COMMENT '投资现金流',
    financing_cash_flow DECIMAL(18,2) COMMENT '筹资现金流',
    
    -- 财务比率
    current_ratio DECIMAL(8,4) COMMENT '流动比率',
    debt_to_equity_ratio DECIMAL(8,4) COMMENT '负债权益比',
    return_on_assets DECIMAL(5,4) COMMENT '资产回报率',
    return_on_equity DECIMAL(5,4) COMMENT '权益回报率',
    
    -- 报表状态
    report_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '报表状态',
    
    -- 编制信息
    prepared_by VARCHAR(32) NOT NULL COMMENT '编制人',
    preparation_date DATE NOT NULL COMMENT '编制日期',
    reviewed_by VARCHAR(32) COMMENT '审核人',
    review_date DATE COMMENT '审核日期',
    approved_by VARCHAR(32) COMMENT '批准人',
    approval_date DATE COMMENT '批准日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_report_code (report_code),
    INDEX idx_report_type (report_type),
    INDEX idx_report_period (report_period),
    INDEX idx_report_dates (start_date, end_date),
    INDEX idx_report_status (report_status),
    INDEX idx_report_prepared_by (prepared_by),
    INDEX idx_report_date (preparation_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务报表数据表';

-- 预算管理表
CREATE TABLE budget_management (
    budget_id VARCHAR(32) PRIMARY KEY COMMENT '预算ID',
    budget_code VARCHAR(50) NOT NULL UNIQUE COMMENT '预算编码',
    budget_name VARCHAR(200) NOT NULL COMMENT '预算名称',
    budget_type VARCHAR(30) NOT NULL COMMENT '预算类型',
    
    -- 预算期间
    budget_year INT NOT NULL COMMENT '预算年度',
    budget_period VARCHAR(20) NOT NULL COMMENT '预算期间',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    
    -- 预算范围
    business_unit VARCHAR(100) COMMENT '业务单元',
    department_id VARCHAR(32) COMMENT '部门ID',
    product_line VARCHAR(100) COMMENT '产品线',
    
    -- 销售预算
    budgeted_sales_volume BIGINT COMMENT '预算销量',
    budgeted_sales_revenue DECIMAL(18,2) COMMENT '预算销售收入',
    budgeted_selling_price DECIMAL(15,6) COMMENT '预算销售价格',
    
    -- 生产预算
    budgeted_production_volume BIGINT COMMENT '预算产量',
    budgeted_production_cost DECIMAL(18,2) COMMENT '预算生产成本',
    budgeted_unit_cost DECIMAL(15,6) COMMENT '预算单位成本',
    
    -- 成本预算
    budgeted_material_cost DECIMAL(18,2) COMMENT '预算材料成本',
    budgeted_labor_cost DECIMAL(18,2) COMMENT '预算人工成本',
    budgeted_overhead_cost DECIMAL(18,2) COMMENT '预算制造费用',
    
    -- 费用预算
    budgeted_selling_expense DECIMAL(18,2) COMMENT '预算销售费用',
    budgeted_admin_expense DECIMAL(18,2) COMMENT '预算管理费用',
    budgeted_rd_expense DECIMAL(18,2) COMMENT '预算研发费用',
    
    -- 投资预算
    budgeted_capex DECIMAL(18,2) COMMENT '预算资本支出',
    budgeted_equipment_investment DECIMAL(18,2) COMMENT '预算设备投资',
    budgeted_rd_investment DECIMAL(18,2) COMMENT '预算研发投资',
    
    -- 利润预算
    budgeted_gross_profit DECIMAL(18,2) COMMENT '预算毛利润',
    budgeted_operating_profit DECIMAL(18,2) COMMENT '预算营业利润',
    budgeted_net_profit DECIMAL(18,2) COMMENT '预算净利润',
    
    -- 现金流预算
    budgeted_operating_cash_flow DECIMAL(18,2) COMMENT '预算经营现金流',
    budgeted_investing_cash_flow DECIMAL(18,2) COMMENT '预算投资现金流',
    budgeted_financing_cash_flow DECIMAL(18,2) COMMENT '预算筹资现金流',
    
    -- 预算状态
    budget_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '预算状态',
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    
    -- 制定信息
    budget_owner VARCHAR(32) NOT NULL COMMENT '预算负责人',
    prepared_by VARCHAR(32) NOT NULL COMMENT '编制人',
    preparation_date DATE NOT NULL COMMENT '编制日期',
    approved_by VARCHAR(32) COMMENT '审批人',
    approval_date DATE COMMENT '审批日期',
    
    -- 备注
    budget_assumptions TEXT COMMENT '预算假设',
    budget_notes TEXT COMMENT '预算说明',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_budget_code (budget_code),
    INDEX idx_budget_type (budget_type),
    INDEX idx_budget_year (budget_year),
    INDEX idx_budget_period (budget_period),
    INDEX idx_budget_status (budget_status),
    INDEX idx_budget_owner (budget_owner),
    INDEX idx_budget_department (department_id),
    INDEX idx_budget_dates (start_date, end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算管理表';

-- 预算执行跟踪表
CREATE TABLE budget_execution_tracking (
    tracking_id VARCHAR(32) PRIMARY KEY COMMENT '跟踪ID',
    budget_id VARCHAR(32) NOT NULL COMMENT '预算ID',
    tracking_period VARCHAR(20) NOT NULL COMMENT '跟踪期间',
    tracking_date DATE NOT NULL COMMENT '跟踪日期',
    
    -- 实际完成情况
    actual_sales_volume BIGINT COMMENT '实际销量',
    actual_sales_revenue DECIMAL(18,2) COMMENT '实际销售收入',
    actual_production_volume BIGINT COMMENT '实际产量',
    actual_production_cost DECIMAL(18,2) COMMENT '实际生产成本',
    
    -- 预算执行率
    sales_volume_achievement DECIMAL(5,2) COMMENT '销量完成率',
    sales_revenue_achievement DECIMAL(5,2) COMMENT '收入完成率',
    cost_control_rate DECIMAL(5,2) COMMENT '成本控制率',
    profit_achievement DECIMAL(5,2) COMMENT '利润完成率',
    
    -- 差异分析
    sales_volume_variance BIGINT COMMENT '销量差异',
    sales_revenue_variance DECIMAL(18,2) COMMENT '收入差异',
    cost_variance DECIMAL(18,2) COMMENT '成本差异',
    profit_variance DECIMAL(18,2) COMMENT '利润差异',
    
    -- 差异原因
    variance_analysis TEXT COMMENT '差异分析',
    corrective_actions TEXT COMMENT '纠正措施',
    
    -- 预测更新
    updated_forecast JSON COMMENT '更新预测',
    forecast_accuracy DECIMAL(5,2) COMMENT '预测准确性',
    
    -- 跟踪人员
    tracked_by VARCHAR(32) NOT NULL COMMENT '跟踪人',
    reviewed_by VARCHAR(32) COMMENT '审核人',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (budget_id) REFERENCES budget_management(budget_id) ON DELETE CASCADE,
    INDEX idx_tracking_budget (budget_id),
    INDEX idx_tracking_period (tracking_period),
    INDEX idx_tracking_date (tracking_date),
    INDEX idx_tracking_tracked_by (tracked_by),
    UNIQUE KEY uk_budget_tracking_period (budget_id, tracking_period)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算执行跟踪表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. product_cost_calculations: 产品成本核算主表，详细的成本计算
2. cost_element_details: 成本要素明细表，成本构成明细
3. standard_costs: 标准成本定义表，成本控制基准
4. cost_variance_analysis: 成本差异分析表，标准与实际对比
5. financial_reports: 财务报表数据表，综合财务信息
6. budget_management: 预算管理表，预算制定和管理
7. budget_execution_tracking: 预算执行跟踪表，预算执行监控

核心特性:
- 完整的产品成本核算体系
- 详细的成本要素分解和追溯
- 标准成本制定和差异分析
- 全面的财务报表管理
- 系统化的预算制定和执行跟踪
- 多维度的财务分析和控制
*/