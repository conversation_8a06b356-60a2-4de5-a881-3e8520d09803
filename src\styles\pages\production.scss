// IC封测CIM系统 - 生产计划页面样式

.production-page {
  padding: var(--spacing-6);
  
  &__header {
    margin-bottom: var(--spacing-6);
  }
  
  &__stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
  }
  
  &__gantt {
    margin-bottom: var(--spacing-6);
    overflow: hidden;
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
  }
  
  &__stages {
    padding: var(--spacing-5);
    background-color: var(--color-card-bg);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
  }
}

.production-stage {
  display: flex;
  align-items: center;
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-2);
  border-radius: var(--radius-base);
  transition: background-color var(--transition-fast);
  
  &:hover {
    background-color: var(--color-bg-hover);
  }
  
  &__icon {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-3);
  }
  
  &__info {
    flex: 1;
  }
  
  &__name {
    margin-bottom: 2px;
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
  }
  
  &__progress {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }
  
  &__status {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-sm);
  }
  
  &--cp {
    .production-stage__icon {
      color: var(--color-stage-cp);
    }
  }
  
  &--assembly {
    .production-stage__icon {
      color: var(--color-stage-assembly);
    }
  }
  
  &--ft {
    .production-stage__icon {
      color: var(--color-stage-ft);
    }
  }
  
  &--delivery {
    .production-stage__icon {
      color: var(--color-stage-delivery);
    }
  }
}