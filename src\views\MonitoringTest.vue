<template>
  <div class="monitoring-test">
    <div class="test-header">
      <h1>监控系统功能测试</h1>
      <p>IC封装测试工厂实时监控中心功能验证</p>
    </div>

    <div class="test-grid">
      <!-- 监控组件测试 -->
      <div class="test-section">
        <h2>监控组件测试</h2>

        <div class="component-tests">
          <h3>KPI卡片组件</h3>
          <div class="kpi-test-grid">
            <KPICard
              title="日产量"
              :value="125600"
              unit="pcs"
              :trend="4.2"
              :target="120000"
              type="primary"
              size="medium"
              subtitle="今日芯片产量统计"
            />

            <KPICard
              title="整体良率"
              :value="98.5"
              unit="%"
              :trend="0.8"
              :target="97.5"
              type="success"
              size="medium"
              subtitle="全工序良率水平"
            />

            <KPICard
              title="设备稼动率"
              :value="87.3"
              unit="%"
              :trend="-1.2"
              target="85"
              type="warning"
              size="medium"
              subtitle="设备有效作业时间比"
            />
          </div>

          <h3>状态指示器组件</h3>
          <div class="status-test-grid">
            <StatusIndicator
              status="running"
              label="生产线A"
              value="运行中"
              description="CP测试线"
            />

            <StatusIndicator
              status="warning"
              label="设备PRB001"
              value="维护中"
              description="探针卡更换"
            />

            <StatusIndicator
              status="danger"
              label="质量告警"
              value="异常"
              description="SPC超控制限"
            />

            <StatusIndicator
              status="success"
              label="系统状态"
              value="正常"
              description="所有数据源连接正常"
            />
          </div>
        </div>
      </div>

      <!-- 图表测试 -->
      <div class="test-section">
        <h2>图表组件测试</h2>

        <div class="chart-tests">
          <ChartPanel
            title="生产趋势图表测试"
            subtitle="验证ECharts集成和数据展示"
            :options="testChartOptions"
            height="300px"
            :time-ranges="timeRanges"
            :show-footer="true"
            :data-count="24"
            @refresh="handleRefresh"
            @time-range-change="handleTimeRangeChange"
          />
        </div>
      </div>

      <!-- API测试 -->
      <div class="test-section">
        <h2>API接口测试</h2>

        <div class="api-tests">
          <el-button @click="testProductionAPI">测试生产数据API</el-button>
          <el-button @click="testEquipmentAPI">测试设备数据API</el-button>
          <el-button @click="testQualityAPI">测试质量数据API</el-button>

          <div
v-if="apiResults" class="api-results"
>
            <h4>API响应结果:</h4>
            <pre>{{ JSON.stringify(apiResults, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- 路由测试 -->
      <div class="test-section">
        <h2>路由导航测试</h2>

        <div class="route-tests">
          <el-button-group>
            <el-button @click="navigateTo('/monitoring/center')">综合监控中心</el-button>
            <el-button @click="navigateTo('/monitoring/production')">生产监控大屏</el-button>
            <el-button @click="navigateTo('/monitoring/equipment')">设备监控大屏</el-button>
            <el-button @click="navigateTo('/monitoring/quality')">质量监控大屏</el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 功能验证 -->
      <div class="test-section">
        <h2>功能验证清单</h2>

        <div class="checklist">
          <el-checkbox v-model="checkList.components" label="组件渲染正常" />
          <el-checkbox v-model="checkList.styles" label="样式显示正确" />
          <el-checkbox v-model="checkList.responsive" label="响应式布局" />
          <el-checkbox v-model="checkList.apis" label="API数据获取" />
          <el-checkbox v-model="checkList.routes" label="路由导航" />
          <el-checkbox v-model="checkList.charts" label="图表显示" />
          <el-checkbox v-model="checkList.realtime" label="实时数据" />
          <el-checkbox v-model="checkList.themes" label="主题切换" />
        </div>

        <div class="test-summary">
          <p>测试完成度: {{ completionRate }}%</p>
          <el-progress :percentage="completionRate" :color="progressColor" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { useRouter } from 'vue-router'
  // Element Plus组件通过unplugin-auto-import自动导入
  import { KPICard, ChartPanel, StatusIndicator } from '@/components/monitoring'
  import { monitoringApi } from '@/api/monitoring'
  import type { EChartsOption } from 'echarts'

  const router = useRouter()

  // 测试数据
  const apiResults = ref<any>(null)
  const checkList = ref({
    components: false,
    styles: false,
    responsive: false,
    apis: false,
    routes: false,
    charts: false,
    realtime: false,
    themes: false
  })

  const timeRanges = [
    { label: '1小时', value: '1h' },
    { label: '4小时', value: '4h' },
    { label: '24小时', value: '24h' }
  ]

  // 测试图表配置
  const testChartOptions = computed<EChartsOption>(() => ({
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['产量', '良率'],
      textStyle: {
        color: 'var(--color-text-primary)'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`),
      axisLabel: {
        color: 'var(--color-text-secondary)'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '产量 (pcs)',
        axisLabel: {
          color: 'var(--color-text-secondary)',
          formatter: (value: number) => value.toLocaleString()
        }
      },
      {
        type: 'value',
        name: '良率 (%)',
        axisLabel: {
          formatter: '{value}%',
          color: 'var(--color-text-secondary)'
        }
      }
    ],
    series: [
      {
        name: '产量',
        type: 'line',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 2000) + 4000),
        smooth: true,
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '良率',
        type: 'line',
        yAxisIndex: 1,
        data: Array.from({ length: 24 }, () => (96 + Math.random() * 3).toFixed(1)),
        smooth: true,
        itemStyle: { color: '#52c41a' }
      }
    ]
  }))

  // 计算完成度
  const completionRate = computed(() => {
    const total = Object.keys(checkList.value).length
    const completed = Object.values(checkList.value).filter(Boolean).length
    return Math.round((completed / total) * 100)
  })

  const progressColor = computed(() => {
    if (completionRate.value >= 80) return '#52c41a'
    if (completionRate.value >= 60) return '#faad14'
    return '#f5222d'
  })

  // 方法
  const testProductionAPI = async () => {
    try {
      const data = await monitoringApi.getProductionKPI()
      apiResults.value = { type: 'Production KPI', data }
      checkList.value.apis = true
      ElMessage.success('生产数据API测试成功')
    } catch (error) {
      ElMessage.error('生产数据API测试失败')
      console.error(error)
    }
  }

  const testEquipmentAPI = async () => {
    try {
      const data = await monitoringApi.getEquipmentStatus()
      apiResults.value = { type: 'Equipment Status', data: data.slice(0, 3) }
      checkList.value.apis = true
      ElMessage.success('设备数据API测试成功')
    } catch (error) {
      ElMessage.error('设备数据API测试失败')
      console.error(error)
    }
  }

  const testQualityAPI = async () => {
    try {
      const data = await monitoringApi.getQualityKPI()
      apiResults.value = { type: 'Quality KPI', data }
      checkList.value.apis = true
      ElMessage.success('质量数据API测试成功')
    } catch (error) {
      ElMessage.error('质量数据API测试失败')
      console.error(error)
    }
  }

  const navigateTo = (path: string) => {
    router.push(path)
    checkList.value.routes = true
    ElMessage.success(`导航到: ${path}`)
  }

  const handleRefresh = () => {
    ElMessage.success('图表刷新功能正常')
    checkList.value.charts = true
  }

  const handleTimeRangeChange = (range: string) => {
    ElMessage.success(`时间范围切换: ${range}`)
    checkList.value.realtime = true
  }

  // 自动检查组件渲染
  setTimeout(() => {
    checkList.value.components = true
    checkList.value.styles = true
    checkList.value.responsive = true
  }, 1000)
</script>

<style lang="scss" scoped>
  .monitoring-test {
    min-height: 100vh;
    padding: var(--spacing-6);
    background: var(--color-bg-page);

    .test-header {
      margin-bottom: var(--spacing-8);
      text-align: center;

      h1 {
        margin-bottom: var(--spacing-2);
        font-size: 2rem;
        color: var(--color-text-primary);
      }

      p {
        font-size: var(--font-size-lg);
        color: var(--color-text-secondary);
      }
    }

    .test-grid {
      display: grid;
      gap: var(--spacing-6);
      max-width: 1400px;
      margin: 0 auto;
    }

    .test-section {
      padding: var(--spacing-6);
      background: var(--color-bg-primary);
      border: 1px solid var(--color-border-light);
      border-radius: var(--radius-lg);

      h2 {
        padding-bottom: var(--spacing-2);
        margin-bottom: var(--spacing-4);
        font-size: var(--font-size-xl);
        color: var(--color-primary);
        border-bottom: 2px solid var(--color-primary);
      }

      h3 {
        margin: var(--spacing-4) 0 var(--spacing-3) 0;
        font-size: var(--font-size-lg);
        color: var(--color-text-primary);
      }

      h4 {
        margin: var(--spacing-3) 0;
        font-size: var(--font-size-base);
        color: var(--color-text-primary);
      }
    }

    .kpi-test-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-6);
    }

    .status-test-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-4);
    }

    .chart-tests {
      margin-top: var(--spacing-4);
    }

    .api-tests {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);

      .el-button {
        align-self: flex-start;
      }

      .api-results {
        padding: var(--spacing-4);
        background: var(--color-bg-secondary);
        border-radius: var(--radius-base);

        pre {
          max-height: 300px;
          padding: var(--spacing-3);
          overflow-x: auto;
          font-size: var(--font-size-sm);
          color: #d4d4d4;
          background: #1e1e1e;
          border-radius: var(--radius-sm);
        }
      }
    }

    .route-tests {
      margin-top: var(--spacing-4);
    }

    .checklist {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-3);
      margin-bottom: var(--spacing-4);

      .el-checkbox {
        margin: 0;
      }
    }

    .test-summary {
      padding: var(--spacing-4);
      text-align: center;
      background: var(--color-bg-secondary);
      border-radius: var(--radius-base);

      p {
        margin-bottom: var(--spacing-3);
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);
      }
    }
  }

  .dark .monitoring-test {
    .test-section {
      background: var(--color-bg-secondary);
      border-color: var(--color-border-dark);
    }

    .api-results,
    .test-summary {
      background: var(--color-bg-tertiary);
    }
  }
</style>
