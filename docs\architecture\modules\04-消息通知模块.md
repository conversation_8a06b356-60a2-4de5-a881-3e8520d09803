# 消息通知模块设计

## 1. 模块概述

### 1.1 模块定位
消息通知模块为CIM系统提供统一的消息推送和通知服务，支持多渠道、多类型的消息发送，确保重要信息及时传达给相关人员，提升系统响应效率和用户体验。

### 1.2 复用价值
- **统一服务**：所有模块的消息通知需求统一处理
- **多渠道支持**：一次配置支持多种通知渠道
- **智能路由**：根据消息类型和用户偏好智能选择通知方式
- **可靠传输**：消息发送失败重试和降级机制

### 1.3 应用场景覆盖
```
消息通知模块应用场景
├── 系统通知
│   ├── 系统维护通知
│   ├── 版本更新通知
│   └── 系统异常通知
├── 业务提醒
│   ├── 工作任务分配
│   ├── 审批流程提醒
│   ├── 截止日期提醒
│   └── 会议安排通知
├── 异常报警
│   ├── 设备故障报警
│   ├── 质量异常报警
│   ├── 库存短缺报警
│   └── 安全事件报警
├── 状态变更
│   ├── 订单状态变更
│   ├── 工单完成通知
│   ├── 检验结果通知
│   └── 权限变更通知
└── 定期报告
    ├── 生产日报推送
    ├── 质量周报推送
    ├── 设备月报推送
    └── 绩效报告推送
```

## 2. 技术架构

### 2.1 架构设计
```
消息通知模块架构
├── 消息接收层              # 消息接收和预处理
├── 消息路由引擎            # 智能路由决策
├── 消息队列中间件          # 消息缓冲和持久化
├── 渠道适配层              # 多渠道适配器
├── 模板管理中心            # 消息模板管理
├── 用户偏好管理            # 个性化设置
├── 发送状态跟踪            # 发送结果跟踪
└── 统计分析中心            # 通知效果分析
```

### 2.2 核心数据模型

#### 2.2.1 消息定义
```sql
-- 消息模板表
CREATE TABLE message_templates (
    template_id VARCHAR(30) PRIMARY KEY,
    template_code VARCHAR(100) UNIQUE,     -- 模板编码
    template_name VARCHAR(200),            -- 模板名称
    template_category VARCHAR(50),         -- 模板分类
    message_type ENUM('system','business','alert','reminder'), -- 消息类型
    title_template TEXT,                   -- 标题模板
    content_template TEXT,                 -- 内容模板
    variables JSON,                        -- 变量定义
    default_channels JSON,                 -- 默认发送渠道
    priority ENUM('low','normal','high','urgent'), -- 优先级
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_category_type (template_category, message_type),
    INDEX idx_code (template_code)
);

-- 消息发送记录表
CREATE TABLE message_records (
    record_id VARCHAR(30) PRIMARY KEY,
    template_id VARCHAR(30),               -- 模板ID
    business_id VARCHAR(100),              -- 业务关联ID
    business_type VARCHAR(50),             -- 业务类型
    recipients JSON,                       -- 接收人信息
    title VARCHAR(500),                    -- 消息标题
    content TEXT,                          -- 消息内容
    channels JSON,                         -- 发送渠道
    variables JSON,                        -- 模板变量
    priority ENUM('low','normal','high','urgent'),
    scheduled_time TIMESTAMP,              -- 计划发送时间
    created_at TIMESTAMP,
    created_by VARCHAR(20),
    
    INDEX idx_business (business_type, business_id),
    INDEX idx_created_time (created_at),
    INDEX idx_scheduled_time (scheduled_time)
);

-- 消息发送明细表
CREATE TABLE message_send_details (
    detail_id VARCHAR(30) PRIMARY KEY,
    record_id VARCHAR(30),                 -- 消息记录ID
    recipient_id VARCHAR(20),              -- 接收人ID
    recipient_type ENUM('user','role','email','phone'), -- 接收人类型
    recipient_address VARCHAR(200),        -- 接收地址
    channel VARCHAR(20),                   -- 发送渠道
    send_time TIMESTAMP,                   -- 发送时间
    status ENUM('pending','sending','success','failed','cancelled'), -- 状态
    error_message TEXT,                    -- 错误信息
    retry_count INT DEFAULT 0,             -- 重试次数
    read_time TIMESTAMP,                   -- 阅读时间
    
    INDEX idx_record_recipient (record_id, recipient_id),
    INDEX idx_status_time (status, send_time),
    INDEX idx_channel_status (channel, status)
);
```

#### 2.2.2 用户偏好配置
```sql
-- 用户通知偏好表
CREATE TABLE user_notification_preferences (
    preference_id VARCHAR(30) PRIMARY KEY,
    user_id VARCHAR(20),                   -- 用户ID
    message_category VARCHAR(50),          -- 消息分类
    enabled_channels JSON,                 -- 启用的通知渠道
    quiet_hours JSON,                      -- 免打扰时间段
    frequency_limit JSON,                  -- 频率限制
    priority_filter VARCHAR(50),          -- 优先级过滤
    keywords_filter JSON,                 -- 关键词过滤
    is_active BOOLEAN DEFAULT TRUE,
    updated_at TIMESTAMP,
    
    UNIQUE KEY uk_user_category (user_id, message_category)
);

-- 通知渠道配置表
CREATE TABLE notification_channels (
    channel_id VARCHAR(30) PRIMARY KEY,
    channel_code VARCHAR(50) UNIQUE,      -- 渠道编码
    channel_name VARCHAR(100),            -- 渠道名称
    channel_type ENUM('email','sms','wechat','app','websocket','webhook'), -- 渠道类型
    provider VARCHAR(100),                -- 服务提供商
    config JSON,                          -- 配置参数
    rate_limit_config JSON,               -- 限流配置
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    
    INDEX idx_type_active (channel_type, is_active)
);
```

## 3. 消息处理引擎

### 3.1 消息接收和预处理
```java
@Service
public class MessageReceiver {
    
    @Autowired
    private MessageTemplateService templateService;
    
    @Autowired
    private MessageRoutingEngine routingEngine;
    
    @Autowired
    private MessageQueue messageQueue;
    
    /**
     * 发送消息（异步）
     */
    public String sendMessage(SendMessageRequest request) {
        // 1. 验证请求参数
        validateRequest(request);
        
        // 2. 获取消息模板
        MessageTemplate template = templateService.getTemplate(request.getTemplateCode());
        
        // 3. 渲染消息内容
        RenderedMessage renderedMessage = renderMessage(template, request.getVariables());
        
        // 4. 创建消息记录
        MessageRecord record = createMessageRecord(template, request, renderedMessage);
        
        // 5. 路由决策
        List<NotificationChannel> channels = routingEngine.route(record);
        
        // 6. 放入消息队列
        for (NotificationChannel channel : channels) {
            MessageTask task = new MessageTask(record, channel);
            messageQueue.enqueue(task);
        }
        
        return record.getRecordId();
    }
    
    /**
     * 发送即时消息（同步）
     */
    public MessageSendResult sendImmediateMessage(SendMessageRequest request) {
        String recordId = sendMessage(request);
        
        // 等待发送完成（设置超时）
        return waitForCompletion(recordId, Duration.ofMinutes(5));
    }
    
    private RenderedMessage renderMessage(MessageTemplate template, Map<String, Object> variables) {
        TemplateEngine templateEngine = getTemplateEngine();
        
        String title = templateEngine.render(template.getTitleTemplate(), variables);
        String content = templateEngine.render(template.getContentTemplate(), variables);
        
        return new RenderedMessage(title, content);
    }
    
    private MessageRecord createMessageRecord(MessageTemplate template, SendMessageRequest request, 
                                            RenderedMessage renderedMessage) {
        MessageRecord record = new MessageRecord();
        record.setRecordId(IdGenerator.generateId());
        record.setTemplateId(template.getTemplateId());
        record.setBusinessId(request.getBusinessId());
        record.setBusinessType(request.getBusinessType());
        record.setRecipients(request.getRecipients());
        record.setTitle(renderedMessage.getTitle());
        record.setContent(renderedMessage.getContent());
        record.setVariables(request.getVariables());
        record.setPriority(template.getPriority());
        record.setScheduledTime(request.getScheduledTime());
        record.setCreatedBy(request.getSenderId());
        
        return messageRecordRepository.save(record);
    }
}
```

### 3.2 智能路由引擎
```java
@Component
public class MessageRoutingEngine {
    
    @Autowired
    private UserPreferenceService userPreferenceService;
    
    @Autowired
    private ChannelAvailabilityService channelService;
    
    @Autowired
    private MessageRoutingRules routingRules;
    
    public List<RouteDecision> route(MessageRecord record) {
        List<RouteDecision> decisions = new ArrayList<>();
        
        List<String> recipients = parseRecipients(record.getRecipients());
        
        for (String recipientId : recipients) {
            RouteDecision decision = routeForRecipient(record, recipientId);
            decisions.add(decision);
        }
        
        return decisions;
    }
    
    private RouteDecision routeForRecipient(MessageRecord record, String recipientId) {
        RouteDecision decision = new RouteDecision(record.getRecordId(), recipientId);
        
        // 1. 获取用户偏好
        UserNotificationPreference preference = userPreferenceService
            .getPreference(recipientId, record.getTemplateCategory());
        
        // 2. 检查免打扰时间
        if (isInQuietHours(preference)) {
            decision.setDelayed(true);
            decision.setDelayUntil(calculateDelayTime(preference));
        }
        
        // 3. 应用频率限制
        if (exceedsFrequencyLimit(recipientId, record.getTemplateCategory(), preference)) {
            decision.setSuppressed(true);
            decision.setSuppressionReason("频率限制");
            return decision;
        }
        
        // 4. 选择发送渠道
        List<String> channels = selectChannels(record, preference);
        decision.setChannels(channels);
        
        // 5. 应用降级策略
        applyFallbackStrategy(decision, record.getPriority());
        
        return decision;
    }
    
    private List<String> selectChannels(MessageRecord record, UserNotificationPreference preference) {
        List<String> selectedChannels = new ArrayList<>();
        
        // 根据消息优先级和用户偏好选择渠道
        MessagePriority priority = record.getPriority();
        List<String> availableChannels = preference.getEnabledChannels();
        
        switch (priority) {
            case URGENT:
                // 紧急消息：使用所有可用渠道
                selectedChannels.addAll(availableChannels);
                break;
                
            case HIGH:
                // 高优先级：使用即时渠道
                selectedChannels.addAll(filterChannelsByType(availableChannels, 
                    Arrays.asList("sms", "app", "wechat")));
                if (selectedChannels.isEmpty()) {
                    selectedChannels.add("email"); // 降级
                }
                break;
                
            case NORMAL:
                // 普通优先级：使用常规渠道
                selectedChannels.addAll(filterChannelsByType(availableChannels, 
                    Arrays.asList("app", "email")));
                break;
                
            case LOW:
                // 低优先级：仅使用非打扰渠道
                selectedChannels.addAll(filterChannelsByType(availableChannels, 
                    Arrays.asList("email")));
                break;
        }
        
        // 过滤不可用的渠道
        return selectedChannels.stream()
            .filter(channelService::isChannelAvailable)
            .collect(Collectors.toList());
    }
}
```

### 3.3 消息队列处理
```java
@Component
public class MessageQueueProcessor {
    
    @Autowired
    private List<ChannelSender> channelSenders;
    
    @RabbitListener(queues = "message.notification.queue")
    public void processMessage(MessageTask task) {
        MessageSendDetail detail = createSendDetail(task);
        
        try {
            detail.setStatus(SendStatus.SENDING);
            detail.setSendTime(LocalDateTime.now());
            messageSendDetailRepository.save(detail);
            
            // 根据渠道类型选择发送器
            ChannelSender sender = getSenderByChannel(task.getChannel().getChannelType());
            
            // 发送消息
            SendResult result = sender.send(task);
            
            // 更新发送状态
            if (result.isSuccess()) {
                detail.setStatus(SendStatus.SUCCESS);
            } else {
                detail.setStatus(SendStatus.FAILED);
                detail.setErrorMessage(result.getErrorMessage());
                
                // 重试机制
                handleSendFailure(task, detail);
            }
            
        } catch (Exception e) {
            log.error("消息发送异常", e);
            detail.setStatus(SendStatus.FAILED);
            detail.setErrorMessage(e.getMessage());
            
            handleSendFailure(task, detail);
        } finally {
            messageSendDetailRepository.save(detail);
        }
    }
    
    private void handleSendFailure(MessageTask task, MessageSendDetail detail) {
        if (detail.getRetryCount() < getMaxRetryCount(task.getPriority())) {
            // 重试
            scheduleRetry(task, detail.getRetryCount() + 1);
        } else {
            // 降级到其他渠道
            tryFallbackChannel(task);
        }
    }
    
    private void scheduleRetry(MessageTask task, int retryCount) {
        // 指数退避重试
        long delaySeconds = (long) Math.pow(2, retryCount) * 60; // 2^n分钟
        
        MessageTask retryTask = task.copy();
        retryTask.setRetryCount(retryCount);
        retryTask.setScheduledTime(LocalDateTime.now().plusSeconds(delaySeconds));
        
        messageQueue.enqueueDelayed(retryTask, Duration.ofSeconds(delaySeconds));
    }
}
```

## 4. 多渠道发送器

### 4.1 邮件发送器
```java
@Component
@ChannelType("email")
public class EmailChannelSender implements ChannelSender {
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Override
    public SendResult send(MessageTask task) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            // 设置邮件信息
            helper.setTo(task.getRecipientAddress());
            helper.setSubject(task.getTitle());
            helper.setText(task.getContent(), true); // HTML格式
            helper.setFrom(getFromAddress());
            
            // 发送邮件
            mailSender.send(message);
            
            return SendResult.success();
            
        } catch (Exception e) {
            return SendResult.failure(e.getMessage());
        }
    }
    
    @Override
    public boolean isAvailable() {
        // 检查邮件服务器连接状态
        try {
            mailSender.createMimeMessage();
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
```

### 4.2 短信发送器
```java
@Component
@ChannelType("sms")
public class SmsChannelSender implements ChannelSender {
    
    @Autowired
    private SmsProvider smsProvider; // 可配置多个SMS提供商
    
    @Override
    public SendResult send(MessageTask task) {
        try {
            // 短信内容长度检查
            String content = task.getContent();
            if (content.length() > MAX_SMS_LENGTH) {
                content = content.substring(0, MAX_SMS_LENGTH - 3) + "...";
            }
            
            // 发送短信
            SmsResult result = smsProvider.sendSms(
                task.getRecipientAddress(),
                content
            );
            
            if (result.isSuccess()) {
                return SendResult.success(result.getMessageId());
            } else {
                return SendResult.failure(result.getErrorMessage());
            }
            
        } catch (Exception e) {
            return SendResult.failure(e.getMessage());
        }
    }
    
    @Override
    public RateLimit getRateLimit() {
        // SMS通常有严格的发送频率限制
        return RateLimit.of(10, Duration.ofMinutes(1)); // 每分钟10条
    }
}
```

### 4.3 APP推送发送器
```java
@Component
@ChannelType("app")
public class AppPushChannelSender implements ChannelSender {
    
    @Autowired
    private PushNotificationService pushService;
    
    @Override
    public SendResult send(MessageTask task) {
        try {
            // 获取用户设备Token
            List<String> deviceTokens = getUserDeviceTokens(task.getRecipientId());
            
            if (deviceTokens.isEmpty()) {
                return SendResult.failure("用户未安装或登录APP");
            }
            
            PushMessage pushMessage = PushMessage.builder()
                .title(task.getTitle())
                .content(task.getContent())
                .data(buildPushData(task))
                .build();
            
            List<String> successTokens = new ArrayList<>();
            List<String> failedTokens = new ArrayList<>();
            
            for (String token : deviceTokens) {
                try {
                    pushService.push(token, pushMessage);
                    successTokens.add(token);
                } catch (Exception e) {
                    failedTokens.add(token);
                    log.warn("推送到设备失败: {}, {}", token, e.getMessage());
                }
            }
            
            if (!successTokens.isEmpty()) {
                return SendResult.success("成功推送到" + successTokens.size() + "个设备");
            } else {
                return SendResult.failure("所有设备推送失败");
            }
            
        } catch (Exception e) {
            return SendResult.failure(e.getMessage());
        }
    }
    
    private Map<String, Object> buildPushData(MessageTask task) {
        Map<String, Object> data = new HashMap<>();
        data.put("messageId", task.getRecordId());
        data.put("businessType", task.getBusinessType());
        data.put("businessId", task.getBusinessId());
        data.put("priority", task.getPriority().name());
        return data;
    }
}
```

### 4.4 WebSocket实时推送
```java
@Component
@ChannelType("websocket")
public class WebSocketChannelSender implements ChannelSender {
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    private WebSocketSessionManager sessionManager;
    
    @Override
    public SendResult send(MessageTask task) {
        try {
            String userId = task.getRecipientId();
            
            // 检查用户是否在线
            if (!sessionManager.isUserOnline(userId)) {
                return SendResult.failure("用户不在线");
            }
            
            // 构建WebSocket消息
            WebSocketMessage wsMessage = WebSocketMessage.builder()
                .type("notification")
                .title(task.getTitle())
                .content(task.getContent())
                .priority(task.getPriority())
                .timestamp(LocalDateTime.now())
                .data(task.getVariables())
                .build();
            
            // 发送到用户
            messagingTemplate.convertAndSendToUser(
                userId, 
                "/queue/notifications", 
                wsMessage
            );
            
            return SendResult.success();
            
        } catch (Exception e) {
            return SendResult.failure(e.getMessage());
        }
    }
    
    @Override
    public boolean isRealtime() {
        return true; // WebSocket是实时通道
    }
}
```

## 5. 消息模板引擎

### 5.1 模板管理服务
```java
@Service
public class MessageTemplateService {
    
    @Autowired
    private MessageTemplateRepository templateRepository;
    
    @Autowired
    private TemplateEngine templateEngine;
    
    public MessageTemplate getTemplate(String templateCode) {
        return templateRepository.findByTemplateCode(templateCode)
            .orElseThrow(() -> new TemplateNotFoundException("模板不存在: " + templateCode));
    }
    
    public String renderTemplate(String templateContent, Map<String, Object> variables) {
        try {
            return templateEngine.process(templateContent, createContext(variables));
        } catch (Exception e) {
            throw new TemplateRenderException("模板渲染失败", e);
        }
    }
    
    public void validateTemplate(MessageTemplate template) {
        // 验证模板语法
        try {
            templateEngine.process(template.getTitleTemplate(), createTestContext(template));
            templateEngine.process(template.getContentTemplate(), createTestContext(template));
        } catch (Exception e) {
            throw new TemplateValidationException("模板语法错误", e);
        }
        
        // 验证必需变量
        validateRequiredVariables(template);
    }
    
    private Context createContext(Map<String, Object> variables) {
        Context context = new Context();
        context.setVariables(variables);
        
        // 添加通用变量
        context.setVariable("currentTime", LocalDateTime.now());
        context.setVariable("currentDate", LocalDate.now());
        context.setVariable("systemName", "CIM系统");
        
        return context;
    }
}
```

### 5.2 模板示例配置
```json
{
  "templateCode": "ORDER_APPROVAL_REQUIRED",
  "templateName": "订单审批提醒",
  "templateCategory": "business",
  "messageType": "reminder",
  "titleTemplate": "订单审批提醒 - ${orderNo}",
  "contentTemplate": "
    <div>
      <h3>订单审批提醒</h3>
      <p>尊敬的 ${approverName}，您好！</p>
      <p>有一个订单需要您的审批：</p>
      <ul>
        <li><strong>订单号：</strong>${orderNo}</li>
        <li><strong>客户名称：</strong>${customerName}</li>
        <li><strong>订单金额：</strong>￥${orderAmount}</li>
        <li><strong>交货日期：</strong>${deliveryDate}</li>
        <li><strong>提交人：</strong>${submitterName}</li>
        <li><strong>提交时间：</strong>${submitTime}</li>
      </ul>
      <p>请及时登录系统进行审批处理。</p>
      <p><a href='${approvalUrl}'>点击这里进行审批</a></p>
      <hr>
      <p><small>此邮件为系统自动发送，请勿回复。如有疑问，请联系系统管理员。</small></p>
    </div>
  ",
  "variables": [
    {"name": "approverName", "label": "审批人姓名", "type": "string", "required": true},
    {"name": "orderNo", "label": "订单号", "type": "string", "required": true},
    {"name": "customerName", "label": "客户名称", "type": "string", "required": true},
    {"name": "orderAmount", "label": "订单金额", "type": "number", "required": true},
    {"name": "deliveryDate", "label": "交货日期", "type": "date", "required": true},
    {"name": "submitterName", "label": "提交人", "type": "string", "required": true},
    {"name": "submitTime", "label": "提交时间", "type": "datetime", "required": true},
    {"name": "approvalUrl", "label": "审批链接", "type": "string", "required": true}
  ],
  "defaultChannels": ["email", "app"],
  "priority": "high"
}
```

## 6. 用户偏好管理

### 6.1 偏好设置服务
```java
@Service
public class UserPreferenceService {
    
    public UserNotificationPreference getPreference(String userId, String category) {
        return preferenceRepository.findByUserIdAndCategory(userId, category)
            .orElse(getDefaultPreference(category));
    }
    
    public void updatePreference(String userId, String category, 
                               UpdatePreferenceRequest request) {
        UserNotificationPreference preference = getPreference(userId, category);
        
        // 更新偏好设置
        if (request.getEnabledChannels() != null) {
            preference.setEnabledChannels(request.getEnabledChannels());
        }
        
        if (request.getQuietHours() != null) {
            preference.setQuietHours(request.getQuietHours());
        }
        
        if (request.getFrequencyLimit() != null) {
            preference.setFrequencyLimit(request.getFrequencyLimit());
        }
        
        preferenceRepository.save(preference);
        
        // 清除缓存
        preferenceCache.evict(userId + ":" + category);
    }
    
    public List<UserNotificationPreference> getAllPreferences(String userId) {
        return preferenceRepository.findByUserId(userId);
    }
    
    private UserNotificationPreference getDefaultPreference(String category) {
        UserNotificationPreference defaultPref = new UserNotificationPreference();
        defaultPref.setEnabledChannels(Arrays.asList("email", "app"));
        defaultPref.setQuietHours(createDefaultQuietHours());
        defaultPref.setFrequencyLimit(createDefaultFrequencyLimit());
        defaultPref.setPriorityFilter("normal");
        return defaultPref;
    }
}
```

### 6.2 偏好设置界面
```vue
<template>
  <div class="notification-preferences">
    <el-card>
      <div slot="header">
        <span>通知偏好设置</span>
      </div>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="通知渠道" name="channels">
          <div class="preference-section">
            <h4>选择接收通知的渠道</h4>
            <el-checkbox-group v-model="preferences.enabledChannels">
              <el-checkbox label="email">邮件通知</el-checkbox>
              <el-checkbox label="sms">短信通知</el-checkbox>
              <el-checkbox label="app">APP推送</el-checkbox>
              <el-checkbox label="wechat">微信通知</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="免打扰时间" name="quiet">
          <div class="preference-section">
            <h4>免打扰时间段</h4>
            <el-time-picker
              v-model="quietStart"
              format="HH:mm"
              placeholder="开始时间">
            </el-time-picker>
            <span style="margin: 0 10px;">至</span>
            <el-time-picker
              v-model="quietEnd"
              format="HH:mm"
              placeholder="结束时间">
            </el-time-picker>
            <p class="help-text">在此时间段内，除紧急消息外，其他通知将被延迟发送</p>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="消息过滤" name="filter">
          <div class="preference-section">
            <h4>优先级过滤</h4>
            <el-select v-model="preferences.priorityFilter" placeholder="选择最低接收优先级">
              <el-option label="低优先级及以上" value="low"></el-option>
              <el-option label="普通优先级及以上" value="normal"></el-option>
              <el-option label="高优先级及以上" value="high"></el-option>
              <el-option label="仅紧急消息" value="urgent"></el-option>
            </el-select>
            
            <h4 style="margin-top: 20px;">关键词过滤</h4>
            <el-input
              v-model="keywordInput"
              placeholder="输入关键词，用逗号分隔"
              @blur="updateKeywords">
            </el-input>
            <p class="help-text">包含这些关键词的消息将被屏蔽</p>
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <div class="preference-actions">
        <el-button type="primary" @click="savePreferences">保存设置</el-button>
        <el-button @click="resetPreferences">恢复默认</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'NotificationPreferences',
  data() {
    return {
      activeTab: 'channels',
      preferences: {
        enabledChannels: [],
        priorityFilter: 'normal',
        keywordsFilter: []
      },
      quietStart: null,
      quietEnd: null,
      keywordInput: ''
    };
  },
  async created() {
    await this.loadPreferences();
  },
  methods: {
    async loadPreferences() {
      try {
        const response = await this.$api.notification.getPreferences();
        this.preferences = response.data;
        this.updateUIFromPreferences();
      } catch (error) {
        this.$message.error('加载偏好设置失败');
      }
    },
    
    async savePreferences() {
      try {
        const data = this.buildPreferencesData();
        await this.$api.notification.updatePreferences(data);
        this.$message.success('偏好设置保存成功');
      } catch (error) {
        this.$message.error('保存失败：' + error.message);
      }
    },
    
    buildPreferencesData() {
      return {
        enabledChannels: this.preferences.enabledChannels,
        quietHours: this.buildQuietHours(),
        priorityFilter: this.preferences.priorityFilter,
        keywordsFilter: this.preferences.keywordsFilter
      };
    },
    
    buildQuietHours() {
      if (!this.quietStart || !this.quietEnd) return null;
      
      return {
        startTime: this.formatTime(this.quietStart),
        endTime: this.formatTime(this.quietEnd)
      };
    }
  }
};
</script>
```

## 7. 统计分析

### 7.1 发送统计分析
```java
@Service
public class MessageAnalyticsService {
    
    public MessageStatistics getStatistics(String userId, LocalDate startDate, LocalDate endDate) {
        MessageStatistics stats = new MessageStatistics();
        
        // 发送统计
        stats.setTotalSent(getTotalSent(userId, startDate, endDate));
        stats.setSuccessCount(getSuccessCount(userId, startDate, endDate));
        stats.setFailureCount(getFailureCount(userId, startDate, endDate));
        stats.setSuccessRate(calculateSuccessRate(stats.getSuccessCount(), stats.getTotalSent()));
        
        // 渠道统计
        stats.setChannelStats(getChannelStatistics(userId, startDate, endDate));
        
        // 消息类型统计
        stats.setTypeStats(getTypeStatistics(userId, startDate, endDate));
        
        // 趋势数据
        stats.setTrendData(getTrendData(userId, startDate, endDate));
        
        return stats;
    }
    
    public List<MessageEffectivenessReport> getEffectivenessReport() {
        // 分析消息的有效性（阅读率、响应率等）
        List<MessageEffectivenessReport> reports = new ArrayList<>();
        
        // 按模板分析
        List<MessageTemplate> templates = templateService.getAllActiveTemplates();
        for (MessageTemplate template : templates) {
            MessageEffectivenessReport report = analyzeTemplateEffectiveness(template);
            reports.add(report);
        }
        
        return reports;
    }
    
    private MessageEffectivenessReport analyzeTemplateEffectiveness(MessageTemplate template) {
        String templateId = template.getTemplateId();
        
        // 获取最近30天的发送数据
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(30);
        
        List<MessageSendDetail> details = messageSendDetailRepository
            .findByTemplateIdAndSendTimeBetween(templateId, startTime, endTime);
        
        MessageEffectivenessReport report = new MessageEffectivenessReport();
        report.setTemplateId(templateId);
        report.setTemplateName(template.getTemplateName());
        report.setTotalSent(details.size());
        
        // 计算成功率
        long successCount = details.stream()
            .filter(d -> d.getStatus() == SendStatus.SUCCESS)
            .count();
        report.setSuccessRate((double) successCount / details.size() * 100);
        
        // 计算阅读率（如果有阅读时间记录）
        long readCount = details.stream()
            .filter(d -> d.getReadTime() != null)
            .count();
        report.setReadRate((double) readCount / successCount * 100);
        
        // 计算平均阅读时间
        OptionalDouble avgReadTime = details.stream()
            .filter(d -> d.getReadTime() != null)
            .mapToLong(d -> Duration.between(d.getSendTime(), d.getReadTime()).toMinutes())
            .average();
        report.setAverageReadTimeMinutes(avgReadTime.orElse(0));
        
        return report;
    }
}
```

## 8. 性能优化

### 8.1 批量发送优化
```java
@Service
public class BatchMessageProcessor {
    
    @Autowired
    private MessageQueue messageQueue;
    
    /**
     * 批量发送消息
     */
    public void sendBatchMessages(List<MessageTask> tasks) {
        // 按渠道分组
        Map<String, List<MessageTask>> groupedTasks = tasks.stream()
            .collect(Collectors.groupingBy(task -> task.getChannel().getChannelCode()));
        
        // 并行处理不同渠道
        groupedTasks.entrySet().parallelStream().forEach(entry -> {
            String channelCode = entry.getKey();
            List<MessageTask> channelTasks = entry.getValue();
            
            processBatchForChannel(channelCode, channelTasks);
        });
    }
    
    private void processBatchForChannel(String channelCode, List<MessageTask> tasks) {
        ChannelSender sender = getSenderByChannel(channelCode);
        
        if (sender.supportsBatch()) {
            // 渠道支持批量发送
            BatchSendResult result = sender.sendBatch(tasks);
            updateBatchSendResults(tasks, result);
        } else {
            // 单条发送但使用连接池优化
            tasks.parallelStream().forEach(task -> {
                SendResult result = sender.send(task);
                updateSendResult(task, result);
            });
        }
    }
    
    @Async("messageExecutor")
    public CompletableFuture<Void> sendAsyncBatch(List<MessageTask> tasks) {
        sendBatchMessages(tasks);
        return CompletableFuture.completedFuture(null);
    }
}
```

### 8.2 缓存优化策略
```java
@Configuration
@EnableCaching
public class MessageCacheConfig {
    
    @Bean
    public CacheManager messageCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .recordStats());
        return cacheManager;
    }
}

@Service
public class MessageCacheService {
    
    @Cacheable(value = "message-templates", key = "#templateCode")
    public MessageTemplate getTemplate(String templateCode) {
        return templateRepository.findByTemplateCode(templateCode);
    }
    
    @Cacheable(value = "user-preferences", key = "#userId + ':' + #category")
    public UserNotificationPreference getUserPreference(String userId, String category) {
        return preferenceRepository.findByUserIdAndCategory(userId, category);
    }
    
    @CacheEvict(value = "user-preferences", key = "#userId + ':*'", allEntries = true)
    public void evictUserPreferences(String userId) {
        // 用户偏好变更时清除缓存
    }
}
```

---

*消息通知模块为CIM系统提供了可靠、灵活、智能的消息推送能力，确保重要信息及时准确地传达给相关人员*