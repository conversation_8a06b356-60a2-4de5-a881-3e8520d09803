<template>
  <div class="c-avatar"
:class="avatarClass" @click="handleClick">
    <el-avatar :size="size" :src="avatarSrc" :alt="alt" :shape="shape" class="avatar-inner">
      <template v-if="!avatarSrc">
        <el-icon v-if="icon"
:size="iconSize">
          <component :is="icon" />
        </el-icon>
        <span v-else-if="initials"
class="avatar-initials">
          {{ initials }}
        </span>
        <el-icon v-else
:size="iconSize">
          <user />
        </el-icon>
      </template>
    </el-avatar>

    <!-- 状态指示器 -->
    <div
      v-if="showStatus"
      class="status-indicator"
      :class="`status-${status}`"
      :title="statusText"
    />

    <!-- 悬浮编辑按钮 -->
    <div v-if="editable && !readonly" class="edit-overlay" @click.stop="handleEdit">
      <el-icon :size="editIconSize">
        <camera />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { User, Camera } from '@element-plus/icons-vue'

  interface Props {
    /** 头像图片地址 */
    src?: string
    /** 头像大小 */
    size?: number | 'large' | 'default' | 'small'
    /** 头像形状 */
    shape?: 'circle' | 'square'
    /** 替代文本 */
    alt?: string
    /** 用户姓名（用于生成首字母） */
    name?: string
    /** 自定义图标 */
    icon?: any
    /** 是否可编辑 */
    editable?: boolean
    /** 是否只读 */
    readonly?: boolean
    /** 是否可点击 */
    clickable?: boolean
    /** 显示在线状态 */
    showStatus?: boolean
    /** 状态类型 */
    status?: 'online' | 'offline' | 'busy' | 'away'
    /** 自定义CSS类 */
    customClass?: string
  }

  interface Emits {
    (e: 'click', event: MouseEvent): void
    (e: 'edit'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    src: '',
    size: 'default',
    shape: 'circle',
    alt: '',
    name: '',
    editable: false,
    readonly: false,
    clickable: false,
    showStatus: false,
    status: 'offline',
    customClass: ''
  })

  const emit = defineEmits<Emits>()

  // 计算头像源地址
  const avatarSrc = computed(() => {
    if (!props.src) return ''

    // 如果是相对路径，转换为完整URL
    if (props.src.startsWith('/')) {
      return `${window.location.origin}${props.src}`
    }

    return props.src
  })

  // 计算首字母
  const initials = computed(() => {
    if (!props.name) return ''

    const words = props.name.trim().split(/\s+/)
    if (words.length === 1) {
      // 单个词取前两个字符
      return words[0].slice(0, 2).toUpperCase()
    } else {
      // 多个词取每个词的首字母
      return words
        .slice(0, 2)
        .map(word => word.charAt(0).toUpperCase())
        .join('')
    }
  })

  // 计算图标大小
  const iconSize = computed(() => {
    const sizeMap = {
      small: 14,
      default: 18,
      large: 24
    }

    if (typeof props.size === 'number') {
      return Math.floor(props.size * 0.4)
    }

    return sizeMap[props.size] || 18
  })

  // 计算编辑图标大小
  const editIconSize = computed(() => {
    if (typeof props.size === 'number') {
      return Math.floor(props.size * 0.25)
    }

    const sizeMap = {
      small: 12,
      default: 14,
      large: 16
    }

    return sizeMap[props.size] || 14
  })

  // 计算CSS类
  const avatarClass = computed(() => {
    return [
      {
        'c-avatar--clickable': props.clickable || props.editable,
        'c-avatar--editable': props.editable && !props.readonly,
        'c-avatar--readonly': props.readonly,
        'c-avatar--with-status': props.showStatus
      },
      props.customClass
    ]
  })

  // 计算状态文本
  const statusText = computed(() => {
    const statusMap = {
      online: '在线',
      offline: '离线',
      busy: '忙碌',
      away: '离开'
    }
    return statusMap[props.status]
  })

  /**
   * 处理点击事件
   */
  const handleClick = (event: MouseEvent): void => {
    if (props.clickable || props.editable) {
      emit('click', event)
    }
  }

  /**
   * 处理编辑事件
   */
  const handleEdit = (): void => {
    if (props.editable && !props.readonly) {
      emit('edit')
    }
  }
</script>

<style lang="scss" scoped>
  .c-avatar {
    position: relative;
    display: inline-block;

    .avatar-inner {
      transition: all 0.3s ease;
    }

    .avatar-initials {
      font-weight: 600;
      user-select: none;
    }

    // 状态指示器
    .status-indicator {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 2px solid var(--color-bg-primary);

      &.status-online {
        background-color: #52c41a;
      }

      &.status-offline {
        background-color: #d9d9d9;
      }

      &.status-busy {
        background-color: #ff4d4f;
      }

      &.status-away {
        background-color: #faad14;
      }
    }

    // 编辑覆盖层
    .edit-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      border-radius: inherit;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      cursor: pointer;
      color: white;

      .el-icon {
        transition: transform 0.3s ease;
      }

      &:hover {
        .el-icon {
          transform: scale(1.1);
        }
      }
    }

    // 可点击状态
    &--clickable {
      cursor: pointer;

      .avatar-inner {
        &:hover {
          transform: scale(1.05);
        }
      }
    }

    // 可编辑状态
    &--editable {
      &:hover .edit-overlay {
        opacity: 1;
      }
    }

    // 只读状态
    &--readonly {
      .edit-overlay {
        display: none;
      }
    }

    // 有状态指示器时调整布局
    &--with-status {
      .status-indicator {
        z-index: 1;
      }
    }
  }

  // 不同大小的状态指示器调整
  :deep(.el-avatar--small) + .status-indicator {
    width: 8px;
    height: 8px;
    border-width: 1px;
  }

  :deep(.el-avatar--large) + .status-indicator {
    width: 16px;
    height: 16px;
    border-width: 3px;
  }

  // 暗色主题适配
  [data-theme='dark'] {
    .c-avatar {
      .status-indicator {
        border-color: var(--color-bg-primary);

        &.status-offline {
          background-color: #595959;
        }
      }

      .edit-overlay {
        background: rgba(0, 0, 0, 0.7);
      }
    }
  }
</style>
