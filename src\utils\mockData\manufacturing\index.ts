// 制造执行管理 Mock 数据
// 用于开发和测试的模拟数据

import type {
  Equipment,
  CPTestRecord,
  AssemblyRecord,
  FinalTestRecord,
  WaferInfo,
  ProbeCard,
  WaferMap,
  WaferMapData,
  TestProgram,
  HandlerOperation,
  ProcessParameter,
  ElectricalTest,
  SPCData,
  DashboardData,
  SECSMessage
} from '@/types/manufacturing'

/**
 * 生成模拟晶圆图数据
 */
export function generateWaferMapData(maxX: number, maxY: number): WaferMapData[] {
  const mapData: WaferMapData[] = []

  for (let x = 0; x < maxX; x++) {
    for (let y = 0; y < maxY; y++) {
      // 模拟圆形晶圆边界
      const centerX = maxX / 2
      const centerY = maxY / 2
      const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2))
      const radius = Math.min(maxX, maxY) / 2 - 2

      if (distance <= radius) {
        // 模拟良率分布 (边缘良率稍低)
        const edgeDistance = distance / radius
        let passRate = 0.95 - edgeDistance * 0.15

        // 添加一些随机性
        passRate += (Math.random() - 0.5) * 0.1
        passRate = Math.max(0, Math.min(1, passRate))

        const result = Math.random() < passRate ? 'pass' : 'fail'

        mapData.push({
          x,
          y,
          result: result as 'pass' | 'fail',
          binCode: result === 'pass' ? '1' : '2',
          testData: {
            VDD_Current: 4.8 + Math.random() * 0.4,
            VOH: 3.2 + Math.random() * 0.2,
            VOL: 0.3 + Math.random() * 0.2
          }
        })
      }
    }
  }

  return mapData
}

/**
 * 生成模拟晶圆图
 */
export function generateMockWaferMap(waferId: string): WaferMap {
  const maxX = 100
  const maxY = 100
  const mapData = generateWaferMapData(maxX, maxY)

  const passDie = mapData.filter(d => d.result === 'pass').length
  const failDie = mapData.filter(d => d.result === 'fail').length
  const yieldValue = (passDie / mapData.length) * 100

  return {
    waferId,
    mapData,
    dimensions: { maxX, maxY },
    binSummary: {
      '1': passDie,
      '2': failDie,
      '0': 0
    },
    yield: yieldValue,
    generatedTime: new Date().toISOString()
  }
}

/**
 * 模拟设备数据
 */
export const mockEquipments: Equipment[] = [
  {
    id: 'cp-tester-01',
    name: 'CP-ATE-001',
    model: 'Advantest T5581',
    station: 'CP-ST01',
    status: 'running',
    lastUpdated: new Date().toISOString(),
    utilization: 95.2,
    oee: 92.1
  },
  {
    id: 'cp-tester-02',
    name: 'CP-ATE-002',
    model: 'Advantest T5581',
    station: 'CP-ST02',
    status: 'idle',
    lastUpdated: new Date().toISOString(),
    utilization: 78.3,
    oee: 85.4
  },
  {
    id: 'die-attach-01',
    name: 'DA-001',
    model: 'ASM Eagle 60',
    station: 'DA-ST01',
    status: 'running',
    lastUpdated: new Date().toISOString(),
    utilization: 92.5,
    oee: 88.3
  },
  {
    id: 'wire-bond-01',
    name: 'WB-001',
    model: 'K&S IConn Pro',
    station: 'WB-ST01',
    status: 'running',
    lastUpdated: new Date().toISOString(),
    utilization: 89.7,
    oee: 85.2
  },
  {
    id: 'molding-01',
    name: 'MD-001',
    model: 'TOWA YPS-D',
    station: 'MD-ST01',
    status: 'idle',
    lastUpdated: new Date().toISOString(),
    utilization: 76.4,
    oee: 82.1
  },
  {
    id: 'ft-tester-01',
    name: 'FT-001',
    model: 'Advantest T2000',
    station: 'FT-ST01',
    status: 'running',
    lastUpdated: new Date().toISOString(),
    utilization: 94.3,
    oee: 91.2
  },
  {
    id: 'ft-tester-02',
    name: 'FT-002',
    model: 'Advantest T2000',
    station: 'FT-ST02',
    status: 'alarm',
    lastUpdated: new Date().toISOString(),
    utilization: 45.6,
    oee: 62.8
  }
]

/**
 * 模拟探针卡数据
 */
export const mockProbeCards: ProbeCard[] = [
  {
    id: 'pc-001',
    cardNumber: 'PC-QFP64-001',
    type: 'Cantilever',
    totalTouches: 875000,
    maxTouches: 1000000,
    lastCleanTime: '2024-01-15T08:00:00Z',
    nextPMTime: '2024-02-01T08:00:00Z',
    status: 'active',
    assignedTester: 'CP-ATE-001'
  },
  {
    id: 'pc-002',
    cardNumber: 'PC-BGA256-001',
    type: 'Vertical',
    totalTouches: 650000,
    maxTouches: 1000000,
    lastCleanTime: '2024-01-20T08:00:00Z',
    nextPMTime: '2024-02-05T08:00:00Z',
    status: 'active',
    assignedTester: 'CP-ATE-002'
  },
  {
    id: 'pc-003',
    cardNumber: 'PC-CSP144-001',
    type: 'MEMS',
    totalTouches: 920000,
    maxTouches: 1000000,
    lastCleanTime: '2024-01-10T08:00:00Z',
    nextPMTime: '2024-01-28T08:00:00Z',
    status: 'maintenance',
    assignedTester: ''
  }
]

/**
 * 模拟测试程序数据
 */
export const mockTestPrograms: TestProgram[] = [
  {
    id: 'tp-001',
    name: 'TP_QFP64_MCU_V1.5',
    version: 'V1.5',
    customerPN: 'ABC123-QFP64',
    testItems: ['DC Parameters', 'AC Parameters', 'Functional Test', 'IDDQ Test'],
    testTime: 2.5,
    temperatureRange: { min: 25, max: 85 },
    isActive: true
  },
  {
    id: 'tp-002',
    name: 'TP_BGA256_FPGA_V2.1',
    version: 'V2.1',
    customerPN: 'XYZ456-BGA256',
    testItems: ['DC Parameters', 'High Speed Test', 'Memory Test', 'Burn-in Test'],
    testTime: 4.8,
    temperatureRange: { min: -40, max: 125 },
    isActive: true
  },
  {
    id: 'tp-003',
    name: 'TP_CSP144_SOC_V1.3',
    version: 'V1.3',
    customerPN: 'DEF789-CSP144',
    testItems: ['DC Parameters', 'RF Test', 'ADC/DAC Test'],
    testTime: 3.2,
    temperatureRange: { min: 0, max: 70 },
    isActive: false
  }
]

/**
 * 模拟分选机数据
 */
export const mockHandlers: HandlerOperation[] = [
  {
    handlerId: 'handler-01',
    handlerName: 'HD-QFP-001',
    throughput: 12000,
    jamCount: 3,
    efficiency: 95.2,
    binMapping: {
      '1': 'Pass - 全参数通过',
      '2': 'Fail - 电参数失效',
      '3': 'Fail - 功能失效',
      '4': 'Fail - 高温失效',
      '0': 'Reject - 废品'
    }
  },
  {
    id: 'handler-02',
    handlerName: 'HD-BGA-001',
    throughput: 8500,
    jamCount: 1,
    efficiency: 97.1,
    binMapping: {
      '1': 'Pass - 全参数通过',
      '2': 'Fail - 电参数失效',
      '3': 'Fail - 高速信号失效',
      '4': 'Fail - 存储器失效',
      '0': 'Reject - 废品'
    }
  },
  {
    handlerId: 'handler-03',
    handlerName: 'HD-CSP-001',
    throughput: 15000,
    jamCount: 5,
    efficiency: 89.8,
    binMapping: {
      '1': 'Pass - 全参数通过',
      '2': 'Fail - RF失效',
      '3': 'Fail - ADC失效',
      '4': 'Fail - DAC失效',
      '0': 'Reject - 废品'
    }
  }
]

/**
 * 生成模拟电测参数
 */
export function generateMockElectricalTests(): ElectricalTest[] {
  return [
    {
      parameterId: 'vdd-current',
      parameterName: 'VDD电流',
      unit: 'mA',
      minLimit: 4.0,
      maxLimit: 6.0,
      measuredValue: 4.98,
      result: 'pass',
      cpk: 1.85
    },
    {
      parameterId: 'voh',
      parameterName: '输出高电平',
      unit: 'V',
      minLimit: 3.1,
      maxLimit: 3.5,
      measuredValue: 3.32,
      result: 'pass',
      cpk: 1.92
    },
    {
      parameterId: 'vol',
      parameterName: '输出低电平',
      unit: 'V',
      minLimit: 0.0,
      maxLimit: 0.5,
      measuredValue: 0.38,
      result: 'pass',
      cpk: 2.15
    },
    {
      parameterId: 'vih',
      parameterName: '输入高电平',
      unit: 'V',
      minLimit: 2.0,
      maxLimit: 5.5,
      measuredValue: 3.8,
      result: 'pass',
      cpk: 3.2
    },
    {
      parameterId: 'vil',
      parameterName: '输入低电平',
      unit: 'V',
      minLimit: -0.3,
      maxLimit: 0.8,
      measuredValue: 0.4,
      result: 'pass',
      cpk: 1.67
    }
  ]
}

/**
 * 生成模拟工艺参数
 */
export function generateMockProcessParameters(): ProcessParameter[] {
  return [
    {
      id: 'cp-temp',
      name: '测试温度',
      unit: '°C',
      targetValue: 25.0,
      tolerance: 2.0,
      currentValue: 25.8,
      minLimit: 20.0,
      maxLimit: 30.0,
      status: 'normal'
    },
    {
      id: 'da-temp',
      name: '贴片温度',
      unit: '°C',
      targetValue: 380.0,
      tolerance: 5.0,
      currentValue: 382.5,
      minLimit: 370.0,
      maxLimit: 390.0,
      status: 'normal'
    },
    {
      id: 'wb-force',
      name: '键合力',
      unit: 'gf',
      targetValue: 65.0,
      tolerance: 3.0,
      currentValue: 67.2,
      minLimit: 60.0,
      maxLimit: 70.0,
      status: 'warning'
    },
    {
      id: 'md-pressure',
      name: '塑封压力',
      unit: 'MPa',
      targetValue: 8.5,
      tolerance: 0.5,
      currentValue: 8.8,
      minLimit: 7.5,
      maxLimit: 9.5,
      status: 'normal'
    },
    {
      id: 'ft-voltage',
      name: '测试电压',
      unit: 'V',
      targetValue: 5.0,
      tolerance: 0.1,
      currentValue: 5.15,
      minLimit: 4.8,
      maxLimit: 5.2,
      status: 'warning'
    }
  ]
}

/**
 * 生成模拟SPC数据
 */
export function generateMockSPCData(parameterId: string): SPCData {
  const baseValue = 380.0 // 贴片温度基准值
  const samples = Array.from({ length: 25 }, () => baseValue + (Math.random() - 0.5) * 8)

  const mean = samples.reduce((a, b) => a + b) / samples.length
  const variance = samples.reduce((a, b) => a + Math.pow(b - mean, 2)) / (samples.length - 1)
  const stdDev = Math.sqrt(variance)

  const usl = 390.0
  const lsl = 370.0
  const cpk = Math.min((usl - mean) / (3 * stdDev), (mean - lsl) / (3 * stdDev))

  return {
    parameterId,
    parameterName: '贴片温度',
    samples,
    mean,
    stdDev,
    cpk,
    ucl: mean + 3 * stdDev,
    lcl: mean - 3 * stdDev,
    usl,
    lsl,
    outOfControl: cpk < 1.0
  }
}

/**
 * 生成模拟SECS消息
 */
export function generateMockSecsMessages(equipmentId: string): SECSMessage[] {
  const messages: SECSMessage[] = []
  const now = new Date()

  // 模拟最近的SECS消息
  for (let i = 0; i < 10; i++) {
    const timestamp = new Date(now.getTime() - i * 30000).toISOString() // 每30秒一条

    messages.push({
      id: `secs-${equipmentId}-${i}`,
      equipmentId,
      stream: Math.floor(Math.random() * 10) + 1,
      function: Math.floor(Math.random() * 20) + 1,
      direction: Math.random() > 0.5 ? 'host_to_equipment' : 'equipment_to_host',
      data: {
        command: ['GO_ONLINE', 'GO_OFFLINE', 'START_TEST', 'END_TEST'][
          Math.floor(Math.random() * 4)
        ],
        value: Math.random() * 100
      },
      timestamp,
      status: ['sent', 'received', 'timeout', 'error'][Math.floor(Math.random() * 4)] as any
    })
  }

  return messages.reverse() // 最新的在前
}

/**
 * 模拟看板数据
 */
export const mockDashboardData: DashboardData = {
  totalEquipment: 12,
  runningEquipment: 8,
  overallOEE: 85.2,
  dailyOutput: 125000,
  cpTesting: {
    activeWafers: 24,
    avgYield: 94.2,
    totalTestedDie: 1250000,
    equipmentUtilization: 87.5
  },
  assembly: {
    activePackages: 18,
    avgCycleTime: 12.5,
    defectRate: 0.0025,
    throughput: 45000
  },
  finalTest: {
    testedUnits: 87500,
    passRate: 96.8,
    avgTestTime: 2.3,
    handlerEfficiency: 92.1
  }
}

/**
 * 生成模拟CP测试记录
 */
export function generateMockCPTestRecords(): CPTestRecord[] {
  return [
    {
      id: 'cp-test-001',
      waferInfo: {
        id: 'wafer-001',
        waferLot: 'LOT2024001',
        waferNumber: 1,
        customerPN: 'ABC123-QFP64',
        productCode: 'MCU001',
        dieSize: { x: 2.5, y: 2.5 },
        totalDie: 8750,
        testedDie: 7820,
        passedDie: 7365,
        yield: 94.2,
        status: 'running',
        startTime: '2024-01-25T08:30:00Z'
      },
      probeCard: mockProbeCards[0],
      tester: mockEquipments[0],
      testProgram: 'TP_QFP64_MCU_V1.5',
      temperature: 25,
      testResults: generateMockElectricalTests(),
      waferMap: generateMockWaferMap('wafer-001'),
      operator: 'OP001',
      startTime: '2024-01-25T08:30:00Z',
      endTime: '',
      duration: 145
    },
    {
      id: 'cp-test-002',
      waferInfo: {
        id: 'wafer-002',
        waferLot: 'LOT2024001',
        waferNumber: 2,
        customerPN: 'XYZ456-BGA256',
        productCode: 'FPGA001',
        dieSize: { x: 3.2, y: 3.2 },
        totalDie: 5280,
        testedDie: 5280,
        passedDie: 4975,
        yield: 94.2,
        status: 'completed',
        startTime: '2024-01-25T06:00:00Z',
        endTime: '2024-01-25T08:15:00Z'
      },
      probeCard: mockProbeCards[1],
      tester: mockEquipments[1],
      testProgram: 'TP_BGA256_FPGA_V2.1',
      temperature: 25,
      testResults: generateMockElectricalTests(),
      waferMap: generateMockWaferMap('wafer-002'),
      operator: 'OP002',
      startTime: '2024-01-25T06:00:00Z',
      endTime: '2024-01-25T08:15:00Z',
      duration: 135
    }
  ]
}

/**
 * 生成模拟封装记录
 */
export function generateMockAssemblyRecords(): AssemblyRecord[] {
  return [
    {
      id: 'asm-001',
      lotNumber: 'ASM2024001',
      packageType: 'QFP',
      customerPN: 'ABC123-QFP64',
      quantity: 50000,
      processedQty: 35000,
      dieAttach: {
        temperature: 380,
        force: 25.5,
        time: 3.2,
        epoxy: 'AG-8020',
        result: 'pass'
      },
      wireBond: {
        wireType: '1mil Au',
        bondForce1: 65,
        bondForce2: 45,
        ultrasonicPower: 85,
        bondTime1: 20,
        bondTime2: 15,
        loopHeight: 50,
        totalWires: 64,
        passedWires: 64,
        result: 'pass'
      },
      molding: {
        compound: 'EME-G770H',
        temperature: 175,
        pressure: 8.5,
        time: 90,
        thickness: 1.2,
        voidRatio: 0.8,
        result: 'pass'
      },
      operator: 'OP003',
      equipment: mockEquipments[2],
      startTime: '2024-01-25T08:00:00Z',
      status: 'running'
    }
  ]
}

/**
 * 生成模拟最终测试记录
 */
export function generateMockFinalTestRecords(): FinalTestRecord[] {
  return [
    {
      id: 'ft-001',
      lotNumber: '*********',
      packageType: 'QFP',
      customerPN: 'ABC123-QFP64',
      testProgram: mockTestPrograms[0],
      testedQty: 45000,
      passedQty: 43560,
      yield: 96.8,
      burnIn: {
        temperature: 125,
        voltage: 5.0,
        duration: 24,
        socketCount: 512,
        failureCount: 23,
        failureRate: 0.051
      },
      handler: mockHandlers[0],
      testResults: generateMockElectricalTests(),
      binSummary: {
        '1': 43560,
        '2': 980,
        '3': 460,
        '0': 0
      },
      operator: 'OP004',
      startTime: '2024-01-25T08:00:00Z',
      endTime: '',
      status: 'running'
    }
  ]
}

// 导出所有Mock数据
export const manufacturingMockData = {
  equipments: mockEquipments,
  probeCards: mockProbeCards,
  testPrograms: mockTestPrograms,
  handlers: mockHandlers,
  dashboardData: mockDashboardData,
  generateWaferMap: generateMockWaferMap,
  generateElectricalTests: generateMockElectricalTests,
  generateProcessParameters: generateMockProcessParameters,
  generateSPCData: generateMockSPCData,
  generateSecsMessages: generateMockSecsMessages,
  generateCPTestRecords: generateMockCPTestRecords,
  generateAssemblyRecords: generateMockAssemblyRecords,
  generateFinalTestRecords: generateMockFinalTestRecords
}
