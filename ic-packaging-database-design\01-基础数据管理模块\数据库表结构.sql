-- ========================================
-- 基础数据管理模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- 组织机构表
CREATE TABLE organizations (
    org_id VARCHAR(32) PRIMARY KEY COMMENT '组织ID',
    org_code VARCHAR(50) NOT NULL UNIQUE COMMENT '组织编码',
    org_name VARCHAR(200) NOT NULL COMMENT '组织名称',
    org_full_name VARCHAR(500) COMMENT '组织全称',
    
    -- 组织层次
    parent_org_id VARCHAR(32) COMMENT '父组织ID',
    org_level INT DEFAULT 1 COMMENT '组织层级',
    org_path VARCHAR(1000) COMMENT '组织路径',
    is_leaf TINYINT(1) DEFAULT 1 COMMENT '是否叶子节点',
    
    -- 组织类型
    org_type VARCHAR(30) NOT NULL COMMENT '组织类型',
    org_category VARCHAR(50) COMMENT '组织分类',
    
    -- 组织信息
    legal_representative VARCHAR(100) COMMENT '法人代表',
    contact_person VARCHAR(100) COMMENT '联系人',
    contact_phone VARCHAR(50) COMMENT '联系电话',
    contact_email VARCHAR(200) COMMENT '联系邮箱',
    
    -- 地址信息
    country VARCHAR(100) DEFAULT 'China' COMMENT '国家',
    province VARCHAR(100) COMMENT '省份',
    city VARCHAR(100) COMMENT '城市',
    district VARCHAR(100) COMMENT '区县',
    address VARCHAR(500) COMMENT '详细地址',
    postal_code VARCHAR(20) COMMENT '邮政编码',
    
    -- 组织属性
    business_license VARCHAR(100) COMMENT '营业执照号',
    tax_id VARCHAR(100) COMMENT '税务登记号',
    industry_type VARCHAR(100) COMMENT '行业类型',
    
    -- 生效信息
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 状态管理
    org_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '组织状态',
    
    -- 排序
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    
    -- 备注
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (parent_org_id) REFERENCES organizations(org_id) ON DELETE SET NULL,
    INDEX idx_org_code (org_code),
    INDEX idx_org_parent (parent_org_id),
    INDEX idx_org_level (org_level),
    INDEX idx_org_type (org_type),
    INDEX idx_org_status (org_status),
    INDEX idx_org_sort (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组织机构表';

-- 部门表
CREATE TABLE departments (
    dept_id VARCHAR(32) PRIMARY KEY COMMENT '部门ID',
    dept_code VARCHAR(50) NOT NULL COMMENT '部门编码',
    dept_name VARCHAR(200) NOT NULL COMMENT '部门名称',
    dept_full_name VARCHAR(500) COMMENT '部门全称',
    
    -- 所属组织
    org_id VARCHAR(32) NOT NULL COMMENT '所属组织ID',
    
    -- 部门层次
    parent_dept_id VARCHAR(32) COMMENT '父部门ID',
    dept_level INT DEFAULT 1 COMMENT '部门层级',
    dept_path VARCHAR(1000) COMMENT '部门路径',
    is_leaf TINYINT(1) DEFAULT 1 COMMENT '是否叶子节点',
    
    -- 部门类型
    dept_type VARCHAR(30) NOT NULL COMMENT '部门类型',
    dept_category VARCHAR(50) COMMENT '部门分类',
    functional_area VARCHAR(100) COMMENT '职能领域',
    
    -- 部门负责人
    manager_id VARCHAR(32) COMMENT '部门经理ID',
    deputy_manager_id VARCHAR(32) COMMENT '副经理ID',
    
    -- 联系信息
    contact_phone VARCHAR(50) COMMENT '部门电话',
    contact_email VARCHAR(200) COMMENT '部门邮箱',
    office_location VARCHAR(200) COMMENT '办公地点',
    
    -- 成本中心
    cost_center VARCHAR(50) COMMENT '成本中心编码',
    
    -- 生效信息
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 状态管理
    dept_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '部门状态',
    
    -- 排序
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    
    -- 备注
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (org_id) REFERENCES organizations(org_id) ON DELETE CASCADE,
    FOREIGN KEY (parent_dept_id) REFERENCES departments(dept_id) ON DELETE SET NULL,
    INDEX idx_dept_code (dept_code),
    INDEX idx_dept_org (org_id),
    INDEX idx_dept_parent (parent_dept_id),
    INDEX idx_dept_level (dept_level),
    INDEX idx_dept_type (dept_type),
    INDEX idx_dept_manager (manager_id),
    INDEX idx_dept_status (dept_status),
    UNIQUE KEY uk_org_dept_code (org_id, dept_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 岗位表
CREATE TABLE positions (
    position_id VARCHAR(32) PRIMARY KEY COMMENT '岗位ID',
    position_code VARCHAR(50) NOT NULL COMMENT '岗位编码',
    position_name VARCHAR(200) NOT NULL COMMENT '岗位名称',
    
    -- 所属部门
    dept_id VARCHAR(32) NOT NULL COMMENT '所属部门ID',
    
    -- 岗位类型
    position_type VARCHAR(30) NOT NULL COMMENT '岗位类型',
    position_category VARCHAR(50) COMMENT '岗位分类',
    job_family VARCHAR(100) COMMENT '岗位族',
    job_level VARCHAR(20) COMMENT '岗位级别',
    
    -- 岗位职责
    job_responsibilities TEXT COMMENT '岗位职责',
    job_requirements TEXT COMMENT '任职要求',
    skill_requirements TEXT COMMENT '技能要求',
    
    -- 汇报关系
    supervisor_position_id VARCHAR(32) COMMENT '上级岗位ID',
    subordinate_positions JSON COMMENT '下属岗位列表',
    
    -- 编制信息
    headcount_planned INT DEFAULT 1 COMMENT '计划编制',
    headcount_actual INT DEFAULT 0 COMMENT '实际人数',
    
    -- 薪酬信息
    salary_grade VARCHAR(20) COMMENT '薪酬等级',
    salary_range_min DECIMAL(12,2) COMMENT '薪酬范围下限',
    salary_range_max DECIMAL(12,2) COMMENT '薪酬范围上限',
    
    -- 工作性质
    employment_type VARCHAR(20) DEFAULT 'FULL_TIME' COMMENT '雇佣类型',
    work_location VARCHAR(200) COMMENT '工作地点',
    travel_required TINYINT(1) DEFAULT 0 COMMENT '是否需要出差',
    
    -- 生效信息
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 状态管理
    position_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '岗位状态',
    
    -- 排序
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    
    -- 备注
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (dept_id) REFERENCES departments(dept_id) ON DELETE CASCADE,
    FOREIGN KEY (supervisor_position_id) REFERENCES positions(position_id) ON DELETE SET NULL,
    INDEX idx_position_code (position_code),
    INDEX idx_position_dept (dept_id),
    INDEX idx_position_type (position_type),
    INDEX idx_position_supervisor (supervisor_position_id),
    INDEX idx_position_status (position_status),
    UNIQUE KEY uk_dept_position_code (dept_id, position_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位表';

-- 员工表
CREATE TABLE employees (
    employee_id VARCHAR(32) PRIMARY KEY COMMENT '员工ID',
    employee_code VARCHAR(50) NOT NULL UNIQUE COMMENT '员工工号',
    
    -- 基本信息
    chinese_name VARCHAR(100) NOT NULL COMMENT '中文姓名',
    english_name VARCHAR(100) COMMENT '英文姓名',
    gender VARCHAR(10) COMMENT '性别',
    birth_date DATE COMMENT '出生日期',
    nationality VARCHAR(50) DEFAULT 'China' COMMENT '国籍',
    
    -- 证件信息
    id_card_type VARCHAR(30) DEFAULT 'ID_CARD' COMMENT '证件类型',
    id_card_number VARCHAR(50) COMMENT '证件号码',
    passport_number VARCHAR(50) COMMENT '护照号码',
    
    -- 联系信息
    mobile_phone VARCHAR(30) COMMENT '手机号码',
    home_phone VARCHAR(30) COMMENT '家庭电话',
    email VARCHAR(200) COMMENT '邮箱地址',
    emergency_contact VARCHAR(100) COMMENT '紧急联系人',
    emergency_phone VARCHAR(30) COMMENT '紧急联系电话',
    
    -- 地址信息
    home_address VARCHAR(500) COMMENT '家庭地址',
    current_address VARCHAR(500) COMMENT '现居地址',
    
    -- 组织关系
    org_id VARCHAR(32) NOT NULL COMMENT '所属组织ID',
    dept_id VARCHAR(32) NOT NULL COMMENT '所属部门ID',
    position_id VARCHAR(32) COMMENT '岗位ID',
    
    -- 汇报关系
    supervisor_id VARCHAR(32) COMMENT '直属上级ID',
    
    -- 雇佣信息
    employee_type VARCHAR(30) NOT NULL COMMENT '员工类型',
    employment_status VARCHAR(30) NOT NULL COMMENT '雇佣状态',
    hire_date DATE NOT NULL COMMENT '入职日期',
    contract_start_date DATE COMMENT '合同开始日期',
    contract_end_date DATE COMMENT '合同结束日期',
    probation_end_date DATE COMMENT '试用期结束日期',
    
    -- 工作信息
    work_location VARCHAR(200) COMMENT '工作地点',
    job_title VARCHAR(200) COMMENT '职位名称',
    job_grade VARCHAR(20) COMMENT '职级',
    seniority_level VARCHAR(20) COMMENT '资历等级',
    
    -- 薪酬信息
    base_salary DECIMAL(12,2) COMMENT '基本工资',
    salary_currency VARCHAR(10) DEFAULT 'CNY' COMMENT '薪酬币种',
    pay_frequency VARCHAR(20) DEFAULT 'MONTHLY' COMMENT '发薪频率',
    
    -- 技能证书
    skills JSON COMMENT '技能列表',
    certifications JSON COMMENT '证书列表',
    training_records JSON COMMENT '培训记录',
    
    -- 教育背景
    education_level VARCHAR(30) COMMENT '最高学历',
    major VARCHAR(200) COMMENT '专业',
    graduation_school VARCHAR(200) COMMENT '毕业院校',
    graduation_year INT COMMENT '毕业年份',
    
    -- 工作经历
    work_experience_years DECIMAL(4,1) COMMENT '工作经验年数',
    previous_companies JSON COMMENT '前工作单位',
    
    -- 绩效信息
    last_performance_rating VARCHAR(20) COMMENT '最近绩效评级',
    last_review_date DATE COMMENT '最近评估日期',
    next_review_date DATE COMMENT '下次评估日期',
    
    -- 离职信息
    termination_date DATE COMMENT '离职日期',
    termination_reason VARCHAR(200) COMMENT '离职原因',
    termination_type VARCHAR(30) COMMENT '离职类型',
    
    -- 账户信息
    user_account VARCHAR(100) COMMENT '用户账号',
    is_system_user TINYINT(1) DEFAULT 0 COMMENT '是否系统用户',
    
    -- 状态管理
    employee_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '员工状态',
    
    -- 备注
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (org_id) REFERENCES organizations(org_id) ON DELETE RESTRICT,
    FOREIGN KEY (dept_id) REFERENCES departments(dept_id) ON DELETE RESTRICT,
    FOREIGN KEY (position_id) REFERENCES positions(position_id) ON DELETE SET NULL,
    FOREIGN KEY (supervisor_id) REFERENCES employees(employee_id) ON DELETE SET NULL,
    INDEX idx_employee_code (employee_code),
    INDEX idx_employee_name (chinese_name),
    INDEX idx_employee_org (org_id),
    INDEX idx_employee_dept (dept_id),
    INDEX idx_employee_position (position_id),
    INDEX idx_employee_supervisor (supervisor_id),
    INDEX idx_employee_status (employee_status),
    INDEX idx_employee_type (employee_type),
    INDEX idx_employee_hire_date (hire_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工表';

-- 客户表
CREATE TABLE customers (
    customer_id VARCHAR(32) PRIMARY KEY COMMENT '客户ID',
    customer_code VARCHAR(50) NOT NULL UNIQUE COMMENT '客户编码',
    customer_name VARCHAR(200) NOT NULL COMMENT '客户名称',
    customer_full_name VARCHAR(500) COMMENT '客户全称',
    customer_english_name VARCHAR(500) COMMENT '客户英文名称',
    
    -- 客户类型
    customer_type VARCHAR(30) NOT NULL COMMENT '客户类型',
    customer_category VARCHAR(50) COMMENT '客户分类',
    industry_type VARCHAR(100) COMMENT '行业类型',
    customer_level VARCHAR(20) DEFAULT 'REGULAR' COMMENT '客户等级',
    
    -- 客户规模
    company_size VARCHAR(30) COMMENT '公司规模',
    annual_revenue DECIMAL(18,2) COMMENT '年营业额',
    employee_count INT COMMENT '员工数量',
    
    -- 基本信息
    legal_representative VARCHAR(100) COMMENT '法人代表',
    business_license VARCHAR(100) COMMENT '营业执照号',
    tax_id VARCHAR(100) COMMENT '税务登记号',
    
    -- 联系信息
    contact_phone VARCHAR(50) COMMENT '联系电话',
    contact_fax VARCHAR(50) COMMENT '传真号码',
    contact_email VARCHAR(200) COMMENT '联系邮箱',
    website VARCHAR(200) COMMENT '官方网站',
    
    -- 地址信息
    country VARCHAR(100) DEFAULT 'China' COMMENT '国家',
    province VARCHAR(100) COMMENT '省份',
    city VARCHAR(100) COMMENT '城市',
    district VARCHAR(100) COMMENT '区县',
    address VARCHAR(500) COMMENT '详细地址',
    postal_code VARCHAR(20) COMMENT '邮政编码',
    
    -- 财务信息
    payment_terms VARCHAR(50) COMMENT '付款条件',
    credit_limit DECIMAL(18,2) COMMENT '信用额度',
    currency_code VARCHAR(10) DEFAULT 'CNY' COMMENT '币种',
    
    -- 销售信息
    sales_rep_id VARCHAR(32) COMMENT '销售代表ID',
    account_manager_id VARCHAR(32) COMMENT '客户经理ID',
    
    -- 合作信息
    cooperation_start_date DATE COMMENT '合作开始日期',
    last_order_date DATE COMMENT '最近订单日期',
    total_order_amount DECIMAL(18,2) DEFAULT 0 COMMENT '累计订单金额',
    
    -- 质量要求
    quality_standards JSON COMMENT '质量标准要求',
    certification_requirements JSON COMMENT '认证要求',
    
    -- 客户状态
    customer_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '客户状态',
    
    -- 客户来源
    lead_source VARCHAR(50) COMMENT '客户来源',
    referral_source VARCHAR(200) COMMENT '推荐来源',
    
    -- 备注
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_customer_code (customer_code),
    INDEX idx_customer_name (customer_name),
    INDEX idx_customer_type (customer_type),
    INDEX idx_customer_level (customer_level),
    INDEX idx_customer_status (customer_status),
    INDEX idx_customer_sales_rep (sales_rep_id),
    INDEX idx_customer_account_manager (account_manager_id),
    INDEX idx_customer_cooperation (cooperation_start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户表';

-- 客户联系人表
CREATE TABLE customer_contacts (
    contact_id VARCHAR(32) PRIMARY KEY COMMENT '联系人ID',
    customer_id VARCHAR(32) NOT NULL COMMENT '客户ID',
    
    -- 基本信息
    contact_name VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    english_name VARCHAR(100) COMMENT '英文姓名',
    gender VARCHAR(10) COMMENT '性别',
    
    -- 职务信息
    job_title VARCHAR(200) COMMENT '职位',
    department VARCHAR(200) COMMENT '部门',
    is_primary_contact TINYINT(1) DEFAULT 0 COMMENT '是否主要联系人',
    is_decision_maker TINYINT(1) DEFAULT 0 COMMENT '是否决策人',
    
    -- 联系方式
    mobile_phone VARCHAR(30) COMMENT '手机号码',
    office_phone VARCHAR(30) COMMENT '办公电话',
    extension VARCHAR(20) COMMENT '分机号',
    email VARCHAR(200) COMMENT '邮箱地址',
    wechat VARCHAR(100) COMMENT '微信号',
    qq VARCHAR(50) COMMENT 'QQ号',
    
    -- 地址信息
    office_address VARCHAR(500) COMMENT '办公地址',
    
    -- 业务关系
    business_scope VARCHAR(200) COMMENT '业务范围',
    authority_level VARCHAR(50) COMMENT '权限级别',
    
    -- 偏好设置
    preferred_contact_method VARCHAR(30) COMMENT '偏好联系方式',
    preferred_contact_time VARCHAR(100) COMMENT '偏好联系时间',
    communication_language VARCHAR(20) DEFAULT 'Chinese' COMMENT '沟通语言',
    
    -- 联系人状态
    contact_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '联系人状态',
    
    -- 备注
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE,
    INDEX idx_contact_customer (customer_id),
    INDEX idx_contact_name (contact_name),
    INDEX idx_contact_primary (is_primary_contact),
    INDEX idx_contact_decision_maker (is_decision_maker),
    INDEX idx_contact_status (contact_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户联系人表';

-- 供应商表
CREATE TABLE suppliers (
    supplier_id VARCHAR(32) PRIMARY KEY COMMENT '供应商ID',
    supplier_code VARCHAR(50) NOT NULL UNIQUE COMMENT '供应商编码',
    supplier_name VARCHAR(200) NOT NULL COMMENT '供应商名称',
    supplier_full_name VARCHAR(500) COMMENT '供应商全称',
    supplier_english_name VARCHAR(500) COMMENT '供应商英文名称',
    
    -- 供应商类型
    supplier_type VARCHAR(30) NOT NULL COMMENT '供应商类型',
    supplier_category VARCHAR(50) COMMENT '供应商分类',
    business_type VARCHAR(100) COMMENT '经营类型',
    supplier_level VARCHAR(20) DEFAULT 'REGULAR' COMMENT '供应商等级',
    
    -- 供应范围
    supply_categories JSON COMMENT '供应类别',
    main_products TEXT COMMENT '主要产品',
    
    -- 基本信息
    legal_representative VARCHAR(100) COMMENT '法人代表',
    business_license VARCHAR(100) COMMENT '营业执照号',
    tax_id VARCHAR(100) COMMENT '税务登记号',
    registration_date DATE COMMENT '注册日期',
    
    -- 规模信息
    company_size VARCHAR(30) COMMENT '公司规模',
    registered_capital DECIMAL(18,2) COMMENT '注册资本',
    annual_revenue DECIMAL(18,2) COMMENT '年营业额',
    employee_count INT COMMENT '员工数量',
    
    -- 联系信息
    contact_person VARCHAR(100) COMMENT '联系人',
    contact_phone VARCHAR(50) COMMENT '联系电话',
    contact_fax VARCHAR(50) COMMENT '传真号码',
    contact_email VARCHAR(200) COMMENT '联系邮箱',
    website VARCHAR(200) COMMENT '官方网站',
    
    -- 地址信息
    country VARCHAR(100) DEFAULT 'China' COMMENT '国家',
    province VARCHAR(100) COMMENT '省份',
    city VARCHAR(100) COMMENT '城市',
    district VARCHAR(100) COMMENT '区县',
    address VARCHAR(500) COMMENT '详细地址',
    postal_code VARCHAR(20) COMMENT '邮政编码',
    
    -- 财务信息
    payment_terms VARCHAR(50) COMMENT '付款条件',
    currency_code VARCHAR(10) DEFAULT 'CNY' COMMENT '币种',
    bank_name VARCHAR(200) COMMENT '开户银行',
    bank_account VARCHAR(100) COMMENT '银行账号',
    
    -- 质量信息
    quality_certifications JSON COMMENT '质量认证',
    quality_rating VARCHAR(20) COMMENT '质量评级',
    
    -- 合作信息
    cooperation_start_date DATE COMMENT '合作开始日期',
    last_purchase_date DATE COMMENT '最近采购日期',
    total_purchase_amount DECIMAL(18,2) DEFAULT 0 COMMENT '累计采购金额',
    
    -- 采购信息
    procurement_rep_id VARCHAR(32) COMMENT '采购代表ID',
    
    -- 供应商状态
    supplier_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '供应商状态',
    
    -- 供应商来源
    lead_source VARCHAR(50) COMMENT '供应商来源',
    
    -- 备注
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_supplier_name (supplier_name),
    INDEX idx_supplier_type (supplier_type),
    INDEX idx_supplier_level (supplier_level),
    INDEX idx_supplier_status (supplier_status),
    INDEX idx_supplier_procurement_rep (procurement_rep_id),
    INDEX idx_supplier_cooperation (cooperation_start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商表';

-- 产品主数据表
CREATE TABLE products (
    product_id VARCHAR(32) PRIMARY KEY COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    product_full_name VARCHAR(500) COMMENT '产品全称',
    product_english_name VARCHAR(500) COMMENT '产品英文名称',
    
    -- 产品分类
    product_type VARCHAR(50) NOT NULL COMMENT '产品类型',
    product_category VARCHAR(50) NOT NULL COMMENT '产品分类',
    product_family VARCHAR(100) COMMENT '产品族',
    product_series VARCHAR(100) COMMENT '产品系列',
    
    -- 产品规格
    specifications JSON COMMENT '产品规格',
    model_number VARCHAR(100) COMMENT '型号',
    
    -- 封装信息(针对IC产品)
    package_type VARCHAR(50) COMMENT '封装类型',
    pin_count INT COMMENT '引脚数量',
    package_size VARCHAR(50) COMMENT '封装尺寸',
    die_size VARCHAR(50) COMMENT 'Die尺寸',
    
    -- 应用领域
    application_areas JSON COMMENT '应用领域',
    target_market VARCHAR(100) COMMENT '目标市场',
    
    -- 客户信息
    customer_id VARCHAR(32) COMMENT '客户ID',
    customer_part_number VARCHAR(100) COMMENT '客户料号',
    
    -- 产品状态
    lifecycle_stage VARCHAR(30) NOT NULL COMMENT '生命周期阶段',
    product_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '产品状态',
    
    -- 开发信息
    development_stage VARCHAR(30) COMMENT '开发阶段',
    npi_project_id VARCHAR(32) COMMENT 'NPI项目ID',
    
    -- 质量要求
    quality_grade VARCHAR(30) COMMENT '质量等级',
    reliability_requirements JSON COMMENT '可靠性要求',
    
    -- 成本信息
    target_cost DECIMAL(15,6) COMMENT '目标成本',
    standard_cost DECIMAL(15,6) COMMENT '标准成本',
    
    -- 版本信息
    product_version VARCHAR(20) DEFAULT '1.0' COMMENT '产品版本',
    
    -- 时间信息
    introduction_date DATE COMMENT '导入日期',
    planned_eol_date DATE COMMENT '计划停产日期',
    actual_eol_date DATE COMMENT '实际停产日期',
    
    -- 备注
    product_description TEXT COMMENT '产品描述',
    remark TEXT COMMENT '备注',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE SET NULL,
    INDEX idx_product_code (product_code),
    INDEX idx_product_name (product_name),
    INDEX idx_product_type (product_type),
    INDEX idx_product_category (product_category),
    INDEX idx_product_customer (customer_id),
    INDEX idx_product_customer_part (customer_part_number),
    INDEX idx_product_lifecycle (lifecycle_stage),
    INDEX idx_product_status (product_status),
    INDEX idx_product_package (package_type),
    INDEX idx_product_npi (npi_project_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品主数据表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. organizations: 组织机构表，支持多级组织结构
2. departments: 部门表，隶属于组织的部门结构
3. positions: 岗位表，部门下的具体岗位定义
4. employees: 员工表，员工基本信息和组织关系
5. customers: 客户表，客户基本信息管理
6. customer_contacts: 客户联系人表，客户的具体联系人
7. suppliers: 供应商表，供应商基本信息管理
8. products: 产品主数据表，产品基础信息管理

核心特性:
- 完整的组织架构体系(组织-部门-岗位-员工)
- 全面的客户关系管理
- 系统的供应商管理
- 标准化的产品主数据管理
- 支持多级层次结构
- 完整的审计跟踪
- 灵活的状态管理
- IC封测行业专业特性支持
*/