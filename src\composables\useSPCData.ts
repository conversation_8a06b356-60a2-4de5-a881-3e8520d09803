// SPC数据管理组合式函数
import { ref, reactive } from 'vue'
import type { SPCData, SPCPoint, ControlLimits, SPCStatistics } from '@/types/quality'
import { spcDataList as mockSPCData, violationRules } from '@/utils/mockData/quality'
import { SPCCalculator } from '@/utils/spcCalculator'

export function useSPCData() {
  const spcDataList = ref<SPCData[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 初始化数据
  const initializeSPCData = async () => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      spcDataList.value = mockSPCData
    } catch (err) {
      error.value = '加载SPC数据失败'
      console.error('SPC数据加载错误:', err)
    } finally {
      loading.value = false
    }
  }

  // 刷新所有SPC数据
  const refreshAllSPCData = async () => {
    loading.value = true

    try {
      await new Promise(resolve => setTimeout(resolve, 800))

      // 模拟数据更新 - 为每个SPC项目添加新的数据点
      spcDataList.value.forEach(spcItem => {
        addRandomSPCPoint(spcItem)
      })

      // 重新计算控制限和统计量
      spcDataList.value.forEach(spcItem => {
        recalculateControlLimits(spcItem)
      })
    } catch (err) {
      error.value = '刷新SPC数据失败'
      console.error('SPC数据刷新错误:', err)
    } finally {
      loading.value = false
    }
  }

  // 刷新指定SPC数据
  const refreshSPCData = async (id?: string) => {
    if (id) {
      const spcItem = spcDataList.value.find(item => item.id === id)
      if (spcItem) {
        addRandomSPCPoint(spcItem)
        recalculateControlLimits(spcItem)
      }
    } else {
      await refreshAllSPCData()
    }
  }

  // 添加随机SPC数据点（模拟实时数据）
  const addRandomSPCPoint = (spcItem: SPCData) => {
    const lastPoint = spcItem.sampleData[spcItem.sampleData.length - 1]
    const baseValue = getParameterBaseValue(spcItem.parameter)
    const variation = getParameterVariation(spcItem.parameter)

    // 生成新的子组数据
    const values = Array.from({ length: 5 }, () => baseValue + (Math.random() - 0.5) * variation)

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const range = Math.max(...values) - Math.min(...values)
    const variance =
      values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1)
    const standardDeviation = Math.sqrt(variance)

    // 检查违规规则
    const violatedRules = checkViolationRules(spcItem, mean, range)

    // 确定结果状态
    let result: 'NORMAL' | 'WARNING' | 'OUT_OF_CONTROL' = 'NORMAL'
    if (violatedRules.some(rule => rule.includes('NELSON_1'))) {
      result = 'OUT_OF_CONTROL'
    } else if (violatedRules.length > 0) {
      result = 'WARNING'
    }

    const newPoint: SPCPoint = {
      id: `spc_point_${Date.now()}`,
      timestamp: new Date(),
      sampleNumber: lastPoint.sampleNumber + 1,
      values,
      mean,
      range,
      standardDeviation,
      result,
      violatedRules
    }

    // 保持最新的100个点
    spcItem.sampleData.push(newPoint)
    if (spcItem.sampleData.length > 100) {
      spcItem.sampleData = spcItem.sampleData.slice(-100)
    }

    spcItem.lastUpdate = new Date()
  }

  // 获取参数基准值
  const getParameterBaseValue = (parameter: string): number => {
    switch (parameter) {
      case '阈值电压(Vth)':
        return 0.75
      case '漏电流(nA)':
        return 5.2
      case '贴片厚度(μm)':
        return 25.0
      case '键合拉力(gf)':
        return 12.5
      case '塑封厚度(μm)':
        return 850
      case '工作电流(mA)':
        return 125
      default:
        return 50
    }
  }

  // 获取参数变化范围
  const getParameterVariation = (parameter: string): number => {
    switch (parameter) {
      case '阈值电压(Vth)':
        return 0.1
      case '漏电流(nA)':
        return 1.0
      case '贴片厚度(μm)':
        return 2.0
      case '键合拉力(gf)':
        return 1.5
      case '塑封厚度(μm)':
        return 30
      case '工作电流(mA)':
        return 8
      default:
        return 10
    }
  }

  // 检查违规规则
  const checkViolationRules = (
    spcItem: SPCData,
    currentMean: number,
    currentRange: number
  ): string[] => {
    const violations: string[] = []
    const recentPoints = spcItem.sampleData.slice(-15) // 最近15个点用于规则检查

    // Nelson规则1: 单点超出3σ控制限
    if (
      currentMean > spcItem.controlLimits.xbar.ucl ||
      currentMean < spcItem.controlLimits.xbar.lcl
    ) {
      violations.push('NELSON_1: 超出3σ控制限')
    }

    // Nelson规则2: 连续9点在中心线同一侧
    if (recentPoints.length >= 9) {
      const last9 = recentPoints.slice(-9)
      const centerLine = spcItem.controlLimits.xbar.cl
      const allAbove = last9.every(point => point.mean > centerLine)
      const allBelow = last9.every(point => point.mean < centerLine)

      if (allAbove || allBelow) {
        violations.push('NELSON_2: 连续9点在中心线同一侧')
      }
    }

    // Nelson规则3: 连续6点持续上升或下降
    if (recentPoints.length >= 6) {
      const last6 = recentPoints.slice(-6)
      let increasing = true
      let decreasing = true

      for (let i = 1; i < last6.length; i++) {
        if (last6[i].mean <= last6[i - 1].mean) increasing = false
        if (last6[i].mean >= last6[i - 1].mean) decreasing = false
      }

      if (increasing || decreasing) {
        violations.push('NELSON_3: 连续6点持续趋势')
      }
    }

    return violations
  }

  // 重新计算控制限和统计量
  const recalculateControlLimits = (spcItem: SPCData) => {
    const calculator = new SPCCalculator()

    // 计算控制限
    spcItem.controlLimits = calculator.calculateControlLimits(spcItem.sampleData)

    // 计算统计量
    const usl = getParameterUSL(spcItem.parameter)
    const lsl = getParameterLSL(spcItem.parameter)
    const target = getParameterTarget(spcItem.parameter)

    spcItem.statistics = calculator.calculateStatistics(spcItem.sampleData, usl, lsl, target)
  }

  // 获取参数规格限
  const getParameterUSL = (parameter: string): number => {
    switch (parameter) {
      case '阈值电压(Vth)':
        return 0.85
      case '漏电流(nA)':
        return 10.0
      case '贴片厚度(μm)':
        return 30
      case '键合拉力(gf)':
        return 18
      case '塑封厚度(μm)':
        return 900
      case '工作电流(mA)':
        return 140
      default:
        return 100
    }
  }

  const getParameterLSL = (parameter: string): number => {
    switch (parameter) {
      case '阈值电压(Vth)':
        return 0.65
      case '漏电流(nA)':
        return 0
      case '贴片厚度(μm)':
        return 20
      case '键合拉力(gf)':
        return 8
      case '塑封厚度(μm)':
        return 800
      case '工作电流(mA)':
        return 110
      default:
        return 0
    }
  }

  const getParameterTarget = (parameter: string): number => {
    switch (parameter) {
      case '阈值电压(Vth)':
        return 0.75
      case '漏电流(nA)':
        return 5.0
      case '贴片厚度(μm)':
        return 25
      case '键合拉力(gf)':
        return 12.5
      case '塑封厚度(μm)':
        return 850
      case '工作电流(mA)':
        return 125
      default:
        return 50
    }
  }

  // 添加新的SPC监控项
  const addSPCItem = async (newItem: {
    processName: string
    parameter: string
    chartType: string
    subgroupSize: number
    usl: number
    lsl: number
    target: number
  }) => {
    loading.value = true

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const spcData: SPCData = {
        id: `spc_${Date.now()}`,
        processName: newItem.processName,
        parameter: newItem.parameter,
        sampleData: [],
        controlLimits: {
          xbar: { ucl: 0, cl: 0, lcl: 0 },
          r: { ucl: 0, cl: 0, lcl: 0 },
          sigma: { ucl: 3, cl: 0, lcl: -3 }
        },
        statistics: {
          cpk: 0,
          ppk: 0,
          cp: 0,
          pp: 0,
          ca: 0,
          mean: 0,
          standardDeviation: 0,
          yield: 0
        },
        violationRules: violationRules,
        lastUpdate: new Date()
      }

      // 生成初始数据点
      for (let i = 0; i < 20; i++) {
        addRandomSPCPoint(spcData)
      }

      // 计算控制限和统计量
      recalculateControlLimits(spcData)

      spcDataList.value.push(spcData)
    } catch (err) {
      error.value = '添加SPC监控项失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新SPC监控项
  const updateSPCItem = async (id: string, updates: Partial<SPCData>) => {
    loading.value = true

    try {
      await new Promise(resolve => setTimeout(resolve, 500))

      const index = spcDataList.value.findIndex(item => item.id === id)
      if (index !== -1) {
        spcDataList.value[index] = { ...spcDataList.value[index], ...updates }
      }
    } catch (err) {
      error.value = '更新SPC监控项失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除SPC监控项
  const deleteSPCItem = async (id: string) => {
    loading.value = true

    try {
      await new Promise(resolve => setTimeout(resolve, 500))

      const index = spcDataList.value.findIndex(item => item.id === id)
      if (index !== -1) {
        spcDataList.value.splice(index, 1)
      }
    } catch (err) {
      error.value = '删除SPC监控项失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 导出SPC数据
  const exportSPCData = (id: string, format: 'excel' | 'pdf' | 'image' = 'excel') => {
    const spcItem = spcDataList.value.find(item => item.id === id)
    if (!spcItem) return

    // 这里实现具体的导出逻辑
    console.log(`导出SPC数据: ${spcItem.processName} - ${spcItem.parameter}, 格式: ${format}`)
  }

  // 获取SPC统计摘要
  const getSPCStatisticsSummary = () => {
    const total = spcDataList.value.length
    const normal = spcDataList.value.filter(item =>
      item.sampleData.every(point => point.result === 'NORMAL')
    ).length
    const warning = spcDataList.value.filter(
      item =>
        item.sampleData.some(point => point.result === 'WARNING') &&
        !item.sampleData.some(point => point.result === 'OUT_OF_CONTROL')
    ).length
    const outOfControl = spcDataList.value.filter(item =>
      item.sampleData.some(point => point.result === 'OUT_OF_CONTROL')
    ).length

    const avgCpk =
      total > 0 ? spcDataList.value.reduce((sum, item) => sum + item.statistics.cpk, 0) / total : 0

    return {
      total,
      normal,
      warning,
      outOfControl,
      avgCpk
    }
  }

  // 初始化数据
  initializeSPCData()

  return {
    spcDataList,
    loading,
    error,
    refreshSPCData,
    addSPCItem,
    updateSPCItem,
    deleteSPCItem,
    exportSPCData,
    getSPCStatisticsSummary,
    refreshAllSPCData
  }
}
