# 制造执行管理模块设计

## 1. 模块概述

### 1.1 模块定位
制造执行管理模块是IC封测CIM系统的核心执行模块，专门管理从Wafer Probe Testing (CP)到Final Test (FT)的完整IC封测制造执行过程。该模块是连接生产计划与实际制造的关键桥梁，确保生产过程的可控性、可追溯性和高效性。

### 1.2 IC封测制造执行特点
- **三段式制造流程**：CP测试 → Assembly封装 → FT测试的标准流程
- **设备密集型**：高度依赖ATE、Prober、Assembly设备的协同作业
- **数据密集型**：每日产生数百万测试数据点和工艺参数记录
- **质量严控**：每个工序都有严格的质量检查和数据记录要求
- **批次追溯**：从Wafer Lot到最终产品的完整批次追溯
- **实时调度**：基于设备状态和优先级的实时生产调度

### 1.3 核心业务价值
- **生产透明化**：实时掌握生产进度、设备状态、质量状况
- **效率最大化**：通过智能调度和设备协同，提升OEE 15-20%
- **质量可控**：全过程SPC控制，确保产品质量稳定性
- **成本优化**：减少WIP库存，降低制造成本10-15%
- **快速响应**：缩短生产周期，提高客户满意度

### 1.4 应用场景覆盖
```
IC封测制造执行管理应用场景
├── CP (Wafer Probe Testing) 执行管理
│   ├── 晶圆电测工单管理
│   ├── Prober设备调度
│   ├── Probe Card管理
│   ├── 测试数据采集和分析
│   ├── Wafer Map生成
│   ├── 良率统计分析
│   └── 不良die标记和报废
├── Assembly (封装) 执行管理
│   ├── 封装工单管理
│   ├── Die Attach执行控制
│   ├── Wire Bond执行管理
│   ├── Molding塑封执行
│   ├── Deflash/Trim&Form执行
│   ├── Marking标记执行
│   ├── 工艺参数实时监控
│   └── 在制品流转控制
├── FT (Final Test) 执行管理
│   ├── 成品测试工单管理
│   ├── ATE设备调度
│   ├── Test Socket管理
│   ├── 测试程序加载
│   ├── 测试数据采集
│   ├── 分选包装执行
│   └── 出货检验管理
├── 生产调度优化
│   ├── 基于约束理论的生产调度
│   ├── 设备产能平衡
│   ├── 优先级动态调整
│   ├── 瓶颈工序识别和优化
│   └── 异常处理和应急调度
├── 质量过程控制
│   ├── SPC实时监控
│   ├── 质量异常报警
│   ├── 不良品追溯分析
│   ├── 质量成本统计
│   └── IATF16949合规记录
├── 数据采集与分析
│   ├── SECS/GEM设备数据采集
│   ├── 工艺参数实时监控
│   ├── 环境参数记录
│   ├── 能耗数据统计
│   └── OEE计算和分析
└── 追溯与报告
    ├── 批次全程追溯
    ├── 客户质量报告
    ├── 生产日报生成
    ├── 设备使用报告
    └── 成本核算支撑
```

## 2. IC封测制造执行专业架构设计

### 2.1 技术架构
```
制造执行管理模块架构
├── 工单执行引擎          # CP/Assembly/FT工单统一管理
├── 生产调度引擎          # 智能生产调度和资源优化
├── 设备集成平台          # SECS/GEM设备通信和控制
├── 质量控制引擎          # SPC实时监控和质量管理
├── 数据采集引擎          # 多源数据实时采集和处理
├── 追溯管理引擎          # 全程批次追溯和历史查询
├── 报告生成引擎          # 自动化报告生成和发布
├── 异常处理引擎          # 生产异常识别和处理
├── 移动作业平台          # 现场移动端作业支持
└── BI分析引擎           # 生产数据分析和决策支持
```

### 2.2 核心数据模型

#### 2.2.1 工单执行管理
```sql
-- 制造工单表
CREATE TABLE ic_manufacturing_orders (
    mo_id VARCHAR(30) PRIMARY KEY,
    mo_code VARCHAR(50) UNIQUE,              -- 工单编号
    production_plan_id VARCHAR(30),          -- 生产计划ID
    product_code VARCHAR(100),               -- 产品编码
    mo_type ENUM('cp_test','assembly','ft_test'), -- 工单类型
    mo_status ENUM('created','released','started','paused','completed','cancelled'), -- 工单状态
    priority_level ENUM('low','normal','high','urgent'), -- 优先级
    
    -- 数量相关
    planned_quantity INT,                    -- 计划数量
    released_quantity INT DEFAULT 0,        -- 投入数量
    completed_quantity INT DEFAULT 0,       -- 完成数量
    pass_quantity INT DEFAULT 0,            -- 合格数量
    fail_quantity INT DEFAULT 0,            -- 不合格数量
    scrap_quantity INT DEFAULT 0,           -- 报废数量
    
    -- 时间相关
    planned_start_time DATETIME,            -- 计划开始时间
    planned_end_time DATETIME,              -- 计划结束时间
    actual_start_time DATETIME,             -- 实际开始时间
    actual_end_time DATETIME,               -- 实际结束时间
    
    -- CP工单专用字段
    wafer_lot_id VARCHAR(30),               -- 晶圆批次ID (CP用)
    wafer_count INT,                        -- 晶圆数量 (CP用)
    die_count_per_wafer INT,                -- 每片die数量 (CP用)
    probe_card_id VARCHAR(30),              -- 探针卡ID (CP用)
    
    -- Assembly工单专用字段
    package_type VARCHAR(50),               -- 封装类型 (Assembly用)
    die_source_mo_id VARCHAR(30),           -- 来源CP工单ID (Assembly用)
    
    -- FT工单专用字段
    assembly_source_mo_id VARCHAR(30),      -- 来源Assembly工单ID (FT用)
    test_socket_id VARCHAR(30),             -- 测试插座ID (FT用)
    
    -- 工艺相关
    process_route_id VARCHAR(30),           -- 工艺路线ID
    current_operation_id VARCHAR(30),       -- 当前工序ID
    assigned_equipment_id VARCHAR(30),      -- 分配设备ID
    assigned_operator VARCHAR(20),          -- 分配操作员
    
    -- 质量相关
    quality_grade ENUM('A','B','C','D'),   -- 质量等级
    inspection_status ENUM('pending','inspected','hold','released'), -- 检验状态
    rework_count INT DEFAULT 0,             -- 返工次数
    
    -- 成本相关
    standard_cost DECIMAL(12,4),            -- 标准成本
    actual_cost DECIMAL(12,4),              -- 实际成本
    
    customer_order_id VARCHAR(30),          -- 客户订单ID
    customer_lot_id VARCHAR(50),            -- 客户批次号
    special_requirements JSON,               -- 特殊要求
    
    created_by VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_mo_code (mo_code),
    INDEX idx_status_priority (mo_status, priority_level),
    INDEX idx_product_type (product_code, mo_type),
    INDEX idx_planned_time (planned_start_time, planned_end_time),
    INDEX idx_wafer_lot (wafer_lot_id),
    INDEX idx_source_mo (die_source_mo_id, assembly_source_mo_id)
);

-- 工单操作记录表
CREATE TABLE ic_mo_operations (
    operation_id VARCHAR(30) PRIMARY KEY,
    mo_id VARCHAR(30),                       -- 制造工单ID
    operation_sequence INT,                  -- 工序序号
    operation_code VARCHAR(50),              -- 工序编码
    operation_name VARCHAR(100),             -- 工序名称
    operation_type ENUM('cp_test','die_attach','wire_bond','molding','deflash','trim_form','marking','ft_test','inspection'), -- 工序类型
    
    -- 状态和时间
    operation_status ENUM('waiting','ready','running','completed','skipped','failed'), -- 工序状态
    setup_time_minutes INT,                  -- 调机时间(分钟)
    standard_cycle_time_seconds INT,         -- 标准节拍时间(秒)
    actual_cycle_time_seconds INT,           -- 实际节拍时间(秒)
    planned_start_time DATETIME,            -- 计划开始时间
    planned_end_time DATETIME,              -- 计划结束时间
    actual_start_time DATETIME,             -- 实际开始时间
    actual_end_time DATETIME,               -- 实际结束时间
    
    -- 设备和人员
    assigned_equipment_id VARCHAR(30),      -- 分配设备ID
    actual_equipment_id VARCHAR(30),        -- 实际使用设备ID
    assigned_operator VARCHAR(20),          -- 分配操作员
    actual_operator VARCHAR(20),            -- 实际操作员
    
    -- 数量统计
    input_quantity INT,                     -- 投入数量
    output_quantity INT,                    -- 产出数量
    pass_quantity INT DEFAULT 0,            -- 合格数量
    fail_quantity INT DEFAULT 0,            -- 不合格数量
    
    -- 工艺参数 (JSON存储关键工艺参数)
    process_parameters JSON,                 -- 工艺参数设定值
    actual_parameters JSON,                  -- 实际工艺参数
    parameter_deviations JSON,               -- 参数偏差记录
    
    -- 质量数据
    quality_data JSON,                       -- 质量检测数据
    spc_data JSON,                          -- SPC统计数据
    defect_codes JSON,                      -- 缺陷代码记录
    
    -- 异常和备注
    exception_codes JSON,                    -- 异常代码
    rework_reason TEXT,                     -- 返工原因
    operation_notes TEXT,                   -- 工序备注
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_mo_sequence (mo_id, operation_sequence),
    INDEX idx_operation_type (operation_type),
    INDEX idx_equipment_time (assigned_equipment_id, actual_start_time),
    INDEX idx_operator_time (actual_operator, actual_start_time),
    INDEX idx_operation_status (operation_status)
);
```

#### 2.2.2 设备执行管理
```sql
-- 设备作业表
CREATE TABLE ic_equipment_jobs (
    job_id VARCHAR(30) PRIMARY KEY,
    equipment_id VARCHAR(30),               -- 设备ID
    mo_id VARCHAR(30),                      -- 制造工单ID
    operation_id VARCHAR(30),               -- 工序ID
    
    job_code VARCHAR(50) UNIQUE,            -- 作业编号
    job_type ENUM('cp_test','die_attach','wire_bond','molding','deflash','trim_form','marking','ft_test'), -- 作业类型
    job_status ENUM('queued','loading','running','unloading','completed','aborted','failed'), -- 作业状态
    
    -- 时间记录
    queue_time DATETIME,                    -- 排队时间
    load_start_time DATETIME,              -- 上料开始时间
    load_end_time DATETIME,                -- 上料结束时间
    process_start_time DATETIME,           -- 加工开始时间
    process_end_time DATETIME,             -- 加工结束时间
    unload_start_time DATETIME,            -- 下料开始时间
    unload_end_time DATETIME,              -- 下料结束时间
    
    -- 生产数据
    loaded_quantity INT,                    -- 上料数量
    processed_quantity INT,                 -- 加工数量
    unloaded_quantity INT,                  -- 下料数量
    pass_quantity INT DEFAULT 0,            -- 合格数量
    fail_quantity INT DEFAULT 0,            -- 不合格数量
    
    -- 工艺和质量数据
    recipe_id VARCHAR(30),                  -- 配方ID
    recipe_version VARCHAR(20),             -- 配方版本
    process_data JSON,                      -- 过程数据
    alarm_events JSON,                      -- 报警事件
    quality_results JSON,                   -- 质量结果
    
    -- CP测试专用
    wafer_id VARCHAR(30),                   -- 晶圆ID (CP用)
    probe_card_id VARCHAR(30),              -- 探针卡ID (CP用)
    test_program_id VARCHAR(30),            -- 测试程序ID (CP用)
    wafer_map_data JSON,                    -- 晶圆Map数据 (CP用)
    
    -- FT测试专用
    test_socket_id VARCHAR(30),             -- 测试插座ID (FT用)
    handler_id VARCHAR(30),                 -- 分选机ID (FT用)
    test_results JSON,                      -- 测试结果 (FT用)
    
    operator_id VARCHAR(20),                -- 操作员ID
    shift_id VARCHAR(20),                   -- 班次ID
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_equipment_status (equipment_id, job_status),
    INDEX idx_mo_operation (mo_id, operation_id),
    INDEX idx_job_time (process_start_time, process_end_time),
    INDEX idx_wafer_job (wafer_id),
    INDEX idx_operator_shift (operator_id, shift_id)
);

-- 实时设备状态表
CREATE TABLE ic_equipment_realtime_status (
    status_id VARCHAR(30) PRIMARY KEY,
    equipment_id VARCHAR(30),               -- 设备ID
    current_job_id VARCHAR(30),             -- 当前作业ID
    
    equipment_status ENUM('idle','setup','running','maintenance','alarm','down'), -- 设备状态
    production_status ENUM('productive','non_productive','standby'), -- 生产状态
    
    current_recipe_id VARCHAR(30),          -- 当前配方ID
    current_operator VARCHAR(20),           -- 当前操作员
    current_shift VARCHAR(20),              -- 当前班次
    
    -- 性能数据
    cycle_time_seconds INT,                 -- 当前节拍时间
    throughput_per_hour DECIMAL(8,2),      -- 每小时产量
    utilization_rate DECIMAL(5,2),         -- 利用率%
    availability_rate DECIMAL(5,2),        -- 可用率%
    quality_rate DECIMAL(5,2),             -- 质量率%
    oee_rate DECIMAL(5,2),                 -- OEE%
    
    -- 计数器
    total_units_processed INT DEFAULT 0,    -- 总加工数量
    pass_units_count INT DEFAULT 0,         -- 合格品数量
    fail_units_count INT DEFAULT 0,         -- 不合格品数量
    daily_reset_count INT DEFAULT 0,        -- 日重置计数
    
    -- 报警信息
    active_alarms JSON,                     -- 当前报警
    alarm_count_today INT DEFAULT 0,        -- 今日报警次数
    
    -- 环境参数
    temperature DECIMAL(6,2),               -- 温度
    humidity DECIMAL(5,2),                  -- 湿度
    vibration_level DECIMAL(8,4),          -- 振动水平
    
    last_heartbeat TIMESTAMP,               -- 最后心跳时间
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_equipment (equipment_id),
    INDEX idx_status_updated (equipment_status, last_updated),
    INDEX idx_current_job (current_job_id),
    INDEX idx_operator_shift (current_operator, current_shift)
);
```

#### 2.2.3 数据采集管理
```sql
-- 实时数据采集表 (按天分区)
CREATE TABLE ic_realtime_data_collection (
    data_id VARCHAR(30) PRIMARY KEY,
    equipment_id VARCHAR(30),               -- 设备ID
    job_id VARCHAR(30),                     -- 作业ID
    mo_id VARCHAR(30),                      -- 工单ID
    
    data_type ENUM('process_parameter','quality_measurement','alarm_event','status_change'), -- 数据类型
    parameter_name VARCHAR(100),            -- 参数名称
    parameter_code VARCHAR(50),             -- 参数编码
    
    data_value DECIMAL(15,8),               -- 数据值
    unit_of_measure VARCHAR(20),            -- 计量单位
    data_timestamp TIMESTAMP(3),            -- 数据时间戳(毫秒精度)
    
    -- 数据来源
    data_source ENUM('secs_gem','plc','sensor','manual_input','calculated'), -- 数据来源
    source_address VARCHAR(100),            -- 来源地址
    
    -- 质量指标
    upper_spec_limit DECIMAL(15,8),         -- 上规格限
    lower_spec_limit DECIMAL(15,8),         -- 下规格限
    upper_control_limit DECIMAL(15,8),      -- 上控制限
    lower_control_limit DECIMAL(15,8),      -- 下控制限
    
    -- 状态标志
    is_alarm BOOLEAN DEFAULT FALSE,         -- 是否报警
    is_out_of_spec BOOLEAN DEFAULT FALSE,   -- 是否超规格
    is_out_of_control BOOLEAN DEFAULT FALSE, -- 是否失控
    
    quality_flag ENUM('normal','warning','alarm','critical'), -- 质量标志
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_equipment_time (equipment_id, data_timestamp),
    INDEX idx_job_parameter (job_id, parameter_code),
    INDEX idx_data_type_time (data_type, data_timestamp),
    INDEX idx_alarm_flag (is_alarm, data_timestamp),
    INDEX idx_quality_flag (quality_flag, data_timestamp)
) PARTITION BY RANGE (TO_DAYS(data_timestamp)) (
    PARTITION p_default VALUES LESS THAN (TO_DAYS('2024-01-01'))
);

-- 测试数据表 (CP和FT专用)
CREATE TABLE ic_test_data_records (
    test_id VARCHAR(30) PRIMARY KEY,
    job_id VARCHAR(30),                     -- 设备作业ID
    mo_id VARCHAR(30),                      -- 工单ID
    
    test_type ENUM('cp_test','ft_test'),    -- 测试类型
    device_id VARCHAR(50),                  -- 器件标识
    site_number INT,                        -- 测试Site号
    
    -- CP测试专用
    wafer_id VARCHAR(30),                   -- 晶圆ID
    die_x INT,                             -- Die X坐标
    die_y INT,                             -- Die Y坐标
    bin_code VARCHAR(10),                   -- Bin代码
    
    -- FT测试专用
    package_id VARCHAR(30),                 -- 封装器件ID
    test_socket_position INT,               -- 插座位置
    
    test_program_id VARCHAR(30),            -- 测试程序ID
    test_program_version VARCHAR(20),       -- 测试程序版本
    
    -- 测试结果
    test_result ENUM('pass','fail','aborted'), -- 测试结果
    test_start_time TIMESTAMP(3),           -- 测试开始时间
    test_end_time TIMESTAMP(3),             -- 测试结束时间
    test_duration_ms INT,                   -- 测试时长(毫秒)
    
    -- 测试数据 (JSON格式存储详细测试参数)
    test_parameters JSON,                   -- 测试参数结果
    fail_tests JSON,                       -- 失效测试项
    datalog_data JSON,                     -- Datalog数据
    
    temperature DECIMAL(6,2),               -- 测试温度
    voltage_supply DECIMAL(8,4),           -- 供电电压
    
    operator_id VARCHAR(20),               -- 操作员
    shift_id VARCHAR(20),                  -- 班次
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_job_device (job_id, device_id),
    INDEX idx_wafer_position (wafer_id, die_x, die_y),
    INDEX idx_test_result (test_result, test_start_time),
    INDEX idx_test_program (test_program_id, test_program_version),
    INDEX idx_bin_code (bin_code),
    INDEX idx_package_id (package_id)
) PARTITION BY RANGE (TO_DAYS(test_start_time)) (
    PARTITION p_default VALUES LESS THAN (TO_DAYS('2024-01-01'))
);
```

## 3. 制造执行管理引擎

### 3.1 工单执行引擎
```java
@Service
public class ManufacturingOrderExecutionService {
    
    @Autowired
    private ManufacturingOrderRepository moRepository;
    
    @Autowired
    private EquipmentJobRepository equipmentJobRepository;
    
    @Autowired
    private ProductionSchedulingService schedulingService;
    
    /**
     * 创建制造工单
     */
    public ICManufacturingOrder createManufacturingOrder(CreateMORequest request) {
        // 1. 验证生产计划和产品信息
        validateProductionPlan(request.getProductionPlanId());
        
        // 2. 创建工单主记录
        ICManufacturingOrder mo = new ICManufacturingOrder();
        mo.setMoId(IdGenerator.generateId());
        mo.setMoCode(generateMOCode(request.getMoType(), request.getProductCode()));
        mo.setProductionPlanId(request.getProductionPlanId());
        mo.setProductCode(request.getProductCode());
        mo.setMoType(request.getMoType());
        mo.setMoStatus(MOStatus.CREATED);
        mo.setPriorityLevel(request.getPriorityLevel());
        mo.setPlannedQuantity(request.getPlannedQuantity());
        mo.setPlannedStartTime(request.getPlannedStartTime());
        mo.setPlannedEndTime(request.getPlannedEndTime());
        mo.setCustomerOrderId(request.getCustomerOrderId());
        mo.setCustomerLotId(request.getCustomerLotId());
        
        // 3. 设置工单类型特定信息
        setupMOTypeSpecificInfo(mo, request);
        
        // 4. 创建工序操作记录
        List<ICMOOperation> operations = createOperations(mo, request.getProcessRouteId());
        
        // 5. 分配初始资源
        ResourceAllocationResult allocation = allocateInitialResources(mo, operations);
        mo.setAssignedEquipmentId(allocation.getPrimaryEquipmentId());
        
        mo = moRepository.save(mo);
        
        // 6. 触发生产调度
        schedulingService.scheduleMO(mo.getMoId());
        
        return mo;
    }
    
    /**
     * 工单投料执行
     */
    public MOExecutionResult releaseMO(String moId, ReleaseMORequest request) {
        ICManufacturingOrder mo = moRepository.findById(moId)
            .orElseThrow(() -> new EntityNotFoundException("工单不存在"));
        
        if (mo.getMoStatus() != MOStatus.CREATED) {
            throw new BusinessException("工单状态不允许投料");
        }
        
        MOExecutionResult result = new MOExecutionResult();
        
        try {
            // 1. 检查前置条件
            checkReleasePrerequisites(mo, request);
            
            // 2. 更新工单状态
            mo.setMoStatus(MOStatus.RELEASED);
            mo.setReleasedQuantity(request.getReleaseQuantity());
            mo.setActualStartTime(LocalDateTime.now());
            
            // 3. 启动首工序
            ICMOOperation firstOperation = getFirstOperation(moId);
            startOperation(firstOperation, request);
            
            // 4. 更新当前工序
            mo.setCurrentOperationId(firstOperation.getOperationId());
            
            moRepository.save(mo);
            
            // 5. 创建设备作业
            ICEquipmentJob equipmentJob = createEquipmentJob(mo, firstOperation, request);
            
            // 6. 触发数据采集
            startDataCollection(equipmentJob);
            
            result.setSuccess(true);
            result.setMessage("工单投料成功");
            result.setEquipmentJobId(equipmentJob.getJobId());
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("工单投料失败: " + e.getMessage());
            log.error("工单投料失败", e);
        }
        
        return result;
    }
    
    /**
     * 工序完成处理
     */
    public OperationCompletionResult completeOperation(String operationId, 
                                                     OperationCompletionRequest request) {
        ICMOOperation operation = moOperationRepository.findById(operationId)
            .orElseThrow(() -> new EntityNotFoundException("工序不存在"));
        
        OperationCompletionResult result = new OperationCompletionResult();
        
        try {
            // 1. 验证工序状态
            if (operation.getOperationStatus() != OperationStatus.RUNNING) {
                throw new BusinessException("工序状态不允许完成");
            }
            
            // 2. 记录完成数据
            operation.setOperationStatus(OperationStatus.COMPLETED);
            operation.setActualEndTime(LocalDateTime.now());
            operation.setOutputQuantity(request.getOutputQuantity());
            operation.setPassQuantity(request.getPassQuantity());
            operation.setFailQuantity(request.getFailQuantity());
            operation.setActualParameters(request.getActualParameters());
            operation.setQualityData(request.getQualityData());
            
            // 3. 计算工序绩效
            OperationPerformance performance = calculateOperationPerformance(operation);
            operation.setActualCycleTimeSeconds(performance.getActualCycleTime());
            
            moOperationRepository.save(operation);
            
            // 4. 更新工单进度
            updateMOProgress(operation.getMoId(), operation);
            
            // 5. 启动下一工序（如果有）
            ICMOOperation nextOperation = getNextOperation(operation);
            if (nextOperation != null) {
                TransferResult transfer = transferToNextOperation(operation, nextOperation, request);
                result.setNextOperationId(nextOperation.getOperationId());
                result.setTransferResult(transfer);
            } else {
                // 所有工序完成，完成工单
                completeMO(operation.getMoId());
            }
            
            // 6. 质量数据分析
            QualityAnalysisResult qualityAnalysis = analyzeOperationQuality(operation);
            result.setQualityAnalysis(qualityAnalysis);
            
            // 7. 更新设备状态
            updateEquipmentStatus(operation.getActualEquipmentId(), EquipmentStatus.IDLE);
            
            result.setSuccess(true);
            result.setMessage("工序完成成功");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("工序完成失败: " + e.getMessage());
            log.error("工序完成失败", e);
        }
        
        return result;
    }
    
    /**
     * CP测试工单专用处理
     */
    public CPTestResult processCPTest(String moId, CPTestRequest request) {
        ICManufacturingOrder mo = moRepository.findById(moId)
            .orElseThrow(() -> new EntityNotFoundException("CP工单不存在"));
        
        if (mo.getMoType() != MOType.CP_TEST) {
            throw new BusinessException("非CP测试工单");
        }
        
        CPTestResult result = new CPTestResult();
        
        try {
            // 1. 加载测试程序
            TestProgramLoadResult programLoad = loadTestProgram(
                request.getTestProgramId(), request.getEquipmentId());
            
            // 2. 安装探针卡
            ProbeCardSetupResult probeSetup = setupProbeCard(
                request.getProbeCardId(), request.getEquipmentId());
            
            // 3. 加载晶圆
            WaferLoadResult waferLoad = loadWafer(
                request.getWaferList(), request.getEquipmentId());
            
            // 4. 执行测试
            for (String waferId : request.getWaferList()) {
                WaferTestResult waferResult = executeWaferTest(waferId, mo, request);
                result.addWaferResult(waferResult);
                
                // 生成Wafer Map
                WaferMap waferMap = generateWaferMap(waferResult);
                result.addWaferMap(waferMap);
            }
            
            // 5. 统计测试结果
            CPTestStatistics statistics = calculateCPStatistics(result.getWaferResults());
            result.setStatistics(statistics);
            
            // 6. 更新工单状态
            updateCPMOStatus(mo, result);
            
            result.setSuccess(true);
            result.setMessage("CP测试完成");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("CP测试失败: " + e.getMessage());
            log.error("CP测试失败", e);
        }
        
        return result;
    }
    
    /**
     * Assembly工单专用处理
     */
    public AssemblyResult processAssembly(String moId, AssemblyRequest request) {
        ICManufacturingOrder mo = moRepository.findById(moId)
            .orElseThrow(() -> new EntityNotFoundException("Assembly工单不存在"));
        
        if (mo.getMoType() != MOType.ASSEMBLY) {
            throw new BusinessException("非Assembly工单");
        }
        
        AssemblyResult result = new AssemblyResult();
        
        try {
            // 1. Die Attach工序
            if (request.getProcessSequence().contains("DIE_ATTACH")) {
                DieAttachResult dieAttach = processDieAttach(mo, request.getDieAttachParams());
                result.setDieAttachResult(dieAttach);
            }
            
            // 2. Wire Bond工序
            if (request.getProcessSequence().contains("WIRE_BOND")) {
                WireBondResult wireBond = processWireBond(mo, request.getWireBondParams());
                result.setWireBondResult(wireBond);
            }
            
            // 3. Molding工序
            if (request.getProcessSequence().contains("MOLDING")) {
                MoldingResult molding = processMolding(mo, request.getMoldingParams());
                result.setMoldingResult(molding);
            }
            
            // 4. Deflash工序
            if (request.getProcessSequence().contains("DEFLASH")) {
                DeflashResult deflash = processDeflash(mo, request.getDeflashParams());
                result.setDeflashResult(deflash);
            }
            
            // 5. Trim & Form工序
            if (request.getProcessSequence().contains("TRIM_FORM")) {
                TrimFormResult trimForm = processTrimForm(mo, request.getTrimFormParams());
                result.setTrimFormResult(trimForm);
            }
            
            // 6. Marking工序
            if (request.getProcessSequence().contains("MARKING")) {
                MarkingResult marking = processMarking(mo, request.getMarkingParams());
                result.setMarkingResult(marking);
            }
            
            // 7. 最终检查
            AssemblyQualityInspection finalInspection = performFinalInspection(mo, result);
            result.setFinalInspection(finalInspection);
            
            // 8. 更新工单状态
            updateAssemblyMOStatus(mo, result);
            
            result.setSuccess(true);
            result.setMessage("Assembly完成");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("Assembly失败: " + e.getMessage());
            log.error("Assembly失败", e);
        }
        
        return result;
    }
    
    /**
     * FT测试工单专用处理
     */
    public FTTestResult processFTTest(String moId, FTTestRequest request) {
        ICManufacturingOrder mo = moRepository.findById(moId)
            .orElseThrow(() -> new EntityNotFoundException("FT工单不存在"));
        
        if (mo.getMoType() != MOType.FT_TEST) {
            throw new BusinessException("非FT测试工单");
        }
        
        FTTestResult result = new FTTestResult();
        
        try {
            // 1. 加载测试程序
            TestProgramLoadResult programLoad = loadFTTestProgram(
                request.getTestProgramId(), request.getAteEquipmentId());
            
            // 2. 设置测试插座
            TestSocketSetupResult socketSetup = setupTestSocket(
                request.getTestSocketId(), request.getAteEquipmentId());
            
            // 3. 配置Handler
            HandlerSetupResult handlerSetup = setupHandler(
                request.getHandlerId(), request.getSortingMap());
            
            // 4. 执行测试
            List<DeviceTestResult> deviceResults = new ArrayList<>();
            for (String packageId : request.getPackageList()) {
                DeviceTestResult deviceResult = executeDeviceTest(packageId, mo, request);
                deviceResults.add(deviceResult);
            }
            result.setDeviceResults(deviceResults);
            
            // 5. 分选包装
            SortingResult sorting = performSorting(deviceResults, request.getSortingMap());
            result.setSortingResult(sorting);
            
            // 6. 统计测试结果
            FTTestStatistics statistics = calculateFTStatistics(deviceResults);
            result.setStatistics(statistics);
            
            // 7. 更新工单状态
            updateFTMOStatus(mo, result);
            
            result.setSuccess(true);
            result.setMessage("FT测试完成");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("FT测试失败: " + e.getMessage());
            log.error("FT测试失败", e);
        }
        
        return result;
    }
}
```

### 3.2 生产调度引擎
```java
@Service
public class ProductionSchedulingService {
    
    @Autowired
    private ManufacturingOrderRepository moRepository;
    
    @Autowired
    private EquipmentRepository equipmentRepository;
    
    @Autowired
    private SchedulingAlgorithmService algorithmService;
    
    /**
     * 智能生产调度
     */
    public SchedulingResult performIntelligentScheduling(SchedulingRequest request) {
        SchedulingResult result = new SchedulingResult();
        
        try {
            // 1. 获取待调度工单
            List<ICManufacturingOrder> pendingMOs = getPendingMOs(request.getSchedulingHorizon());
            
            // 2. 获取可用设备资源
            List<ICEquipment> availableEquipments = getAvailableEquipments(request.getEquipmentTypes());
            
            // 3. 计算约束条件
            SchedulingConstraints constraints = calculateConstraints(pendingMOs, availableEquipments);
            
            // 4. 执行调度算法
            SchedulingAlgorithmResult algorithmResult;
            switch (request.getAlgorithmType()) {
                case PRIORITY_BASED:
                    algorithmResult = algorithmService.priorityBasedScheduling(
                        pendingMOs, availableEquipments, constraints);
                    break;
                case GENETIC_ALGORITHM:
                    algorithmResult = algorithmService.geneticAlgorithmScheduling(
                        pendingMOs, availableEquipments, constraints);
                    break;
                case CONSTRAINT_PROGRAMMING:
                    algorithmResult = algorithmService.constraintProgrammingScheduling(
                        pendingMOs, availableEquipments, constraints);
                    break;
                default:
                    algorithmResult = algorithmService.defaultScheduling(
                        pendingMOs, availableEquipments, constraints);
            }
            
            // 5. 优化调度结果
            OptimizedSchedule optimized = optimizeSchedule(algorithmResult, request.getOptimizationGoals());
            
            // 6. 验证调度可行性
            ScheduleFeasibilityCheck feasibility = validateScheduleFeasibility(optimized);
            if (!feasibility.isFeasible()) {
                throw new SchedulingException("调度方案不可行: " + feasibility.getFailureReason());
            }
            
            // 7. 应用调度结果
            applySchedulingResult(optimized);
            
            // 8. 生成调度报告
            SchedulingReport report = generateSchedulingReport(optimized, algorithmResult);
            result.setReport(report);
            
            result.setSuccess(true);
            result.setScheduledMOCount(pendingMOs.size());
            result.setOptimizedSchedule(optimized);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("调度失败: " + e.getMessage());
            log.error("生产调度失败", e);
        }
        
        return result;
    }
    
    /**
     * 实时调度调整
     */
    public ReschedulingResult performRealtimeRescheduling(ReschedulingTrigger trigger) {
        ReschedulingResult result = new ReschedulingResult();
        
        try {
            // 1. 分析调度触发原因
            TriggerAnalysis analysis = analyzeTrigger(trigger);
            
            // 2. 评估影响范围
            ImpactAssessment impact = assessReschedulingImpact(trigger, analysis);
            
            // 3. 生成调整方案
            List<ReschedulingOption> options = generateReschedulingOptions(impact);
            
            // 4. 选择最优方案
            ReschedulingOption selectedOption = selectOptimalOption(options, trigger.getObjectives());
            
            // 5. 执行调度调整
            executeRescheduling(selectedOption);
            
            // 6. 通知相关人员
            notifyReschedulingStakeholders(selectedOption, trigger);
            
            result.setSuccess(true);
            result.setSelectedOption(selectedOption);
            result.setImpactAssessment(impact);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("重新调度失败: " + e.getMessage());
            log.error("实时调度调整失败", e);
        }
        
        return result;
    }
    
    /**
     * 瓶颈分析和优化
     */
    public BottleneckAnalysisResult analyzeProductionBottlenecks(BottleneckAnalysisRequest request) {
        BottleneckAnalysisResult result = new BottleneckAnalysisResult();
        
        try {
            // 1. 收集生产数据
            ProductionDataCollection dataCollection = collectProductionData(
                request.getAnalysisPeriod(), request.getProductLines());
            
            // 2. 计算设备利用率
            Map<String, EquipmentUtilization> utilizationMap = calculateEquipmentUtilization(dataCollection);
            
            // 3. 识别瓶颈设备
            List<BottleneckEquipment> bottlenecks = identifyBottleneckEquipments(utilizationMap);
            result.setBottleneckEquipments(bottlenecks);
            
            // 4. 分析瓶颈原因
            for (BottleneckEquipment bottleneck : bottlenecks) {
                BottleneckRootCause rootCause = analyzeBottleneckRootCause(bottleneck, dataCollection);
                bottleneck.setRootCause(rootCause);
            }
            
            // 5. 生成优化建议
            List<BottleneckOptimizationRecommendation> recommendations = 
                generateBottleneckOptimizations(bottlenecks);
            result.setOptimizationRecommendations(recommendations);
            
            // 6. 计算改善潜力
            ImprovementPotential potential = calculateImprovementPotential(
                bottlenecks, recommendations);
            result.setImprovementPotential(potential);
            
            result.setSuccess(true);
            result.setAnalysisDate(LocalDateTime.now());
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("瓶颈分析失败: " + e.getMessage());
            log.error("瓶颈分析失败", e);
        }
        
        return result;
    }
}
```

---

*制造执行管理模块为IC封测CIM系统提供了完整的制造执行控制能力，包括CP/Assembly/FT三段式制造流程的统一管理，确保生产过程的可控性、可追溯性和高效性*