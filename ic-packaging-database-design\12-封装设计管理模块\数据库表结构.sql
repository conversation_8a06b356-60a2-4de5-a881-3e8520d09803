-- ========================================
-- 封装设计管理模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- 封装设计主表
CREATE TABLE package_designs (
    design_id VARCHAR(32) PRIMARY KEY COMMENT '封装设计ID',
    design_code VARCHAR(50) NOT NULL UNIQUE COMMENT '设计编码',
    design_name VARCHAR(200) NOT NULL COMMENT '设计名称',
    design_version VARCHAR(20) NOT NULL COMMENT '设计版本',
    
    -- 产品信息
    product_id VARCHAR(32) NOT NULL COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL COMMENT '产品编码',
    device_name VARCHAR(200) NOT NULL COMMENT '器件名称',
    
    -- 封装基本信息
    package_type VARCHAR(50) NOT NULL COMMENT '封装类型(QFP/BGA/CSP/FC/SIP)',
    package_family VARCHAR(50) COMMENT '封装系列',
    package_outline VARCHAR(50) COMMENT '封装外形',
    
    -- 尺寸规格
    package_length DECIMAL(8,3) NOT NULL COMMENT '封装长度(mm)',
    package_width DECIMAL(8,3) NOT NULL COMMENT '封装宽度(mm)',
    package_height DECIMAL(8,3) NOT NULL COMMENT '封装高度(mm)',
    package_thickness DECIMAL(8,3) COMMENT '封装厚度(mm)',
    
    -- 引脚信息
    pin_count INT NOT NULL COMMENT '总引脚数',
    pin_pitch DECIMAL(8,3) COMMENT '引脚间距(mm)',
    pin_configuration VARCHAR(50) COMMENT '引脚配置',
    ball_diameter DECIMAL(8,3) COMMENT '焊球直径(mm,BGA用)',
    
    -- Die信息
    die_size_x DECIMAL(8,3) NOT NULL COMMENT 'Die长度(mm)',
    die_size_y DECIMAL(8,3) NOT NULL COMMENT 'Die宽度(mm)',
    die_thickness DECIMAL(8,3) COMMENT 'Die厚度(um)',
    die_pad_count INT COMMENT 'Die焊盘数量',
    
    -- 基板信息(BGA/CSP)
    substrate_layers INT COMMENT '基板层数',
    substrate_material VARCHAR(100) COMMENT '基板材料',
    substrate_thickness DECIMAL(8,3) COMMENT '基板厚度(mm)',
    via_design VARCHAR(100) COMMENT '过孔设计',
    
    -- 引线框架信息(QFP/SOP)
    leadframe_material VARCHAR(100) COMMENT '引线框架材料',
    leadframe_plating VARCHAR(50) COMMENT '引线框架电镀',
    leadframe_thickness DECIMAL(8,3) COMMENT '引线框架厚度(mm)',
    
    -- 封装材料
    molding_compound VARCHAR(100) COMMENT '封装胶型号',
    die_attach_material VARCHAR(100) COMMENT '粘片材料',
    wire_bond_material VARCHAR(100) COMMENT '键合线材料',
    wire_diameter DECIMAL(8,3) COMMENT '键合线直径(um)',
    
    -- 热特性
    thermal_resistance_jc DECIMAL(8,3) COMMENT '热阻Rth(j-c) K/W',
    thermal_resistance_ja DECIMAL(8,3) COMMENT '热阻Rth(j-a) K/W',
    max_junction_temperature DECIMAL(6,2) COMMENT '最大结温(℃)',
    
    -- 设计状态
    design_status VARCHAR(20) NOT NULL DEFAULT 'CONCEPT' COMMENT '设计状态',
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态',
    qualification_status VARCHAR(20) COMMENT '认证状态',
    
    -- 开发信息
    development_phase VARCHAR(30) COMMENT '开发阶段',
    npi_project_id VARCHAR(32) COMMENT 'NPI项目ID',
    customer_id VARCHAR(32) COMMENT '客户ID',
    design_team_lead VARCHAR(32) COMMENT '设计团队负责人',
    
    -- 技术规格
    operating_temperature_min DECIMAL(6,2) COMMENT '工作温度下限(℃)',
    operating_temperature_max DECIMAL(6,2) COMMENT '工作温度上限(℃)',
    moisture_sensitivity_level VARCHAR(10) COMMENT '湿敏等级(MSL)',
    
    -- 可靠性要求
    reliability_requirements JSON COMMENT '可靠性要求',
    qualification_standards JSON COMMENT '认证标准',
    
    -- 生效信息
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    
    -- 文件信息
    design_files JSON COMMENT '设计文件列表',
    simulation_files JSON COMMENT '仿真文件列表',
    documentation_files JSON COMMENT '文档文件列表',
    
    -- 审批信息
    approved_by VARCHAR(32) COMMENT '审批人',
    approved_date DATE COMMENT '审批日期',
    approval_comments TEXT COMMENT '审批意见',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version_no INT DEFAULT 1 COMMENT '版本号',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_design_code (design_code),
    INDEX idx_design_product (product_id),
    INDEX idx_design_package_type (package_type),
    INDEX idx_design_status (design_status),
    INDEX idx_design_npi (npi_project_id),
    INDEX idx_design_customer (customer_id),
    INDEX idx_design_team_lead (design_team_lead),
    INDEX idx_design_version (design_code, design_version),
    INDEX idx_design_effective (effective_date, expiry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='封装设计主表';

-- 引脚定义表
CREATE TABLE package_pin_definitions (
    pin_id VARCHAR(32) PRIMARY KEY COMMENT '引脚ID',
    design_id VARCHAR(32) NOT NULL COMMENT '封装设计ID',
    pin_number VARCHAR(20) NOT NULL COMMENT '引脚编号',
    pin_name VARCHAR(100) COMMENT '引脚名称',
    
    -- 引脚位置
    position_x DECIMAL(8,3) COMMENT 'X坐标(mm)',
    position_y DECIMAL(8,3) COMMENT 'Y坐标(mm)',
    quadrant VARCHAR(10) COMMENT '象限',
    row_position VARCHAR(10) COMMENT '行位置',
    column_position VARCHAR(10) COMMENT '列位置',
    
    -- 引脚功能
    pin_function VARCHAR(100) NOT NULL COMMENT '引脚功能',
    signal_type VARCHAR(50) COMMENT '信号类型(POWER/GND/IO/ANALOG)',
    pin_category VARCHAR(50) COMMENT '引脚分类',
    
    -- 电气特性
    voltage_level VARCHAR(50) COMMENT '电压等级',
    current_rating DECIMAL(8,3) COMMENT '电流额定值(mA)',
    impedance DECIMAL(8,3) COMMENT '阻抗(Ohm)',
    capacitance DECIMAL(8,3) COMMENT '电容(pF)',
    
    -- 物理特性
    pin_diameter DECIMAL(8,3) COMMENT '引脚直径(mm)',
    pin_length DECIMAL(8,3) COMMENT '引脚长度(mm)',
    plating_material VARCHAR(50) COMMENT '电镀材料',
    plating_thickness DECIMAL(8,3) COMMENT '电镀厚度(um)',
    
    -- 键合信息
    bond_pad_id VARCHAR(32) COMMENT 'Die键合焊盘ID',
    wire_bond_type VARCHAR(50) COMMENT '键合方式',
    wire_count INT DEFAULT 1 COMMENT '键合线数量',
    
    -- 测试信息
    is_testable TINYINT(1) DEFAULT 1 COMMENT '是否可测试',
    test_group VARCHAR(50) COMMENT '测试分组',
    
    -- 状态信息
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (design_id) REFERENCES package_designs(design_id) ON DELETE CASCADE,
    INDEX idx_pin_design (design_id),
    INDEX idx_pin_number (pin_number),
    INDEX idx_pin_function (pin_function),
    INDEX idx_pin_signal_type (signal_type),
    INDEX idx_pin_position (position_x, position_y),
    INDEX idx_pin_bond_pad (bond_pad_id),
    UNIQUE KEY uk_design_pin_number (design_id, pin_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='引脚定义表';

-- Die焊盘定义表
CREATE TABLE die_pad_definitions (
    pad_id VARCHAR(32) PRIMARY KEY COMMENT '焊盘ID',
    design_id VARCHAR(32) NOT NULL COMMENT '封装设计ID',
    pad_number VARCHAR(20) NOT NULL COMMENT '焊盘编号',
    pad_name VARCHAR(100) COMMENT '焊盘名称',
    
    -- 焊盘位置
    position_x DECIMAL(8,3) NOT NULL COMMENT 'X坐标(um)',
    position_y DECIMAL(8,3) NOT NULL COMMENT 'Y坐标(um)',
    edge_distance DECIMAL(8,3) COMMENT '到边缘距离(um)',
    
    -- 焊盘尺寸
    pad_width DECIMAL(8,3) NOT NULL COMMENT '焊盘宽度(um)',
    pad_length DECIMAL(8,3) NOT NULL COMMENT '焊盘长度(um)',
    pad_area DECIMAL(10,3) COMMENT '焊盘面积(um2)',
    
    -- 焊盘功能
    pad_function VARCHAR(100) NOT NULL COMMENT '焊盘功能',
    signal_type VARCHAR(50) COMMENT '信号类型',
    pad_category VARCHAR(50) COMMENT '焊盘分类',
    
    -- 金属层信息
    metal_layer VARCHAR(20) COMMENT '金属层',
    metal_thickness DECIMAL(8,3) COMMENT '金属厚度(um)',
    passivation_opening TINYINT(1) DEFAULT 1 COMMENT '钝化层开窗',
    
    -- 键合信息
    bond_wire_count INT DEFAULT 1 COMMENT '键合线数量',
    bond_wire_diameter DECIMAL(8,3) COMMENT '键合线直径(um)',
    bond_method VARCHAR(50) COMMENT '键合方式',
    
    -- 电气特性
    resistance DECIMAL(8,3) COMMENT '电阻(mOhm)',
    current_density DECIMAL(8,3) COMMENT '电流密度(mA/um2)',
    
    -- 可靠性
    electromigration_limit DECIMAL(8,3) COMMENT '电迁移限制',
    stress_migration_limit DECIMAL(8,3) COMMENT '应力迁移限制',
    
    -- 状态信息
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (design_id) REFERENCES package_designs(design_id) ON DELETE CASCADE,
    INDEX idx_die_pad_design (design_id),
    INDEX idx_die_pad_number (pad_number),
    INDEX idx_die_pad_function (pad_function),
    INDEX idx_die_pad_signal_type (signal_type),
    INDEX idx_die_pad_position (position_x, position_y),
    UNIQUE KEY uk_design_pad_number (design_id, pad_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Die焊盘定义表';

-- 键合线连接表
CREATE TABLE wire_bond_connections (
    connection_id VARCHAR(32) PRIMARY KEY COMMENT '连接ID',
    design_id VARCHAR(32) NOT NULL COMMENT '封装设计ID',
    connection_name VARCHAR(100) COMMENT '连接名称',
    
    -- 连接点
    die_pad_id VARCHAR(32) NOT NULL COMMENT 'Die焊盘ID',
    package_pin_id VARCHAR(32) NOT NULL COMMENT '封装引脚ID',
    
    -- 键合线信息
    wire_material VARCHAR(50) NOT NULL COMMENT '键合线材料',
    wire_diameter DECIMAL(8,3) NOT NULL COMMENT '键合线直径(um)',
    wire_count INT DEFAULT 1 COMMENT '键合线数量',
    wire_length DECIMAL(8,3) COMMENT '键合线长度(mm)',
    
    -- 键合参数
    first_bond_force DECIMAL(8,3) COMMENT '第一键合力(gf)',
    second_bond_force DECIMAL(8,3) COMMENT '第二键合力(gf)',
    bond_temperature DECIMAL(6,2) COMMENT '键合温度(℃)',
    ultrasonic_power INT COMMENT '超声功率(mW)',
    
    -- 键合高度
    bond_height DECIMAL(8,3) COMMENT '键合高度(um)',
    loop_height DECIMAL(8,3) COMMENT '弧高(um)',
    
    -- 电气特性
    resistance DECIMAL(8,3) COMMENT '电阻(mOhm)',
    inductance DECIMAL(8,3) COMMENT '电感(nH)',
    current_capacity DECIMAL(8,3) COMMENT '电流容量(mA)',
    
    -- 可靠性要求
    pull_test_requirement DECIMAL(8,3) COMMENT '拉力测试要求(gf)',
    shear_test_requirement DECIMAL(8,3) COMMENT '剪切测试要求(gf)',
    
    -- 状态信息
    is_critical TINYINT(1) DEFAULT 0 COMMENT '是否关键连接',
    connection_status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '连接状态',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (design_id) REFERENCES package_designs(design_id) ON DELETE CASCADE,
    FOREIGN KEY (die_pad_id) REFERENCES die_pad_definitions(pad_id) ON DELETE CASCADE,
    FOREIGN KEY (package_pin_id) REFERENCES package_pin_definitions(pin_id) ON DELETE CASCADE,
    INDEX idx_connection_design (design_id),
    INDEX idx_connection_die_pad (die_pad_id),
    INDEX idx_connection_package_pin (package_pin_id),
    INDEX idx_connection_material (wire_material),
    INDEX idx_connection_critical (is_critical),
    UNIQUE KEY uk_design_pad_pin (design_id, die_pad_id, package_pin_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='键合线连接表';

-- 仿真分析表
CREATE TABLE package_simulations (
    simulation_id VARCHAR(32) PRIMARY KEY COMMENT '仿真ID',
    design_id VARCHAR(32) NOT NULL COMMENT '封装设计ID',
    simulation_code VARCHAR(50) NOT NULL COMMENT '仿真编码',
    simulation_name VARCHAR(200) NOT NULL COMMENT '仿真名称',
    simulation_type VARCHAR(50) NOT NULL COMMENT '仿真类型',
    
    -- 仿真分类
    analysis_type VARCHAR(50) NOT NULL COMMENT '分析类型',
    simulation_category VARCHAR(50) COMMENT '仿真类别',
    
    -- 仿真工具
    simulation_tool VARCHAR(100) NOT NULL COMMENT '仿真工具',
    tool_version VARCHAR(50) COMMENT '工具版本',
    
    -- 仿真模型
    model_type VARCHAR(50) COMMENT '模型类型',
    model_complexity VARCHAR(20) COMMENT '模型复杂度',
    mesh_resolution VARCHAR(20) COMMENT '网格精度',
    
    -- 仿真条件
    simulation_conditions JSON COMMENT '仿真条件',
    boundary_conditions JSON COMMENT '边界条件',
    material_properties JSON COMMENT '材料属性',
    
    -- 仿真结果
    simulation_results JSON COMMENT '仿真结果',
    key_parameters JSON COMMENT '关键参数',
    analysis_summary TEXT COMMENT '分析总结',
    
    -- 文件信息
    input_files JSON COMMENT '输入文件列表',
    output_files JSON COMMENT '输出文件列表',
    report_files JSON COMMENT '报告文件列表',
    
    -- 状态信息
    simulation_status VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '仿真状态',
    
    -- 执行信息
    analyst_id VARCHAR(32) NOT NULL COMMENT '分析员ID',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    computation_time DECIMAL(8,2) COMMENT '计算时间(分钟)',
    
    -- 验证信息
    verification_status VARCHAR(20) COMMENT '验证状态',
    verified_by VARCHAR(32) COMMENT '验证人',
    verification_comments TEXT COMMENT '验证意见',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (design_id) REFERENCES package_designs(design_id) ON DELETE CASCADE,
    INDEX idx_sim_design (design_id),
    INDEX idx_sim_code (simulation_code),
    INDEX idx_sim_type (simulation_type),
    INDEX idx_sim_analyst (analyst_id),
    INDEX idx_sim_status (simulation_status),
    INDEX idx_sim_category (simulation_category),
    UNIQUE KEY uk_design_sim_code (design_id, simulation_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仿真分析表';

-- 设计评审表
CREATE TABLE design_reviews (
    review_id VARCHAR(32) PRIMARY KEY COMMENT '评审ID',
    design_id VARCHAR(32) NOT NULL COMMENT '封装设计ID',
    review_code VARCHAR(50) NOT NULL COMMENT '评审编码',
    review_name VARCHAR(200) NOT NULL COMMENT '评审名称',
    review_type VARCHAR(30) NOT NULL COMMENT '评审类型',
    
    -- 评审阶段
    review_stage VARCHAR(30) NOT NULL COMMENT '评审阶段',
    design_phase VARCHAR(30) COMMENT '设计阶段',
    
    -- 评审计划
    planned_date DATE NOT NULL COMMENT '计划评审日期',
    actual_date DATE COMMENT '实际评审日期',
    review_duration DECIMAL(4,1) COMMENT '评审时长(小时)',
    
    -- 评审人员
    review_leader VARCHAR(32) NOT NULL COMMENT '评审组长',
    reviewers JSON NOT NULL COMMENT '评审员列表',
    design_presenters JSON COMMENT '设计报告人列表',
    
    -- 评审内容
    review_scope TEXT COMMENT '评审范围',
    review_criteria JSON COMMENT '评审标准',
    documents_reviewed JSON COMMENT '评审文档列表',
    
    -- 评审结果
    review_status VARCHAR(20) NOT NULL DEFAULT 'SCHEDULED' COMMENT '评审状态',
    overall_result VARCHAR(20) COMMENT '总体结果',
    
    -- 评审意见
    technical_comments TEXT COMMENT '技术意见',
    design_recommendations TEXT COMMENT '设计建议',
    risk_concerns TEXT COMMENT '风险关注点',
    
    -- 行动项
    action_items JSON COMMENT '行动项列表',
    follow_up_required TINYINT(1) DEFAULT 0 COMMENT '是否需要跟进',
    next_review_date DATE COMMENT '下次评审日期',
    
    -- 客户参与
    customer_participation TINYINT(1) DEFAULT 0 COMMENT '客户是否参与',
    customer_feedback TEXT COMMENT '客户反馈',
    
    -- 文档记录
    meeting_minutes TEXT COMMENT '会议纪要',
    presentation_files JSON COMMENT '演示文件列表',
    review_report_file VARCHAR(500) COMMENT '评审报告文件',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (design_id) REFERENCES package_designs(design_id) ON DELETE CASCADE,
    INDEX idx_review_design (design_id),
    INDEX idx_review_code (review_code),
    INDEX idx_review_type (review_type),
    INDEX idx_review_stage (review_stage),
    INDEX idx_review_leader (review_leader),
    INDEX idx_review_date (planned_date),
    INDEX idx_review_status (review_status),
    UNIQUE KEY uk_design_review_code (design_id, review_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设计评审表';

-- 设计变更表
CREATE TABLE design_changes (
    change_id VARCHAR(32) PRIMARY KEY COMMENT '变更ID',
    design_id VARCHAR(32) NOT NULL COMMENT '封装设计ID',
    change_no VARCHAR(50) NOT NULL UNIQUE COMMENT '变更编号',
    change_title VARCHAR(200) NOT NULL COMMENT '变更标题',
    change_type VARCHAR(30) NOT NULL COMMENT '变更类型',
    
    -- 变更描述
    change_description TEXT NOT NULL COMMENT '变更描述',
    change_reason TEXT NOT NULL COMMENT '变更原因',
    current_design TEXT COMMENT '当前设计',
    proposed_design TEXT COMMENT '建议设计',
    
    -- 影响分析
    technical_impact TEXT COMMENT '技术影响',
    performance_impact TEXT COMMENT '性能影响',
    cost_impact DECIMAL(15,2) COMMENT '成本影响',
    schedule_impact_days INT COMMENT '进度影响天数',
    risk_assessment TEXT COMMENT '风险评估',
    
    -- 变更状态
    change_status VARCHAR(20) NOT NULL DEFAULT 'SUBMITTED' COMMENT '变更状态',
    priority_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '优先级',
    
    -- 申请信息
    requester_id VARCHAR(32) NOT NULL COMMENT '申请人ID',
    request_date DATE NOT NULL COMMENT '申请日期',
    
    -- 审批信息
    approvers JSON COMMENT '审批人列表',
    approval_status VARCHAR(20) COMMENT '审批状态',
    approved_date DATE COMMENT '批准日期',
    approval_comments TEXT COMMENT '审批意见',
    
    -- 实施信息
    implementer_id VARCHAR(32) COMMENT '实施人ID',
    planned_implementation_date DATE COMMENT '计划实施日期',
    actual_implementation_date DATE COMMENT '实际实施日期',
    implementation_notes TEXT COMMENT '实施说明',
    
    -- 验证信息
    verification_required TINYINT(1) DEFAULT 0 COMMENT '是否需要验证',
    verification_plan TEXT COMMENT '验证计划',
    verification_results TEXT COMMENT '验证结果',
    
    -- 客户确认
    customer_notification_required TINYINT(1) DEFAULT 0 COMMENT '是否需要客户通知',
    customer_approval_required TINYINT(1) DEFAULT 0 COMMENT '是否需要客户批准',
    customer_notified_date DATE COMMENT '客户通知日期',
    customer_approved_date DATE COMMENT '客户批准日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (design_id) REFERENCES package_designs(design_id) ON DELETE CASCADE,
    INDEX idx_change_design (design_id),
    INDEX idx_change_status (change_status),
    INDEX idx_change_requester (requester_id),
    INDEX idx_change_type (change_type),
    INDEX idx_change_date (request_date),
    INDEX idx_change_priority (priority_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设计变更表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. package_designs: 封装设计主表，定义封装的整体设计信息
2. package_pin_definitions: 引脚定义表，详细的引脚规格和功能
3. die_pad_definitions: Die焊盘定义表，芯片焊盘的位置和属性
4. wire_bond_connections: 键合线连接表，焊盘与引脚的连接关系
5. package_simulations: 仿真分析表，各种工程仿真和分析
6. design_reviews: 设计评审表，设计评审过程管理
7. design_changes: 设计变更表，设计变更控制和跟踪

核心特性:
- 完整的封装设计数据结构
- 详细的引脚和焊盘定义
- 键合线连接关系管理
- 多物理场仿真支持
- 严格的设计评审流程
- 完善的变更控制管理
- 支持多种封装类型(QFP/BGA/CSP/FC等)
*/