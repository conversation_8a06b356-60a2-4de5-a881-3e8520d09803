<template>
  <el-dialog
    v-model="dialogVisible"
    :title="mode === 'create' ? '新增物料' : '编辑物料'"
    width="90%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form
ref="formRef" :model="formData"
:rules="formRules" label-width="120px" size="default"
>
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物料编码" prop="basicInfo.materialCode">
                <el-input
                  v-model="formData.basicInfo.materialCode"
                  :disabled="mode === 'edit'"
                  placeholder="系统自动生成或手动输入"
                >
                  <template #append>
                    <el-button @click="generateMaterialCode">自动生成</el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料名称" prop="basicInfo.materialName">
                <el-input v-model="formData.basicInfo.materialName" placeholder="请输入物料名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="英文名称" prop="basicInfo.englishName">
                <el-input v-model="formData.basicInfo.englishName" placeholder="请输入英文名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格型号" prop="basicInfo.specification">
                <el-input v-model="formData.basicInfo.specification" placeholder="请输入规格型号" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="物料分类" prop="basicInfo.category">
                <el-select
                  v-model="formData.basicInfo.category"
                  placeholder="请选择物料分类"
                  style="width: 100%"
                  @change="handleCategoryChange"
                >
                  <el-option
                    v-for="option in MATERIAL_CATEGORY_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  >
                    <div class="category-option">
                      <el-icon :style="{ color: option.color }">
                        <component :is="option.icon" />
                      </el-icon>
                      <span>{{ option.label }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="物料状态" prop="basicInfo.status">
                <el-select
                  v-model="formData.basicInfo.status"
                  placeholder="请选择物料状态"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in MATERIAL_STATUS_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="制造商" prop="basicInfo.manufacturer">
                <el-input v-model="formData.basicInfo.manufacturer" placeholder="请输入制造商" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="品牌" prop="basicInfo.brand">
                <el-input v-model="formData.basicInfo.brand" placeholder="请输入品牌" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号" prop="basicInfo.model">
                <el-input v-model="formData.basicInfo.model" placeholder="请输入型号" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="描述" prop="basicInfo.description">
            <el-input
              v-model="formData.basicInfo.description"
              type="textarea"
              :rows="3"
              placeholder="请输入物料描述"
            />
          </el-form-item>

          <el-form-item label="备注" prop="basicInfo.remarks">
            <el-input
              v-model="formData.basicInfo.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-tab-pane>

        <!-- 技术参数 -->
        <el-tab-pane label="技术参数" name="technical">
          <!-- 通用尺寸参数 -->
          <div class="param-section">
            <h4>尺寸参数</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="长度(mm)">
                  <el-input-number
                    v-model="formData.technicalParams.dimensions.length"
                    :precision="2"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="宽度(mm)">
                  <el-input-number
                    v-model="formData.technicalParams.dimensions.width"
                    :precision="2"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="高度(mm)">
                  <el-input-number
                    v-model="formData.technicalParams.dimensions.height"
                    :precision="2"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="厚度(μm)">
                  <el-input-number
                    v-model="formData.technicalParams.dimensions.thickness"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="直径(μm)">
                  <el-input-number
                    v-model="formData.technicalParams.dimensions.diameter"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="材质">
                  <el-input v-model="formData.technicalParams.material" placeholder="请输入材质" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="纯度(%)">
                  <el-input-number
                    v-model="formData.technicalParams.purity"
                    :precision="2"
                    :min="0"
                    :max="100"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 晶圆参数 -->
          <div v-if="formData.basicInfo.category === 'wafer'" class="param-section">
            <h4>晶圆参数</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="晶圆尺寸">
                  <el-select
                    v-model="formData.technicalParams.waferInfo.waferSize"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in WAFER_SIZE_OPTIONS"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="厚度(μm)">
                  <el-input-number
                    v-model="formData.technicalParams.waferInfo.thickness"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="晶向">
                  <el-input
                    v-model="formData.technicalParams.waferInfo.orientation"
                    placeholder="如: <100>"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="电阻率(Ω·cm)">
                  <el-input-number
                    v-model="formData.technicalParams.waferInfo.resistivity"
                    :precision="2"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="芯片尺寸">
                  <el-input
                    v-model="formData.technicalParams.waferInfo.dieSize"
                    placeholder="如: 5x5mm"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 封装参数 -->
          <div
            v-if="['substrate', 'leadframe'].includes(formData.basicInfo.category)"
            class="param-section"
          >
            <h4>封装参数</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="封装类型">
                  <el-select
                    v-model="formData.technicalParams.packageInfo.packageType"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in PACKAGE_TYPE_OPTIONS"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="引脚数">
                  <el-input-number
                    v-model="formData.technicalParams.packageInfo.pinCount"
                    :precision="0"
                    :min="1"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="引脚间距(mm)">
                  <el-input-number
                    v-model="formData.technicalParams.packageInfo.pitchSize"
                    :precision="2"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="封装体尺寸">
                  <el-input
                    v-model="formData.technicalParams.packageInfo.bodySize"
                    placeholder="如: 20x20mm"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 线材参数 -->
          <div v-if="formData.basicInfo.category === 'wirebond'" class="param-section">
            <h4>线材参数</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="线材类型">
                  <el-select
                    v-model="formData.technicalParams.wirebondInfo.wireType"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in WIRE_TYPE_OPTIONS"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    >
                      <div class="wire-option">
                        <span
class="wire-color" :style="{ backgroundColor: option.color }" />
                        <span>{{ option.label }}</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="直径(μm)">
                  <el-input-number
                    v-model="formData.technicalParams.wirebondInfo.diameter"
                    :precision="0"
                    :min="1"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="拉伸强度(MPa)">
                  <el-input-number
                    v-model="formData.technicalParams.wirebondInfo.tensileStrength"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="延伸率(%)">
                  <el-input-number
                    v-model="formData.technicalParams.wirebondInfo.elongation"
                    :precision="1"
                    :min="0"
                    :max="100"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="纯度(%)">
                  <el-input-number
                    v-model="formData.technicalParams.wirebondInfo.purity"
                    :precision="2"
                    :min="0"
                    :max="100"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 电气特性 -->
          <div class="param-section">
            <h4>电气特性</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="电阻(Ω)">
                  <el-input-number
                    v-model="formData.technicalParams.electricalProperties.resistance"
                    :precision="6"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="导电率(S/m)">
                  <el-input-number
                    v-model="formData.technicalParams.electricalProperties.conductivity"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="电压(V)">
                  <el-input-number
                    v-model="formData.technicalParams.electricalProperties.voltage"
                    :precision="2"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="电流(A)">
                  <el-input-number
                    v-model="formData.technicalParams.electricalProperties.current"
                    :precision="3"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="功率(W)">
                  <el-input-number
                    v-model="formData.technicalParams.electricalProperties.power"
                    :precision="2"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 环境要求 -->
          <div class="param-section">
            <h4>环境要求</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="最低温度(°C)">
                  <el-input-number
                    v-model="formData.technicalParams.environmentalRequirements.temperatureMin"
                    :precision="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最高温度(°C)">
                  <el-input-number
                    v-model="formData.technicalParams.environmentalRequirements.temperatureMax"
                    :precision="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最大湿度(%)">
                  <el-input-number
                    v-model="formData.technicalParams.environmentalRequirements.humidityMax"
                    :precision="0"
                    :min="0"
                    :max="100"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="存储条件">
              <el-select
                v-model="formData.technicalParams.environmentalRequirements.storageConditions"
                multiple
                placeholder="请选择存储条件"
                style="width: 100%"
              >
                <el-option label="ESD安全" value="ESD安全" />
                <el-option label="防潮" value="防潮" />
                <el-option label="防静电" value="防静电" />
                <el-option label="恒温恒湿" value="恒温恒湿" />
                <el-option label="洁净环境" value="洁净环境" />
                <el-option label="避光保存" value="避光保存" />
                <el-option label="密封包装" value="密封包装" />
                <el-option label="防氧化" value="防氧化" />
                <el-option label="干燥环境" value="干燥环境" />
              </el-select>
            </el-form-item>
          </div>
        </el-tab-pane>

        <!-- 供应商信息 -->
        <el-tab-pane label="供应商信息" name="suppliers">
          <div class="suppliers-section">
            <div class="section-header">
              <h4>供应商列表</h4>
              <el-button type="primary" size="small" @click="addSupplier">
                <el-icon><Plus /></el-icon>
                添加供应商
              </el-button>
            </div>

            <div v-if="formData.suppliers.length === 0" class="empty-suppliers">
              <el-empty description="暂无供应商信息" :image-size="100">
                <el-button
type="primary" @click="addSupplier">添加第一个供应商</el-button>
              </el-empty>
            </div>

            <div v-else class="suppliers-list">
              <el-card
                v-for="(supplier, index) in formData.suppliers"
                :key="index"
                class="supplier-card"
                shadow="never"
              >
                <template #header>
                  <div class="supplier-header">
                    <span>供应商 {{ index + 1 }}</span>
                    <div class="supplier-actions">
                      <el-switch
                        v-model="supplier.isPrimary"
                        active-text="主供应商"
                        @change="handlePrimaryChange(index)"
                      />
                      <el-button type="danger" size="small" @click="removeSupplier(index)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item :label="`供应商名称`" :prop="`suppliers.${index}.supplierName`">
                      <el-input v-model="supplier.supplierName" placeholder="请输入供应商名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item :label="`联系人`" :prop="`suppliers.${index}.contactPerson`">
                      <el-input v-model="supplier.contactPerson" placeholder="请输入联系人" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item :label="`联系电话`" :prop="`suppliers.${index}.contactPhone`">
                      <el-input v-model="supplier.contactPhone" placeholder="请输入联系电话" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item :label="`联系邮箱`" :prop="`suppliers.${index}.contactEmail`">
                      <el-input v-model="supplier.contactEmail" placeholder="请输入联系邮箱" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item :label="`供货周期(天)`" :prop="`suppliers.${index}.leadTime`">
                      <el-input-number
                        v-model="supplier.leadTime"
                        :min="1"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item :label="`最小订货量`" :prop="`suppliers.${index}.minOrderQty`">
                      <el-input-number
                        v-model="supplier.minOrderQty"
                        :min="1"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-form-item :label="`质量评级(%)`" :prop="`suppliers.${index}.qualityRating`">
                      <el-input-number
                        v-model="supplier.qualityRating"
                        :min="0"
                        :max="100"
                        :precision="1"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      :label="`交期评级(%)`"
                      :prop="`suppliers.${index}.deliveryRating`"
                    >
                      <el-input-number
                        v-model="supplier.deliveryRating"
                        :min="0"
                        :max="100"
                        :precision="1"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="`认证证书`" :prop="`suppliers.${index}.certifications`">
                      <el-select
                        v-model="supplier.certifications"
                        multiple
                        placeholder="请选择认证证书"
                        style="width: 100%"
                      >
                        <el-option label="ISO9001" value="ISO9001" />
                        <el-option label="IATF16949" value="IATF16949" />
                        <el-option label="ISO14001" value="ISO14001" />
                        <el-option label="OHSAS18001" value="OHSAS18001" />
                        <el-option label="RoHS" value="RoHS" />
                        <el-option label="REACH" value="REACH" />
                        <el-option label="UL认证" value="UL认证" />
                        <el-option label="CE认证" value="CE认证" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 质量信息 -->
        <el-tab-pane label="质量信息" name="quality">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="执行标准" prop="qualityInfo.standard">
                <el-input v-model="formData.qualityInfo.standard" placeholder="请输入执行标准" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="技术规范" prop="qualityInfo.specification">
                <el-input
                  v-model="formData.qualityInfo.specification"
                  placeholder="请输入技术规范"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="检验方法" prop="qualityInfo.inspectionMethod">
            <el-input
              v-model="formData.qualityInfo.inspectionMethod"
              type="textarea"
              :rows="3"
              placeholder="请输入检验方法"
            />
          </el-form-item>

          <el-form-item label="接收标准" prop="qualityInfo.acceptanceCriteria">
            <el-input
              v-model="formData.qualityInfo.acceptanceCriteria"
              type="textarea"
              :rows="3"
              placeholder="请输入接收标准"
            />
          </el-form-item>

          <el-form-item label="认证信息" prop="qualityInfo.certifications">
            <el-select
              v-model="formData.qualityInfo.certifications"
              multiple
              placeholder="请选择认证信息"
              style="width: 100%"
            >
              <el-option label="SEMI标准认证" value="SEMI标准认证" />
              <el-option label="JEITA认证" value="JEITA认证" />
              <el-option label="IPC认证" value="IPC认证" />
              <el-option label="EIAJ认证" value="EIAJ认证" />
              <el-option label="RoHS认证" value="RoHS认证" />
              <el-option label="REACH认证" value="REACH认证" />
              <el-option label="UL认证" value="UL认证" />
              <el-option label="LBMA认证" value="LBMA认证" />
            </el-select>
          </el-form-item>

          <!-- 可靠性信息 -->
          <div class="reliability-section">
            <h4>可靠性信息</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="MTBF(小时)" prop="qualityInfo.reliability.mtbf">
                  <el-input-number
                    v-model="formData.qualityInfo.reliability.mtbf"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="寿命测试" prop="qualityInfo.reliability.lifeTest">
                  <el-input
                    v-model="formData.qualityInfo.reliability.lifeTest"
                    placeholder="如: 1000小时老化测试"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="认证状态" prop="qualityInfo.reliability.qualificationStatus">
                  <el-select
                    v-model="formData.qualityInfo.reliability.qualificationStatus"
                    style="width: 100%"
                  >
                    <el-option label="已认证" value="已认证" />
                    <el-option label="认证中" value="认证中" />
                    <el-option label="待认证" value="待认证" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- 库存和成本信息 -->
        <el-tab-pane label="库存成本" name="stock-cost">
          <div class="stock-cost-section">
            <h4>库存信息</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="计量单位" prop="stockInfo.unitOfMeasure">
                  <el-select v-model="formData.stockInfo.unitOfMeasure" style="width: 100%">
                    <el-option label="PCS (个)" value="PCS" />
                    <el-option label="KG (公斤)" value="KG" />
                    <el-option label="M (米)" value="M" />
                    <el-option label="L (升)" value="L" />
                    <el-option label="SET (套)" value="SET" />
                    <el-option label="BOX (盒)" value="BOX" />
                    <el-option label="ROLL (卷)" value="ROLL" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="安全库存" prop="stockInfo.safetyStock">
                  <el-input-number
                    v-model="formData.stockInfo.safetyStock"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="最大库存" prop="stockInfo.maxStock">
                  <el-input-number
                    v-model="formData.stockInfo.maxStock"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="再订货点" prop="stockInfo.reorderPoint">
                  <el-input-number
                    v-model="formData.stockInfo.reorderPoint"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="采购周期(天)" prop="stockInfo.leadTime">
                  <el-input-number
                    v-model="formData.stockInfo.leadTime"
                    :min="1"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="保质期(天)" prop="stockInfo.shelfLife">
                  <el-input-number
                    v-model="formData.stockInfo.shelfLife"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                    placeholder="永久有效请输入9999"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="存储位置" prop="stockInfo.storageLocation">
                  <el-input
                    v-model="formData.stockInfo.storageLocation"
                    placeholder="请输入存储位置编码"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="存储要求" prop="stockInfo.storageRequirements">
              <el-select
                v-model="formData.stockInfo.storageRequirements"
                multiple
                placeholder="请选择存储要求"
                style="width: 100%"
              >
                <el-option label="ESD防护" value="ESD防护" />
                <el-option label="防潮包装" value="防潮包装" />
                <el-option label="防静电" value="防静电" />
                <el-option label="恒温恒湿" value="恒温恒湿" />
                <el-option label="洁净室存储" value="洁净室存储" />
                <el-option label="避光保存" value="避光保存" />
                <el-option label="密封包装" value="密封包装" />
                <el-option label="防氧化" value="防氧化" />
                <el-option label="水平存放" value="水平存放" />
                <el-option label="贵金属保险库" value="贵金属保险库" />
              </el-select>
            </el-form-item>
          </div>

          <div class="cost-section">
            <h4>成本信息</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="标准成本" prop="costInfo.standardCost">
                  <el-input-number
                    v-model="formData.costInfo.standardCost"
                    :precision="4"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="当前价格" prop="costInfo.currentPrice">
                  <el-input-number
                    v-model="formData.costInfo.currentPrice"
                    :precision="4"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="货币单位" prop="costInfo.currency">
                  <el-select v-model="formData.costInfo.currency" style="width: 100%">
                    <el-option label="USD (美元)" value="USD" />
                    <el-option label="CNY (人民币)" value="CNY" />
                    <el-option label="JPY (日元)" value="JPY" />
                    <el-option label="EUR (欧元)" value="EUR" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="价格单位" prop="costInfo.priceUnit">
                  <el-select v-model="formData.costInfo.priceUnit" style="width: 100%">
                    <el-option label="PCS" value="PCS" />
                    <el-option label="KG" value="KG" />
                    <el-option label="M" value="M" />
                    <el-option label="L" value="L" />
                    <el-option label="SET" value="SET" />
                    <el-option label="1K PCS" value="1K PCS" />
                    <el-option label="WAFER" value="WAFER" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ mode === 'create' ? '创建' : '更新' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, nextTick } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import type { FormInstance, FormRules } from 'element-plus'
  import type {
    MaterialMaster,
    CreateMaterialMasterData,
    UpdateMaterialMasterData,
    MaterialMasterCategory,
    SupplierInfo
  } from '@/types/materialMaster'
  import {
    MATERIAL_CATEGORY_OPTIONS,
    MATERIAL_STATUS_OPTIONS,
    PACKAGE_TYPE_OPTIONS,
    WAFER_SIZE_OPTIONS,
    WIRE_TYPE_OPTIONS,
    generateNewMaterialCode
  } from '@/utils/mockData/materialMaster'

  interface Props {
    visible: boolean
    material: MaterialMaster | null
    mode: 'create' | 'edit'
  }

  interface Emits {
    'update:visible': [visible: boolean]
    save: [data: CreateMaterialMasterData | UpdateMaterialMasterData]
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 组件引用
  const formRef = ref<FormInstance>()

  // 页面状态
  const activeTab = ref('basic')
  const saving = ref(false)

  // 对话框显示状态
  const dialogVisible = computed({
    get: () => props.visible,
    set: value => emit('update:visible', value)
  })

  // 初始化表单数据
  const initFormData = () => ({
    basicInfo: {
      materialCode: '',
      materialName: '',
      englishName: '',
      specification: '',
      category: '' as MaterialMasterCategory,
      manufacturer: '',
      brand: '',
      model: '',
      status: 'active' as const,
      description: '',
      remarks: ''
    },
    technicalParams: {
      dimensions: {
        length: undefined,
        width: undefined,
        height: undefined,
        thickness: undefined,
        diameter: undefined
      },
      material: '',
      purity: undefined,
      waferInfo: {
        waferSize: undefined,
        thickness: undefined,
        orientation: '',
        resistivity: undefined,
        dieSize: ''
      },
      packageInfo: {
        packageType: undefined,
        pinCount: undefined,
        pitchSize: undefined,
        bodySize: ''
      },
      wirebondInfo: {
        wireType: undefined,
        diameter: undefined,
        tensileStrength: undefined,
        elongation: undefined,
        purity: undefined
      },
      electricalProperties: {
        resistance: undefined,
        conductivity: undefined,
        voltage: undefined,
        current: undefined,
        power: undefined
      },
      environmentalRequirements: {
        temperatureMin: undefined,
        temperatureMax: undefined,
        humidityMax: undefined,
        storageConditions: [] as string[]
      }
    },
    suppliers: [] as Omit<SupplierInfo, 'supplierId'>[],
    qualityInfo: {
      standard: '',
      specification: '',
      inspectionMethod: '',
      acceptanceCriteria: '',
      certifications: [] as string[],
      reliability: {
        mtbf: undefined,
        lifeTest: '',
        qualificationStatus: ''
      }
    },
    stockInfo: {
      unitOfMeasure: 'PCS',
      safetyStock: 0,
      maxStock: 0,
      reorderPoint: 0,
      leadTime: 30,
      storageRequirements: [] as string[],
      shelfLife: 9999,
      storageLocation: ''
    },
    costInfo: {
      standardCost: 0,
      currentPrice: 0,
      currency: 'USD',
      priceUnit: 'PCS',
      lastPriceUpdate: new Date().toISOString()
    }
  })

  // 表单数据
  const formData = reactive(initFormData())

  // 表单验证规则
  const formRules: FormRules = {
    'basicInfo.materialCode': [{ required: true, message: '请输入物料编码', trigger: 'blur' }],
    'basicInfo.materialName': [{ required: true, message: '请输入物料名称', trigger: 'blur' }],
    'basicInfo.specification': [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
    'basicInfo.category': [{ required: true, message: '请选择物料分类', trigger: 'change' }],
    'basicInfo.manufacturer': [{ required: true, message: '请输入制造商', trigger: 'blur' }],
    'basicInfo.status': [{ required: true, message: '请选择物料状态', trigger: 'change' }],
    'stockInfo.unitOfMeasure': [{ required: true, message: '请选择计量单位', trigger: 'change' }],
    'costInfo.currency': [{ required: true, message: '请选择货币单位', trigger: 'change' }],
    'costInfo.priceUnit': [{ required: true, message: '请选择价格单位', trigger: 'change' }]
  }

  // 监听对话框打开，初始化表单数据
  watch(
    () => props.visible,
    visible => {
      if (visible) {
        nextTick(() => {
          if (props.material && props.mode === 'edit') {
            // 编辑模式，填充现有数据
            loadMaterialData(props.material)
          } else {
            // 新增模式，重置表单
            resetForm()
          }
        })
      }
    }
  )

  // 加载物料数据到表单
  const loadMaterialData = (material: MaterialMaster) => {
    Object.assign(formData.basicInfo, material.basicInfo)
    Object.assign(formData.technicalParams, material.technicalParams)
    formData.suppliers = material.suppliers.map(s => ({ ...s })) // 移除supplierId
    Object.assign(formData.qualityInfo, material.qualityInfo)
    Object.assign(formData.stockInfo, material.stockInfo)
    Object.assign(formData.costInfo, material.costInfo)
  }

  // 生成物料编码
  const generateMaterialCode = () => {
    if (formData.basicInfo.category) {
      formData.basicInfo.materialCode = generateNewMaterialCode(formData.basicInfo.category)
    } else {
      ElMessage.warning('请先选择物料分类')
    }
  }

  // 处理分类变化
  const handleCategoryChange = (category: MaterialMasterCategory) => {
    // 根据分类自动生成编码（如果还没有编码）
    if (!formData.basicInfo.materialCode && props.mode === 'create') {
      formData.basicInfo.materialCode = generateNewMaterialCode(category)
    }
  }

  // 添加供应商
  const addSupplier = () => {
    const newSupplier: Omit<SupplierInfo, 'supplierId'> = {
      supplierName: '',
      contactPerson: '',
      contactPhone: '',
      contactEmail: '',
      leadTime: 30,
      minOrderQty: 1000,
      isPrimary: formData.suppliers.length === 0, // 第一个供应商默认为主供应商
      qualityRating: 90,
      deliveryRating: 90,
      certifications: []
    }
    formData.suppliers.push(newSupplier)
  }

  // 删除供应商
  const removeSupplier = (index: number) => {
    const removedSupplier = formData.suppliers[index]
    formData.suppliers.splice(index, 1)

    // 如果删除的是主供应商，将第一个供应商设为主供应商
    if (removedSupplier.isPrimary && formData.suppliers.length > 0) {
      formData.suppliers[0].isPrimary = true
    }
  }

  // 处理主供应商变更
  const handlePrimaryChange = (index: number) => {
    if (formData.suppliers[index].isPrimary) {
      // 将其他供应商设为非主供应商
      formData.suppliers.forEach((supplier, i) => {
        if (i !== index) {
          supplier.isPrimary = false
        }
      })
    } else {
      // 如果取消主供应商，检查是否有其他主供应商
      const hasPrimary = formData.suppliers.some((supplier, i) => i !== index && supplier.isPrimary)
      if (!hasPrimary && formData.suppliers.length > 0) {
        // 没有主供应商，将第一个设为主供应商
        formData.suppliers[0].isPrimary = true
        ElMessage.warning('至少需要一个主供应商')
      }
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, initFormData())
    formRef.value?.resetFields()
    activeTab.value = 'basic'
  }

  // 保存表单
  const handleSave = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()

      // 检查是否有供应商
      if (formData.suppliers.length === 0) {
        ElMessage.warning('请至少添加一个供应商')
        activeTab.value = 'suppliers'
        return
      }

      // 检查是否有主供应商
      const hasPrimary = formData.suppliers.some(s => s.isPrimary)
      if (!hasPrimary) {
        ElMessage.warning('请设置一个主供应商')
        activeTab.value = 'suppliers'
        return
      }

      saving.value = true

      // 构建保存数据
      const saveData: CreateMaterialMasterData | UpdateMaterialMasterData = {
        basicInfo: { ...formData.basicInfo },
        technicalParams: { ...formData.technicalParams },
        suppliers: formData.suppliers.map(s => ({ ...s })),
        qualityInfo: { ...formData.qualityInfo },
        stockInfo: { ...formData.stockInfo },
        costInfo: { ...formData.costInfo }
      }

      // 如果是编辑模式，添加ID
      if (props.mode === 'edit' && props.material) {
        ;(saveData as UpdateMaterialMasterData).id = props.material.id
      }

      emit('save', saveData)
    } catch (error) {
      console.error('表单验证失败:', error)
      ElMessage.error('请检查表单填写是否完整')
    } finally {
      saving.value = false
    }
  }
</script>

<style lang="scss" scoped>
  .param-section {
    margin-bottom: 24px;

    h4 {
      padding-left: 12px;
      margin: 0 0 16px;
      font-size: 16px;
      font-weight: 500;
      color: var(--color-text-primary);
      border-left: 3px solid var(--color-primary);
    }
  }

  .category-option {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .wire-option {
    display: flex;
    gap: 8px;
    align-items: center;

    .wire-color {
      width: 12px;
      height: 12px;
      border: 1px solid var(--color-border-light);
      border-radius: 50%;
    }
  }

  .suppliers-section {
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: var(--color-text-primary);
      }
    }

    .empty-suppliers {
      padding: 40px 0;
      text-align: center;
    }

    .suppliers-list {
      .supplier-card {
        margin-bottom: 16px;

        :deep(.el-card__header) {
          padding: 16px 20px;
        }

        .supplier-header {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .supplier-actions {
            display: flex;
            gap: 12px;
            align-items: center;
          }
        }
      }
    }
  }

  .stock-cost-section,
  .cost-section {
    margin-bottom: 24px;

    h4 {
      padding-left: 12px;
      margin: 0 0 16px;
      font-size: 16px;
      font-weight: 500;
      color: var(--color-text-primary);
      border-left: 3px solid var(--color-primary);
    }
  }

  .reliability-section {
    padding-top: 24px;
    margin-top: 24px;
    border-top: 1px solid var(--color-border-lighter);

    h4 {
      padding-left: 12px;
      margin: 0 0 16px;
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-primary);
      border-left: 3px solid var(--color-success);
    }
  }

  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 10px;
    }
  }

  :deep(.el-tabs__content) {
    padding: 20px 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  // 响应式设计
  @media (width <= 1200px) {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto !important;
    }

    .el-col {
      margin-bottom: 16px;
    }

    .suppliers-list .supplier-card {
      .supplier-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;

        .supplier-actions {
          justify-content: space-between;
          width: 100%;
        }
      }
    }
  }
</style>
