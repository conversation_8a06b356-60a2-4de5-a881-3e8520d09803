<template>
  <div class="quotation-form">
    <!-- 页面头部 -->
    <div class="quotation-form__header">
      <div class="quotation-form__title">
        <h2>智能报价单生成</h2>
        <p class="subtitle">基于询价信息自动计算成本并生成专业报价单</p>
      </div>
      <div class="quotation-form__actions">
        <el-button @click="handleBack">
          <el-icon><ArrowLeft /></el-icon>
          返回询价列表
        </el-button>
      </div>
    </div>

    <!-- 进度步骤 -->
    <div class="quotation-form__steps">
      <el-steps :active="activeStep"
align-center finish-status="success"
>
        <el-step title="询价回顾"
description="确认客户需求信息"
/>
        <el-step title="成本分析"
description="智能计算各项成本"
/>
        <el-step title="报价配置"
description="配置利润和价格策略"
/>
        <el-step title="预览发送"
description="预览并发送报价单"
/>
      </el-steps>
    </div>

    <!-- 主要内容区域 -->
    <div class="quotation-form__content">
      <!-- 步骤1: 询价回顾 -->
      <div v-if="activeStep === 0"
class="step-content"
>
        <div class="step-title">
          <h3>询价信息回顾</h3>
          <p>请确认以下客户询价信息是否准确</p>
        </div>

        <div class="info-cards">
          <!-- 客户信息卡片 -->
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><User /></el-icon>
                <span>客户信息</span>
              </div>
            </template>
            <div class="info-item">
              <label>客户名称：</label>
              <span>{{ inquiryData.customer.name }}</span>
            </div>
            <div class="info-item">
              <label>联系人：</label>
              <span>{{ inquiryData.customer.contact.name }}</span>
            </div>
            <div class="info-item">
              <label>联系方式：</label>
              <span>
                {{ inquiryData.customer.contact.email }} / {{ inquiryData.customer.contact.phone }}
              </span>
            </div>
            <div class="info-item">
              <label>行业类型：</label>
              <el-tag>{{ getIndustryText(inquiryData.customer.industryType) }}</el-tag>
            </div>
          </el-card>

          <!-- 产品信息卡片 -->
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Box /></el-icon>
                <span>产品信息</span>
              </div>
            </template>
            <div class="info-item">
              <label>产品名称：</label>
              <span>{{ inquiryData.productInfo.productName }}</span>
            </div>
            <div class="info-item">
              <label>封装类型：</label>
              <el-tag type="primary">
                {{ inquiryData.productInfo.packageType }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>数量需求：</label>
              <span>
                {{ inquiryData.productInfo.quantity.toLocaleString() }}
                {{ inquiryData.productInfo.quantityLevel }}pcs
              </span>
            </div>
            <div class="info-item">
              <label>封装规格：</label>
              <span>
                {{ inquiryData.productInfo.dieSize }} / {{ inquiryData.productInfo.leadCount }}引脚
              </span>
            </div>
          </el-card>

          <!-- 测试要求卡片 -->
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><CircuitBoard /></el-icon>
                <span>测试要求</span>
              </div>
            </template>
            <div class="info-item">
              <label>测试类型：</label>
              <el-tag type="warning">
                {{ getTestTypeText(inquiryData.testRequirements.testType) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>良率要求：</label>
              <span>{{ inquiryData.testRequirements.yieldRequirement }}%</span>
            </div>
            <div class="info-item">
              <label>CP测试要求：</label>
              <span>{{ inquiryData.testRequirements.cpTestRequirement || '无特殊要求' }}</span>
            </div>
            <div class="info-item">
              <label>FT测试要求：</label>
              <span>{{ inquiryData.testRequirements.ftTestRequirement || '无特殊要求' }}</span>
            </div>
          </el-card>

          <!-- 时间要求卡片 -->
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Clock /></el-icon>
                <span>时间要求</span>
              </div>
            </template>
            <div class="info-item">
              <label>询价日期：</label>
              <span>{{ formatDate(inquiryData.schedule.inquiryDate) }}</span>
            </div>
            <div class="info-item">
              <label>期望报价日期：</label>
              <span>{{ formatDate(inquiryData.schedule.expectedQuoteDate) }}</span>
            </div>
            <div class="info-item">
              <label>目标交期：</label>
              <span>{{ formatDate(inquiryData.schedule.targetDeliveryDate) }}</span>
            </div>
            <div class="info-item">
              <label>紧急程度：</label>
              <el-tag :type="getPriorityType(inquiryData.schedule.urgencyLevel)">
                {{ getPriorityText(inquiryData.schedule.urgencyLevel) }}
              </el-tag>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 步骤2: 成本分析 -->
      <div v-if="activeStep === 1"
class="step-content"
>
        <div class="step-title">
          <h3>智能成本分析</h3>
          <p>基于产品规格和测试要求自动计算各项成本</p>
        </div>

        <div class="cost-analysis">
          <!-- 成本概览 -->
          <el-card class="cost-overview">
            <template #header>
              <div class="card-header">
                <el-icon><TrendCharts /></el-icon>
                <span>成本构成概览</span>
                <el-button
                  type="primary"
                  size="small"
                  :loading="calculating"
                  @click="calculateCosts"
                >
                  重新计算
                </el-button>
              </div>
            </template>

            <div class="cost-chart">
              <div class="cost-pie">
                <!-- 这里可以集成ECharts饼图 -->
                <div class="cost-placeholder">
                  <p>成本构成分析图</p>
                  <p class="cost-total">总成本：¥{{ totalCost.toFixed(2) }}/Kpcs</p>
                </div>
              </div>
              <div class="cost-breakdown">
                <div v-for="item in costBreakdown" class="cost-item" :key="item.name">
                  <div
class="cost-item__color" :style="{ backgroundColor: item.color }"
/>
                  <div class="cost-item__info">
                    <span class="cost-item__name">{{ item.name }}</span>
                    <span class="cost-item__value">¥{{ item.value.toFixed(2) }}</span>
                    <span class="cost-item__percent">{{ item.percentage.toFixed(1) }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 详细成本计算 -->
          <div class="cost-details">
            <!-- CP测试成本 -->
            <el-card class="cost-card">
              <template #header>
                <div class="card-header">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>CP测试成本</span>
                </div>
              </template>
              <el-form :model="costData.cpTesting"
label-width="140px"
>
                <el-form-item label="Probe Card成本">
                  <el-input-number v-model="costData.cpTesting.probeCardCost"
:precision="2"
/>
                  <span class="unit">元/K次</span>
                </el-form-item>
                <el-form-item label="测试时间成本">
                  <el-input-number v-model="costData.cpTesting.testTimeCost"
:precision="2"
/>
                  <span class="unit">元/秒</span>
                </el-form-item>
                <el-form-item label="设备折旧成本">
                  <el-input-number v-model="costData.cpTesting.equipmentCost"
:precision="2"
/>
                  <span class="unit">元/小时</span>
                </el-form-item>
                <el-form-item label="人工成本">
                  <el-input-number v-model="costData.cpTesting.laborCost"
:precision="2"
/>
                  <span class="unit">元/Kpcs</span>
                </el-form-item>
                <div class="cost-subtotal">
                  CP测试小计：¥{{ cpTestingSubtotal.toFixed(2) }}/Kpcs
                </div>
              </el-form>
            </el-card>

            <!-- Assembly封装成本 -->
            <el-card class="cost-card">
              <template #header>
                <div class="card-header">
                  <el-icon><Setting /></el-icon>
                  <span>Assembly封装成本</span>
                </div>
              </template>
              <el-form :model="costData.assembly"
label-width="140px"
>
                <el-form-item label="Die成本">
                  <el-input-number v-model="costData.assembly.dieCost"
:precision="2"
/>
                  <span class="unit">元/pcs</span>
                </el-form-item>
                <el-form-item label="基板成本">
                  <el-input-number v-model="costData.assembly.substrateCost"
:precision="2"
/>
                  <span class="unit">元/pcs</span>
                </el-form-item>
                <el-form-item label="金线成本">
                  <el-input-number v-model="costData.assembly.wireBondCost"
:precision="2"
/>
                  <span class="unit">元/pcs</span>
                </el-form-item>
                <el-form-item label="塑封成本">
                  <el-input-number v-model="costData.assembly.moldingCost"
:precision="2"
/>
                  <span class="unit">元/pcs</span>
                </el-form-item>
                <el-form-item label="加工费用">
                  <el-input-number v-model="costData.assembly.processingCost"
:precision="2"
/>
                  <span class="unit">元/Kpcs</span>
                </el-form-item>
                <div class="cost-subtotal">
                  Assembly小计：¥{{ assemblySubtotal.toFixed(2) }}/Kpcs
                </div>
              </el-form>
            </el-card>

            <!-- FT测试成本 -->
            <el-card class="cost-card">
              <template #header>
                <div class="card-header">
                  <el-icon><Monitor /></el-icon>
                  <span>FT最终测试成本</span>
                </div>
              </template>
              <el-form :model="costData.ftTesting"
label-width="140px"
>
                <el-form-item label="Handler成本">
                  <el-input-number v-model="costData.ftTesting.handlerCost"
:precision="2"
/>
                  <span class="unit">元/小时</span>
                </el-form-item>
                <el-form-item label="测试时间成本">
                  <el-input-number v-model="costData.ftTesting.testTimeCost"
:precision="2"
/>
                  <span class="unit">元/秒</span>
                </el-form-item>
                <el-form-item label="Burn-in成本">
                  <el-input-number v-model="costData.ftTesting.burnInCost"
:precision="2"
/>
                  <span class="unit">元/Kpcs</span>
                </el-form-item>
                <el-form-item label="筛选成本">
                  <el-input-number v-model="costData.ftTesting.screeningCost"
:precision="2"
/>
                  <span class="unit">元/Kpcs</span>
                </el-form-item>
                <div class="cost-subtotal">
                  FT测试小计：¥{{ ftTestingSubtotal.toFixed(2) }}/Kpcs
                </div>
              </el-form>
            </el-card>

            <!-- 管理费用和其他成本 -->
            <el-card class="cost-card">
              <template #header>
                <div class="card-header">
                  <el-icon><Management /></el-icon>
                  <span>管理费用</span>
                </div>
              </template>
              <el-form :model="costData.management"
label-width="140px"
>
                <el-form-item label="质量管理">
                  <el-input-number v-model="costData.management.qualityManagement"
:precision="2"
/>
                  <span class="unit">元/Kpcs</span>
                </el-form-item>
                <el-form-item label="物流包装">
                  <el-input-number v-model="costData.management.logistics"
:precision="2"
/>
                  <span class="unit">元/Kpcs</span>
                </el-form-item>
                <el-form-item label="管理费用率">
                  <el-input-number
                    v-model="costData.management.managementRate"
                    :precision="1"
                    :max="50"
                  />
                  <span class="unit">%</span>
                </el-form-item>
                <el-form-item label="风险费用">
                  <el-input-number v-model="costData.management.riskCost"
:precision="2"
/>
                  <span class="unit">元/Kpcs</span>
                </el-form-item>
                <div class="cost-subtotal">
                  管理费用小计：¥{{ managementSubtotal.toFixed(2) }}/Kpcs
                </div>
              </el-form>
            </el-card>
          </div>
        </div>
      </div>

      <!-- 步骤3: 报价配置 -->
      <div v-if="activeStep === 2"
class="step-content"
>
        <div class="step-title">
          <h3>报价策略配置</h3>
          <p>设置利润率和价格策略，支持阶梯报价</p>
        </div>

        <div class="pricing-config">
          <!-- 利润率配置 -->
          <el-card class="pricing-card">
            <template #header>
              <div class="card-header">
                <el-icon><Coin /></el-icon>
                <span>利润率配置</span>
              </div>
            </template>
            <el-form :model="pricingData"
label-width="120px"
>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="目标利润率">
                    <el-input-number
                      v-model="pricingData.targetProfitRate"
                      :precision="1"
                      :max="100"
                      @change="updatePricing"
                    />
                    <span class="unit">%</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最低利润率">
                    <el-input-number
                      v-model="pricingData.minProfitRate"
                      :precision="1"
                      :max="100"
                      @change="updatePricing"
                    />
                    <span class="unit">%</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="价格策略">
                <el-radio-group v-model="pricingData.pricingStrategy"
@change="updatePricing"
>
                  <el-radio value="standard">标准定价</el-radio>
                  <el-radio value="competitive">竞争定价</el-radio>
                  <el-radio value="premium">高端定价</el-radio>
                  <el-radio value="penetration">渗透定价</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 阶梯报价 -->
          <el-card class="pricing-card">
            <template #header>
              <div class="card-header">
                <el-icon><DataLine /></el-icon>
                <span>阶梯报价设置</span>
                <el-switch v-model="pricingData.enableTieredPricing" @change="updatePricing" />
              </div>
            </template>
            <div v-if="pricingData.enableTieredPricing">
              <el-table :data="pricingData.tieredPricing"
style="width: 100%"
>
                <el-table-column label="数量区间"
width="200"
>
                  <template #default="scope">
                    <span>
                      {{ scope.row.minQuantity.toLocaleString() }}K -
                      {{ scope.row.maxQuantity.toLocaleString() }}K pcs
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="单价 (元/Kpcs)"
width="150"
>
                  <template #default="scope">
                    <el-input-number
                      v-model="scope.row.unitPrice"
                      :precision="2"
                      size="small"
                      @change="updatePricing"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="利润率"
width="100"
>
                  <template #default="scope">
                    <span>{{ scope.row.profitRate.toFixed(1) }}%</span>
                  </template>
                </el-table-column>
                <el-table-column label="折扣"
width="100"
>
                  <template #default="scope">
                    <span>{{ scope.row.discount.toFixed(1) }}%</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                    <el-button type="danger" size="small" @click="removeTier(scope.$index)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="tier-actions">
                <el-button @click="addTier">添加价格档位</el-button>
              </div>
            </div>
            <div v-else
class="single-price"
>
              <div class="price-display">
                <span class="price-label">统一单价：</span>
                <span class="price-value">¥{{ finalUnitPrice.toFixed(2) }} / Kpcs</span>
                <span class="profit-info">(利润率: {{ actualProfitRate.toFixed(1) }}%)</span>
              </div>
            </div>
          </el-card>

          <!-- 质量等级定价 -->
          <el-card class="pricing-card">
            <template #header>
              <div class="card-header">
                <el-icon><Medal /></el-icon>
                <span>质量等级差异定价</span>
              </div>
            </template>
            <el-table :data="qualityGradePricing"
style="width: 100%"
>
              <el-table-column label="质量等级"
width="120"
>
                <template #default="scope">
                  <el-tag :type="scope.row.type">
                    {{ scope.row.grade }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="应用场景"
width="150"
>
                <template #default="scope">
                  <span>{{ scope.row.application }}</span>
                </template>
              </el-table-column>
              <el-table-column label="价格系数"
width="120"
>
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.priceMultiplier"
                    :precision="2"
                    :step="0.1"
                    size="small"
                    @change="updatePricing"
                  />
                </template>
              </el-table-column>
              <el-table-column label="调整后单价 (元/Kpcs)">
                <template #default="scope">
                  <span class="adjusted-price">
                    ¥{{ (finalUnitPrice * scope.row.priceMultiplier).toFixed(2) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>

          <!-- 付款条件和其他商务条款 -->
          <el-card class="pricing-card">
            <template #header>
              <div class="card-header">
                <el-icon><DocumentChecked /></el-icon>
                <span>商务条款</span>
              </div>
            </template>
            <el-form :model="pricingData"
label-width="120px"
>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="付款条件">
                    <el-select v-model="pricingData.paymentTerms"
style="width: 100%"
>
                      <el-option label="T/T 30天"
value="TT_30"
/>
                      <el-option label="T/T 60天"
value="TT_60"
/>
                      <el-option label="T/T 90天"
value="TT_90"
/>
                      <el-option label="月结"
value="MONTHLY"
/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="报价币种">
                    <el-select v-model="pricingData.currency"
style="width: 100%"
>
                      <el-option label="人民币 (CNY)"
value="CNY"
/>
                      <el-option label="美元 (USD)"
value="USD"
/>
                      <el-option label="欧元 (EUR)"
value="EUR"
/>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="报价有效期">
                    <el-input-number v-model="pricingData.validityDays"
:min="1"
/>
                    <span class="unit">天</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="交期承诺">
                    <el-input-number v-model="pricingData.deliveryDays"
:min="1"
/>
                    <span class="unit">天</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="特殊条款">
                <el-input
                  v-model="pricingData.specialTerms"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入特殊商务条款..."
                />
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </div>

      <!-- 步骤4: 预览发送 -->
      <div v-if="activeStep === 3"
class="step-content"
>
        <div class="step-title">
          <h3>报价单预览</h3>
          <p>最终确认报价单内容并发送给客户</p>
        </div>

        <div class="quotation-preview">
          <el-card class="preview-card">
            <template #header>
              <div class="card-header">
                <el-icon><Document /></el-icon>
                <span>报价单预览</span>
                <el-button type="primary"
@click="generatePDF"
>
                  <el-icon><Download /></el-icon>
                  生成PDF
                </el-button>
              </div>
            </template>

            <!-- 报价单内容 -->
            <div class="quotation-document">
              <div class="doc-header">
                <h2>产品封装测试服务报价单</h2>
                <div class="doc-info">
                  <div>报价单号：{{ quotationNumber }}</div>
                  <div>日期：{{ formatDate(new Date()) }}</div>
                  <div>有效期：{{ pricingData.validityDays }}天</div>
                </div>
              </div>

              <div class="doc-customer">
                <h3>客户信息</h3>
                <div class="customer-details">
                  <div>
                    <strong>客户名称：</strong>
                    {{ inquiryData.customer.name }}
                  </div>
                  <div>
                    <strong>联系人：</strong>
                    {{ inquiryData.customer.contact.name }}
                  </div>
                  <div>
                    <strong>联系方式：</strong>
                    {{ inquiryData.customer.contact.email }} /
                    {{ inquiryData.customer.contact.phone }}
                  </div>
                </div>
              </div>

              <div class="doc-product">
                <h3>产品信息</h3>
                <el-table :data="[inquiryData.productInfo]"
border
>
                  <el-table-column label="产品名称"
prop="productName"
/>
                  <el-table-column label="封装类型"
prop="packageType"
/>
                  <el-table-column label="数量"
:formatter="formatQuantity"
/>
                  <el-table-column label="引脚数"
prop="leadCount"
/>
                  <el-table-column label="芯片尺寸"
prop="dieSize"
/>
                </el-table>
              </div>

              <div class="doc-pricing">
                <h3>报价明细</h3>
                <div v-if="pricingData.enableTieredPricing">
                  <el-table :data="pricingData.tieredPricing"
border
>
                    <el-table-column label="数量区间 (Kpcs)">
                      <template #default="scope">
                        {{ scope.row.minQuantity.toLocaleString() }} -
                        {{ scope.row.maxQuantity.toLocaleString() }}
                      </template>
                    </el-table-column>
                    <el-table-column label="单价 (元/Kpcs)">
                      <template #default="scope">
¥{{ scope.row.unitPrice.toFixed(2) }}
</template>
                    </el-table-column>
                    <el-table-column label="折扣">
                      <template #default="scope">
{{ scope.row.discount.toFixed(1) }}%
</template>
                    </el-table-column>
                  </el-table>
                </div>
                <div v-else
class="single-pricing"
>
                  <div class="price-item">
                    <span>统一单价：</span>
                    <span class="price">¥{{ finalUnitPrice.toFixed(2) }} / Kpcs</span>
                  </div>
                </div>

                <div class="pricing-summary">
                  <div class="summary-item">
                    <span>总金额：</span>
                    <span class="amount">¥{{ totalQuotationAmount.toLocaleString() }}</span>
                  </div>
                  <div class="summary-item">
                    <span>币种：</span>
                    <span>{{ pricingData.currency }}</span>
                  </div>
                </div>
              </div>

              <div class="doc-terms">
                <h3>商务条款</h3>
                <ul class="terms-list">
                  <li>
                    <strong>付款条件：</strong>
                    {{ getPaymentTermsText(pricingData.paymentTerms) }}
                  </li>
                  <li>
                    <strong>交期：</strong>
                    {{ pricingData.deliveryDays }}天
                  </li>
                  <li>
                    <strong>质量标准：</strong>
                    IATF16949、JEDEC标准
                  </li>
                  <li>
                    <strong>良率保证：</strong>
                    {{ inquiryData.testRequirements.yieldRequirement }}%
                  </li>
                  <li v-if="pricingData.specialTerms">
                    <strong>特殊条款：</strong>
                    {{ pricingData.specialTerms }}
                  </li>
                </ul>
              </div>
            </div>
          </el-card>

          <!-- 发送配置 -->
          <el-card class="send-config">
            <template #header>
              <div class="card-header">
                <el-icon><Message /></el-icon>
                <span>发送配置</span>
              </div>
            </template>
            <el-form :model="sendConfig"
label-width="100px"
>
              <el-form-item label="收件人">
                <el-input v-model="sendConfig.recipients"
placeholder="多个邮箱用分号分隔"
/>
              </el-form-item>
              <el-form-item label="邮件主题">
                <el-input v-model="sendConfig.subject" />
              </el-form-item>
              <el-form-item label="邮件正文">
                <el-input
                  v-model="sendConfig.content"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入邮件正文内容..."
                />
              </el-form-item>
              <el-form-item label="发送方式">
                <el-checkbox-group v-model="sendConfig.sendMethods">
                  <el-checkbox value="email">邮件发送</el-checkbox>
                  <el-checkbox value="system">系统通知</el-checkbox>
                  <el-checkbox value="sms">短信通知</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="quotation-form__footer">
      <el-button v-if="activeStep > 0" @click="previousStep" :disabled="submitting">
        上一步
      </el-button>
      <el-button v-if="activeStep < 3" type="primary" :disabled="!canProceed" @click="nextStep">
        下一步
      </el-button>
      <el-button
        v-if="activeStep === 3"
        type="success"
        :loading="submitting"
        @click="submitQuotation"
      >
        发送报价单
      </el-button>
      <el-button @click="handleSaveDraft">
保存草稿
</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  // Element Plus组件通过unplugin-auto-import自动导入
  import type {
    CustomerInquiry,
    PackageType,
    ProductType as ProductTypeEnum,
    TestRequirement,
    OrderPriority,
    CurrencyType
  } from '@/types/order'

  // 路由相关
  const router = useRouter()
  const route = useRoute()

  // 响应式数据
  const activeStep = ref(0)
  const calculating = ref(false)
  const submitting = ref(false)

  // 模拟询价数据
  const inquiryData = ref<CustomerInquiry>({
    id: (route.params.id as string) || 'INQ001',
    inquiryNumber: 'INQ-2024-001',
    customerId: 'CUST001',
    customer: {
      id: 'CUST001',
      name: '华为技术有限公司',
      code: 'HUAWEI',
      contact: {
        name: '张工',
        phone: '+86-138-0000-1234',
        email: '<EMAIL>',
        department: '采购部'
      },
      industryType: 'communication' as ProductTypeEnum
    },
    productInfo: {
      productName: 'HiSilicon 5G射频芯片',
      productCode: 'HS-RF5G-001',
      productType: 'communication' as ProductTypeEnum,
      packageType: 'QFN' as PackageType,
      quantity: 1000,
      quantityLevel: 'K' as 'K' | 'M',
      waferSize: 8,
      dieSize: '3.2×3.2mm',
      leadCount: 48,
      specifications: '5G射频前端芯片，工作频率28GHz，功率增益30dB',
      datasheet: ''
    },
    testRequirements: {
      testType: 'cp_ft' as TestRequirement,
      cpTestRequirement: 'RF参数测试，S参数测试',
      ftTestRequirement: '功率测试，频率响应测试',
      reliabilityTest: true,
      yieldRequirement: 99.5
    },
    schedule: {
      inquiryDate: '2024-01-15',
      expectedQuoteDate: '2024-01-20',
      targetDeliveryDate: '2024-03-15',
      urgencyLevel: 'high' as OrderPriority
    },
    businessInfo: {
      budgetRange: {
        min: 180,
        max: 220,
        currency: 'CNY' as CurrencyType
      },
      paymentTerms: 'T/T 30天'
    },
    status: 'evaluating' as any,
    priority: 'high' as OrderPriority,
    assignedSalesManager: '李经理',
    assignedEngineer: '王工程师',
    evaluation: {
      technicalFeasibility: 'feasible' as any,
      capacityAvailability: 'available' as any
    },
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T09:00:00Z',
    createdBy: 'system'
  })

  // 成本数据
  const costData = ref({
    cpTesting: {
      probeCardCost: 2.5,
      testTimeCost: 0.15,
      equipmentCost: 120,
      laborCost: 5.0
    },
    assembly: {
      dieCost: 45.0,
      substrateCost: 15.0,
      wireBondCost: 8.0,
      moldingCost: 12.0,
      processingCost: 25.0
    },
    ftTesting: {
      handlerCost: 80,
      testTimeCost: 0.2,
      burnInCost: 15.0,
      screeningCost: 8.0
    },
    management: {
      qualityManagement: 5.0,
      logistics: 3.0,
      managementRate: 15.0,
      riskCost: 4.0
    }
  })

  // 报价数据
  const pricingData = ref({
    targetProfitRate: 25.0,
    minProfitRate: 15.0,
    pricingStrategy: 'standard' as 'standard' | 'competitive' | 'premium' | 'penetration',
    enableTieredPricing: false,
    tieredPricing: [
      { minQuantity: 1, maxQuantity: 100, unitPrice: 205.5, profitRate: 25.0, discount: 0 },
      { minQuantity: 100, maxQuantity: 500, unitPrice: 195.25, profitRate: 23.0, discount: 5.0 },
      { minQuantity: 500, maxQuantity: 9999, unitPrice: 185.0, profitRate: 21.0, discount: 10.0 }
    ],
    paymentTerms: 'TT_30' as string,
    currency: 'CNY' as CurrencyType,
    validityDays: 15,
    deliveryDays: 60,
    specialTerms: ''
  })

  // 质量等级定价
  const qualityGradePricing = ref([
    {
      grade: '汽车级',
      application: '汽车电子',
      type: 'danger',
      priceMultiplier: 1.3
    },
    {
      grade: '工业级',
      application: '工业控制',
      type: 'warning',
      priceMultiplier: 1.1
    },
    {
      grade: '消费级',
      application: '消费电子',
      type: 'success',
      priceMultiplier: 1.0
    }
  ])

  // 发送配置
  const sendConfig = ref({
    recipients: '<EMAIL>',
    subject: `【产品封装测试服务报价单】- ${inquiryData.value.productInfo.productName}`,
    content: `尊敬的${inquiryData.value.customer.contact.name}：

您好！

感谢您对我司的信任，现就您咨询的"${inquiryData.value.productInfo.productName}"产品封装测试服务事宜，提供正式报价单。

请查阅附件中的详细报价信息，如有任何疑问，请随时联系我们。

期待与贵公司的进一步合作！

此致
敬礼！`,
    sendMethods: ['email', 'system']
  })

  // 计算属性
  const cpTestingSubtotal = computed(() => {
    const { probeCardCost, testTimeCost, equipmentCost, laborCost } = costData.value.cpTesting
    return probeCardCost + testTimeCost * 60 + (equipmentCost / 1000) * 8 + laborCost
  })

  const assemblySubtotal = computed(() => {
    const { dieCost, substrateCost, wireBondCost, moldingCost, processingCost } =
      costData.value.assembly
    return ((dieCost + substrateCost + wireBondCost + moldingCost) * 1000) / 1000 + processingCost
  })

  const ftTestingSubtotal = computed(() => {
    const { handlerCost, testTimeCost, burnInCost, screeningCost } = costData.value.ftTesting
    return (handlerCost / 1000) * 6 + testTimeCost * 45 + burnInCost + screeningCost
  })

  const managementSubtotal = computed(() => {
    const directCost = cpTestingSubtotal.value + assemblySubtotal.value + ftTestingSubtotal.value
    const { qualityManagement, logistics, managementRate, riskCost } = costData.value.management
    return qualityManagement + logistics + (directCost * managementRate) / 100 + riskCost
  })

  const totalCost = computed(() => {
    return (
      cpTestingSubtotal.value +
      assemblySubtotal.value +
      ftTestingSubtotal.value +
      managementSubtotal.value
    )
  })

  const costBreakdown = computed(() => {
    const total = totalCost.value
    return [
      {
        name: 'CP测试',
        value: cpTestingSubtotal.value,
        percentage: (cpTestingSubtotal.value / total) * 100,
        color: '#409EFF'
      },
      {
        name: '封装工艺',
        value: assemblySubtotal.value,
        percentage: (assemblySubtotal.value / total) * 100,
        color: '#67C23A'
      },
      {
        name: 'FT测试',
        value: ftTestingSubtotal.value,
        percentage: (ftTestingSubtotal.value / total) * 100,
        color: '#E6A23C'
      },
      {
        name: '管理费用',
        value: managementSubtotal.value,
        percentage: (managementSubtotal.value / total) * 100,
        color: '#F56C6C'
      }
    ]
  })

  const finalUnitPrice = computed(() => {
    const profitRate = pricingData.value.targetProfitRate / 100
    return totalCost.value / (1 - profitRate)
  })

  const actualProfitRate = computed(() => {
    return ((finalUnitPrice.value - totalCost.value) / finalUnitPrice.value) * 100
  })

  const totalQuotationAmount = computed(() => {
    const quantity = inquiryData.value.productInfo.quantity
    return Math.round(finalUnitPrice.value * quantity)
  })

  const quotationNumber = computed(() => {
    return `QUO-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`
  })

  const canProceed = computed(() => {
    switch (activeStep.value) {
      case 0:
        return true // 询价信息确认
      case 1:
        return totalCost.value > 0 // 成本计算完成
      case 2:
        return pricingData.value.targetProfitRate > 0 // 报价配置完成
      default:
        return true
    }
  })

  // 方法
  const handleBack = () => {
    router.push('/inquiry/list')
  }

  const previousStep = () => {
    if (activeStep.value > 0) {
      activeStep.value--
    }
  }

  const nextStep = () => {
    if (activeStep.value < 3) {
      activeStep.value++
    }
  }

  const calculateCosts = () => {
    calculating.value = true

    // 模拟计算过程
    setTimeout(() => {
      // 基于产品规格自动调整成本参数
      const { packageType, leadCount, productType } = inquiryData.value.productInfo

      // 封装复杂度调整
      let complexityFactor = 1.0
      switch (packageType) {
        case 'BGA':
          complexityFactor = 1.3
          break
        case 'FC':
          complexityFactor = 1.5
          break
        case 'QFN':
          complexityFactor = 1.1
          break
        default:
          complexityFactor = 1.0
      }

      // 引脚数调整
      if (leadCount > 100) complexityFactor *= 1.2
      else if (leadCount > 50) complexityFactor *= 1.1

      // 应用类型调整
      if (productType === 'automotive') complexityFactor *= 1.4
      else if (productType === 'aerospace') complexityFactor *= 1.6

      // 更新成本数据
      costData.value.assembly.processingCost *= complexityFactor
      costData.value.ftTesting.burnInCost *= complexityFactor

      calculating.value = false
      ElMessage.success('成本计算完成')
    }, 1500)
  }

  const updatePricing = () => {
    // 重新计算阶梯报价的利润率和折扣
    if (pricingData.value.enableTieredPricing) {
      pricingData.value.tieredPricing.forEach((tier, index) => {
        const discount = index * 5 // 每个档位5%递增折扣
        tier.discount = discount
        tier.unitPrice = finalUnitPrice.value * (1 - discount / 100)
        tier.profitRate = ((tier.unitPrice - totalCost.value) / tier.unitPrice) * 100
      })
    }
  }

  const addTier = () => {
    const lastTier = pricingData.value.tieredPricing[pricingData.value.tieredPricing.length - 1]
    const newTier = {
      minQuantity: lastTier.maxQuantity,
      maxQuantity: lastTier.maxQuantity * 2,
      unitPrice: finalUnitPrice.value * 0.85, // 15%折扣
      profitRate: 20.0,
      discount: 15.0
    }
    pricingData.value.tieredPricing.push(newTier)
  }

  const removeTier = (index: number) => {
    if (pricingData.value.tieredPricing.length > 1) {
      pricingData.value.tieredPricing.splice(index, 1)
    }
  }

  const generatePDF = () => {
    // 模拟PDF生成
    ElMessage.success('PDF报价单生成中...')
    // 这里可以集成PDF生成库，如jsPDF
  }

  const submitQuotation = async () => {
    try {
      submitting.value = true

      await ElMessageBox.confirm('确认发送报价单吗？发送后无法撤回。', '发送确认', {
        confirmButtonText: '确认发送',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // 模拟提交过程
      await new Promise(resolve => setTimeout(resolve, 2000))

      ElMessage.success('报价单发送成功！')

      // 跳转到询价列表
      router.push('/inquiry/list')
    } catch (error) {
      console.error('发送失败:', error)
    } finally {
      submitting.value = false
    }
  }

  const handleSaveDraft = () => {
    ElMessage.success('草稿已保存')
  }

  // 辅助方法
  const formatDate = (date: string | Date) => {
    const d = new Date(date)
    return d.toLocaleDateString('zh-CN')
  }

  const formatQuantity = (row: any) => {
    return `${row.quantity.toLocaleString()} ${row.quantityLevel}pcs`
  }

  const getIndustryText = (industry: string) => {
    const map: Record<string, string> = {
      communication: '通信设备',
      automotive: '汽车电子',
      consumer: '消费电子',
      industrial: '工业控制',
      medical: '医疗电子'
    }
    return map[industry] || industry
  }

  const getTestTypeText = (testType: string) => {
    const map: Record<string, string> = {
      cp_only: '仅CP测试',
      ft_only: '仅FT测试',
      cp_ft: 'CP+FT测试',
      reliability: '可靠性测试',
      custom: '定制测试'
    }
    return map[testType] || testType
  }

  const getPriorityText = (priority: string) => {
    const map: Record<string, string> = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    }
    return map[priority] || priority
  }

  const getPriorityType = (priority: string) => {
    const map: Record<string, string> = {
      low: 'info',
      medium: 'warning',
      high: 'danger',
      urgent: 'danger'
    }
    return map[priority] || 'info'
  }

  const getPaymentTermsText = (terms: string) => {
    const map: Record<string, string> = {
      TT_30: 'T/T 30天',
      TT_60: 'T/T 60天',
      TT_90: 'T/T 90天',
      MONTHLY: '月结'
    }
    return map[terms] || terms
  }

  // 生命周期
  onMounted(() => {
    // 自动计算初始成本
    calculateCosts()
  })
</script>

<style lang="scss" scoped>
  .quotation-form {
    min-height: 100vh;
    padding: var(--spacing-6);
    background-color: var(--color-bg-page);

    &__header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: var(--spacing-6);
      margin-bottom: var(--spacing-6);
      background: var(--color-bg-primary);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-sm);
    }

    &__title {
      h2 {
        margin: 0 0 var(--spacing-2) 0;
        font-size: 28px;
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .subtitle {
        margin: 0;
        font-size: 16px;
        color: var(--color-text-secondary);
      }
    }

    &__steps {
      padding: var(--spacing-6);
      margin-bottom: var(--spacing-8);
      background: var(--color-bg-primary);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-sm);
    }

    &__content {
      min-height: 600px;
    }

    &__footer {
      display: flex;
      gap: var(--spacing-4);
      justify-content: center;
      padding: var(--spacing-6);
      margin-top: var(--spacing-8);
      background: var(--color-bg-primary);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-sm);
    }
  }

  .step-content {
    .step-title {
      margin-bottom: var(--spacing-6);
      text-align: center;

      h3 {
        margin: 0 0 var(--spacing-2) 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--color-text-primary);
      }

      p {
        margin: 0;
        font-size: 16px;
        color: var(--color-text-secondary);
      }
    }
  }

  // 询价回顾样式
  .info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-6);

    .info-card {
      .card-header {
        display: flex;
        gap: var(--spacing-2);
        align-items: center;
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-3);

        label {
          min-width: 100px;
          font-weight: 500;
          color: var(--color-text-secondary);
        }

        span {
          color: var(--color-text-primary);
        }
      }
    }
  }

  // 成本分析样式
  .cost-analysis {
    .cost-overview {
      margin-bottom: var(--spacing-6);

      .cost-chart {
        display: flex;
        gap: var(--spacing-6);

        .cost-pie {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: center;
          min-height: 300px;
          background: var(--color-bg-secondary);
          border-radius: var(--radius-base);

          .cost-placeholder {
            color: var(--color-text-secondary);
            text-align: center;

            .cost-total {
              margin-top: var(--spacing-4);
              font-size: 24px;
              font-weight: 600;
              color: var(--color-primary);
            }
          }
        }

        .cost-breakdown {
          display: flex;
          flex: 1;
          flex-direction: column;
          gap: var(--spacing-3);

          .cost-item {
            display: flex;
            gap: var(--spacing-3);
            align-items: center;
            padding: var(--spacing-3);
            background: var(--color-bg-secondary);
            border-radius: var(--radius-base);

            &__color {
              width: 16px;
              height: 16px;
              border-radius: 50%;
            }

            &__info {
              display: flex;
              flex: 1;
              align-items: center;
              justify-content: space-between;

              .cost-item__name {
                font-weight: 500;
              }

              .cost-item__value {
                font-weight: 600;
                color: var(--color-primary);
              }

              .cost-item__percent {
                font-size: 14px;
                color: var(--color-text-secondary);
              }
            }
          }
        }
      }
    }

    .cost-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spacing-6);

      .cost-card {
        .card-header {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
          font-weight: 600;
        }

        .unit {
          margin-left: var(--spacing-2);
          font-size: 14px;
          color: var(--color-text-secondary);
        }

        .cost-subtotal {
          padding: var(--spacing-3);
          margin-top: var(--spacing-4);
          font-weight: 600;
          color: var(--color-primary);
          text-align: center;
          background: var(--color-primary-light);
          border-radius: var(--radius-base);
        }
      }
    }
  }

  // 报价配置样式
  .pricing-config {
    display: grid;
    gap: var(--spacing-6);

    .pricing-card {
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;

        > div {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
        }
      }

      .unit {
        margin-left: var(--spacing-2);
        font-size: 14px;
        color: var(--color-text-secondary);
      }

      .single-price {
        .price-display {
          display: flex;
          gap: var(--spacing-4);
          align-items: center;
          padding: var(--spacing-4);
          background: var(--color-primary-light);
          border-radius: var(--radius-base);

          .price-label {
            font-weight: 500;
          }

          .price-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--color-primary);
          }

          .profit-info {
            font-weight: 500;
            color: var(--color-success);
          }
        }
      }

      .tier-actions {
        margin-top: var(--spacing-4);
        text-align: center;
      }

      .adjusted-price {
        font-weight: 600;
        color: var(--color-primary);
      }

      .terms-list {
        padding-left: var(--spacing-6);
        margin: 0;

        li {
          margin-bottom: var(--spacing-2);
          color: var(--color-text-primary);
        }
      }
    }
  }

  // 预览样式
  .quotation-preview {
    .preview-card {
      margin-bottom: var(--spacing-6);

      .quotation-document {
        .doc-header {
          padding-bottom: var(--spacing-4);
          margin-bottom: var(--spacing-6);
          text-align: center;
          border-bottom: 2px solid var(--color-border-light);

          h2 {
            margin: 0 0 var(--spacing-4) 0;
            font-size: 28px;
            color: var(--color-text-primary);
          }

          .doc-info {
            display: flex;
            gap: var(--spacing-6);
            justify-content: center;
            color: var(--color-text-secondary);
          }
        }

        .doc-customer,
        .doc-product,
        .doc-pricing,
        .doc-terms {
          margin-bottom: var(--spacing-6);

          h3 {
            margin: 0 0 var(--spacing-4) 0;
            font-size: 20px;
            font-weight: 600;
            color: var(--color-text-primary);
          }
        }

        .customer-details {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: var(--spacing-3);

          > div {
            color: var(--color-text-primary);
          }
        }

        .single-pricing {
          .price-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-4);
            background: var(--color-bg-secondary);
            border-radius: var(--radius-base);

            .price {
              font-size: 24px;
              font-weight: 600;
              color: var(--color-primary);
            }
          }
        }

        .pricing-summary {
          padding: var(--spacing-4);
          margin-top: var(--spacing-4);
          background: var(--color-primary-light);
          border-radius: var(--radius-base);

          .summary-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-2);

            &:last-child {
              margin-bottom: 0;
            }

            .amount {
              font-size: 24px;
              font-weight: 600;
              color: var(--color-primary);
            }
          }
        }
      }
    }

    .send-config {
      .card-header {
        display: flex;
        gap: var(--spacing-2);
        align-items: center;
        font-weight: 600;
      }
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .quotation-form {
      padding: var(--spacing-4);

      &__header {
        flex-direction: column;
        gap: var(--spacing-4);
      }

      .info-cards {
        grid-template-columns: 1fr;
      }

      .cost-details {
        grid-template-columns: 1fr;
      }

      .cost-chart {
        flex-direction: column;
      }

      .customer-details {
        grid-template-columns: 1fr;
      }
    }
  }
</style>
