<template>
  <svg
    v-if="name"
    class="nav-icon"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="1.5"
  >
    <!-- CPU图标 -->
    <template v-if="name === 'cpu'">
      <rect
x="4" y="4"
width="16" height="16" rx="2"
/>
      <rect
x="9" y="9"
width="6" height="6"
/>
      <line
x1="9" y1="1"
x2="9" y2="4"
/>
      <line
x1="15" y1="1"
x2="15" y2="4"
/>
      <line
x1="9" y1="20"
x2="9" y2="23"
/>
      <line
x1="15" y1="20"
x2="15" y2="23"
/>
      <line
x1="20" y1="9"
x2="23" y2="9"
/>
      <line
x1="20" y1="14"
x2="23" y2="14"
/>
      <line
x1="1" y1="9"
x2="4" y2="9"
/>
      <line
x1="1" y1="14"
x2="4" y2="14"
/>
    </template>

    <!-- Monitor图标 -->
    <template v-else-if="name === 'monitor'">
      <rect
x="2" y="3"
width="20" height="14" rx="2" ry="2"
/>
      <line
x1="8" y1="21"
x2="16" y2="21"
/>
      <line
x1="12" y1="17"
x2="12" y2="21"
/>
    </template>

    <!-- Shield Check图标 -->
    <template v-else-if="name === 'shield-check'">
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
      <path d="m9 12 2 2 4-4" />
    </template>

    <!-- Bar Chart图标 -->
    <template v-else-if="name === 'bar-chart'">
      <line
x1="12" y1="20"
x2="12" y2="10"
/>
      <line
x1="18" y1="20"
x2="18" y2="4"
/>
      <line
x1="6" y1="20"
x2="6" y2="16"
/>
    </template>

    <!-- Home图标 -->
    <template v-else-if="name === 'home'">
      <path d="M3 9.5L12 3L21 9.5" />
      <path d="M5 21V10.5" />
      <path d="M19 21V10.5" />
    </template>

    <!-- Users图标 -->
    <template v-else-if="name === 'users'">
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle
cx="9" cy="7"
r="4"
/>
      <path d="m22 21-3-3m0 0a5.5 5.5 0 1 0-7.8-7.8 5.5 5.5 0 0 0 7.8 7.8Z" />
    </template>

    <!-- Clipboard图标 -->
    <template v-else-if="name === 'clipboard'">
      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
      <rect
x="8" y="2"
width="8" height="4" rx="1" ry="1"
/>
    </template>

    <!-- Chart图标 -->
    <template v-else-if="name === 'chart'">
      <rect
x="3" y="4"
width="18" height="16" rx="2"
/>
      <path d="M7 8h10" />
      <path d="M7 12h7" />
    </template>

    <!-- Box图标 -->
    <template v-else-if="name === 'box'">
      <path d="M21 8.5L12 3L3 8.5V18L12 23L21 18V8.5Z" />
      <path d="M3 8.5L12 13L21 8.5" />
      <path d="M12 13V23" />
    </template>

    <!-- Component图标 -->
    <template v-else-if="name === 'component'">
      <path d="M5.5 8.5L9 12L5.5 15.5" />
      <path d="m12 2l3 7h7l-5.5 5.5L18 22l-6-4-6 4 1.5-7.5L2 9h7l3-7z" />
    </template>
  </svg>
</template>

<script setup lang="ts">
  interface Props {
    name: string
  }

  defineProps<Props>()
</script>

<style lang="scss" scoped>
  .nav-icon {
    flex-shrink: 0;
    width: 18px;
    height: 18px;

    // 性能优化：启用GPU加速
    transform: translateZ(0);
  }
</style>
