<template>
  <el-dialog
    v-model="visible"
    title="个人信息管理"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="profile-content">
      <!-- 头像编辑区域 -->
      <div class="avatar-section">
        <div class="avatar-container">
          <c-avatar
            :src="formData.avatar"
            :name="formData.realName"
            :size="120"
            editable
            @edit="showAvatarUpload"
          />

          <div class="avatar-actions">
            <el-button type="primary" size="small" :icon="Upload" @click="showAvatarUpload">
              更换头像
            </el-button>
            <el-button
              v-if="formData.avatar"
              type="danger"
              size="small"
              :icon="Delete"
              @click="removeAvatar"
            >
              删除头像
            </el-button>
          </div>
        </div>

        <div class="avatar-tips">
          <h4>头像要求：</h4>
          <ul>
            <li>推荐尺寸：200x200像素</li>
            <li>支持格式：JPG、PNG</li>
            <li>文件大小：不超过2MB</li>
            <li>建议使用清晰的正面照片</li>
          </ul>
        </div>
      </div>

      <!-- 个人信息表单 -->
      <div class="info-section">
        <el-tabs v-model="activeTab"
type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息"
name="basic">
            <el-form
              ref="basicFormRef"
              :model="formData"
              :rules="formRules"
              label-width="100px"
              class="profile-form"
            >
              <el-form-item label="用户名">
                <el-input v-model="formData.username"
readonly>
                  <template #suffix>
                    <el-tooltip content="用户名不可修改">
                      <el-icon><info-filled /></el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="真实姓名"
prop="realName">
                <el-input v-model="formData.realName" placeholder="请输入真实姓名" clearable />
              </el-form-item>

              <el-form-item label="邮箱"
prop="email">
                <el-input
                  v-model="formData.email"
                  type="email"
                  placeholder="请输入邮箱"
                  clearable
                />
              </el-form-item>

              <el-form-item label="电话"
prop="phone">
                <el-input v-model="formData.phone" placeholder="请输入电话号码" clearable />
              </el-form-item>

              <el-form-item label="部门">
                <el-input v-model="formData.department"
readonly>
                  <template #suffix>
                    <el-tooltip content="部门信息由管理员维护">
                      <el-icon><info-filled /></el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="职位">
                <el-input v-model="formData.position"
readonly>
                  <template #suffix>
                    <el-tooltip content="职位信息由管理员维护">
                      <el-icon><info-filled /></el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 安全设置 -->
          <el-tab-pane label="安全设置"
name="security">
            <div class="security-content">
              <el-descriptions :column="1"
border>
                <el-descriptions-item label="最后登录时间">
                  {{
                    userData?.lastLoginTime ? formatDateTime(userData.lastLoginTime) : '从未登录'
                  }}
                </el-descriptions-item>
                <el-descriptions-item label="最后登录IP">
                  {{ userData?.lastLoginIp || '未知' }}
                </el-descriptions-item>
                <el-descriptions-item label="账号状态">
                  <el-tag :type="getStatusType(userData?.status || 'inactive')">
                    {{ getStatusText(userData?.status || 'inactive') }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">
                  {{ userData?.createTime ? formatDateTime(userData.createTime) : '未知' }}
                </el-descriptions-item>
              </el-descriptions>

              <div class="security-actions">
                <el-button type="warning" :icon="Key" @click="showChangePassword">
                  修改密码
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 角色权限 -->
          <el-tab-pane label="角色权限"
name="permissions">
            <div class="permissions-content">
              <div class="roles-section">
                <h4>当前角色</h4>
                <div class="role-tags">
                  <el-tag
                    v-for="role in userData?.roles || []"
                    :key="role"
                    size="large"
                    type="primary"
                  >
                    {{ getRoleName(role) }}
                  </el-tag>
                  <el-empty
                    v-if="!userData?.roles?.length"
                    description="未分配任何角色"
                    :image-size="60"
                  />
                </div>
              </div>

              <div class="permissions-section">
                <h4>拥有权限</h4>
                <div class="permission-groups">
                  <div v-for="group in permissionGroups" :key="group.name" class="permission-group">
                    <div class="group-header">
                      <el-icon>
                        <component :is="group.icon" />
                      </el-icon>
                      <span>{{ group.label }}</span>
                      <el-tag size="small">
                        {{ group.permissions.length }}
                      </el-tag>
                    </div>
                    <div class="group-permissions">
                      <el-tag
                        v-for="permission in group.permissions"
                        :key="permission"
                        size="small"
                        type="info"
                      >
                        {{ getPermissionName(permission) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
                <el-empty
                  v-if="!userData?.permissions?.length"
                  description="未拥有任何权限"
                  :image-size="60"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="activeTab === 'basic'"
          type="primary"
          :loading="submitting"
          @click="handleSave"
        >
          {{ submitting ? '保存中...' : '保存' }}
        </el-button>
      </span>
    </template>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="avatarUploadVisible" title="上传头像" width="400px" append-to-body>
      <div class="avatar-upload-content">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :limit="1"
          :on-change="handleAvatarChange"
          :before-upload="beforeAvatarUpload"
          accept="image/jpeg,image/png"
          drag
        >
          <el-icon class="el-icon--upload">
            <upload-filled />
          </el-icon>
          <div class="el-upload__text">
            将头像拖拽到此处，或
            <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
支持jpg/png文件，且不超过2MB
</div>
          </template>
        </el-upload>

        <div v-if="avatarPreview"
class="avatar-preview">
          <h4>预览：</h4>
          <c-avatar :src="avatarPreview"
:size="80" />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelAvatarUpload">取消</el-button>
          <el-button
            type="primary"
            :loading="uploadingAvatar"
            :disabled="!avatarPreview"
            @click="confirmAvatarUpload"
          >
            {{ uploadingAvatar ? '上传中...' : '确认上传' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <change-password-dialog
      v-model="changePasswordVisible"
      :user-data="userData"
      @success="handlePasswordChangeSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import type { FormInstance, FormRules, UploadInstance, UploadFile } from 'element-plus'
  import {
    Upload,
    Delete,
    Key,
    InfoFilled,
    UploadFilled,
    User,
    Setting,
    Box,
    Monitor,
    DataAnalysis,
    Document
  } from '@element-plus/icons-vue'
  import type { UserInfo } from '@/types/user'
  import { useAuthStore } from '@/stores/auth'
  import CAvatar from '@/components/base/CAvatar.vue'
  import ChangePasswordDialog from './ChangePasswordDialog.vue'

  interface Props {
    modelValue: boolean
    userData?: UserInfo | null
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'success'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    userData: null
  })

  const emit = defineEmits<Emits>()

  const authStore = useAuthStore()

  // 对话框显示状态
  const visible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  // 表单引用
  const basicFormRef = ref<FormInstance>()
  const uploadRef = ref<UploadInstance>()

  // 状态管理
  const submitting = ref(false)
  const activeTab = ref('basic')
  const avatarUploadVisible = ref(false)
  const uploadingAvatar = ref(false)
  const changePasswordVisible = ref(false)
  const avatarPreview = ref('')
  const avatarFile = ref<File | null>(null)

  // 表单数据
  const formData = reactive({
    username: '',
    realName: '',
    email: '',
    phone: '',
    department: '',
    position: '',
    avatar: ''
  })

  // 表单验证规则
  const formRules: FormRules = {
    realName: [
      { required: true, message: '请输入真实姓名', trigger: 'blur' },
      { min: 2, max: 10, message: '姓名长度在2-10个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    phone: [
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur'
      }
    ]
  }

  // Mock权限和角色数据
  const mockRoles = [
    { code: 'system_admin', name: '系统管理员' },
    { code: 'process_engineer', name: '工艺工程师' },
    { code: 'production_operator', name: '生产操作员' },
    { code: 'quality_engineer', name: '质量工程师' }
  ]

  const mockPermissions = [
    { code: 'system:read', name: '系统查看' },
    { code: 'system:write', name: '系统编辑' },
    { code: 'user:read', name: '用户查看' },
    { code: 'user:write', name: '用户编辑' },
    { code: 'production:read', name: '生产查看' },
    { code: 'equipment:read', name: '设备查看' },
    { code: 'quality:read', name: '质量查看' }
  ]

  // 计算权限分组
  const permissionGroups = computed(() => {
    if (!props.userData?.permissions) return []

    const groups = new Map()

    props.userData.permissions.forEach(permission => {
      const prefix = permission.split(':')[0]
      if (!groups.has(prefix)) {
        groups.set(prefix, {
          name: prefix,
          label: getGroupLabel(prefix),
          icon: getGroupIcon(prefix),
          permissions: []
        })
      }
      groups.get(prefix).permissions.push(permission)
    })

    return Array.from(groups.values())
  })

  /**
   * 获取分组标签
   */
  const getGroupLabel = (prefix: string): string => {
    const labels = {
      system: '系统管理',
      user: '用户管理',
      production: '生产管理',
      equipment: '设备管理',
      quality: '质量管理',
      report: '报表管理'
    }
    return labels[prefix] || prefix.toUpperCase()
  }

  /**
   * 获取分组图标
   */
  const getGroupIcon = (prefix: string) => {
    const icons = {
      system: Setting,
      user: User,
      production: Box,
      equipment: Monitor,
      quality: DataAnalysis,
      report: Document
    }
    return icons[prefix] || Setting
  }

  /**
   * 获取角色名称
   */
  const getRoleName = (roleCode: string): string => {
    const role = mockRoles.find(r => r.code === roleCode)
    return role?.name || roleCode
  }

  /**
   * 获取权限名称
   */
  const getPermissionName = (permissionCode: string): string => {
    const permission = mockPermissions.find(p => p.code === permissionCode)
    return permission?.name || permissionCode
  }

  /**
   * 获取状态类型
   */
  const getStatusType = (status: string): string => {
    const types = {
      active: 'success',
      inactive: 'warning',
      locked: 'danger'
    }
    return types[status] || 'info'
  }

  /**
   * 获取状态文本
   */
  const getStatusText = (status: string): string => {
    const texts = {
      active: '正常',
      inactive: '禁用',
      locked: '锁定'
    }
    return texts[status] || status
  }

  /**
   * 格式化日期时间
   */
  const formatDateTime = (dateTime: string): string => {
    return new Date(dateTime).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  /**
   * 显示头像上传对话框
   */
  const showAvatarUpload = (): void => {
    avatarUploadVisible.value = true
    avatarPreview.value = ''
    avatarFile.value = null
  }

  /**
   * 处理头像文件变化
   */
  const handleAvatarChange = (file: UploadFile): void => {
    if (file.raw) {
      avatarFile.value = file.raw

      // 创建预览
      const reader = new FileReader()
      reader.onload = e => {
        avatarPreview.value = e.target?.result as string
      }
      reader.readAsDataURL(file.raw)
    }
  }

  /**
   * 头像上传前验证
   */
  const beforeAvatarUpload = (file: File): boolean => {
    const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isImage) {
      ElMessage.error('头像只能是JPG或PNG格式!')
      return false
    }
    if (!isLt2M) {
      ElMessage.error('头像大小不能超过2MB!')
      return false
    }
    return true
  }

  /**
   * 确认上传头像
   */
  const confirmAvatarUpload = async (): Promise<void> => {
    if (!avatarFile.value) return

    try {
      uploadingAvatar.value = true

      // 模拟上传
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 更新头像
      formData.avatar = avatarPreview.value

      ElMessage.success('头像上传成功')
      avatarUploadVisible.value = false
    } catch (error) {
      console.error('Upload avatar error:', error)
      ElMessage.error('头像上传失败')
    } finally {
      uploadingAvatar.value = false
    }
  }

  /**
   * 取消头像上传
   */
  const cancelAvatarUpload = (): void => {
    avatarUploadVisible.value = false
    avatarPreview.value = ''
    avatarFile.value = null
    uploadRef.value?.clearFiles()
  }

  /**
   * 删除头像
   */
  const removeAvatar = async (): Promise<void> => {
    try {
      await ElMessageBox.confirm('确定要删除头像吗？', '确认删除')
      formData.avatar = ''
      ElMessage.success('头像已删除')
    } catch (error) {
      // 用户取消
    }
  }

  /**
   * 显示修改密码对话框
   */
  const showChangePassword = (): void => {
    changePasswordVisible.value = true
  }

  /**
   * 处理密码修改成功
   */
  const handlePasswordChangeSuccess = (): void => {
    changePasswordVisible.value = false
    ElMessage.success('密码修改成功，请重新登录')
  }

  /**
   * 保存个人信息
   */
  const handleSave = async (): Promise<void> => {
    if (!basicFormRef.value) return

    try {
      const valid = await basicFormRef.value.validate()
      if (!valid) return

      submitting.value = true

      // 调用更新用户信息API
      const success = await authStore.updateUserInfo({
        realName: formData.realName,
        email: formData.email,
        phone: formData.phone || undefined,
        avatar: formData.avatar || undefined
      })

      if (success) {
        emit('success')
        handleClose()
      }
    } catch (error) {
      console.error('Save profile error:', error)
    } finally {
      submitting.value = false
    }
  }

  /**
   * 处理关闭
   */
  const handleClose = (): void => {
    visible.value = false
    activeTab.value = 'basic'

    // 延迟重置表单
    setTimeout(() => {
      basicFormRef.value?.resetFields()
      initFormData()
    }, 300)
  }

  /**
   * 初始化表单数据
   */
  const initFormData = (): void => {
    if (props.userData) {
      Object.assign(formData, {
        username: props.userData.username,
        realName: props.userData.realName,
        email: props.userData.email,
        phone: props.userData.phone || '',
        department: props.userData.department,
        position: props.userData.position,
        avatar: props.userData.avatar || ''
      })
    }
  }

  // 监听对话框打开
  watch(
    () => props.modelValue,
    newValue => {
      if (newValue) {
        initFormData()
      }
    },
    { immediate: true }
  )
</script>

<style lang="scss" scoped>
  .profile-content {
    display: flex;
    gap: 24px;
  }

  .avatar-section {
    flex-shrink: 0;
    width: 200px;

    .avatar-container {
      text-align: center;
      margin-bottom: 16px;

      .avatar-actions {
        margin-top: 12px;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }

    .avatar-tips {
      background: var(--color-bg-light);
      padding: 12px;
      border-radius: 6px;
      border-left: 3px solid var(--color-primary);

      h4 {
        margin: 0 0 8px 0;
        font-size: 12px;
        color: var(--color-text-primary);
      }

      ul {
        margin: 0;
        padding-left: 16px;

        li {
          font-size: 11px;
          color: var(--color-text-regular);
          line-height: 1.4;
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .info-section {
    flex: 1;
    min-width: 0;
  }

  .profile-form {
    padding: 16px;
  }

  .security-content {
    padding: 16px;

    .security-actions {
      margin-top: 24px;
      text-align: center;
    }
  }

  .permissions-content {
    padding: 16px;

    .roles-section,
    .permissions-section {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: var(--color-text-primary);
      }
    }

    .role-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .permission-groups {
      .permission-group {
        margin-bottom: 16px;
        border: 1px solid var(--color-border-lighter);
        border-radius: 6px;
        overflow: hidden;

        .group-header {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 16px;
          background: var(--color-bg-light);
          border-bottom: 1px solid var(--color-border-lighter);

          .el-icon {
            color: var(--color-primary);
          }

          span {
            flex: 1;
            font-weight: 500;
          }
        }

        .group-permissions {
          padding: 12px 16px;
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
        }
      }
    }
  }

  .avatar-upload-content {
    .avatar-preview {
      margin-top: 16px;
      text-align: center;

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: var(--color-text-primary);
      }
    }
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }

  :deep(.el-descriptions__label) {
    font-weight: 500;
    color: var(--color-text-primary);
  }

  // 响应式设计
  @media (max-width: 768px) {
    .profile-content {
      flex-direction: column;
      gap: 16px;
    }

    .avatar-section {
      width: 100%;

      .avatar-container {
        .avatar-actions {
          flex-direction: row;
          justify-content: center;
        }
      }
    }

    :deep(.el-dialog) {
      width: 95% !important;
      margin: 2vh auto;
    }
  }
</style>
