<template>
  <el-dialog
    v-model="visible"
    title="修改密码"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="password-form">
      <el-alert title="为了您的账户安全，请定期更改密码" type="info" :closable="false" show-icon />

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="change-password-form"
        @submit.prevent
      >
        <el-form-item label="当前密码"
prop="oldPassword">
          <el-input
            v-model="formData.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>

        <el-form-item label="新密码"
prop="newPassword">
          <el-input
            v-model="formData.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
            clearable
            @keyup.enter="handleSubmit"
            @input="handlePasswordInput"
          />

          <!-- 密码强度指示器 -->
          <div v-if="formData.newPassword"
class="password-strength">
            <div class="strength-bar">
              <div
                class="strength-fill"
                :class="`strength-${passwordStrength.level}`"
                :style="{ width: `${passwordStrength.percentage}%` }"
              />
            </div>
            <span class="strength-text" :class="`strength-${passwordStrength.level}`">
              {{ passwordStrength.text }}
            </span>
          </div>
        </el-form-item>

        <el-form-item label="确认密码"
prop="confirmPassword">
          <el-input
            v-model="formData.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>

        <!-- 密码要求提示 -->
        <div class="password-requirements">
          <h4>密码要求：</h4>
          <ul>
            <li :class="{ 'requirement-met': requirements.length }">
              <el-icon>
                <component :is="requirements.length ? 'SuccessFilled' : 'CircleFilled'" />
              </el-icon>
              至少8位字符
            </li>
            <li :class="{ 'requirement-met': requirements.hasLetter }">
              <el-icon>
                <component :is="requirements.hasLetter ? 'SuccessFilled' : 'CircleFilled'" />
              </el-icon>
              包含字母
            </li>
            <li :class="{ 'requirement-met': requirements.hasNumber }">
              <el-icon>
                <component :is="requirements.hasNumber ? 'SuccessFilled' : 'CircleFilled'" />
              </el-icon>
              包含数字
            </li>
            <li :class="{ 'requirement-met': requirements.hasSpecial }">
              <el-icon>
                <component :is="requirements.hasSpecial ? 'SuccessFilled' : 'CircleFilled'" />
              </el-icon>
              包含特殊字符（推荐）
            </li>
          </ul>
        </div>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitting ? '修改中...' : '确认修改' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import type { FormInstance, FormRules } from 'element-plus'
  import { SuccessFilled, CircleFilled } from '@element-plus/icons-vue'
  import type { UserInfo } from '@/types/user'
  import { useAuthStore } from '@/stores/auth'

  interface Props {
    modelValue: boolean
    userData?: UserInfo | null
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'success'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    userData: null
  })

  const emit = defineEmits<Emits>()

  const authStore = useAuthStore()

  // 对话框显示状态
  const visible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  // 表单引用
  const formRef = ref<FormInstance>()
  const submitting = ref(false)

  // 表单数据
  const formData = reactive({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  // 密码要求验证
  const requirements = computed(() => {
    const password = formData.newPassword
    return {
      length: password.length >= 8,
      hasLetter: /[a-zA-Z]/.test(password),
      hasNumber: /\d/.test(password),
      hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    }
  })

  // 密码强度计算
  const passwordStrength = computed(() => {
    const password = formData.newPassword
    if (!password) return { level: 'weak', percentage: 0, text: '' }

    let score = 0
    const checks = [
      password.length >= 8,
      /[a-z]/.test(password),
      /[A-Z]/.test(password),
      /\d/.test(password),
      /[!@#$%^&*(),.?":{}|<>]/.test(password),
      password.length >= 12
    ]

    score = checks.filter(Boolean).length

    if (score <= 2) {
      return { level: 'weak', percentage: 33, text: '弱' }
    } else if (score <= 4) {
      return { level: 'medium', percentage: 66, text: '中等' }
    } else {
      return { level: 'strong', percentage: 100, text: '强' }
    }
  })

  // 自定义验证器
  const validateOldPassword = (rule: any, value: string, callback: Function) => {
    if (!value) {
      callback(new Error('请输入当前密码'))
    } else {
      callback()
    }
  }

  const validateNewPassword = (rule: any, value: string, callback: Function) => {
    if (!value) {
      callback(new Error('请输入新密码'))
    } else if (value === formData.oldPassword) {
      callback(new Error('新密码不能与当前密码相同'))
    } else if (value.length < 8) {
      callback(new Error('密码至少需要8位字符'))
    } else if (!/[a-zA-Z]/.test(value)) {
      callback(new Error('密码必须包含字母'))
    } else if (!/\d/.test(value)) {
      callback(new Error('密码必须包含数字'))
    } else {
      // 如果确认密码已输入，需要重新验证确认密码
      if (formData.confirmPassword) {
        formRef.value?.validateField('confirmPassword')
      }
      callback()
    }
  }

  const validateConfirmPassword = (rule: any, value: string, callback: Function) => {
    if (!value) {
      callback(new Error('请再次输入新密码'))
    } else if (value !== formData.newPassword) {
      callback(new Error('两次输入的密码不一致'))
    } else {
      callback()
    }
  }

  // 表单验证规则
  const formRules: FormRules = {
    oldPassword: [{ validator: validateOldPassword, trigger: 'blur' }],
    newPassword: [{ validator: validateNewPassword, trigger: 'blur' }],
    confirmPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }]
  }

  /**
   * 处理密码输入（实时验证）
   */
  const handlePasswordInput = (): void => {
    // 清除确认密码的错误状态
    if (formData.confirmPassword) {
      formRef.value?.validateField('confirmPassword')
    }
  }

  /**
   * 处理提交
   */
  const handleSubmit = async (): Promise<void> => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      submitting.value = true

      // 调用修改密码API
      const success = await authStore.changePassword(formData.oldPassword, formData.newPassword)

      if (success) {
        emit('success')
        handleClose()
      }
    } catch (error) {
      console.error('Change password error:', error)
    } finally {
      submitting.value = false
    }
  }

  /**
   * 处理关闭
   */
  const handleClose = (): void => {
    visible.value = false

    // 延迟重置表单，避免动画闪烁
    setTimeout(() => {
      formRef.value?.resetFields()
      Object.assign(formData, {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    }, 300)
  }

  // 监听对话框打开
  watch(
    () => props.modelValue,
    newValue => {
      if (newValue) {
        // 对话框打开时重置表单
        Object.assign(formData, {
          oldPassword: '',
          newPassword: '',
          confirmPassword: ''
        })
      }
    }
  )
</script>

<style lang="scss" scoped>
  .password-form {
    .el-alert {
      margin-bottom: 24px;
    }
  }

  .change-password-form {
    margin: 24px 0;
  }

  .password-strength {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 8px;

    .strength-bar {
      flex: 1;
      height: 6px;
      background-color: var(--color-border-light);
      border-radius: 3px;
      overflow: hidden;

      .strength-fill {
        height: 100%;
        transition: all 0.3s ease;

        &.strength-weak {
          background-color: #ff4d4f;
        }

        &.strength-medium {
          background-color: #faad14;
        }

        &.strength-strong {
          background-color: #52c41a;
        }
      }
    }

    .strength-text {
      font-size: 12px;
      font-weight: 500;
      min-width: 24px;

      &.strength-weak {
        color: #ff4d4f;
      }

      &.strength-medium {
        color: #faad14;
      }

      &.strength-strong {
        color: #52c41a;
      }
    }
  }

  .password-requirements {
    margin-top: 16px;
    padding: 16px;
    background: var(--color-bg-light);
    border-radius: 6px;
    border-left: 3px solid var(--color-primary);

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: var(--color-text-primary);
    }

    ul {
      margin: 0;
      padding: 0;
      list-style: none;

      li {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 13px;
        color: var(--color-text-regular);
        line-height: 1.5;
        margin-bottom: 6px;
        transition: color 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        .el-icon {
          font-size: 12px;
          color: var(--color-text-placeholder);
          transition: color 0.3s ease;
        }

        &.requirement-met {
          color: var(--color-success);

          .el-icon {
            color: var(--color-success);
          }
        }
      }
    }
  }

  :deep(.el-dialog) {
    border-radius: 8px;
  }

  :deep(.el-dialog__header) {
    background: var(--color-bg-light);
    border-bottom: 1px solid var(--color-border-lighter);
    border-radius: 8px 8px 0 0;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--color-text-primary);
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 10vh auto;
    }

    :deep(.el-dialog__body) {
      padding: 16px;
    }

    .password-requirements {
      ul li {
        font-size: 12px;
      }
    }
  }
</style>
