<template>
  <div class="alarm-list">
    <!-- 筛选器 -->
    <div
v-if="showFilters" class="filters"
>
      <el-row :gutter="16">
        <el-col :span="6">
          <el-select
v-model="severityFilter" placeholder="严重程度"
clearable size="small"
>
            <el-option label="严重" value="CRITICAL" />
            <el-option label="重要" value="MAJOR" />
            <el-option label="次要" value="MINOR" />
            <el-option label="警告" value="WARNING" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
v-model="categoryFilter" placeholder="告警类别"
clearable size="small"
>
            <el-option label="机械" value="MECHANICAL" />
            <el-option label="电气" value="ELECTRICAL" />
            <el-option label="工艺" value="PROCESS" />
            <el-option label="安全" value="SAFETY" />
            <el-option label="通信" value="COMMUNICATION" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="equipmentFilter"
            placeholder="设备筛选"
            clearable
            filterable
            size="small"
          >
            <el-option
v-for="eq in equipmentList" :key="eq.id"
:label="eq.name" :value="eq.id"
/>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-switch
v-model="activeOnly" active-text="仅活动告警"
size="small"
/>
        </el-col>
      </el-row>
    </div>

    <!-- 统计信息 -->
    <div
v-if="showStats" class="alarm-stats"
>
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="严重告警" :value="criticalCount">
            <template #suffix>
              <el-icon class="critical-icon">
                <Warning />
              </el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="重要告警" :value="majorCount">
            <template #suffix>
              <el-icon class="major-icon">
                <InfoFilled />
              </el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="总告警数" :value="totalCount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已确认" :value="acknowledgedCount" />
        </el-col>
      </el-row>
    </div>

    <!-- 告警列表 -->
    <div class="alarms">
      <div v-if="filteredAlarms.length === 0" class="empty-state">
        <el-empty description="暂无告警信息" />
      </div>

      <div
        v-for="alarm in paginatedAlarms"
        :key="alarm.id"
        :class="['alarm-item', `severity-${alarm.severity.toLowerCase()}`]"
        @click="handleAlarmClick(alarm)"
      >
        <div class="alarm-header">
          <div class="alarm-info">
            <div class="alarm-title">
              <el-icon :class="['severity-icon', `severity-${alarm.severity.toLowerCase()}`]">
                <Warning v-if="alarm.severity === 'CRITICAL'" />
                <InfoFilled v-else />
              </el-icon>
              <span class="alarm-text">{{ alarm.alarmText }}</span>
              <el-tag
:type="getSeverityType(alarm.severity)" size="small"
class="severity-tag"
>
                {{ getSeverityText(alarm.severity) }}
              </el-tag>
            </div>
            <div class="alarm-meta">
              <span class="equipment-name">{{ alarm.equipmentName }}</span>
              <span class="alarm-code">{{ alarm.alarmCode }}</span>
              <span class="timestamp">{{ formatTimestamp(alarm.timestamp) }}</span>
            </div>
          </div>

          <div class="alarm-actions" @click.stop>
            <el-button
              v-if="alarm.isActive && !alarm.acknowledgedBy"
              type="warning"
              size="small"
              @click="handleAcknowledge(alarm)"
            >
              确认
            </el-button>

            <el-button
              v-if="alarm.isActive && alarm.acknowledgedBy"
              type="danger"
              size="small"
              @click="handleClear(alarm)"
            >
              清除
            </el-button>

            <el-button type="info"
size="small" @click="handleViewDetail(alarm)"
>
详情
</el-button>
          </div>
        </div>

        <div
v-if="alarm.description" class="alarm-body"
>
          <p class="alarm-description">
            {{ alarm.description }}
          </p>

          <div
            v-if="alarm.recommendations && alarm.recommendations.length > 0"
            class="recommendations"
          >
            <h5>推荐处理方案:</h5>
            <ul>
              <li v-for="rec in alarm.recommendations" :key="rec">
                {{ rec }}
              </li>
            </ul>
          </div>
        </div>

        <div
v-if="alarm.acknowledgedBy || alarm.clearedAt" class="alarm-footer"
>
          <div v-if="alarm.acknowledgedBy" class="status-info acknowledged">
            <el-icon><Check /></el-icon>
            <span>
              {{ alarm.acknowledgedBy }} 于 {{ formatTimestamp(alarm.acknowledgedAt!) }} 确认
            </span>
          </div>

          <div v-if="alarm.clearedAt" class="status-info cleared">
            <el-icon><CircleCheck /></el-icon>
            <span>于 {{ formatTimestamp(alarm.clearedAt) }} 清除</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div
v-if="showPagination && totalCount > pageSize" class="pagination"
>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 告警详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="告警详情"
      width="60%"
      @close="selectedAlarm = null"
    >
      <div v-if="selectedAlarm" class="alarm-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="告警代码">
            {{ selectedAlarm.alarmCode }}
          </el-descriptions-item>
          <el-descriptions-item label="严重程度">
            <el-tag :type="getSeverityType(selectedAlarm.severity)">
              {{ getSeverityText(selectedAlarm.severity) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备名称">
            {{ selectedAlarm.equipmentName }}
          </el-descriptions-item>
          <el-descriptions-item label="告警类别">
            {{ getCategoryText(selectedAlarm.category) }}
          </el-descriptions-item>
          <el-descriptions-item label="发生时间">
            {{ formatTimestamp(selectedAlarm.timestamp) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedAlarm.isActive ? 'danger' : 'success'">
              {{ selectedAlarm.isActive ? '活动' : '已清除' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div
v-if="selectedAlarm.description" class="alarm-content"
>
          <h4>告警描述</h4>
          <p>{{ selectedAlarm.description }}</p>
        </div>

        <div
v-if="selectedAlarm.recommendations?.length" class="recommendations"
>
          <h4>处理建议</h4>
          <ul>
            <li v-for="rec in selectedAlarm.recommendations" :key="rec">
              {{ rec }}
            </li>
          </ul>
        </div>

        <div class="status-history">
          <h4>状态历史</h4>
          <el-timeline>
            <el-timeline-item
              timestamp="发生时间"
              :time-timestamp="new Date(selectedAlarm.timestamp)"
              type="danger"
            >
              告警产生: {{ selectedAlarm.alarmText }}
            </el-timeline-item>

            <el-timeline-item
              v-if="selectedAlarm.acknowledgedAt"
              timestamp="确认时间"
              :time-timestamp="new Date(selectedAlarm.acknowledgedAt)"
              type="warning"
            >
              {{ selectedAlarm.acknowledgedBy }} 确认了告警
            </el-timeline-item>

            <el-timeline-item
              v-if="selectedAlarm.clearedAt"
              timestamp="清除时间"
              :time-timestamp="new Date(selectedAlarm.clearedAt)"
              type="success"
            >
              告警已清除
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue'
  import { Warning, InfoFilled, Check, CircleCheck } from '@element-plus/icons-vue'
  import type { EquipmentAlarm, Equipment } from '@/types/equipment'

  interface Props {
    alarms: EquipmentAlarm[]
    equipmentList?: Equipment[]
    showFilters?: boolean
    showStats?: boolean
    showPagination?: boolean
    pageSize?: number
  }

  interface Emits {
    (e: 'acknowledge', alarm: EquipmentAlarm): void
    (e: 'clear', alarm: EquipmentAlarm): void
    (e: 'detail', alarm: EquipmentAlarm): void
  }

  const props = withDefaults(defineProps<Props>(), {
    equipmentList: () => [],
    showFilters: true,
    showStats: true,
    showPagination: true,
    pageSize: 20
  })

  const emit = defineEmits<Emits>()

  // 筛选器状态
  const severityFilter = ref('')
  const categoryFilter = ref('')
  const equipmentFilter = ref('')
  const activeOnly = ref(true)

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(props.pageSize)

  // 详情弹窗
  const detailDialogVisible = ref(false)
  const selectedAlarm = ref<EquipmentAlarm | null>(null)

  // 筛选后的告警列表
  const filteredAlarms = computed(() => {
    let result = props.alarms

    if (severityFilter.value) {
      result = result.filter(alarm => alarm.severity === severityFilter.value)
    }

    if (categoryFilter.value) {
      result = result.filter(alarm => alarm.category === categoryFilter.value)
    }

    if (equipmentFilter.value) {
      result = result.filter(alarm => alarm.equipmentId === equipmentFilter.value)
    }

    if (activeOnly.value) {
      result = result.filter(alarm => alarm.isActive)
    }

    return result.sort((a, b) => {
      // 按时间倒序，最新的在前
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    })
  })

  // 分页后的告警列表
  const paginatedAlarms = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return filteredAlarms.value.slice(start, end)
  })

  // 统计数据
  const totalCount = computed(() => filteredAlarms.value.length)
  const criticalCount = computed(
    () => filteredAlarms.value.filter(a => a.severity === 'CRITICAL' && a.isActive).length
  )
  const majorCount = computed(
    () => filteredAlarms.value.filter(a => a.severity === 'MAJOR' && a.isActive).length
  )
  const acknowledgedCount = computed(
    () => filteredAlarms.value.filter(a => a.acknowledgedBy).length
  )

  // 工具方法
  const getSeverityText = (severity: string): string => {
    const textMap: Record<string, string> = {
      CRITICAL: '严重',
      MAJOR: '重要',
      MINOR: '次要',
      WARNING: '警告'
    }
    return textMap[severity] || severity
  }

  const getSeverityType = (severity: string): string => {
    const typeMap: Record<string, string> = {
      CRITICAL: 'danger',
      MAJOR: 'warning',
      MINOR: 'info',
      WARNING: 'info'
    }
    return typeMap[severity] || 'info'
  }

  const getCategoryText = (category: string): string => {
    const textMap: Record<string, string> = {
      MECHANICAL: '机械',
      ELECTRICAL: '电气',
      PROCESS: '工艺',
      SAFETY: '安全',
      COMMUNICATION: '通信'
    }
    return textMap[category] || category
  }

  const formatTimestamp = (timestamp: string): string => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 事件处理
  const handleAlarmClick = (alarm: EquipmentAlarm) => {
    // 点击告警项展开详情（可选）
  }

  const handleAcknowledge = (alarm: EquipmentAlarm) => {
    emit('acknowledge', alarm)
  }

  const handleClear = (alarm: EquipmentAlarm) => {
    emit('clear', alarm)
  }

  const handleViewDetail = (alarm: EquipmentAlarm) => {
    selectedAlarm.value = alarm
    detailDialogVisible.value = true
    emit('detail', alarm)
  }

  const handleSizeChange = (newSize: number) => {
    pageSize.value = newSize
    currentPage.value = 1
  }

  const handleCurrentChange = (newPage: number) => {
    currentPage.value = newPage
  }

  // 重置分页当筛选条件变化时
  watch([severityFilter, categoryFilter, equipmentFilter, activeOnly], () => {
    currentPage.value = 1
  })
</script>

<style lang="scss" scoped>
  .alarm-list {
    .filters {
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      background-color: var(--color-bg-soft);
      border-radius: var(--radius-base);
    }

    .alarm-stats {
      margin-bottom: var(--spacing-4);

      .critical-icon {
        color: var(--color-danger);
      }

      .major-icon {
        color: var(--color-warning);
      }
    }

    .alarms {
      .empty-state {
        padding: var(--spacing-8);
        text-align: center;
      }

      .alarm-item {
        margin-bottom: var(--spacing-3);
        cursor: pointer;
        background-color: var(--color-bg-primary);
        border: 1px solid var(--color-border-light);
        border-radius: var(--radius-base);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--shadow-base);
          transform: translateY(-1px);
        }

        &.severity-critical {
          background-color: rgb(245 108 108 / 5%);
          border-left: 4px solid var(--color-danger);
        }

        &.severity-major {
          background-color: rgb(230 162 60 / 5%);
          border-left: 4px solid var(--color-warning);
        }

        &.severity-minor {
          border-left: 4px solid var(--color-info);
        }

        &.severity-warning {
          border-left: 4px solid var(--color-info);
        }
      }

      .alarm-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: var(--spacing-4);
      }

      .alarm-info {
        flex: 1;

        .alarm-title {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
          margin-bottom: var(--spacing-2);

          .severity-icon {
            font-size: 16px;

            &.severity-critical {
              color: var(--color-danger);
            }

            &.severity-major {
              color: var(--color-warning);
            }

            &.severity-minor {
              color: var(--color-info);
            }

            &.severity-warning {
              color: var(--color-info);
            }
          }

          .alarm-text {
            font-weight: 500;
            color: var(--color-text-primary);
          }

          .severity-tag {
            margin-left: auto;
          }
        }

        .alarm-meta {
          display: flex;
          gap: var(--spacing-3);
          font-size: 0.875rem;
          color: var(--color-text-secondary);

          .equipment-name {
            font-weight: 500;
          }

          .alarm-code {
            padding: 2px 6px;
            font-family: var(--font-mono);
            background-color: var(--color-bg-soft);
            border-radius: var(--radius-small);
          }
        }
      }

      .alarm-actions {
        display: flex;
        flex-shrink: 0;
        gap: var(--spacing-2);
      }

      .alarm-body {
        padding: 0 var(--spacing-4) var(--spacing-4);
        border-top: 1px solid var(--color-border-lighter);

        .alarm-description {
          margin-bottom: var(--spacing-3);
          line-height: 1.5;
          color: var(--color-text-primary);
        }

        .recommendations {
          h5 {
            margin-bottom: var(--spacing-2);
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--color-text-primary);
          }

          ul {
            padding-left: var(--spacing-4);
            margin: 0;

            li {
              margin-bottom: var(--spacing-1);
              font-size: 0.875rem;
              line-height: 1.4;
              color: var(--color-text-secondary);
            }
          }
        }
      }

      .alarm-footer {
        padding: var(--spacing-3) var(--spacing-4);
        background-color: var(--color-bg-soft);
        border-top: 1px solid var(--color-border-lighter);
        border-radius: 0 0 var(--radius-base) var(--radius-base);

        .status-info {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
          font-size: 0.875rem;

          &.acknowledged {
            color: var(--color-warning);
          }

          &.cleared {
            color: var(--color-success);
          }
        }
      }
    }

    .pagination {
      display: flex;
      justify-content: center;
      margin-top: var(--spacing-4);
    }

    .alarm-detail {
      .alarm-content {
        margin: var(--spacing-4) 0;

        h4 {
          margin-bottom: var(--spacing-2);
          font-size: 1rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        p {
          line-height: 1.5;
          color: var(--color-text-secondary);
        }
      }

      .recommendations {
        margin: var(--spacing-4) 0;

        h4 {
          margin-bottom: var(--spacing-2);
          font-size: 1rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        ul {
          padding-left: var(--spacing-4);
          margin: 0;

          li {
            margin-bottom: var(--spacing-1);
            line-height: 1.5;
            color: var(--color-text-secondary);
          }
        }
      }

      .status-history {
        margin: var(--spacing-4) 0;

        h4 {
          margin-bottom: var(--spacing-3);
          font-size: 1rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }
      }
    }
  }
</style>
