# D盘软件安装指南

## 📁 D盘目录规划

```
D:\
├── Development\                    # 开发环境主目录
│   ├── Java\                      # Java相关
│   │   ├── jdk-17\                # Java JDK 17
│   │   └── maven\                 # Maven
│   ├── Redis\                     # Redis数据库
│   ├── Tools\                     # 开发工具
│   │   ├── IntelliJ-IDEA\         # IntelliJ IDEA
│   │   ├── MySQL-Workbench\       # MySQL Workbench
│   │   └── Postman\               # API测试工具
│   ├── Databases\                 # 数据库相关
│   │   ├── InfluxDB\              # 时序数据库
│   │   ├── Elasticsearch\         # 搜索引擎
│   │   └── Neo4j\                 # 图数据库
│   ├── Middleware\                # 中间件
│   │   ├── RabbitMQ\              # 消息队列
│   │   ├── Nginx\                 # Web服务器
│   │   ├── Prometheus\            # 监控系统
│   │   └── Grafana\               # 数据可视化
│   └── Data\                      # 数据目录
│       ├── mysql-data\            # MySQL数据
│       ├── redis-data\            # Redis数据
│       └── influxdb-data\         # InfluxDB数据
├── Projects\                      # 项目代码目录
│   └── JSCIM-System\              # 当前项目
└── Workspace\                     # 工作区
    ├── maven-repo\                # Maven本地仓库
    ├── npm-cache\                 # npm缓存
    └── temp\                      # 临时文件
```

## 🚀 手动下载安装指南

### 1. Java JDK 17 (Eclipse Temurin - 免费)
```
下载地址: https://adoptium.net/temurin/releases/
选择版本: JDK 17 LTS Windows x64
安装目录: D:\Development\Java\jdk-17\
```

**安装步骤:**
1. 下载 `OpenJDK17U-jdk_x64_windows_hotspot_17.0.10_7.msi`
2. 运行安装程序，自定义安装路径为: `D:\Development\Java\jdk-17\`
3. 添加环境变量:
   - JAVA_HOME: `D:\Development\Java\jdk-17`
   - PATH: 添加 `%JAVA_HOME%\bin`

### 2. Apache Maven
```
下载地址: https://maven.apache.org/download.cgi
选择版本: Binary zip archive (apache-maven-3.9.6-bin.zip)
安装目录: D:\Development\Java\maven\
```

**安装步骤:**
1. 下载并解压到 `D:\Development\Java\maven\`
2. 配置环境变量:
   - MAVEN_HOME: `D:\Development\Java\maven`
   - PATH: 添加 `%MAVEN_HOME%\bin`
3. 配置Maven本地仓库: 编辑 `D:\Development\Java\maven\conf\settings.xml`
   ```xml
   <localRepository>D:\Workspace\maven-repo</localRepository>
   ```

### 3. Redis (Windows版本)
```
下载地址: https://github.com/microsoftarchive/redis/releases
选择版本: Redis-x64-3.0.504.zip (Windows稳定版)
安装目录: D:\Development\Redis\
```

**安装步骤:**
1. 下载并解压到 `D:\Development\Redis\`
2. 修改配置文件 `redis.windows.conf`:
   ```
   dir D:\Development\Data\redis-data\
   logfile D:\Development\Redis\logs\redis.log
   ```
3. 创建Windows服务:
   ```cmd
   D:\Development\Redis\redis-server.exe --service-install --service-name Redis --port 6379
   ```

### 4. IntelliJ IDEA Community (免费版)
```
下载地址: https://www.jetbrains.com/idea/download/
选择版本: Community Edition (免费)
安装目录: D:\Development\Tools\IntelliJ-IDEA\
```

### 5. MySQL Workbench
```
下载地址: https://dev.mysql.com/downloads/workbench/
选择版本: MySQL Workbench 8.0.36 Windows (x86, 64-bit), MSI Installer
安装目录: D:\Development\Tools\MySQL-Workbench\
```

## ⚙️ 使用脚本自动化安装

### PowerShell安装脚本 (管理员权限运行)
```powershell
# 创建目录结构
$basePath = "D:\Development"
$dirs = @(
    "$basePath\Java\jdk-17",
    "$basePath\Java\maven",
    "$basePath\Redis",
    "$basePath\Tools\IntelliJ-IDEA",
    "$basePath\Tools\MySQL-Workbench",
    "$basePath\Databases\InfluxDB",
    "$basePath\Middleware\RabbitMQ",
    "$basePath\Data\mysql-data",
    "$basePath\Data\redis-data",
    "D:\Workspace\maven-repo",
    "D:\Workspace\npm-cache",
    "D:\Projects\JSCIM-System"
)

foreach ($dir in $dirs) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "Created directory: $dir" -ForegroundColor Green
    }
}

# 使用Chocolatey安装 (如果已安装choco)
if (Get-Command choco -ErrorAction SilentlyContinue) {
    Write-Host "使用Chocolatey安装软件..." -ForegroundColor Yellow
    
    # 安装到自定义路径
    choco install openjdk17 --installargs="INSTALLDIR=D:\Development\Java\jdk-17" -y
    choco install maven --installargs="INSTALLDIR=D:\Development\Java\maven" -y
    choco install redis-64 --installargs="TARGETDIR=D:\Development\Redis" -y
    choco install intellijidea-community --installargs="INSTALLDIR=D:\Development\Tools\IntelliJ-IDEA" -y
}

Write-Host "目录结构创建完成！请手动下载并安装到指定目录。" -ForegroundColor Green
```

## 🔧 环境变量配置脚本

### 批处理脚本设置环境变量
```batch
@echo off
echo 配置环境变量...

:: Java相关
setx JAVA_HOME "D:\Development\Java\jdk-17" /M
setx MAVEN_HOME "D:\Development\Java\maven" /M

:: 更新PATH
for /f "usebackq tokens=2,*" %%A in (`reg query HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session` do (
    if "%%A"=="Path" (
        setx PATH "%%B;%JAVA_HOME%\bin;%MAVEN_HOME%\bin" /M
    )
)

:: Redis服务配置
setx REDIS_HOME "D:\Development\Redis" /M

echo 环境变量配置完成！
echo 请重启命令行窗口使环境变量生效。
pause
```

## 📦 Docker Compose 统一部署方案

### docker-compose.yml (D盘数据目录)
```yaml
version: '3.8'
services:
  redis:
    image: redis:7.2-alpine
    container_name: ic-cim-redis
    ports:
      - "6379:6379"
    volumes:
      - D:\Development\Data\redis-data:/data
    restart: unless-stopped

  influxdb:
    image: influxdb:2.7
    container_name: ic-cim-influxdb
    ports:
      - "8086:8086"
    volumes:
      - D:\Development\Data\influxdb-data:/var/lib/influxdb2
    environment:
      - INFLUXDB_DB=ic_cim
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=admin123
    restart: unless-stopped

  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: ic-cim-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - D:\Development\Data\rabbitmq-data:/var/lib/rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
    restart: unless-stopped

  elasticsearch:
    image: elasticsearch:8.12.0
    container_name: ic-cim-elasticsearch
    ports:
      - "9200:9200"
    volumes:
      - D:\Development\Data\elasticsearch-data:/usr/share/elasticsearch/data
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    restart: unless-stopped
```

## ✅ 安装验证命令

### 验证脚本
```batch
@echo off
echo ==========================================
echo          软件安装验证检查
echo ==========================================

echo.
echo 检查Java...
java -version
echo JAVA_HOME: %JAVA_HOME%

echo.
echo 检查Maven...
mvn --version
echo MAVEN_HOME: %MAVEN_HOME%

echo.
echo 检查Redis...
D:\Development\Redis\redis-cli.exe ping

echo.
echo 检查目录结构...
if exist "D:\Development\" (
    echo ✓ D:\Development\ 目录存在
) else (
    echo ✗ D:\Development\ 目录不存在
)

if exist "D:\Workspace\" (
    echo ✓ D:\Workspace\ 目录存在
) else (
    echo ✗ D:\Workspace\ 目录不存在
)

echo.
echo ==========================================
echo           验证检查完成
echo ==========================================
pause
```

## 🎯 安装顺序建议

1. **创建目录结构** (运行PowerShell脚本)
2. **安装Java JDK 17** → 配置JAVA_HOME
3. **安装Maven** → 配置MAVEN_HOME和本地仓库
4. **安装Redis** → 配置服务和数据目录
5. **安装开发工具** (IntelliJ IDEA Community, MySQL Workbench)
6. **验证安装** (运行验证脚本)

## 💡 注意事项

- **权限**: 部分安装需要管理员权限
- **防火墙**: 可能需要配置防火墙允许端口访问
- **路径**: 避免使用包含空格或中文的路径
- **备份**: 安装前建议备份重要数据
- **环境变量**: 安装后需要重启命令行使环境变量生效

---
*D盘安装方案 - 更新时间: 2025年1月*