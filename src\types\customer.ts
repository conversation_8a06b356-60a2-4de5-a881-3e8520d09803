/**
 * 客户相关类型定义
 */

import type { ProductType } from './order'

// 客户状态
export type CustomerStatus = 'active' | 'inactive' | 'pending' | 'suspended' | 'archived'

// 客户类型 - OSAT行业专业分类
export type CustomerType = 'fabless' | 'idm' | 'foundry' | 'distributor' | 'broker' | 'ems'

// 客户等级
export type CustomerLevel = 'strategic' | 'important' | 'standard' | 'potential'

// 客户规模
export type CustomerScale = 'startup' | 'small' | 'medium' | 'large' | 'enterprise'

// 应用领域 - IC应用分类
export type ApplicationField =
  | 'automotive' // 汽车电子
  | 'consumer' // 消费电子
  | 'communication' // 通信
  | 'industrial' // 工业控制
  | 'ai' // AI芯片
  | 'iot' // 物联网
  | 'medical' // 医疗电子
  | 'aerospace' // 航空航天
  | 'power' // 电源管理
  | 'security' // 安全芯片

// 工艺节点
export type ProcessNode =
  | '3nm'
  | '5nm'
  | '7nm'
  | '10nm'
  | '14nm'
  | '16nm'
  | '22nm'
  | '28nm'
  | '40nm'
  | '55nm'
  | '65nm'
  | '90nm'
  | '130nm'
  | '180nm'
  | 'above_180nm'

// 封装类型偏好
export type PackagePreference =
  | 'QFP'
  | 'BGA'
  | 'CSP'
  | 'QFN'
  | 'SOP'
  | 'TSOP'
  | 'TQFP'
  | 'LQFP'
  | 'FC'
  | 'WLCSP'
  | 'DIP'

// 信用等级
export type CreditLevel = 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D'

// 联系人角色枚举 - IC设计行业专业角色
export type ContactRole =
  | 'FAE' // 现场应用工程师
  | 'DesignManager' // 设计经理
  | 'TestEngineer' // 测试工程师
  | 'QualityEngineer' // 质量工程师
  | 'PurchaseManager' // 采购经理
  | 'SupplyChainDirector' // 供应链总监
  | 'BusinessManager' // 商务经理
  | 'LegalManager' // 法务经理
  | 'CTO' // 首席技术官
  | 'VP' // 副总裁
  | 'ProjectDirector' // 项目总监
  | 'TechnicalDirector' // 技术总监
  | 'PackageEngineer' // 封装工程师
  | 'ReliabilityEngineer' // 可靠性工程师
  | 'PMManager' // 项目经理
  | 'CustomerService' // 客服代表

// 联系人重要程度
export type ContactImportance = 1 | 2 | 3 | 4 | 5

// 联系人影响力等级
export type InfluenceLevel = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10

// 沟通方式偏好
export type CommunicationPreference =
  | 'Email'
  | 'Phone'
  | 'WeChat'
  | 'InPerson'
  | 'VideoConference'
  | 'DingTalk'
  | 'Teams'

// 联系人类型
export type ContactType =
  | 'Technical' // 技术对接
  | 'Business' // 商务对接
  | 'Quality' // 质量对接
  | 'Procurement' // 采购对接
  | 'Decision' // 决策者
  | 'Support' // 支持角色

// 联系人状态
export type ContactStatus = 'active' | 'inactive' | 'departed' | 'suspended'

// 客户技术信息接口
export interface CustomerTechnicalInfo {
  mainProductTypes: string[] // 主要产品类型
  packageTypePreferences: string[] // 封装类型偏好
  testRequirements: string[] // 测试要求
  qualityStandards: string[] // 质量标准
  certifications?: string[] // 认证信息
}

// 客户财务信息接口
export interface CustomerFinancialInfo {
  creditLimit: number
  creditLevel: CreditLevel // 信用等级
  paymentTerms: string
  paymentCycle: number // 付款周期（天）
  currency: string
  taxId?: string
  taxRate: number // 税率（小数形式，如0.13表示13%）
  bankInfo?: {
    bankName: string
    accountNumber: string
    swiftCode?: string
  }
  invoiceInfo?: {
    // 发票信息
    invoiceTitle: string // 发票抬头
    taxNumber: string // 税号
    bankName: string // 开户银行
    bankAccount: string // 银行账号
    registeredAddress?: string // 注册地址
    registeredPhone?: string // 注册电话
  }
}

// 合作历史接口
export interface CooperationHistory {
  firstCooperationTime: string
  cooperationYears: number
  totalOrders: number
  totalSalesAmount: number
  ordersLast12Months: number
  salesLast12Months: number
  averageOrderAmount: number
  satisfactionScore: number
}

// 客户联系人（简化版本，用于Mock数据）
export interface CustomerContact {
  id: string
  name: string
  position: string
  department: string
  mobile: string
  email: string
  isPrimary: boolean
  contactType: ContactType[]
  communicationRecords: any[] // 可以根据需要进一步细化
}

// 联系人基本信息
export interface Contact {
  id: string
  customerId: string // 所属客户ID
  name: string
  englishName?: string
  avatar?: string
  position: string
  department: string
  level?: string // 职级：总监、经理、主管、专员等
  email: string
  phone: string
  mobile?: string
  wechat?: string
  dingTalk?: string
  role: ContactRole
  contactType: ContactType[] // 联系人类型（可多选）
  status: ContactStatus

  // 工作信息
  hireDate?: string
  reportTo?: string // 汇报对象
  industryExperience?: number // 年限
  professionalDomain?: string[] // 专业领域

  // 关系属性
  importance: ContactImportance // 1-5级重要程度
  trustLevel: ContactImportance // 1-5级信任度
  influenceScore: InfluenceLevel // 1-10级影响力
  isDecisionMaker: boolean // 是否决策者
  isPrimaryContact: boolean // 是否主要联系人
  isPrimary: boolean // 是否主要联系人（简化字段）

  // 偏好设置
  languagePreference?: string[] // 语言偏好
  communicationPreference?: CommunicationPreference[] // 沟通方式偏好
  preferredContactTime?: string // 最佳联系时间
  timezone?: string // 时区

  // 沟通历史统计
  lastContactDate?: string
  communicationFrequency?: number // 沟通频次（每月）
  totalCommunications?: number // 历史沟通次数

  // 额外信息
  specialties?: string[] // 专长领域
  hobbies?: string[] // 个人爱好
  birthday?: string // 生日
  remarks?: string // 备注

  // 系统信息
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy?: string
}

// 客户公司组织结构
export interface CustomerOrganization {
  id: string
  name: string
  contacts: Contact[]
  subDepartments?: CustomerOrganization[]
}

// 沟通记录类型
export type CommunicationRecordType =
  | 'Call' // 电话沟通
  | 'Email' // 邮件沟通
  | 'Meeting' // 会议
  | 'Visit' // 拜访
  | 'WeChat' // 微信沟通
  | 'VideoCall' // 视频通话
  | 'Exhibition' // 展会接触
  | 'OnlineDemo' // 线上演示

// 沟通结果状态
export type CommunicationStatus = 'completed' | 'pending' | 'cancelled' | 'postponed'

// 沟通结果评估
export type CommunicationResult = 'excellent' | 'good' | 'average' | 'poor' | 'failed'

// 沟通场景类型 - OSAT行业专业场景
export type CommunicationScene =
  | 'TechDiscussion' // 技术讨论
  | 'BusinessNegotiation' // 商务谈判
  | 'ProductIntro' // 产品介绍
  | 'QualityIssue' // 质量问题
  | 'DeliveryCoordination' // 交期协商
  | 'ProjectReview' // 项目评审
  | 'ContractDiscussion' // 合同讨论
  | 'ProblemSolving' // 问题解决
  | 'RelationshipBuilding' // 关系维护
  | 'MarketInfo' // 市场信息

// 附件类型
export interface CommunicationAttachment {
  id: string
  fileName: string
  fileSize: number
  fileType: string
  uploadTime: string
  downloadUrl: string
  description?: string
}

// 跟进任务
export interface FollowUpTask {
  id: string
  description: string
  assignee: string // 负责人
  dueDate: string
  status: 'pending' | 'in_progress' | 'completed' | 'overdue'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdAt: string
}

// 沟通记录
export interface CommunicationRecord {
  id: string
  contactId: string
  customerId: string
  type: CommunicationRecordType
  scene: CommunicationScene // 沟通场景
  subject: string // 沟通主题
  date: string
  startTime?: string
  endTime?: string
  duration?: number // 沟通时长（分钟）
  location?: string // 沟通地点
  content: string
  participants: string[] // 参与人员
  followUpTasks?: FollowUpTask[] // 后续任务
  importance: 1 | 2 | 3 | 4 | 5 // 重要程度
  result: CommunicationResult // 沟通结果
  status: CommunicationStatus // 沟通状态
  customerFeedback?: string // 客户反馈
  nextContactDate?: string // 下次联系时间
  nextContactPurpose?: string // 下次联系目的
  attachments?: CommunicationAttachment[] // 附件
  tags?: string[] // 标签
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy?: string
}

// 沟通记录查询参数
export interface CommunicationRecordQueryParams {
  page?: number
  pageSize?: number
  keyword?: string // 搜索关键词
  customerId?: string // 客户ID
  contactId?: string // 联系人ID
  type?: CommunicationRecordType[] // 沟通方式
  scene?: CommunicationScene[] // 沟通场景
  importance?: number[] // 重要程度
  result?: CommunicationResult[] // 沟通结果
  status?: CommunicationStatus[] // 沟通状态
  dateRange?: [string, string] | null // 时间范围
  createdBy?: string // 创建人
  hasFollowUp?: boolean // 是否有跟进任务
}

// 沟通记录列表响应
export interface CommunicationRecordListResponse {
  data: CommunicationRecord[]
  total: number
  page: number
  pageSize: number
}

// 创建沟通记录数据
export interface CreateCommunicationRecordData {
  contactId: string
  customerId: string
  type: CommunicationRecordType
  scene: CommunicationScene
  subject: string
  date: string
  startTime?: string
  endTime?: string
  duration?: number
  location?: string
  content: string
  participants: string[]
  importance: 1 | 2 | 3 | 4 | 5
  result: CommunicationResult
  status: CommunicationStatus
  customerFeedback?: string
  nextContactDate?: string
  nextContactPurpose?: string
  tags?: string[]
  attachments?: File[]
  followUpTasks?: Omit<FollowUpTask, 'id' | 'createdAt'>[]
}

// 更新沟通记录数据
export type UpdateCommunicationRecordData = Partial<CreateCommunicationRecordData> & {
  id: string
}

// 沟通统计数据
export interface CommunicationStatistics {
  totalRecords: number
  thisMonthRecords: number
  averagePerMonth: number
  mostActiveCustomer: {
    customerId: string
    customerName: string
    count: number
  }
  typeDistribution: Record<CommunicationRecordType, number>
  sceneDistribution: Record<CommunicationScene, number>
  resultDistribution: Record<CommunicationResult, number>
  upcomingFollowUps: number
  overdueFollowUps: number
}

// 联系人查询参数
export interface ContactQueryParams {
  page?: number
  pageSize?: number
  keyword?: string // 搜索关键词
  customerId?: string // 客户ID
  customerIds?: string[] // 批量客户ID
  roles?: ContactRole[] // 角色筛选
  contactTypes?: ContactType[] // 联系人类型筛选
  status?: ContactStatus[] // 状态筛选
  importance?: ContactImportance[] // 重要程度筛选
  isDecisionMaker?: boolean // 是否决策者
  isPrimaryContact?: boolean // 是否主要联系人
  departmentKeyword?: string // 部门关键词
  createdDateRange?: [string, string] | null
  lastContactDateRange?: [string, string] | null
}

// 联系人列表响应
export interface ContactListResponse {
  data: Contact[]
  total: number
  page: number
  pageSize: number
}

// 创建联系人数据
export interface CreateContactData {
  customerId: string
  name: string
  englishName?: string
  position: string
  department: string
  level?: string
  email: string
  phone: string
  mobile?: string
  wechat?: string
  dingTalk?: string
  role: ContactRole
  contactType: ContactType[]
  importance: ContactImportance
  trustLevel: ContactImportance
  influenceScore: InfluenceLevel
  isDecisionMaker: boolean
  isPrimaryContact: boolean
  languagePreference?: string[]
  communicationPreference?: CommunicationPreference[]
  preferredContactTime?: string
  timezone?: string
  specialties?: string[]
  hobbies?: string[]
  birthday?: string
  remarks?: string
}

// 更新联系人数据
export type UpdateContactData = Partial<CreateContactData> & {
  id: string
}

// 客户主信息
export interface Customer {
  id: string
  code: string
  name: string
  englishName?: string
  shortName?: string
  logo?: string
  website?: string
  foundedYear?: number

  // 客户属性
  type: CustomerType
  level: CustomerLevel
  status: CustomerStatus
  scale: CustomerScale
  industryType: ProductType
  creditLevel: CreditLevel

  // OSAT行业专业属性
  applicationFields: ApplicationField[] // 主要应用领域
  processNodes: ProcessNode[] // 支持工艺节点
  packagePreferences: PackagePreference[] // 封装偏好
  qualityStandard: string[] // 质量标准 (JEDEC, AEC-Q100等)
  complianceRequirements: string[] // 合规要求

  // 联系信息
  contact?: Contact // 主要联系人（可选，兼容现有Mock数据）
  contacts?: Contact[] // 支持多个联系人
  address?: {
    country: string
    province: string
    city: string
    district?: string
    street: string
    postalCode?: string
    coordinates?: {
      lat: number
      lng: number
    }
  }

  // 地址信息
  registeredAddress?: string // 注册地址
  officeAddress?: string // 办公地址
  unifiedSocialCreditCode?: string // 统一社会信用代码

  // 业务信息
  businessInfo?: {
    annualRevenue?: number
    employeeCount?: number
    mainProducts?: string[]
    marketPosition?: string
    competitorInfo?: string[]
  }

  // 财务信息
  financialInfo?: CustomerFinancialInfo

  // 质量认证
  qualityCertifications?: string[]

  // 技术信息
  technicalInfo?: CustomerTechnicalInfo

  // 备注信息
  remarks?: string

  // 关键指标
  metrics?: {
    totalOrders: number
    totalRevenue: number
    averageOrderValue: number
    onTimeDeliveryRate: number
    qualityScore: number
    satisfactionScore: number
  }

  // 合作历史
  cooperationHistory?: CooperationHistory

  // 时间信息
  createdAt: string
  updatedAt: string
  createdBy: string
  lastContactDate?: string
}

// 客户查询参数
export interface CustomerQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  status?: CustomerStatus[]
  type?: CustomerType[]
  scale?: CustomerScale[]
  industryType?: ProductType[]
  creditLevel?: CreditLevel[]
  createdDateRange?: [string, string] | null
}

// 客户列表响应
export interface CustomerListResponse {
  data: Customer[]
  total: number
  page: number
  pageSize: number
  totalPages: number // 添加totalPages字段
}

// 创建客户数据
export interface CreateCustomerData {
  code: string
  name: string
  englishName?: string
  shortName?: string
  website?: string
  foundedYear?: number
  type: CustomerType
  level: CustomerLevel
  scale: CustomerScale
  industryType: ProductType
  creditLevel: CreditLevel
  applicationFields: ApplicationField[]
  processNodes: ProcessNode[]
  packagePreferences: PackagePreference[]
  qualityStandard: string[]
  complianceRequirements: string[]
  unifiedSocialCreditCode?: string
  registeredAddress?: string
  officeAddress?: string
  contacts: Array<Omit<Contact, 'id' | 'customerId'>>
  contact?: Omit<Contact, 'id'> // 可选，兼容现有数据结构
  address?: Customer['address']
  businessInfo?: Customer['businessInfo']
  financialInfo?: Customer['financialInfo']
  technicalInfo?: Customer['technicalInfo']
  remarks?: string
}

// 更新客户数据
export type UpdateCustomerData = Partial<CreateCustomerData> & {
  id: string
}

// 客户导入数据
export interface CustomerImportData {
  customerCode?: string
  customerName: string
  englishName?: string
  shortName?: string
  customerType: string
  customerLevel: string
  customerScale: string
  industryType: string
  creditLevel: string
  applicationFields: string
  processNodes: string
  packagePreferences: string
  qualityStandard: string
  complianceRequirements: string
  contactName: string
  contactEmail: string
  contactPhone: string
  contactPosition: string
  country: string
  province: string
  city: string
  address: string
  website?: string
  remarks?: string
}
