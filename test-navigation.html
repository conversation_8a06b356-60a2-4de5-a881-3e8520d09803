<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-link {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #409eff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #337ecc;
        }
        .iframe-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background: #f0f9ff;
            color: #059669;
            border-left: 4px solid #10b981;
        }
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border-left: 4px solid #ef4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CIM系统导航功能测试</h1>
        <p>点击下面的链接测试各个页面是否可以正常访问：</p>
        
        <div class="links-container">
            <a href="http://localhost:8854/" class="test-link" target="testFrame">首页</a>
            <a href="http://localhost:8854/customers" class="test-link" target="testFrame">客户管理</a>
            <a href="http://localhost:8854/production" class="test-link" target="testFrame">生产计划</a>
            <a href="http://localhost:8854/orders" class="test-link" target="testFrame">订单管理</a>
            <a href="http://localhost:8854/inquiry/list" class="test-link" target="testFrame">客户询价</a>
            <a href="http://localhost:8854/inventory/management" class="test-link" target="testFrame">物料库存</a>
        </div>
        
        <div id="status" class="status"></div>
        
        <div class="iframe-container">
            <iframe name="testFrame" src="http://localhost:8854/"></iframe>
        </div>
    </div>

    <script>
        // 测试页面加载状态
        const iframe = document.querySelector('iframe');
        const statusDiv = document.getElementById('status');
        
        iframe.onload = function() {
            try {
                const currentUrl = iframe.contentWindow.location.href;
                statusDiv.className = 'status success';
                statusDiv.textContent = `✅ 页面加载成功: ${currentUrl}`;
            } catch (e) {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 页面加载成功 (跨域限制，无法获取URL)';
            }
        };
        
        iframe.onerror = function() {
            statusDiv.className = 'status error';
            statusDiv.textContent = '❌ 页面加载失败';
        };
        
        // 为测试链接添加点击事件
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', function() {
                statusDiv.className = 'status';
                statusDiv.textContent = '⏳ 正在加载...';
            });
        });
    </script>
</body>
</html>