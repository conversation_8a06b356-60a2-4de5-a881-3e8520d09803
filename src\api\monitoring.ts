/**
 * 监控中心API接口
 * IC封装测试工厂实时监控数据服务
 */

import type {
  ProductionKPI,
  EquipmentStatus,
  QualityKPI,
  SystemStatusSummary,
  RealtimeMessage,
  MonitoringDataResponse,
  ProductionTrendPoint,
  EquipmentAlarm,
  SPCStatus,
  DefectAnalysis,
  CustomerSatisfaction,
  ChartSeries,
  MonitoringApiResponse
} from '@/types/monitoring'

import { MockApiHelper } from './config'

// 模拟API请求的辅助函数
const mockRequest = async <T>(url: string, data: T): Promise<T> => {
  await MockApiHelper.delay()
  return data
}

/**
 * 监控数据API类
 */
export class MonitoringAPI {
  /**
   * 获取生产KPI数据
   */
  static async getProductionKPI(): Promise<ProductionKPI> {
    return mockRequest('/api/monitoring/production/kpi', {
      dailyOutput: 125600,
      outputTarget: 120000,
      outputRate: 104.7,
      overallYield: 98.2,
      yieldTarget: 97.5,
      oeeRate: 87.3,
      wipCount: 8420,
      planAchievementRate: 102.1,
      updateTime: new Date().toISOString()
    })
  }

  /**
   * 获取生产趋势数据
   */
  static async getProductionTrend(timeRange: string = '24h'): Promise<ChartSeries[]> {
    const now = Date.now()
    const hours = timeRange === '24h' ? 24 : 8
    const interval = 3600000 // 1小时间隔

    const data: ProductionTrendPoint[] = []
    for (let i = hours; i >= 0; i--) {
      const timestamp = new Date(now - i * interval).toISOString()
      data.push({
        timestamp,
        output: 5000 + Math.random() * 2000,
        yield: 96.5 + Math.random() * 3,
        oee: 85 + Math.random() * 10,
        defectRate: Math.random() * 0.5
      })
    }

    return mockRequest('/api/monitoring/production/trend', [
      {
        name: '产量',
        data: data.map(d => ({ timestamp: d.timestamp, value: d.output })),
        type: 'line',
        color: '#1890ff'
      },
      {
        name: '良率',
        data: data.map(d => ({ timestamp: d.timestamp, value: d.yield })),
        type: 'line',
        color: '#52c41a'
      },
      {
        name: 'OEE',
        data: data.map(d => ({ timestamp: d.timestamp, value: d.oee })),
        type: 'line',
        color: '#faad14'
      }
    ])
  }

  /**
   * 获取设备状态列表
   */
  static async getEquipmentStatus(): Promise<EquipmentStatus[]> {
    const equipments = [
      { id: 'PRB001', name: 'Prober #1', type: 'prober', area: 'CP测试区', position: 'A1' },
      { id: 'PRB002', name: 'Prober #2', type: 'prober', area: 'CP测试区', position: 'A2' },
      { id: 'BND001', name: 'Wire Bonder #1', type: 'bonder', area: '线键合区', position: 'B1' },
      { id: 'BND002', name: 'Wire Bonder #2', type: 'bonder', area: '线键合区', position: 'B2' },
      { id: 'MOL001', name: 'Molding Machine #1', type: 'molding', area: '塑封区', position: 'C1' },
      { id: 'TST001', name: 'Final Tester #1', type: 'tester', area: '最终测试区', position: 'D1' },
      { id: 'TST002', name: 'Final Tester #2', type: 'tester', area: '最终测试区', position: 'D2' },
      { id: 'HDL001', name: 'Handler #1', type: 'handler', area: '分选区', position: 'E1' }
    ]

    const statuses = ['running', 'idle', 'maintenance', 'alarm', 'offline']
    const healths = ['excellent', 'good', 'fair', 'poor', 'critical']

    return mockRequest(
      '/api/monitoring/equipment/status',
      equipments.map(eq => ({
        equipmentId: eq.id,
        equipmentName: eq.name,
        equipmentType: eq.type as any,
        status: statuses[Math.floor(Math.random() * statuses.length)] as any,
        health: healths[Math.floor(Math.random() * healths.length)] as any,
        temperature: 25 + Math.random() * 10,
        pressure: 100 + Math.random() * 20,
        vibration: Math.random() * 0.5,
        utilization: 60 + Math.random() * 35,
        oee: {
          availability: 85 + Math.random() * 10,
          performance: 88 + Math.random() * 8,
          quality: 95 + Math.random() * 4,
          overall: 80 + Math.random() * 15
        },
        currentRecipe: `Recipe_${eq.type.toUpperCase()}_${Math.floor(Math.random() * 5) + 1}`,
        alarmCount: Math.floor(Math.random() * 3),
        lastMaintenance: new Date(Date.now() - Math.random() * 7 * 24 * 3600000).toISOString(),
        nextMaintenance: new Date(
          Date.now() + (7 + Math.random() * 14) * 24 * 3600000
        ).toISOString(),
        location: {
          area: eq.area,
          position: eq.position
        },
        operator: Math.random() > 0.3 ? `操作员${Math.floor(Math.random() * 20) + 1}` : undefined,
        updateTime: new Date().toISOString()
      }))
    )
  }

  /**
   * 获取设备告警列表
   */
  static async getEquipmentAlarms(): Promise<EquipmentAlarm[]> {
    const alarmTypes = ['critical', 'warning', 'info']
    const alarmMessages = [
      '温度超出正常范围',
      '压力异常',
      'Recipe版本不匹配',
      '探针卡寿命到期提醒',
      '设备通信超时',
      '气压不足',
      '定期维护到期'
    ]

    const alarms: EquipmentAlarm[] = []
    for (let i = 0; i < 15; i++) {
      const alarmTime = new Date(Date.now() - Math.random() * 24 * 3600000)
      const isActive = Math.random() > 0.3
      alarms.push({
        alarmId: `ALM${String(i + 1).padStart(3, '0')}`,
        equipmentId: `EQ${String(Math.floor(Math.random() * 8) + 1).padStart(3, '0')}`,
        equipmentName: `设备${Math.floor(Math.random() * 8) + 1}`,
        alarmType: alarmTypes[Math.floor(Math.random() * alarmTypes.length)] as any,
        alarmCode: `E${Math.floor(Math.random() * 9000) + 1000}`,
        alarmMessage: alarmMessages[Math.floor(Math.random() * alarmMessages.length)],
        alarmTime: alarmTime.toISOString(),
        acknowledgedBy:
          !isActive && Math.random() > 0.5
            ? `技术员${Math.floor(Math.random() * 5) + 1}`
            : undefined,
        acknowledgedTime:
          !isActive && Math.random() > 0.5
            ? new Date(alarmTime.getTime() + Math.random() * 3600000).toISOString()
            : undefined,
        resolvedTime: !isActive
          ? new Date(alarmTime.getTime() + Math.random() * 7200000).toISOString()
          : undefined,
        isActive
      })
    }

    return mockRequest(
      '/api/monitoring/equipment/alarms',
      alarms.sort((a, b) => new Date(b.alarmTime).getTime() - new Date(a.alarmTime).getTime())
    )
  }

  /**
   * 获取质量KPI数据
   */
  static async getQualityKPI(): Promise<QualityKPI> {
    return mockRequest('/api/monitoring/quality/kpi', {
      overallYield: 98.2,
      firstPassYield: 96.8,
      defectDensity: 0.18,
      customerComplaints: 2,
      qualityCostRatio: 1.2,
      cpkIndex: 1.67,
      supplierQualityIndex: 94.5,
      updateTime: new Date().toISOString()
    })
  }

  /**
   * 获取SPC控制状态
   */
  static async getSPCStatus(): Promise<SPCStatus[]> {
    const processes = [
      { id: 'CP_001', name: 'CP测试电流参数', parameter: 'Leakage Current' },
      { id: 'CP_002', name: 'CP测试电压参数', parameter: 'Breakdown Voltage' },
      { id: 'ASM_001', name: '线键合拉力测试', parameter: 'Bond Pull Strength' },
      { id: 'ASM_002', name: '芯片贴附压力', parameter: 'Die Attach Force' },
      { id: 'FT_001', name: '最终测试功耗', parameter: 'Power Consumption' },
      { id: 'FT_002', name: '最终测试频率', parameter: 'Operating Frequency' }
    ]

    const statuses = ['in_control', 'out_of_control', 'warning']

    return mockRequest(
      '/api/monitoring/quality/spc',
      processes.map(proc => ({
        processId: proc.id,
        processName: proc.name,
        parameter: proc.parameter,
        status: statuses[Math.floor(Math.random() * statuses.length)] as any,
        cpk: 1.2 + Math.random() * 0.8,
        ucl: 100 + Math.random() * 20,
        lcl: 80 + Math.random() * 20,
        target: 90 + Math.random() * 10,
        lastValue: 85 + Math.random() * 20,
        trendDirection: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as any,
        updateTime: new Date().toISOString()
      }))
    )
  }

  /**
   * 获取缺陷分析数据
   */
  static async getDefectAnalysis(): Promise<DefectAnalysis[]> {
    const defectTypes = [
      '线键合不良',
      '芯片贴附偏移',
      '塑封气泡',
      '外观不良',
      '电测失效',
      '封装开裂',
      '引脚氧化'
    ]

    return mockRequest(
      '/api/monitoring/quality/defects',
      defectTypes
        .map((type, index) => ({
          defectType: type,
          count: Math.floor(Math.random() * 200) + 10,
          percentage: Math.random() * 25 + 5,
          trend: ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)] as any,
          rootCause: index < 3 ? `${type}根本原因分析` : undefined,
          correctionAction: index < 2 ? `${type}改善措施` : undefined
        }))
        .sort((a, b) => b.count - a.count)
    )
  }

  /**
   * 获取客户满意度数据
   */
  static async getCustomerSatisfaction(): Promise<CustomerSatisfaction[]> {
    const customers = [
      'Apple Inc.',
      'Samsung',
      'Qualcomm',
      'MediaTek',
      'Broadcom',
      'AMD',
      'NVIDIA',
      'Intel'
    ]

    return mockRequest(
      '/api/monitoring/quality/customer-satisfaction',
      customers.map((name, index) => ({
        customerId: `CUST${String(index + 1).padStart(3, '0')}`,
        customerName: name,
        satisfactionScore: 85 + Math.random() * 12,
        qualityRating: 88 + Math.random() * 10,
        deliveryRating: 90 + Math.random() * 8,
        serviceRating: 87 + Math.random() * 11,
        feedbackCount: Math.floor(Math.random() * 50) + 10,
        lastFeedbackTime: new Date(Date.now() - Math.random() * 30 * 24 * 3600000).toISOString()
      }))
    )
  }

  /**
   * 获取系统状态摘要
   */
  static async getSystemStatusSummary(): Promise<SystemStatusSummary> {
    return mockRequest('/api/monitoring/system/status', {
      overallHealth: ['healthy', 'warning', 'critical'][Math.floor(Math.random() * 3)] as any,
      productionStatus: ['normal', 'behind_schedule', 'ahead_schedule', 'stopped'][
        Math.floor(Math.random() * 4)
      ] as any,
      equipmentSummary: {
        total: 24,
        running: 18,
        idle: 3,
        maintenance: 2,
        alarm: 1
      },
      qualityStatus: ['excellent', 'good', 'needs_attention', 'critical'][
        Math.floor(Math.random() * 4)
      ] as any,
      activeAlarms: {
        critical: Math.floor(Math.random() * 3),
        warning: Math.floor(Math.random() * 8) + 2,
        info: Math.floor(Math.random() * 15) + 5
      },
      dataSourceStatus: {
        mes: ['connected', 'disconnected', 'warning'][Math.floor(Math.random() * 3)] as any,
        equipment: 'connected' as any,
        quality: 'connected' as any,
        erp: ['connected', 'warning'][Math.floor(Math.random() * 2)] as any
      }
    })
  }

  /**
   * 获取实时消息
   */
  static async getRealtimeMessages(): Promise<RealtimeMessage[]> {
    const messageTypes = ['info', 'warning', 'error', 'success']
    const sources = ['production', 'equipment', 'quality', 'system']
    const messages = [
      '生产批次LT240825001已完成CP测试',
      '设备PRB001需要更换探针卡',
      '质量告警：线键合拉力测试超出控制限',
      '预测性维护：Molding Machine #1建议下周进行保养',
      '客户订单ORD20240825001交付完成',
      '系统数据备份成功完成',
      '新Recipe版本已同步到所有测试设备'
    ]

    const realtimeMessages: RealtimeMessage[] = []
    for (let i = 0; i < 20; i++) {
      realtimeMessages.push({
        id: `MSG${String(i + 1).padStart(3, '0')}`,
        type: messageTypes[Math.floor(Math.random() * messageTypes.length)] as any,
        source: sources[Math.floor(Math.random() * sources.length)] as any,
        title: '系统通知',
        message: messages[Math.floor(Math.random() * messages.length)],
        timestamp: new Date(Date.now() - Math.random() * 24 * 3600000).toISOString(),
        acknowledged: Math.random() > 0.4,
        priority: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as any
      })
    }

    return mockRequest(
      '/api/monitoring/system/messages',
      realtimeMessages.sort(
        (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )
    )
  }

  /**
   * 获取完整监控数据
   */
  static async getMonitoringData(): Promise<MonitoringDataResponse> {
    const [productionKPI, equipmentStatus, qualityKPI, systemStatus, realtimeMessages] =
      await Promise.all([
        this.getProductionKPI(),
        this.getEquipmentStatus(),
        this.getQualityKPI(),
        this.getSystemStatusSummary(),
        this.getRealtimeMessages()
      ])

    return {
      productionKPI,
      equipmentStatus,
      qualityKPI,
      systemStatus,
      realtimeMessages
    }
  }
}

// 导出默认API实例
export const monitoringApi = MonitoringAPI
export default monitoringApi
