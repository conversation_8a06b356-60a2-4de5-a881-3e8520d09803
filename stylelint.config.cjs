module.exports = {
  extends: [
    'stylelint-config-standard-scss',
    'stylelint-config-standard-vue/scss',
    'stylelint-config-recess-order'
  ],
  plugins: ['stylelint-order'],
  rules: {
    // SCSS 相关规则
    'scss/at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: ['use', 'forward', 'include', 'mixin', 'function']
      }
    ],
    'scss/dollar-variable-pattern': '^[a-z][a-zA-Z0-9-]*$',
    'scss/percent-placeholder-pattern': '^[a-z][a-zA-Z0-9-]*$',
    'scss/at-mixin-pattern': '^[a-z][a-zA-Z0-9-]*$',
    'scss/at-function-pattern': '^[a-z][a-zA-Z0-9-]*$',

    // CSS 规则优化
    'custom-property-pattern': '^[a-z][a-zA-Z0-9-]*$',
    'selector-class-pattern': '^[a-z]([a-z0-9-]+)?(__([a-z0-9]+-?)+)?(--([a-z0-9]+-?)+){0,2}$',
    'selector-id-pattern': '^[a-z]([a-z0-9-]+)?$',
    'keyframes-name-pattern': '^[a-z]([a-z0-9-]+)?$',

    // 颜色相关
    'color-hex-case': 'lower',
    'color-hex-length': 'short',
    'color-no-invalid-hex': true,

    // 字体相关
    'font-family-name-quotes': 'always-where-recommended',
    'font-weight-notation': 'numeric',

    // 单位相关
    'length-zero-no-unit': true,
    'number-leading-zero': 'always',
    'number-no-trailing-zeros': true,

    // 值相关
    'value-keyword-case': 'lower',
    'value-list-comma-space-after': 'always-single-line',
    'value-list-comma-space-before': 'never',

    // 属性相关
    'property-case': 'lower',
    'property-no-unknown': [
      true,
      {
        ignoreProperties: ['composes']
      }
    ],

    // 选择器相关
    'selector-pseudo-class-case': 'lower',
    'selector-pseudo-element-case': 'lower',
    'selector-type-case': 'lower',
    'selector-max-id': 1,
    'selector-max-universal': 1,
    'selector-no-qualifying-type': [
      true,
      {
        ignore: ['attribute', 'class']
      }
    ],

    // 规则相关
    'rule-empty-line-before': [
      'always',
      {
        except: ['first-nested'],
        ignore: ['after-comment']
      }
    ],

    // 注释相关
    'comment-empty-line-before': [
      'always',
      {
        except: ['first-nested'],
        ignore: ['stylelint-commands']
      }
    ],

    // Vue 特定规则
    'selector-pseudo-element-no-unknown': [
      true,
      {
        ignorePseudoElements: ['v-deep', 'v-global', 'v-slotted']
      }
    ],

    // 禁用一些过于严格的规则
    'no-descending-specificity': null,
    'no-duplicate-selectors': null,
    'declaration-empty-line-before': null,
    'scss/no-global-function-names': null
  },
  ignoreFiles: ['dist/**/*', 'node_modules/**/*']
}
