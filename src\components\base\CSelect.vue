<template>
  <div :class="selectClasses" @click.stop>
    <div
ref="triggerRef" :class="triggerClasses"
@click="toggleDropdown"
>
      <div class="c-select__content">
        <span v-if="displayText" class="c-select__text">{{ displayText }}</span>
        <span v-else class="c-select__placeholder">{{ placeholder }}</span>
      </div>
      <span :class="arrowClasses">▼</span>
    </div>

    <teleport to="body">
      <div
v-show="visible" ref="dropdownRef"
:class="dropdownClasses" :style="dropdownStyle"
>
        <div v-if="filterable" class="c-select__search">
          <c-input
            v-model="searchValue"
            size="small"
            placeholder="搜索选项"
            @input="handleSearch"
          />
        </div>

        <div class="c-select__options" :style="optionsStyle">
          <div
            v-for="option in filteredOptions"
            :key="getOptionValue(option)"
            :class="getOptionClasses(option)"
            @click="handleSelect(option)"
          >
            <slot name="option" :option="option">
              {{ getOptionLabel(option) }}
            </slot>
          </div>

          <div v-if="!filteredOptions.length" class="c-select__empty">
            {{ emptyText }}
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup lang="ts">
  import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
  import { onClickOutside } from '@vueuse/core'

  export interface CSelectOption {
    label: string
    value: any
    disabled?: boolean
    [key: string]: any
  }

  export interface CSelectProps {
    modelValue?: any
    options?: CSelectOption[]
    placeholder?: string
    disabled?: boolean
    clearable?: boolean
    filterable?: boolean
    multiple?: boolean
    size?: 'small' | 'medium' | 'large'
    maxHeight?: number
    emptyText?: string
    valueKey?: string
    labelKey?: string
  }

  const props = withDefaults(defineProps<CSelectProps>(), {
    placeholder: '请选择',
    disabled: false,
    clearable: false,
    filterable: false,
    multiple: false,
    size: 'medium',
    maxHeight: 200,
    emptyText: '无匹配数据',
    valueKey: 'value',
    labelKey: 'label'
  })

  const emit = defineEmits<{
    'update:modelValue': [value: any]
    change: [value: any]
    'visible-change': [visible: boolean]
  }>()

  // 引用
  const triggerRef = ref<HTMLElement>()
  const dropdownRef = ref<HTMLElement>()

  // 内部状态
  const visible = ref(false)
  const searchValue = ref('')
  const dropdownStyle = ref({})

  // 计算属性
  const selectClasses = computed(() => [
    'c-select',
    `c-select--${props.size}`,
    {
      'c-select--disabled': props.disabled,
      'c-select--multiple': props.multiple,
      'c-select--open': visible.value
    }
  ])

  const triggerClasses = computed(() => [
    'c-select__trigger',
    {
      'c-select__trigger--focused': visible.value,
      'c-select__trigger--disabled': props.disabled
    }
  ])

  const arrowClasses = computed(() => [
    'c-select__arrow',
    {
      'c-select__arrow--open': visible.value
    }
  ])

  const dropdownClasses = computed(() => [
    'c-select__dropdown',
    `c-select__dropdown--${props.size}`
  ])

  const optionsStyle = computed(() => ({
    maxHeight: `${props.maxHeight}px`
  }))

  const displayText = computed(() => {
    if (props.multiple && Array.isArray(props.modelValue)) {
      return props.modelValue
        .map(val => {
          const option = props.options?.find(opt => getOptionValue(opt) === val)
          return option ? getOptionLabel(option) : val
        })
        .join(', ')
    }

    if (props.modelValue != null) {
      const option = props.options?.find(opt => getOptionValue(opt) === props.modelValue)
      return option ? getOptionLabel(option) : props.modelValue
    }

    return ''
  })

  const filteredOptions = computed(() => {
    if (!props.filterable || !searchValue.value) {
      return props.options || []
    }

    return (props.options || []).filter(option =>
      getOptionLabel(option).toLowerCase().includes(searchValue.value.toLowerCase())
    )
  })

  // 方法
  const getOptionValue = (option: CSelectOption) => {
    return option[props.valueKey]
  }

  const getOptionLabel = (option: CSelectOption) => {
    return option[props.labelKey]
  }

  const getOptionClasses = (option: CSelectOption) => [
    'c-select__option',
    {
      'c-select__option--disabled': option.disabled,
      'c-select__option--selected': isSelected(option)
    }
  ]

  const isSelected = (option: CSelectOption) => {
    const value = getOptionValue(option)
    if (props.multiple && Array.isArray(props.modelValue)) {
      return props.modelValue.includes(value)
    }
    return props.modelValue === value
  }

  const toggleDropdown = () => {
    if (props.disabled) return

    if (visible.value) {
      hideDropdown()
    } else {
      showDropdown()
    }
  }

  const showDropdown = async () => {
    if (props.disabled) return

    visible.value = true
    emit('visible-change', true)

    await nextTick()
    updateDropdownPosition()
  }

  const hideDropdown = () => {
    visible.value = false
    searchValue.value = ''
    emit('visible-change', false)
  }

  const updateDropdownPosition = () => {
    if (!triggerRef.value || !dropdownRef.value) return

    const triggerRect = triggerRef.value.getBoundingClientRect()
    const dropdownEl = dropdownRef.value

    dropdownStyle.value = {
      position: 'fixed',
      top: `${triggerRect.bottom + 4}px`,
      left: `${triggerRect.left}px`,
      width: `${triggerRect.width}px`,
      zIndex: 1000
    }
  }

  const handleSelect = (option: CSelectOption) => {
    if (option.disabled) return

    const value = getOptionValue(option)

    if (props.multiple) {
      const currentValue = Array.isArray(props.modelValue) ? [...props.modelValue] : []
      const index = currentValue.indexOf(value)

      if (index > -1) {
        currentValue.splice(index, 1)
      } else {
        currentValue.push(value)
      }

      emit('update:modelValue', currentValue)
      emit('change', currentValue)
    } else {
      emit('update:modelValue', value)
      emit('change', value)
      hideDropdown()
    }
  }

  const handleSearch = (value: string | number) => {
    searchValue.value = String(value)
  }

  // 点击外部关闭
  onClickOutside(triggerRef, () => {
    if (visible.value) {
      hideDropdown()
    }
  })

  // 监听窗口大小变化
  const handleResize = () => {
    if (visible.value) {
      updateDropdownPosition()
    }
  }

  onMounted(() => {
    window.addEventListener('resize', handleResize)
    window.addEventListener('scroll', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('scroll', handleResize)
  })

  // 监听visible变化
  watch(visible, val => {
    if (val) {
      nextTick(updateDropdownPosition)
    }
  })
</script>

<style lang="scss">
  .c-select {
    position: relative;
    display: inline-block;
    width: 100%;

    &--disabled {
      cursor: not-allowed;
    }
  }

  .c-select__trigger {
    @include input-base;
    @include flex-between;
    cursor: pointer;

    &--focused {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgb(37 99 235 / 10%);
    }

    &--disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  .c-select__content {
    flex: 1;
    min-width: 0;
  }

  .c-select__text {
    color: var(--color-text-primary);

    @include text-ellipsis;
  }

  .c-select__placeholder {
    color: var(--color-text-placeholder);
  }

  .c-select__arrow {
    margin-left: var(--spacing-2);
    font-size: 12px;
    color: var(--color-text-tertiary);
    transition: transform var(--transition-fast);

    &--open {
      transform: rotate(180deg);
    }
  }

  .c-select__dropdown {
    overflow: hidden;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-base);
    border-radius: var(--radius-base);
    box-shadow: var(--shadow-md);

    &--small {
      .c-select__option {
        padding: 6px 12px;
        font-size: var(--font-size-xs);
      }
    }

    &--medium {
      .c-select__option {
        padding: 8px 12px;
        font-size: var(--font-size-sm);
      }
    }

    &--large {
      .c-select__option {
        padding: 10px 16px;
        font-size: var(--font-size-base);
      }
    }
  }

  .c-select__search {
    padding: var(--spacing-2);
    border-bottom: 1px solid var(--color-border-light);
  }

  .c-select__options {
    overflow-y: auto;

    @include scrollbar;
  }

  .c-select__option {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color var(--transition-fast);

    &:hover {
      background: var(--color-bg-hover);
    }

    &--selected {
      font-weight: var(--font-weight-medium);
      color: var(--color-primary);
      background: var(--color-primary-light);
    }

    &--disabled {
      color: var(--color-text-disabled);
      cursor: not-allowed;

      &:hover {
        background: transparent;
      }
    }
  }

  .c-select__empty {
    padding: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    text-align: center;
  }

  // 尺寸变体
  .c-select--small {
    .c-select__trigger {
      min-height: 28px;
      padding: 4px 8px;
      font-size: var(--font-size-xs);
    }
  }

  .c-select--medium {
    .c-select__trigger {
      min-height: 36px;
      padding: var(--spacing-2) var(--spacing-3);
      font-size: var(--font-size-sm);
    }
  }

  .c-select--large {
    .c-select__trigger {
      min-height: 44px;
      padding: 10px var(--spacing-4);
      font-size: var(--font-size-base);
    }
  }
</style>
