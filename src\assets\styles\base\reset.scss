// 极简样式重置
// 基于现代 CSS Reset 理念，保持最小化和实用性

*,
*::before,
*::after {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  height: 100%;
  line-height: var(--line-height-base, 1.5);
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
  scroll-behavior: smooth;
}

// 默认应用浅色主题
html:not(.dark-theme) {
  // 主色调
  --color-primary: #2563eb;
  --color-primary-hover: #3b82f6;
  --color-primary-active: #1d4ed8;
  --color-primary-light: #dbeafe;
  --color-primary-dark: #1e40af;

  // 功能色彩
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #6b7280;

  // 文字颜色
  --color-text-primary: #111827;
  --color-text-secondary: #6b7280;
  --color-text-tertiary: #9ca3af;
  --color-text-disabled: #d1d5db;
  --color-text-placeholder: #9ca3af;
  --color-text-inverse: #fff;

  // 背景色
  --color-bg-primary: #fff;
  --color-bg-secondary: #f9fafb;
  --color-bg-tertiary: #f3f4f6;
  --color-bg-hover: #f5f5f5;
  --color-bg-active: #e5e7eb;
  --color-bg-disabled: #f9fafb;

  // 导航颜色
  --color-nav-bg: #fff;
  --color-nav-text: #64748b;
  --color-nav-text-active: #2563eb;
  --color-nav-item-hover: rgb(37 99 235 / 10%);
  --color-nav-item-active: rgb(37 99 235 / 15%);

  // 卡片颜色
  --color-card-bg: #fff;
  --color-card-border: #e5e7eb;

  // 边框色
  --color-border-light: #f3f4f6;
  --color-border-base: #e5e7eb;
  --color-border-dark: #d1d5db;
  --color-border-focus: #2563eb;

  // 阴影
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 5%);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 8%), 0 1px 2px 0 rgb(0 0 0 / 4%);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 8%), 0 2px 4px -1px rgb(0 0 0 / 4%);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 8%), 0 4px 6px -2px rgb(0 0 0 / 4%);

  // 专业颜色
  --color-wafer: #e0f2fe;
  --color-die-pass: #dcfce7;
  --color-die-fail: #fee2e2;
  --color-cp-test: #f0f9ff;
  --color-assembly: #f3e8ff;
  --color-ft-test: #ecfdf5;
}

body {
  min-height: 100%;
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  transition: background-color var(--transition-normal), 
              color var(--transition-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 应用根节点
#app {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
}

// 移除默认样式
button,
input,
optgroup,
select,
textarea {
  padding: 0;
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  background: transparent;
  border: none;
  outline: none;
}

// 按钮重置
button,
[role="button"] {
  cursor: pointer;
  background: transparent;
  border: none;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// 焦点样式
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

// 链接样式
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-hover);
  }
  
  &:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

h1 { font-size: var(--font-size-3xl); }

h2 { font-size: var(--font-size-2xl); }

h3 { font-size: var(--font-size-xl); }

h4 { font-size: var(--font-size-lg); }

h5 { font-size: var(--font-size-base); }

h6 { font-size: var(--font-size-sm); }

// 段落和文本
p {
  margin: 0;
  line-height: var(--line-height-base);
}

// 列表
ul, ol {
  padding: 0;
  margin: 0;
  list-style: none;
}

// 图片
img {
  display: block;
  max-width: 100%;
  height: auto;
}

// 表格
table {
  width: 100%;
  border-collapse: collapse;
}

// 表单元素
input, textarea, select {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  color: var(--color-text-primary);
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-base);
  border-radius: var(--radius-base);
  transition: border-color var(--transition-fast), 
              box-shadow var(--transition-fast);
  
  &:hover {
    border-color: var(--color-border-dark);
  }
  
  &:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 10%);
  }
  
  &::placeholder {
    color: var(--color-text-placeholder);
  }
  
  &:disabled {
    color: var(--color-text-disabled);
    cursor: not-allowed;
    background: var(--color-bg-disabled);
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-dark);
  border-radius: 3px;
  
  &:hover {
    background: var(--color-text-tertiary);
  }
}

::-webkit-scrollbar-corner {
  background: var(--color-bg-secondary);
}

// Firefox 滚动条
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-dark) var(--color-bg-secondary);
}

// 选择文本样式
::selection {
  color: var(--color-text-primary);
  background: rgb(37 99 235 / 20%);
}

::selection {
  color: var(--color-text-primary);
  background: rgb(37 99 235 / 20%);
}

// 隐藏元素
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}