<template>
  <div class="system-settings">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Setting /></el-icon>
        系统设置
      </h1>
      <p class="page-description">系统配置和个人偏好设置</p>
    </div>

    <div class="page-content">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>个人偏好</span>
            </template>
            <el-form :model="settingsForm" label-width="120px">
              <el-form-item label="主题模式">
                <el-radio-group v-model="settingsForm.theme">
                  <el-radio label="light">浅色模式</el-radio>
                  <el-radio label="dark">深色模式</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="语言">
                <el-select v-model="settingsForm.language" placeholder="选择语言">
                  <el-option label="简体中文" value="zh-CN" />
                  <el-option label="English" value="en" />
                </el-select>
              </el-form-item>
              <el-form-item label="时区">
                <el-select v-model="settingsForm.timezone" placeholder="选择时区">
                  <el-option label="Asia/Shanghai" value="Asia/Shanghai" />
                  <el-option label="UTC" value="UTC" />
                </el-select>
              </el-form-item>
              <el-form-item label="每页显示">
                <el-input-number v-model="settingsForm.pageSize" :min="10" :max="100" />
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card>
            <template #header>
              <span>安全设置</span>
            </template>
            <el-form label-width="120px">
              <el-form-item label="修改密码">
                <el-button type="primary">修改密码</el-button>
              </el-form-item>
              <el-form-item label="登录日志">
                <el-button>查看登录历史</el-button>
              </el-form-item>
              <el-form-item label="会话管理">
                <el-button>管理活跃会话</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <div style="margin-top: 24px; text-align: center;">
        <el-button type="primary" size="large">保存设置</el-button>
        <el-button size="large">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive } from 'vue'

  // 系统设置表单
  const settingsForm = reactive({
    theme: 'light',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    pageSize: 20
  })

  console.log('系统设置页面已加载')
</script>

<style lang="scss" scoped>
  .system-settings {
    padding: var(--spacing-4);
  }

  .page-header {
    margin-bottom: var(--spacing-6);

    .page-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
    }

    .page-description {
      margin: 0;
      color: var(--color-text-secondary);
    }
  }
</style>