// 物料主数据管理类型定义 - IC封装测试专业版
// 按照第一阶段规划：标准化物料主数据维护，支持BOM管理和采购计划

export enum MaterialMasterCategory {
  // IC封装测试专业物料分类
  WAFER = 'wafer', // 晶圆
  SUBSTRATE = 'substrate', // 基板
  LEADFRAME = 'leadframe', // 引线框架
  WIREBOND = 'wirebond', // 键合材料
  MOLDING = 'molding', // 塑封材料
  TEST_DEVICE = 'test_device', // 测试器件
  CHEMICAL = 'chemical', // 化学品
  CONSUMABLE = 'consumable', // 易耗品
  AUXILIARY = 'auxiliary' // 辅助材料
}

export enum MaterialStatus {
  ACTIVE = 'active', // 正常
  DISCONTINUED = 'discontinued', // 停产
  RESTRICTED = 'restricted', // 限制使用
  OBSOLETE = 'obsolete', // 淘汰
  DEVELOPMENT = 'development' // 开发中
}

export enum WaferSize {
  INCH_6 = '6', // 6寸
  INCH_8 = '8', // 8寸
  INCH_12 = '12' // 12寸
}

export enum PackageType {
  QFP = 'qfp', // Quad Flat Package
  BGA = 'bga', // Ball Grid Array
  CSP = 'csp', // Chip Scale Package
  SOP = 'sop', // Small Outline Package
  TSOP = 'tsop', // Thin Small Outline Package
  LQFP = 'lqfp', // Low-profile Quad Flat Package
  FCBGA = 'fcbga', // Flip Chip Ball Grid Array
  WLCSP = 'wlcsp' // Wafer Level Chip Scale Package
}

export enum WireType {
  GOLD = 'gold', // 金线
  SILVER = 'silver', // 银线
  COPPER = 'copper', // 铜线
  ALUMINUM = 'aluminum' // 铝线
}

// 物料基本信息
export interface MaterialBasicInfo {
  materialCode: string // 物料编码
  materialName: string // 物料名称
  englishName?: string // 英文名称
  specification: string // 规格型号
  category: MaterialMasterCategory // 物料分类
  manufacturer: string // 制造商
  brand?: string // 品牌
  model?: string // 型号
  status: MaterialStatus // 物料状态
  description?: string // 描述
  remarks?: string // 备注
}

// 技术参数
export interface TechnicalParameters {
  // 尺寸参数
  dimensions?: {
    length?: number // 长度(mm)
    width?: number // 宽度(mm)
    height?: number // 高度(mm)
    thickness?: number // 厚度(μm)
    diameter?: number // 直径(μm)
  }

  // 材质参数
  material?: string // 材质
  purity?: number // 纯度(%)

  // 电气特性
  electricalProperties?: {
    resistance?: number // 电阻(Ω)
    conductivity?: number // 导电率
    voltage?: number // 电压(V)
    current?: number // 电流(A)
    power?: number // 功率(W)
  }

  // 环境要求
  environmentalRequirements?: {
    temperatureMin?: number // 最低温度(°C)
    temperatureMax?: number // 最高温度(°C)
    humidityMax?: number // 最大湿度(%)
    storageConditions?: string[] // 存储条件
  }

  // 封装特有参数
  packageInfo?: {
    packageType?: PackageType // 封装类型
    pinCount?: number // 引脚数
    pitchSize?: number // 引脚间距(mm)
    bodySize?: string // 封装体尺寸
  }

  // 晶圆特有参数
  waferInfo?: {
    waferSize?: WaferSize // 晶圆尺寸
    thickness?: number // 厚度(μm)
    orientation?: string // 晶向
    resistivity?: number // 电阻率
    dieSize?: string // 芯片尺寸
  }

  // 键合材料参数
  wirebondInfo?: {
    wireType?: WireType // 线材类型
    diameter?: number // 直径(μm)
    tensileStrength?: number // 拉伸强度
    elongation?: number // 延伸率(%)
    purity?: number // 纯度(%)
  }
}

// 供应商信息
export interface SupplierInfo {
  supplierId: string // 供应商ID
  supplierName: string // 供应商名称
  contactPerson: string // 联系人
  contactPhone: string // 联系电话
  contactEmail: string // 联系邮箱
  leadTime: number // 供货周期(天)
  minOrderQty: number // 最小订货量
  isPrimary: boolean // 是否主供应商
  qualityRating: number // 质量评级(0-100)
  deliveryRating: number // 交期评级(0-100)
  certifications: string[] // 认证证书
}

// 质量信息
export interface QualityInfo {
  standard: string // 执行标准
  specification: string // 技术规范
  inspectionMethod: string // 检验方法
  acceptanceCriteria: string // 接收标准
  certifications: string[] // 认证信息
  reliability?: {
    mtbf?: number // 平均无故障时间(小时)
    lifeTest?: string // 寿命测试
    qualificationStatus?: string // 认证状态
  }
}

// 库存信息
export interface StockInfo {
  unitOfMeasure: string // 计量单位
  safetyStock: number // 安全库存
  maxStock: number // 最大库存
  reorderPoint: number // 再订货点
  leadTime: number // 采购周期(天)
  storageRequirements: string[] // 存储要求
  shelfLife?: number // 保质期(天)
  storageLocation?: string // 存储位置
}

// 成本信息
export interface CostInfo {
  standardCost: number // 标准成本
  currentPrice: number // 当前价格
  currency: string // 货币单位
  priceUnit: string // 价格单位
  lastPriceUpdate: string // 最后更新时间
  priceHistory?: PriceHistory[] // 价格历史
  costAnalysis?: {
    materialCost?: number // 材料成本
    laborCost?: number // 人工成本
    overheadCost?: number // 制造费用
    transportCost?: number // 运输费用
  }
}

// 价格历史
export interface PriceHistory {
  date: string // 日期
  price: number // 价格
  supplier: string // 供应商
  quantity: number // 数量
  reason?: string // 变动原因
}

// 替代关系
export interface AlternativeRelation {
  alternativeCode: string // 替代物料编码
  alternativeName: string // 替代物料名称
  relationType: 'FULL' | 'PARTIAL' | 'EMERGENCY' // 替代类型
  substitutionRatio: number // 替代比例
  priority: number // 优先级
  remarks?: string // 备注
  validFrom: string // 生效日期
  validTo?: string // 失效日期
}

// 物料主数据
export interface MaterialMaster {
  id: string // 系统ID
  basicInfo: MaterialBasicInfo // 基本信息
  technicalParams: TechnicalParameters // 技术参数
  suppliers: SupplierInfo[] // 供应商信息
  qualityInfo: QualityInfo // 质量信息
  stockInfo: StockInfo // 库存信息
  costInfo: CostInfo // 成本信息
  alternatives: AlternativeRelation[] // 替代关系
  attachments?: Attachment[] // 附件
  auditInfo: {
    createdBy: string // 创建人
    createdAt: string // 创建时间
    updatedBy: string // 更新人
    updatedAt: string // 更新时间
    version: number // 版本号
    approvedBy?: string // 审批人
    approvedAt?: string // 审批时间
  }
}

// 附件信息
export interface Attachment {
  id: string // 附件ID
  fileName: string // 文件名
  fileType: string // 文件类型
  fileSize: number // 文件大小
  uploadTime: string // 上传时间
  uploadBy: string // 上传人
  category: 'SPEC' | 'DRAWING' | 'CERTIFICATE' | 'PHOTO' | 'OTHER' // 附件分类
  description?: string // 描述
}

// 物料使用统计
export interface MaterialUsageStats {
  materialCode: string // 物料编码
  materialName: string // 物料名称
  category: MaterialMasterCategory // 分类
  usageQty: number // 使用数量
  usageValue: number // 使用金额
  frequency: number // 使用频次
  lastUsedDate?: string // 最后使用日期
  trend: 'UP' | 'DOWN' | 'STABLE' // 使用趋势
}

// 缺料风险预警
export interface MaterialRiskWarning {
  materialCode: string // 物料编码
  materialName: string // 物料名称
  riskType: 'SHORTAGE' | 'DISCONTINUATION' | 'PRICE_INCREASE' | 'QUALITY_ISSUE' // 风险类型
  riskLevel: 'HIGH' | 'MEDIUM' | 'LOW' // 风险等级
  description: string // 风险描述
  impact: string // 影响分析
  recommendation: string // 建议措施
  dueDate?: string // 预期发生时间
  responsible: string // 责任人
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' // 处理状态
}

// API查询参数
export interface MaterialMasterQueryParams {
  keyword?: string // 关键词
  category?: MaterialMasterCategory[] // 分类
  status?: MaterialStatus[] // 状态
  manufacturer?: string[] // 制造商
  supplier?: string[] // 供应商
  packageType?: PackageType[] // 封装类型
  waferSize?: WaferSize[] // 晶圆尺寸
  wireType?: WireType[] // 线材类型
  priceRange?: {
    min: number
    max: number
  } // 价格范围
  createdDateRange?: [string, string] // 创建日期范围
  hasAlternatives?: boolean // 是否有替代料
  isLowStock?: boolean // 是否低库存
  page?: number // 页码
  pageSize?: number // 页大小
  sortBy?: string // 排序字段
  sortOrder?: 'ASC' | 'DESC' // 排序方向
}

// 创建物料主数据参数
export interface CreateMaterialMasterData {
  basicInfo: MaterialBasicInfo
  technicalParams?: TechnicalParameters
  suppliers?: Omit<SupplierInfo, 'supplierId'>[] // 创建时不需要ID
  qualityInfo?: QualityInfo
  stockInfo?: StockInfo
  costInfo?: Partial<CostInfo>
  alternatives?: Omit<AlternativeRelation, 'alternativeCode'>[] // 创建时引用其他物料
  attachments?: File[] // 文件上传
}

// 更新物料主数据参数
export interface UpdateMaterialMasterData extends Partial<CreateMaterialMasterData> {
  id: string
}

// API响应类型
export interface MaterialMasterListResponse {
  data: MaterialMaster[]
  total: number
  page: number
  pageSize: number
  stats?: {
    totalMaterials: number
    activeMaterials: number
    categoryCounts: Record<MaterialMasterCategory, number>
    supplierCounts: number
  }
}

export interface MaterialMasterDetailResponse {
  data: MaterialMaster
  usageStats?: MaterialUsageStats[]
  riskWarnings?: MaterialRiskWarning[]
  relatedMaterials?: MaterialMaster[]
}

// 批量操作
export interface BatchUpdateMaterialMaster {
  materialIds: string[]
  updates: {
    status?: MaterialStatus
    supplier?: string
    standardCost?: number
    safetyStock?: number
  }
}

// 导入导出
export interface MaterialMasterImportData {
  basicInfo: MaterialBasicInfo
  technicalParams?: TechnicalParameters
  suppliers?: SupplierInfo[]
  qualityInfo?: QualityInfo
  stockInfo?: StockInfo
  costInfo?: CostInfo
}

export interface MaterialMasterExportParams {
  materialIds?: string[]
  category?: MaterialMasterCategory[]
  status?: MaterialStatus[]
  includeDetails?: boolean
  format: 'EXCEL' | 'CSV' | 'PDF'
}
