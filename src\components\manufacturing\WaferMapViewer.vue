<template>
  <div class="wafer-map-viewer">
    <div class="wafer-map-viewer__header">
      <div class="wafer-info">
        <h3>晶圆图 - {{ waferMap.waferId }}</h3>
        <div class="wafer-stats">
          <span>
            良率:
            <strong class="yield-value">{{ waferMap.yield.toFixed(2) }}%</strong>
          </span>
          <span>总数: {{ totalDie }}</span>
          <span>良品: {{ passDie }}</span>
        </div>
      </div>
      <div class="wafer-controls">
        <el-select v-model="displayMode" placeholder="显示模式" size="small">
          <el-option label="分档结果" value="bin" />
          <el-option label="良率分布" value="yield" />
          <el-option label="缺陷类型" value="defect" />
        </el-select>
        <CButton size="small" @click="exportMap">
          <el-icon><Download /></el-icon>
          导出
        </CButton>
        <CButton size="small" @click="zoomReset">
          <el-icon><ZoomIn /></el-icon>
          复位
        </CButton>
      </div>
    </div>

    <div class="wafer-map-viewer__content">
      <div
ref="mapContainer" class="wafer-map-container"
>
        <svg
          :width="svgSize"
          :height="svgSize"
          class="wafer-map-svg"
          @wheel="handleZoom"
          @mousedown="handlePanStart"
          @mousemove="handlePanMove"
          @mouseup="handlePanEnd"
          @mouseleave="handlePanEnd"
        >
          <!-- 晶圆轮廓 -->
          <circle
            :cx="svgSize / 2"
            :cy="svgSize / 2"
            :r="waferRadius"
            class="wafer-outline"
            fill="none"
            stroke="var(--color-border-base)"
            stroke-width="2"
            :transform="transformString"
          />

          <!-- Die网格 -->
          <g :transform="transformString">
            <rect
              v-for="(die, index) in waferMap.mapData"
              :key="index"
              :x="getDieX(die.x)"
              :y="getDieY(die.y)"
              :width="dieSize"
              :height="dieSize"
              :fill="getDieColor(die)"
              :stroke="getDieStroke(die)"
              stroke-width="0.5"
              class="die-cell"
              @click="selectDie(die)"
              @mouseenter="showDieTooltip($event, die)"
              @mouseleave="hideDieTooltip"
            />
          </g>

          <!-- 坐标轴 -->
          <g
v-if="showAxes" class="wafer-axes"
>
            <!-- X轴 -->
            <line
              :x1="svgSize / 2 - waferRadius"
              :y1="svgSize / 2"
              :x2="svgSize / 2 + waferRadius"
              :y2="svgSize / 2"
              stroke="var(--color-text-secondary)"
              stroke-width="1"
              stroke-dasharray="2,2"
            />
            <!-- Y轴 -->
            <line
              :x1="svgSize / 2"
              :y1="svgSize / 2 - waferRadius"
              :x2="svgSize / 2"
              :y2="svgSize / 2 + waferRadius"
              stroke="var(--color-text-secondary)"
              stroke-width="1"
              stroke-dasharray="2,2"
            />
          </g>
        </svg>

        <!-- 工具提示 -->
        <div
          v-show="tooltip.visible"
          class="die-tooltip"
          :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
        >
          <div class="tooltip-header">Die ({{ tooltip.die?.x }}, {{ tooltip.die?.y }})</div>
          <div class="tooltip-content">
            <div>结果: {{ getResultText(tooltip.die?.result) }}</div>
            <div v-if="tooltip.die?.binCode">分档: {{ tooltip.die.binCode }}</div>
            <div v-if="tooltip.die?.testData">
              <div v-for="(value, key) in tooltip.die.testData" :key="key">
                {{ key }}: {{ value }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图例 -->
      <div class="wafer-legend">
        <h4>图例</h4>
        <div class="legend-items">
          <div
v-for="(item, key) in legendItems" :key="key"
class="legend-item"
>
            <div class="legend-color"
:style="{ backgroundColor: item.color }"
/>
            <span class="legend-text">{{ item.label }} ({{ item.count }})</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="wafer-map-viewer__footer">
      <div class="bin-summary">
        <h4>分档汇总</h4>
        <div class="bin-stats">
          <div
v-for="(count, bin) in waferMap.binSummary" :key="bin"
class="bin-stat-item"
>
            <span class="bin-code">{{ bin }}</span>
            <span class="bin-count">{{ count }}</span>
            <span class="bin-percentage">({{ ((count / totalDie) * 100).toFixed(1) }}%)</span>
          </div>
        </div>
      </div>

      <div class="generation-info">
        <span class="generation-time">生成时间: {{ formatTime(waferMap.generatedTime) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { WaferMap, WaferMapData, QualityResult } from '@/types/manufacturing'
  import CButton from '@/components/base/CButton.vue'
  import { Download, ZoomIn } from '@element-plus/icons-vue'

  interface Props {
    waferMap: WaferMap
    showAxes?: boolean
    interactive?: boolean
  }

  interface Emits {
    (e: 'die-selected', die: WaferMapData): void
    (e: 'export-map'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    showAxes: true,
    interactive: true
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const displayMode = ref<'bin' | 'yield' | 'defect'>('bin')
  const svgSize = 400
  const waferRadius = 180
  const dieSize = 8
  const scale = ref(1)
  const panX = ref(0)
  const panY = ref(0)
  const isPanning = ref(false)
  const lastPanPoint = ref({ x: 0, y: 0 })

  // 工具提示
  const tooltip = reactive({
    visible: false,
    x: 0,
    y: 0,
    die: null as WaferMapData | null
  })

  // 计算属性
  const totalDie = computed(() => props.waferMap.mapData.length)

  const passDie = computed(() => props.waferMap.mapData.filter(die => die.result === 'pass').length)

  const transformString = computed(
    () => `translate(${panX.value}, ${panY.value}) scale(${scale.value})`
  )

  const legendItems = computed(() => {
    const items: Record<string, { color: string; label: string; count: number }> = {}

    props.waferMap.mapData.forEach(die => {
      const key = die.result
      if (!items[key]) {
        items[key] = {
          color: getDieColor(die),
          label: getResultText(die.result),
          count: 0
        }
      }
      items[key].count++
    })

    return items
  })

  // 方法
  const getDieX = (x: number): number => {
    const centerX = svgSize / 2
    return centerX + (x - props.waferMap.dimensions.maxX / 2) * dieSize
  }

  const getDieY = (y: number): number => {
    const centerY = svgSize / 2
    return centerY + (y - props.waferMap.dimensions.maxY / 2) * dieSize
  }

  const getDieColor = (die: WaferMapData): string => {
    const colorMap: Record<QualityResult, string> = {
      pass: '#67c23a',
      fail: '#f56c6c',
      retest: '#e6a23c',
      pending: '#909399'
    }
    return colorMap[die.result] || '#dcdfe6'
  }

  const getDieStroke = (die: WaferMapData): string => {
    return die.result === 'fail' ? '#f56c6c' : 'var(--color-border-light)'
  }

  const getResultText = (result?: QualityResult): string => {
    const textMap: Record<QualityResult, string> = {
      pass: '良品',
      fail: '不良',
      retest: '重测',
      pending: '待测'
    }
    return textMap[result as QualityResult] || '未知'
  }

  const selectDie = (die: WaferMapData) => {
    if (!props.interactive) return
    emit('die-selected', die)
  }

  const showDieTooltip = (event: MouseEvent, die: WaferMapData) => {
    if (!props.interactive) return

    const rect = (event.target as Element).getBoundingClientRect()
    const container = document.querySelector('.wafer-map-container')?.getBoundingClientRect()

    if (container) {
      tooltip.x = event.clientX - container.left + 10
      tooltip.y = event.clientY - container.top - 10
      tooltip.die = die
      tooltip.visible = true
    }
  }

  const hideDieTooltip = () => {
    tooltip.visible = false
  }

  const handleZoom = (event: WheelEvent) => {
    if (!props.interactive) return

    event.preventDefault()
    const delta = event.deltaY > 0 ? 0.9 : 1.1
    scale.value = Math.max(0.5, Math.min(3, scale.value * delta))
  }

  const handlePanStart = (event: MouseEvent) => {
    if (!props.interactive) return

    isPanning.value = true
    lastPanPoint.value = { x: event.clientX, y: event.clientY }
  }

  const handlePanMove = (event: MouseEvent) => {
    if (!props.interactive || !isPanning.value) return

    const deltaX = event.clientX - lastPanPoint.value.x
    const deltaY = event.clientY - lastPanPoint.value.y

    panX.value += deltaX
    panY.value += deltaY

    lastPanPoint.value = { x: event.clientX, y: event.clientY }
  }

  const handlePanEnd = () => {
    isPanning.value = false
  }

  const zoomReset = () => {
    scale.value = 1
    panX.value = 0
    panY.value = 0
  }

  const exportMap = () => {
    emit('export-map')
  }

  const formatTime = (timeStr: string): string => {
    return new Date(timeStr).toLocaleString('zh-CN')
  }
</script>

<style lang="scss" scoped>
  .wafer-map-viewer {
    padding: var(--spacing-4);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);

    &__header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: var(--spacing-4);
    }

    &__content {
      display: flex;
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
    }

    &__footer {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      padding-top: var(--spacing-4);
      border-top: 1px solid var(--color-border-light);
    }
  }

  .wafer-info {
    h3 {
      margin: 0 0 var(--spacing-2) 0;
      color: var(--color-text-primary);
    }
  }

  .wafer-stats {
    display: flex;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);

    .yield-value {
      font-size: var(--font-size-base);
      color: var(--color-success);
    }
  }

  .wafer-controls {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
  }

  .wafer-map-container {
    position: relative;
    overflow: hidden;
    background: var(--color-bg-secondary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);
  }

  .wafer-map-svg {
    cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }

  .wafer-outline {
    opacity: 0.8;
  }

  .die-cell {
    cursor: pointer;
    transition: stroke-width 0.2s ease;

    &:hover {
      stroke: var(--color-primary) !important;
      stroke-width: 2 !important;
    }
  }

  .die-tooltip {
    position: absolute;
    z-index: 1000;
    padding: var(--spacing-2);
    font-size: var(--font-size-sm);
    pointer-events: none;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-md);

    .tooltip-header {
      margin-bottom: var(--spacing-1);
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .tooltip-content {
      color: var(--color-text-secondary);

      div {
        margin: 2px 0;
      }
    }
  }

  .wafer-legend {
    min-width: 200px;

    h4 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .legend-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .legend-item {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    font-size: var(--font-size-sm);
  }

  .legend-color {
    width: 16px;
    height: 16px;
    border: 1px solid var(--color-border-light);
    border-radius: 2px;
  }

  .legend-text {
    color: var(--color-text-secondary);
  }

  .bin-summary {
    h4 {
      margin: 0 0 var(--spacing-2) 0;
      color: var(--color-text-primary);
    }
  }

  .bin-stats {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4);
  }

  .bin-stat-item {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;
    font-size: var(--font-size-sm);

    .bin-code {
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .bin-count {
      color: var(--color-text-primary);
    }

    .bin-percentage {
      color: var(--color-text-secondary);
    }
  }

  .generation-info {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  @media (width <= 768px) {
    .wafer-map-viewer {
      &__header {
        flex-direction: column;
        gap: var(--spacing-3);
      }

      &__content {
        flex-direction: column;
      }

      &__footer {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: flex-start;
      }
    }

    .wafer-controls {
      justify-content: center;
      width: 100%;
    }

    .wafer-stats {
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .bin-stats {
      flex-direction: column;
      gap: var(--spacing-2);
    }
  }
</style>
