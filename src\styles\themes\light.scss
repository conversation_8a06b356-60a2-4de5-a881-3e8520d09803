// IC封测CIM系统 - 浅色主题
// 极简主义浅色主题色彩系统

.light-theme {
  // ===== 主色调 - 极简蓝色系 =====
  --color-primary: #2563eb;           // 主品牌色 (蓝色)
  --color-primary-light: #3b82f6;     // 主色-浅
  --color-primary-dark: #1d4ed8;      // 主色-深
  --color-primary-hover: #3b82f6;     // 悬停色
  --color-primary-active: #1d4ed8;    // 激活色

  // ===== 功能色彩 - 柔和版本 =====
  --color-success: #10b981;           // 成功色 (绿色)
  --color-success-light: #34d399;
  --color-success-dark: #059669;
  --color-warning: #f59e0b;           // 警告色 (橙色) 
  --color-warning-light: #fbbf24;
  --color-warning-dark: #d97706;
  --color-error: #ef4444;             // 错误色 (红色)
  --color-error-light: #f87171;
  --color-error-dark: #dc2626;
  --color-info: #6b7280;              // 信息色 (灰色)
  --color-info-light: #9ca3af;
  --color-info-dark: #4b5563;

  // ===== 中性色阶 - 极简灰色系 =====
  --color-text-primary: #111827;      // 主要文字
  --color-text-secondary: #6b7280;    // 次要文字
  --color-text-tertiary: #9ca3af;     // 第三级文字
  --color-text-disabled: #d1d5db;     // 禁用文字
  --color-text-white: #fff;        // 白色文字
  --color-text-inverse: #fff;      // 反色文字

  // ===== 背景色 - 纯净白色系 =====
  --color-bg-primary: #fff;        // 主背景 (纯白)
  --color-bg-secondary: #f9fafb;      // 次要背景
  --color-bg-tertiary: #f3f4f6;       // 第三背景
  --color-bg-hover: #f5f5f5;          // 悬停背景
  --color-bg-active: #e5e7eb;         // 激活背景
  --color-bg-disabled: #f9fafb;       // 禁用背景
  --color-bg-mask: rgb(0 0 0 / 50%); // 遮罩背景

  // ===== 边框色 - 清淡边框 =====
  --color-border-light: #f3f4f6;      // 轻边框
  --color-border-base: #e5e7eb;       // 基础边框
  --color-border-dark: #d1d5db;       // 深边框
  --color-border-darker: #9ca3af;     // 更深边框
  --color-border-focus: #2563eb;      // 焦点边框

  // ===== 阴影系统 - 微妙层次 =====
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 3%);
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 5%);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 8%), 0 1px 2px 0 rgb(0 0 0 / 4%);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 8%), 0 2px 4px -1px rgb(0 0 0 / 4%);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 8%), 0 4px 6px -2px rgb(0 0 0 / 4%);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 8%), 0 10px 10px -5px rgb(0 0 0 / 4%);

  // ===== 表格特定颜色 =====
  --color-table-header-bg: #fafbfc;
  --color-table-border: #e5e7eb;
  --color-table-hover: #f9fafb;
  --color-table-selected: #eff6ff;

  // ===== 表单特定颜色 =====
  --color-input-bg: #fff;
  --color-input-border: #e5e7eb;
  --color-input-focus: #2563eb;
  --color-input-placeholder: #9ca3af;

  // ===== 按钮特定颜色 =====
  --color-btn-secondary-bg: #fff;
  --color-btn-secondary-border: #e5e7eb;
  --color-btn-secondary-hover: #f9fafb;

  // ===== 卡片特定颜色 =====
  --color-card-bg: #fff;
  --color-card-border: #e5e7eb;
  --color-card-shadow: var(--shadow-base);

  // ===== 导航特定颜色 =====
  --color-nav-bg: #fff;
  --color-nav-item-hover: #f3f4f6;
  --color-nav-item-active: #eff6ff;
  --color-nav-text: #374151;
  --color-nav-text-active: #2563eb;

  // ===== 侧边栏特定颜色 =====
  --color-sidebar-bg: #fff;
  --color-sidebar-border: #e5e7eb;
  --color-sidebar-item-hover: #f3f4f6;
  --color-sidebar-item-active: #eff6ff;
}