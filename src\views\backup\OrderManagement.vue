<template>
  <div class="order-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">订单管理</h1>
      <div class="header-actions">
        <c-button type="primary"
@click="showCreateModal = true"
>
新建订单
</c-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <c-card class="search-card">
      <div class="search-form">
        <div class="search-row">
          <div class="search-item">
            <label>订单号</label>
            <c-input
v-model="searchForm.orderNo" placeholder="请输入订单号"
clearable
/>
          </div>
          <div class="search-item">
            <label>客户名称</label>
            <c-input
v-model="searchForm.customerName" placeholder="请输入客户名称"
clearable
/>
          </div>
          <div class="search-item">
            <label>订单状态</label>
            <c-select
              v-model="searchForm.status"
              :options="statusOptions"
              placeholder="请选择状态"
              clearable
            />
          </div>
          <div class="search-item">
            <label>封装类型</label>
            <c-select
              v-model="searchForm.packageType"
              :options="packageTypeOptions"
              placeholder="请选择封装类型"
              clearable
            />
          </div>
        </div>
        <div class="search-actions">
          <c-button type="primary"
@click="handleSearch"
>
搜索
</c-button>
          <c-button @click="handleReset">重置</c-button>
        </div>
      </div>
    </c-card>

    <!-- 订单表格 -->
    <c-card class="table-card">
      <c-table
        v-model:selected-row-keys="selectedRowKeys"
        v-model:current="pagination.current"
        v-model:page-size="pagination.pageSize"
        :data-source="orders"
        :columns="columns"
        :loading="tableLoading"
        :show-selection="true"
        :show-pagination="true"
        :total="pagination.total"
        show-toolbar
        show-refresh
        class="order-table"
        @refresh="loadOrders"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <!-- 订单号列 -->
        <template #orderNo="{ record }">
          <span class="order-no">{{ record.orderNo }}</span>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <span :class="getStatusClass(record.status)">
            {{ getStatusText(record.status) }}
          </span>
        </template>

        <!-- 封装类型列 -->
        <template #packageType="{ record }">
          <span class="package-type">{{ record.packageType }}</span>
        </template>

        <!-- 数量列 -->
        <template #quantity="{ record }">
          <span class="quantity">{{ formatNumber(record.quantity) }}</span>
        </template>

        <!-- 金额列 -->
        <template #amount="{ record }">
          <span class="amount">¥{{ formatNumber(record.amount) }}</span>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <div class="table-actions">
            <c-button type="text"
size="small" @click="handleView(record)"
>
查看
</c-button>
            <c-button type="text"
size="small" @click="handleEdit(record)"
>
编辑
</c-button>
            <c-button
              type="text"
              size="small"
              style="color: var(--color-error)"
              @click="handleDelete(record)"
            >
              删除
            </c-button>
          </div>
        </template>
      </c-table>
    </c-card>

    <!-- 新建/编辑订单模态框 -->
    <c-modal
      v-model:visible="showCreateModal"
      :title="editingOrder ? '编辑订单' : '新建订单'"
      width="800px"
      :confirm-loading="submitLoading"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <div class="order-form">
        <div class="form-section">
          <h3>基础信息</h3>
          <div class="form-grid">
            <div class="form-item">
              <label class="required">客户名称</label>
              <c-input
v-model="formData.customerName" placeholder="请输入客户名称"
/>
            </div>
            <div class="form-item">
              <label class="required">产品名称</label>
              <c-input
v-model="formData.productName" placeholder="请输入产品名称"
/>
            </div>
            <div class="form-item">
              <label class="required">封装类型</label>
              <c-select
                v-model="formData.packageType"
                :options="packageTypeOptions"
                placeholder="请选择封装类型"
              />
            </div>
            <div class="form-item">
              <label class="required">订单数量</label>
              <c-input
v-model="formData.quantity" type="number"
placeholder="请输入数量"
/>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3>技术要求</h3>
          <div class="form-grid">
            <div class="form-item">
              <label>测试温度</label>
              <c-select
                v-model="formData.testTemperature"
                :options="temperatureOptions"
                placeholder="请选择测试温度"
              />
            </div>
            <div class="form-item">
              <label>质量等级</label>
              <c-select
                v-model="formData.qualityGrade"
                :options="qualityGradeOptions"
                placeholder="请选择质量等级"
              />
            </div>
            <div class="form-item full-width">
              <label>特殊要求</label>
              <c-input
                v-model="formData.specialRequirements"
                type="textarea"
                :rows="3"
                placeholder="请输入特殊要求"
              />
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3>交期信息</h3>
          <div class="form-grid">
            <div class="form-item">
              <label class="required">要求交期</label>
              <c-input
v-model="formData.deliveryDate" type="date"
placeholder="请选择交期"
/>
            </div>
            <div class="form-item">
              <label>紧急程度</label>
              <c-select
                v-model="formData.urgency"
                :options="urgencyOptions"
                placeholder="请选择紧急程度"
              />
            </div>
          </div>
        </div>
      </div>
    </c-modal>

    <!-- 订单详情模态框 -->
    <c-modal
      v-model:visible="showDetailModal"
      title="订单详情"
      width="900px"
      :show-footer="false"
      class="c-modal--wafer-detail"
    >
      <div v-if="currentOrder" class="order-detail">
        <div class="detail-section">
          <h3>基础信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">订单号</span>
              <span class="value">{{ currentOrder.orderNo }}</span>
            </div>
            <div class="info-item">
              <span class="label">客户名称</span>
              <span class="value">{{ currentOrder.customerName }}</span>
            </div>
            <div class="info-item">
              <span class="label">产品名称</span>
              <span class="value">{{ currentOrder.productName }}</span>
            </div>
            <div class="info-item">
              <span class="label">封装类型</span>
              <span class="value">{{ currentOrder.packageType }}</span>
            </div>
            <div class="info-item">
              <span class="label">订单状态</span>
              <span class="value" :class="getStatusClass(currentOrder.status)">
                {{ getStatusText(currentOrder.status) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">订单数量</span>
              <span class="value">{{ formatNumber(currentOrder.quantity) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3>生产进度</h3>
          <div class="progress-timeline">
            <div
              v-for="stage in productionStages"
              :key="stage.key"
              :class="getStageClass(stage.key, currentOrder.currentStage)"
            >
              <div class="stage-icon">
                {{ stage.icon }}
              </div>
              <div class="stage-info">
                <div class="stage-name">
                  {{ stage.name }}
                </div>
                <div class="stage-status">
                  {{ getStageStatus(stage.key, currentOrder.currentStage) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </c-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { formatNumber } from '@/utils/common'
  import type { CTableColumn } from '@/components/base'

  // 接口定义
  interface Order {
    id: string
    orderNo: string
    customerName: string
    productName: string
    packageType: string
    status: string
    quantity: number
    amount: number
    deliveryDate: string
    createTime: string
    currentStage?: string
  }

  // 响应式数据
  const orders = ref<Order[]>([])
  const selectedRowKeys = ref<(string | number)[]>([])
  const tableLoading = ref(false)
  const submitLoading = ref(false)
  const showCreateModal = ref(false)
  const showDetailModal = ref(false)
  const editingOrder = ref<Order | null>(null)
  const currentOrder = ref<Order | null>(null)

  // 搜索表单
  const searchForm = reactive({
    orderNo: '',
    customerName: '',
    status: '',
    packageType: ''
  })

  // 分页
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0
  })

  // 表单数据
  const formData = reactive({
    customerName: '',
    productName: '',
    packageType: '',
    quantity: '',
    testTemperature: '',
    qualityGrade: '',
    specialRequirements: '',
    deliveryDate: '',
    urgency: ''
  })

  // 选项数据
  const statusOptions = [
    { label: '询价中', value: 'inquiry' },
    { label: '待报价', value: 'quotation' },
    { label: '待确认', value: 'pending' },
    { label: '生产中', value: 'production' },
    { label: '测试中', value: 'testing' },
    { label: '已完成', value: 'completed' },
    { label: '已取消', value: 'cancelled' }
  ]

  const packageTypeOptions = [
    { label: 'QFP-44', value: 'QFP-44' },
    { label: 'QFP-64', value: 'QFP-64' },
    { label: 'QFP-100', value: 'QFP-100' },
    { label: 'BGA-144', value: 'BGA-144' },
    { label: 'BGA-256', value: 'BGA-256' },
    { label: 'CSP-48', value: 'CSP-48' },
    { label: 'FC-BGA', value: 'FC-BGA' }
  ]

  const temperatureOptions = [
    { label: '-40℃~+85℃', value: 'industrial' },
    { label: '-40℃~+125℃', value: 'automotive' },
    { label: '0℃~+70℃', value: 'commercial' }
  ]

  const qualityGradeOptions = [
    { label: '汽车级', value: 'automotive' },
    { label: '工业级', value: 'industrial' },
    { label: '商业级', value: 'commercial' }
  ]

  const urgencyOptions = [
    { label: '普通', value: 'normal' },
    { label: '紧急', value: 'urgent' },
    { label: '特急', value: 'critical' }
  ]

  // 生产阶段
  const productionStages = [
    { key: 'cp', name: 'CP测试', icon: '🔍' },
    { key: 'assembly', name: '封装', icon: '📦' },
    { key: 'ft', name: 'FT测试', icon: '⚡' },
    { key: 'delivery', name: '交付', icon: '🚚' }
  ]

  // 表格列配置
  const columns: CTableColumn[] = [
    {
      key: 'orderNo',
      title: '订单号',
      dataIndex: 'orderNo',
      width: 150,
      sortable: true
    },
    {
      key: 'customerName',
      title: '客户名称',
      dataIndex: 'customerName',
      width: 150
    },
    {
      key: 'productName',
      title: '产品名称',
      dataIndex: 'productName',
      width: 180
    },
    {
      key: 'packageType',
      title: '封装类型',
      dataIndex: 'packageType',
      width: 100
    },
    {
      key: 'quantity',
      title: '数量',
      dataIndex: 'quantity',
      width: 100,
      align: 'right',
      sortable: true
    },
    {
      key: 'amount',
      title: '金额',
      dataIndex: 'amount',
      width: 120,
      align: 'right',
      sortable: true
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      width: 100
    },
    {
      key: 'deliveryDate',
      title: '交期',
      dataIndex: 'deliveryDate',
      width: 120
    },
    {
      key: 'createTime',
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160
    },
    {
      key: 'actions',
      title: '操作',
      width: 150,
      fixed: 'right'
    }
  ]

  // 方法
  const getStatusClass = (status: string) => {
    const classMap: Record<string, string> = {
      inquiry: 'status-inquiry',
      quotation: 'status-quotation',
      pending: 'status-pending',
      production: 'status-production',
      testing: 'status-testing',
      completed: 'status-completed',
      cancelled: 'status-cancelled'
    }
    return classMap[status] || ''
  }

  const getStatusText = (status: string) => {
    const option = statusOptions.find(opt => opt.value === status)
    return option?.label || status
  }

  const getStageClass = (stageKey: string, currentStage: string) => {
    const stages = ['cp', 'assembly', 'ft', 'delivery']
    const currentIndex = stages.indexOf(currentStage)
    const stageIndex = stages.indexOf(stageKey)

    return [
      'stage-item',
      {
        'stage-completed': stageIndex < currentIndex,
        'stage-current': stageIndex === currentIndex,
        'stage-pending': stageIndex > currentIndex
      }
    ]
  }

  const getStageStatus = (stageKey: string, currentStage: string) => {
    const stages = ['cp', 'assembly', 'ft', 'delivery']
    const currentIndex = stages.indexOf(currentStage)
    const stageIndex = stages.indexOf(stageKey)

    if (stageIndex < currentIndex) return '已完成'
    if (stageIndex === currentIndex) return '进行中'
    return '待开始'
  }

  const loadOrders = async () => {
    tableLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      // 模拟数据
      const mockOrders: Order[] = [
        {
          id: '1',
          orderNo: 'ORD-2024-001',
          customerName: '华为技术有限公司',
          productName: 'HiSilicon Kirin 9000',
          packageType: 'FC-BGA',
          status: 'production',
          quantity: 10000,
          amount: 2500000,
          deliveryDate: '2024-03-15',
          createTime: '2024-02-01 10:30:00',
          currentStage: 'assembly'
        },
        {
          id: '2',
          orderNo: 'ORD-2024-002',
          customerName: '小米科技有限公司',
          productName: 'Surge C2',
          packageType: 'QFP-100',
          status: 'testing',
          quantity: 50000,
          amount: 8750000,
          deliveryDate: '2024-03-20',
          createTime: '2024-02-03 14:20:00',
          currentStage: 'ft'
        },
        {
          id: '3',
          orderNo: 'ORD-2024-003',
          customerName: 'OPPO广东移动通信',
          productName: 'MariSilicon X',
          packageType: 'BGA-256',
          status: 'completed',
          quantity: 30000,
          amount: 6200000,
          deliveryDate: '2024-02-28',
          createTime: '2024-01-15 09:15:00',
          currentStage: 'delivery'
        }
      ]

      orders.value = mockOrders
      pagination.total = mockOrders.length
    } finally {
      tableLoading.value = false
    }
  }

  const handleSearch = () => {
    pagination.current = 1
    loadOrders()
  }

  const handleReset = () => {
    Object.keys(searchForm).forEach(key => {
      searchForm[key] = ''
    })
    handleSearch()
  }

  const handleRowClick = (record: Order) => {
    currentOrder.value = record
    showDetailModal.value = true
  }

  const handleSelectionChange = (selectedRows: Order[], keys: (string | number)[]) => {
    selectedRowKeys.value = keys
  }

  const handleView = (record: Order) => {
    currentOrder.value = record
    showDetailModal.value = true
  }

  const handleEdit = (record: Order) => {
    editingOrder.value = record
    Object.keys(formData).forEach(key => {
      formData[key] = record[key] || ''
    })
    showCreateModal.value = true
  }

  const handleDelete = (record: Order) => {
    // 实际项目中应该弹出确认框
    console.log('删除订单:', record.orderNo)
  }

  const handleSubmit = async () => {
    submitLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('提交订单数据:', formData)
      showCreateModal.value = false
      loadOrders()
    } finally {
      submitLoading.value = false
    }
  }

  const handleCancel = () => {
    editingOrder.value = null
    Object.keys(formData).forEach(key => {
      formData[key] = ''
    })
  }

  // 生命周期
  onMounted(() => {
    loadOrders()
  })
</script>

<style lang="scss">
  .order-management {
    padding: var(--spacing-6);
  }

  .page-header {
    @include flex-between;
    margin-bottom: var(--spacing-6);
  }

  .page-title {
    margin: 0;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
  }

  .header-actions {
    display: flex;
    gap: var(--spacing-3);
  }

  .search-card {
    margin-bottom: var(--spacing-6);
  }

  .search-form {
    .search-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
    }

    .search-item {
      label {
        display: block;
        margin-bottom: var(--spacing-1);
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    .search-actions {
      display: flex;
      gap: var(--spacing-2);
    }
  }

  .table-card {
    .order-table {
      .order-no {
        font-family: var(--font-family-mono);
        font-weight: var(--font-weight-medium);
        color: var(--color-primary);
      }

      .package-type {
        padding: 2px 6px;
        font-size: var(--font-size-xs);
        background: var(--color-bg-secondary);
        border-radius: var(--radius-sm);
      }

      .quantity,
      .amount {
        font-family: var(--font-family-mono);
        font-weight: var(--font-weight-medium);
      }

      .table-actions {
        display: flex;
        gap: var(--spacing-1);
      }
    }
  }

  // 状态样式
  .status-inquiry {
    color: var(--color-info);
  }

  .status-quotation {
    color: var(--color-warning);
  }

  .status-pending {
    color: var(--color-warning);
  }

  .status-production {
    color: var(--color-primary);
  }

  .status-testing {
    color: var(--color-primary);
  }

  .status-completed {
    color: var(--color-success);
  }

  .status-cancelled {
    color: var(--color-error);
  }

  // 表单样式
  .order-form {
    .form-section {
      margin-bottom: var(--spacing-6);

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        padding-bottom: var(--spacing-2);
        margin: 0 0 var(--spacing-4) 0;
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
        border-bottom: 1px solid var(--color-border-light);
      }
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-4);

      .full-width {
        grid-column: 1 / -1;
      }
    }

    .form-item {
      label {
        display: block;
        margin-bottom: var(--spacing-1);
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);

        &.required::after {
          color: var(--color-error);
          content: ' *';
        }
      }
    }
  }

  // 订单详情样式
  .order-detail {
    .detail-section {
      margin-bottom: var(--spacing-6);

      h3 {
        padding-bottom: var(--spacing-2);
        margin: 0 0 var(--spacing-4) 0;
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-bold);
        color: var(--color-text-primary);
        border-bottom: 1px solid var(--color-border-light);
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-4);
    }

    .info-item {
      .label {
        display: block;
        margin-bottom: var(--spacing-1);
        font-size: var(--font-size-xs);
        color: var(--color-text-secondary);
      }

      .value {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
      }
    }

    .progress-timeline {
      position: relative;
      display: flex;
      justify-content: space-between;

      &::before {
        position: absolute;
        top: 20px;
        right: 40px;
        left: 40px;
        z-index: 1;
        height: 2px;
        content: '';
        background: var(--color-border-base);
      }

      .stage-item {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;

        .stage-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          margin-bottom: var(--spacing-2);
          font-size: 18px;
          border-radius: 50%;
        }

        .stage-info {
          text-align: center;

          .stage-name {
            margin-bottom: var(--spacing-1);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
          }

          .stage-status {
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
          }
        }

        &.stage-completed {
          .stage-icon {
            color: white;
            background: var(--color-success);
          }

          .stage-name {
            color: var(--color-success);
          }
        }

        &.stage-current {
          .stage-icon {
            color: white;
            background: var(--color-primary);
          }

          .stage-name {
            color: var(--color-primary);
          }
        }

        &.stage-pending {
          .stage-icon {
            color: var(--color-text-tertiary);
            background: var(--color-bg-secondary);
          }
        }
      }
    }
  }

  // 响应式
  @media (width <= 768px) {
    .order-management {
      padding: var(--spacing-4);
    }

    .search-form .search-row {
      grid-template-columns: 1fr;
    }

    .form-grid {
      grid-template-columns: 1fr !important;
    }

    .info-grid {
      grid-template-columns: 1fr !important;
    }

    .progress-timeline {
      flex-direction: column;
      gap: var(--spacing-4);

      &::before {
        top: 0;
        right: auto;
        left: 20px;
        width: 2px;
        height: 100%;
      }

      .stage-item {
        flex-direction: row;
        text-align: left;

        .stage-info {
          margin-left: var(--spacing-3);
          text-align: left;
        }
      }
    }
  }
</style>
