/**
 * IC封测CIM系统 - 基础数据类型定义
 * Basic Data Type Definitions for IC Packaging & Testing CIM System
 */

// ===== 产品相关基础数据 =====

// 产品分类
export enum ProductCategory {
  MICROCONTROLLER = 'microcontroller', // 微控制器
  ANALOG_IC = 'analog_ic', // 模拟IC
  DIGITAL_IC = 'digital_ic', // 数字IC
  MIXED_SIGNAL = 'mixed_signal', // 混合信号IC
  POWER_IC = 'power_ic', // 电源IC
  RF_IC = 'rf_ic', // 射频IC
  MEMORY_IC = 'memory_ic', // 存储IC
  SENSOR_IC = 'sensor_ic' // 传感器IC
}

// 产品信息
export interface Product {
  id: string
  productCode: string // 产品代码
  productName: string // 产品名称
  category: ProductCategory // 产品分类
  packageTypes: PackageType[] // 支持的封装类型
  specifications: {
    workingVoltage: string // 工作电压范围
    workingTemperature: string // 工作温度范围
    pinCount: number // 引脚数量
    dieSize: string // 芯片尺寸
    applications: string[] // 应用领域
  }
  processRequirements: {
    waferSize: number // 晶圆尺寸(英寸)
    probeCardType: string // 探针卡类型
    assemblyProcess: string[] // 封装工艺流程
    testProgram: string // 测试程序
  }
  qualityStandards: {
    yieldTarget: number // 良率目标(%)
    reliabilityLevel: 'A' | 'B' | 'C' // 可靠性等级
    qualificationStandard: string[] // 认证标准
  }
  status: 'active' | 'inactive' | 'discontinued' // 产品状态
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 封装类型定义(从order.ts移植并扩展)
export enum PackageType {
  // Ball Grid Array系列
  BGA = 'BGA',
  PBGA = 'PBGA', // Plastic BGA
  CBGA = 'CBGA', // Ceramic BGA
  FCBGA = 'FCBGA', // Flip Chip BGA

  // Quad Flat Package系列
  QFP = 'QFP',
  LQFP = 'LQFP', // Low-profile QFP
  TQFP = 'TQFP', // Thin QFP
  PQFP = 'PQFP', // Plastic QFP

  // Chip Scale Package系列
  CSP = 'CSP',
  WLCSP = 'WLCSP', // Wafer Level CSP
  FCSP = 'FCSP', // Flip Chip Scale Package

  // No-Lead Package系列
  QFN = 'QFN', // Quad Flat No-leads
  DFN = 'DFN', // Dual Flat No-leads
  SON = 'SON', // Small Outline No-leads

  // Small Outline Package系列
  SOP = 'SOP',
  SSOP = 'SSOP', // Shrink SOP
  TSOP = 'TSOP', // Thin SOP
  VSOP = 'VSOP', // Very Small SOP

  // 其他封装类型
  FC = 'FC', // Flip Chip
  COB = 'COB', // Chip on Board
  SIP = 'SIP', // System in Package
  POP = 'POP' // Package on Package
}

// 封装类型详细信息
export interface PackageTypeInfo {
  type: PackageType
  name: string // 封装名称
  description: string // 详细描述
  features: string[] // 特点
  applications: string[] // 应用场合
  pitchRange: string // 引脚间距范围
  pinCountRange: string // 引脚数量范围
  thermalCharacteristics: string // 热特性
  electricalCharacteristics: string // 电气特性
  packageSize: {
    minSize: string // 最小尺寸
    maxSize: string // 最大尺寸
    thickness: string // 厚度范围
  }
  assemblyComplexity: 'low' | 'medium' | 'high' // 封装复杂度
  cost: 'low' | 'medium' | 'high' // 成本等级
  status: 'active' | 'deprecated' // 状态
}

// ===== 工艺参数基础数据 =====

// 工艺类型
export enum ProcessType {
  CP_TEST = 'cp_test', // CP测试
  DIE_ATTACH = 'die_attach', // 贴芯
  WIRE_BOND = 'wire_bond', // 金线键合
  MOLDING = 'molding', // 塑封
  DEFLASH = 'deflash', // 去飞边
  MARKING = 'marking', // 打标
  TRIM_FORM = 'trim_form', // 切筋成形
  FT_TEST = 'ft_test', // 成品测试
  PACKAGING = 'packaging' // 包装
}

// 工艺参数
export interface ProcessParameter {
  id: string
  processType: ProcessType // 工艺类型
  parameterName: string // 参数名称
  parameterCode: string // 参数代码
  description: string // 参数描述
  unit: string // 单位
  dataType: 'number' | 'string' | 'boolean' | 'enum' // 数据类型
  defaultValue: any // 默认值
  validRange: {
    min?: number // 最小值
    max?: number // 最大值
    enumValues?: string[] // 枚举值
  }
  controlLimits: {
    lsl?: number // 下规格限
    usl?: number // 上规格限
    target?: number // 目标值
  }
  monitoringLevel: 'critical' | 'important' | 'normal' // 监控等级
  category: string // 参数分类
  applicablePackages: PackageType[] // 适用封装类型
  createdAt: string
  updatedAt: string
  createdBy: string
}

// ===== 设备信息基础数据 =====

// 设备类型
export enum EquipmentType {
  PROBER = 'prober', // 探针台
  TESTER = 'tester', // 测试机
  DIE_BONDER = 'die_bonder', // 贴片机
  WIRE_BONDER = 'wire_bonder', // 金线键合机
  MOLDING_PRESS = 'molding_press', // 塑封机
  DEFLASH_SYSTEM = 'deflash_system', // 去飞边机
  LASER_MARKER = 'laser_marker', // 激光打标机
  TRIM_FORM = 'trim_form', // 切筋成形机
  HANDLER = 'handler', // 分选机
  VISION_SYSTEM = 'vision_system' // 视觉检测系统
}

// 设备状态
export enum EquipmentStatus {
  RUNNING = 'running', // 运行中
  IDLE = 'idle', // 空闲
  MAINTENANCE = 'maintenance', // 维护中
  ERROR = 'error', // 故障
  SETUP = 'setup', // 设置中
  DISABLED = 'disabled' // 停用
}

// 设备信息
export interface Equipment {
  id: string
  equipmentCode: string // 设备编号
  equipmentName: string // 设备名称
  type: EquipmentType // 设备类型
  model: string // 设备型号
  manufacturer: string // 制造商
  location: {
    building: string // 建筑物
    floor: string // 楼层
    room: string // 房间
    position: string // 具体位置
  }
  specifications: {
    capacity: number // 产能(UPH - Units Per Hour)
    accuracy: string // 精度
    repeatability: string // 重复精度
    workingEnvironment: {
      temperature: string // 工作温度
      humidity: string // 工作湿度
      cleanLevel: string // 洁净度等级
    }
  }
  status: EquipmentStatus // 当前状态
  operationalInfo: {
    installDate: string // 安装日期
    warrantyExpiry: string // 保修到期日期
    lastMaintenanceDate: string // 上次维护日期
    nextMaintenanceDate: string // 下次维护日期
    operatingHours: number // 运行小时数
    cycleCount: number // 运行周期数
  }
  capabilities: {
    supportedPackages: PackageType[] // 支持的封装类型
    supportedProcesses: ProcessType[] // 支持的工艺类型
    maxWaferSize: number // 最大晶圆尺寸
    parallelSites: number // 并行测试位点数
  }
  maintenanceInfo: {
    pmInterval: number // 预防性维护间隔(小时)
    pmItems: string[] // PM项目列表
    sparePartsList: Array<{
      partName: string
      partNumber: string
      quantity: number
      supplier: string
    }>
  }
  createdAt: string
  updatedAt: string
  createdBy: string
}

// ===== 质量标准基础数据 =====

// 质量标准类型
export enum QualityStandardType {
  JEDEC = 'jedec', // JEDEC标准
  IPC = 'ipc', // IPC标准
  IATF16949 = 'iatf16949', // 汽车行业质量标准
  ISO9001 = 'iso9001', // ISO9001质量标准
  AEC_Q100 = 'aec_q100', // 汽车电子AEC-Q100
  MIL_STD = 'mil_std', // 军用标准
  COMPANY = 'company' // 公司内部标准
}

// 质量标准定义
export interface QualityStandard {
  id: string
  standardCode: string // 标准编号
  standardName: string // 标准名称
  type: QualityStandardType // 标准类型
  version: string // 标准版本
  description: string // 标准描述
  applicableProducts: ProductCategory[] // 适用产品类别
  requirements: Array<{
    category: string // 要求类别
    parameter: string // 参数名称
    specification: string // 规格要求
    testMethod: string // 测试方法
    acceptanceCriteria: string // 接受标准
  }>
  documentPath?: string // 文档路径
  effectiveDate: string // 生效日期
  reviewDate?: string // 复审日期
  status: 'active' | 'draft' | 'obsolete' // 状态
  createdAt: string
  updatedAt: string
  createdBy: string
}

// ===== 供应商信息基础数据 =====

// 供应商类型
export enum SupplierType {
  WAFER = 'wafer', // 晶圆供应商
  MATERIAL = 'material', // 材料供应商
  EQUIPMENT = 'equipment', // 设备供应商
  SERVICE = 'service', // 服务供应商
  CONSUMABLE = 'consumable' // 消耗品供应商
}

// 供应商等级
export enum SupplierGrade {
  A = 'A', // 优秀供应商
  B = 'B', // 良好供应商
  C = 'C', // 一般供应商
  D = 'D' // 需要改进供应商
}

// 供应商信息
export interface Supplier {
  id: string
  supplierCode: string // 供应商编码
  supplierName: string // 供应商名称
  supplierNameEn?: string // 英文名称
  type: SupplierType // 供应商类型
  grade: SupplierGrade // 供应商等级

  contactInfo: {
    address: string // 地址
    city: string // 城市
    country: string // 国家
    postalCode: string // 邮政编码
    website?: string // 网站
    primaryContact: {
      name: string // 联系人姓名
      position: string // 职位
      phone: string // 电话
      email: string // 邮箱
    }
    salesContact?: {
      name: string
      phone: string
      email: string
    }
    technicalContact?: {
      name: string
      phone: string
      email: string
    }
  }

  businessInfo: {
    registrationNumber: string // 注册号
    taxId: string // 税号
    establishedDate: string // 成立日期
    employees: number // 员工数量
    annualRevenue?: number // 年营业额
    certifications: string[] // 认证证书
  }

  capabilities: {
    mainProducts: string[] // 主要产品
    productionCapacity: string // 生产能力
    qualitySystem: string[] // 质量体系
    deliveryPerformance: {
      onTimeDeliveryRate: number // 准时交付率(%)
      qualityScore: number // 质量评分
      responseTime: number // 响应时间(小时)
    }
  }

  cooperationInfo: {
    cooperationStartDate: string // 合作开始日期
    contractType: string // 合同类型
    paymentTerms: string // 付款条件
    totalOrderValue: number // 累计订单金额
    lastOrderDate?: string // 最后订单日期
  }

  status: 'active' | 'inactive' | 'suspended' // 状态
  createdAt: string
  updatedAt: string
  createdBy: string
}

// ===== 基础数据查询参数 =====

export interface BasicDataQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  status?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 基础数据列表响应
export interface BasicDataListResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// ===== 基础数据统计 =====

export interface BasicDataStats {
  products: {
    total: number
    active: number
    byCategory: Record<ProductCategory, number>
  }
  equipment: {
    total: number
    running: number
    byType: Record<EquipmentType, number>
    utilizationRate: number
  }
  suppliers: {
    total: number
    active: number
    byType: Record<SupplierType, number>
    byGrade: Record<SupplierGrade, number>
  }
  processParameters: {
    total: number
    critical: number
    byProcess: Record<ProcessType, number>
  }
  qualityStandards: {
    total: number
    active: number
    byType: Record<QualityStandardType, number>
  }
}
