<template>
  <div class="system-profile">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><User /></el-icon>
        个人资料
      </h1>
      <p class="page-description">个人信息和账户设置</p>
    </div>

    <div class="page-content">
      <el-row :gutter="24">
        <el-col :span="8">
          <el-card>
            <template #header>
              <span>用户头像</span>
            </template>
            <div class="avatar-section">
              <el-avatar :size="100" :src="currentUser?.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <el-button type="primary" size="small" style="margin-top: 16px;">
                上传头像
              </el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="16">
          <el-card>
            <template #header>
              <span>基本信息</span>
            </template>
            <el-form :model="profileForm" label-width="100px">
              <el-form-item label="真实姓名">
                <el-input v-model="profileForm.realName" />
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="profileForm.email" />
              </el-form-item>
              <el-form-item label="手机号">
                <el-input v-model="profileForm.phone" />
              </el-form-item>
              <el-form-item label="部门">
                <el-input v-model="profileForm.department" disabled />
              </el-form-item>
              <el-form-item label="职位">
                <el-input v-model="profileForm.position" disabled />
              </el-form-item>
              <el-form-item>
                <el-button type="primary">保存修改</el-button>
                <el-button>取消</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive, computed } from 'vue'
  import { useAuthStore } from '@/stores/auth'

  const authStore = useAuthStore()
  const currentUser = computed(() => authStore.currentUser)

  // 个人资料表单
  const profileForm = reactive({
    realName: currentUser.value?.realName || '',
    email: currentUser.value?.email || '',
    phone: currentUser.value?.phone || '',
    department: currentUser.value?.department || '',
    position: currentUser.value?.position || ''
  })

  console.log('个人资料页面已加载')
</script>

<style lang="scss" scoped>
  .system-profile {
    padding: var(--spacing-4);
  }

  .page-header {
    margin-bottom: var(--spacing-6);

    .page-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
    }

    .page-description {
      margin: 0;
      color: var(--color-text-secondary);
    }
  }

  .avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-4);
  }
</style>