// 极简工具类 - 只包含最常用的实用类

// 布局工具类
.flex { display: flex; }

.inline-flex { display: inline-flex; }

.grid { display: grid; }

.inline-grid { display: inline-grid; }

.block { display: block; }

.inline-block { display: inline-block; }

.inline { display: inline; }

.hidden { display: none; }

// Flex 工具类
.flex-row { flex-direction: row; }

.flex-col { flex-direction: column; }

.flex-wrap { flex-wrap: wrap; }

.flex-nowrap { flex-wrap: nowrap; }

.flex-1 { flex: 1 1 0%; }

.flex-auto { flex: 1 1 auto; }

.flex-none { flex: none; }

.flex-shrink-0 { flex-shrink: 0; }

.flex-grow { flex-grow: 1; }

// 对齐工具类
.justify-start { justify-content: flex-start; }

.justify-center { justify-content: center; }

.justify-end { justify-content: flex-end; }

.justify-between { justify-content: space-between; }

.justify-around { justify-content: space-around; }

.justify-evenly { justify-content: space-evenly; }

.items-start { align-items: flex-start; }

.items-center { align-items: center; }

.items-end { align-items: flex-end; }

.items-baseline { align-items: baseline; }

.items-stretch { align-items: stretch; }

.content-start { align-content: flex-start; }

.content-center { align-content: center; }

.content-end { align-content: flex-end; }

.content-between { align-content: space-between; }

.content-around { align-content: space-around; }

.self-auto { align-self: auto; }

.self-start { align-self: flex-start; }

.self-center { align-self: center; }

.self-end { align-self: flex-end; }

.self-stretch { align-self: stretch; }

// 位置工具类
.relative { position: relative; }

.absolute { position: absolute; }

.fixed { position: fixed; }

.sticky { position: sticky; }

.static { position: static; }

.inset-0 { inset: 0; }

.top-0 { top: 0; }

.right-0 { right: 0; }

.bottom-0 { bottom: 0; }

.left-0 { left: 0; }

// 间距工具类 - 基于 4px 网格
.m-0 { margin: 0; }

.m-1 { margin: var(--spacing-1); }

.m-2 { margin: var(--spacing-2); }

.m-3 { margin: var(--spacing-3); }

.m-4 { margin: var(--spacing-4); }

.m-5 { margin: var(--spacing-5); }

.m-6 { margin: var(--spacing-6); }

.m-8 { margin: var(--spacing-8); }

.mx-0 { margin-right: 0; margin-left: 0; }

.mx-1 { margin-right: var(--spacing-1); margin-left: var(--spacing-1); }

.mx-2 { margin-right: var(--spacing-2); margin-left: var(--spacing-2); }

.mx-3 { margin-right: var(--spacing-3); margin-left: var(--spacing-3); }

.mx-4 { margin-right: var(--spacing-4); margin-left: var(--spacing-4); }

.mx-auto { margin-right: auto; margin-left: auto; }

.my-0 { margin-top: 0; margin-bottom: 0; }

.my-1 { margin-top: var(--spacing-1); margin-bottom: var(--spacing-1); }

.my-2 { margin-top: var(--spacing-2); margin-bottom: var(--spacing-2); }

.my-3 { margin-top: var(--spacing-3); margin-bottom: var(--spacing-3); }

.my-4 { margin-top: var(--spacing-4); margin-bottom: var(--spacing-4); }

.mt-0 { margin-top: 0; }

.mt-1 { margin-top: var(--spacing-1); }

.mt-2 { margin-top: var(--spacing-2); }

.mt-3 { margin-top: var(--spacing-3); }

.mt-4 { margin-top: var(--spacing-4); }

.mb-0 { margin-bottom: 0; }

.mb-1 { margin-bottom: var(--spacing-1); }

.mb-2 { margin-bottom: var(--spacing-2); }

.mb-3 { margin-bottom: var(--spacing-3); }

.mb-4 { margin-bottom: var(--spacing-4); }

.ml-0 { margin-left: 0; }

.ml-1 { margin-left: var(--spacing-1); }

.ml-2 { margin-left: var(--spacing-2); }

.ml-3 { margin-left: var(--spacing-3); }

.ml-4 { margin-left: var(--spacing-4); }

.mr-0 { margin-right: 0; }

.mr-1 { margin-right: var(--spacing-1); }

.mr-2 { margin-right: var(--spacing-2); }

.mr-3 { margin-right: var(--spacing-3); }

.mr-4 { margin-right: var(--spacing-4); }

// 内边距工具类
.p-0 { padding: 0; }

.p-1 { padding: var(--spacing-1); }

.p-2 { padding: var(--spacing-2); }

.p-3 { padding: var(--spacing-3); }

.p-4 { padding: var(--spacing-4); }

.p-5 { padding: var(--spacing-5); }

.p-6 { padding: var(--spacing-6); }

.px-0 { padding-right: 0; padding-left: 0; }

.px-1 { padding-right: var(--spacing-1); padding-left: var(--spacing-1); }

.px-2 { padding-right: var(--spacing-2); padding-left: var(--spacing-2); }

.px-3 { padding-right: var(--spacing-3); padding-left: var(--spacing-3); }

.px-4 { padding-right: var(--spacing-4); padding-left: var(--spacing-4); }

.py-0 { padding-top: 0; padding-bottom: 0; }

.py-1 { padding-top: var(--spacing-1); padding-bottom: var(--spacing-1); }

.py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }

.py-3 { padding-top: var(--spacing-3); padding-bottom: var(--spacing-3); }

.py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }

// 尺寸工具类
.w-full { width: 100%; }

.w-auto { width: auto; }

.w-fit { width: fit-content; }

.w-screen { width: 100vw; }

.h-full { height: 100%; }

.h-auto { height: auto; }

.h-fit { height: fit-content; }

.h-screen { height: 100vh; }

.min-w-0 { min-width: 0; }

.min-w-full { min-width: 100%; }

.min-h-0 { min-height: 0; }

.min-h-full { min-height: 100%; }

.min-h-screen { min-height: 100vh; }

.max-w-none { max-width: none; }

.max-w-full { max-width: 100%; }

.max-h-full { max-height: 100%; }

.max-h-screen { max-height: 100vh; }

// 文字工具类
.text-left { text-align: left; }

.text-center { text-align: center; }

.text-right { text-align: right; }

.text-justify { text-align: justify; }

.text-xs { font-size: var(--font-size-xs); }

.text-sm { font-size: var(--font-size-sm); }

.text-base { font-size: var(--font-size-base); }

.text-lg { font-size: var(--font-size-lg); }

.text-xl { font-size: var(--font-size-xl); }

.text-2xl { font-size: var(--font-size-2xl); }

.text-3xl { font-size: var(--font-size-3xl); }

.font-normal { font-weight: var(--font-weight-normal); }

.font-medium { font-weight: var(--font-weight-medium); }

.font-bold { font-weight: var(--font-weight-bold); }

.leading-tight { line-height: var(--line-height-tight); }

.leading-normal { line-height: var(--line-height-base); }

.leading-loose { line-height: var(--line-height-loose); }

// 颜色工具类
.text-primary { color: var(--color-text-primary); }

.text-secondary { color: var(--color-text-secondary); }

.text-tertiary { color: var(--color-text-tertiary); }

.text-disabled { color: var(--color-text-disabled); }

.text-success { color: var(--color-success); }

.text-warning { color: var(--color-warning); }

.text-error { color: var(--color-error); }

.text-info { color: var(--color-info); }

.bg-primary { background-color: var(--color-bg-primary); }

.bg-secondary { background-color: var(--color-bg-secondary); }

.bg-tertiary { background-color: var(--color-bg-tertiary); }

// 边框工具类
.border { border: 1px solid var(--color-border-base); }

.border-0 { border: 0; }

.border-t { border-top: 1px solid var(--color-border-base); }

.border-r { border-right: 1px solid var(--color-border-base); }

.border-b { border-bottom: 1px solid var(--color-border-base); }

.border-l { border-left: 1px solid var(--color-border-base); }

.rounded { border-radius: var(--radius-base); }

.rounded-sm { border-radius: var(--radius-sm); }

.rounded-md { border-radius: var(--radius-md); }

.rounded-lg { border-radius: var(--radius-lg); }

.rounded-full { border-radius: var(--radius-full); }

.rounded-none { border-radius: 0; }

// 阴影工具类
.shadow { box-shadow: var(--shadow-base); }

.shadow-sm { box-shadow: var(--shadow-sm); }

.shadow-md { box-shadow: var(--shadow-md); }

.shadow-lg { box-shadow: var(--shadow-lg); }

.shadow-none { box-shadow: none; }

// 溢出工具类
.overflow-auto { overflow: auto; }

.overflow-hidden { overflow: hidden; }

.overflow-visible { overflow: visible; }

.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }

.overflow-y-auto { overflow-y: auto; }

.overflow-x-hidden { overflow-x: hidden; }

.overflow-y-hidden { overflow-y: hidden; }

// 透明度工具类
.opacity-0 { opacity: 0; }

.opacity-25 { opacity: 0.25; }

.opacity-50 { opacity: 0.5; }

.opacity-75 { opacity: 0.75; }

.opacity-100 { opacity: 1; }

// 过渡动画工具类
.transition { transition: all var(--transition-normal); }

.transition-fast { transition: all var(--transition-fast); }

.transition-slow { transition: all var(--transition-slow); }

.transition-none { transition: none; }

// 指针事件工具类
.pointer-events-none { pointer-events: none; }

.pointer-events-auto { pointer-events: auto; }

// 用户选择工具类
.select-none { user-select: none; }

.select-text { user-select: text; }

.select-all { user-select: all; }

.select-auto { user-select: auto; }

// 响应式断点工具类
@media (width >= 640px) {
  .sm\:hidden { display: none; }

  .sm\:block { display: block; }

  .sm\:flex { display: flex; }
}

@media (width >= 768px) {
  .md\:hidden { display: none; }

  .md\:block { display: block; }

  .md\:flex { display: flex; }
}

@media (width >= 1024px) {
  .lg\:hidden { display: none; }

  .lg\:block { display: block; }

  .lg\:flex { display: flex; }
}