<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><user /></el-icon>
          用户管理
        </h1>
        <p class="page-description">
IC封测CIM系统用户账号管理、角色分配和权限控制
</p>
      </div>

      <div class="header-actions">
        <el-button type="primary"
:icon="Plus" @click="showCreateDialog"
>
新增用户
</el-button>

        <el-dropdown @command="handleBatchAction">
          <el-button>
            批量操作
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="enable"
:disabled="!hasSelectedUsers">
                批量启用
              </el-dropdown-item>
              <el-dropdown-item command="disable"
:disabled="!hasSelectedUsers">
                批量禁用
              </el-dropdown-item>
              <el-dropdown-item command="export"
divided
>
导出用户数据
</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card"
shadow="never">
      <el-form :model="queryParams" :inline="true" label-width="80px" class="search-form">
        <el-form-item label="关键字">
          <el-input
            v-model="queryParams.keyword"
            placeholder="用户名/真实姓名/邮箱"
            clearable
            style="width: 200px"
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-form-item>

        <el-form-item label="部门">
          <el-select
            v-model="queryParams.department"
            placeholder="选择部门"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="queryParams.status"
            placeholder="选择状态"
            clearable
            style="width: 120px"
            @change="handleSearch"
          >
            <el-option label="启用"
value="active" />
            <el-option label="禁用"
value="inactive" />
            <el-option label="锁定"
value="locked" />
          </el-select>
        </el-form-item>

        <el-form-item label="角色">
          <el-select
            v-model="queryParams.role"
            placeholder="选择角色"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option
              v-for="role in roles"
              :key="role.code"
              :label="role.name"
              :value="role.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary"
:icon="Search" @click="handleSearch"
>
查询
</el-button>
          <el-button :icon="Refresh"
@click="resetSearch"
>
重置
</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card"
shadow="never">
      <div class="table-header">
        <div class="table-title">
          用户列表
          <span class="total-count">（共 {{ pagination.total }} 条）</span>
        </div>

        <div class="table-tools">
          <el-tooltip content="刷新">
            <el-button :icon="Refresh" circle @click="loadUsers" />
          </el-tooltip>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="users"
        stripe
        border
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection"
width="55" />

        <el-table-column label="用户信息" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="40" :src="row.avatar" :alt="row.realName" class="user-avatar">
                <el-icon><user /></el-icon>
              </el-avatar>

              <div class="user-details">
                <div class="user-name">
                  {{ row.realName }}
                </div>
                <div class="username">@{{ row.username }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip />

        <el-table-column prop="phone" label="电话" width="130" show-overflow-tooltip />

        <el-table-column prop="department" label="部门" width="120" show-overflow-tooltip />

        <el-table-column prop="position" label="职位" width="120" show-overflow-tooltip />

        <el-table-column label="角色" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="role-tags">
              <el-tag v-for="role in row.roles" :key="role" size="small" class="role-tag">
                {{ getRoleName(role) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="lastLoginTime" label="最后登录" width="160" sortable="custom">
          <template #default="{ row }">
            <div v-if="row.lastLoginTime"
class="login-info">
              <div>{{ formatDateTime(row.lastLoginTime) }}</div>
              <div class="login-ip">
                {{ row.lastLoginIp }}
              </div>
            </div>
            <span v-else
class="text-muted">未登录</span>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button type="primary" link :icon="Edit" @click="showEditDialog(row)">
                编辑
              </el-button>

              <el-button type="warning" link :icon="Key" @click="showResetPasswordDialog(row)">
                重置密码
              </el-button>

              <el-dropdown @command="(cmd: string) => handleUserAction(cmd, row)">
                <el-button type="info"
link>
                  更多
                  <el-icon class="el-icon--right">
                    <arrow-down />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :command="`${row.status === 'active' ? 'disable' : 'enable'}`"
                    >
                      {{ row.status === 'active' ? '禁用' : '启用' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="permissions">
权限管理
</el-dropdown-item>
                    <el-dropdown-item command="logs">
操作日志
</el-dropdown-item>
                    <el-dropdown-item command="delete"
divided>
                      <span style="color: var(--color-danger)">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑用户对话框 -->
    <user-form-dialog
      v-model="userDialogVisible"
      :user-data="currentUser"
      :is-edit="isEditMode"
      :departments="departments"
      :roles="roles"
      @success="handleFormSuccess"
    />

    <!-- 重置密码对话框 -->
    <reset-password-dialog
      v-model="resetPasswordVisible"
      :user-data="currentUser"
      @success="handleResetPasswordSuccess"
    />

    <!-- 权限管理对话框 -->
    <user-permissions-dialog
      v-model="permissionsVisible"
      :user-data="currentUser"
      :permissions="permissions"
      @success="handlePermissionsSuccess"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { User, Plus, Search, Refresh, Edit, Key, ArrowDown } from '@element-plus/icons-vue'
  import type {
    UserInfo,
    UserQueryParams,
    Department,
    RoleConfig,
    PermissionConfig
  } from '@/types/user'
  import { useUserManagement } from '@/composables/useUserManagement'
  import UserFormDialog from '@/components/system/UserFormDialog.vue'
  import ResetPasswordDialog from '@/components/system/ResetPasswordDialog.vue'
  import UserPermissionsDialog from '@/components/system/UserPermissionsDialog.vue'

  // 组合式函数
  const {
    users,
    loading,
    pagination,
    departments,
    roles,
    permissions,
    loadUsers,
    searchUsers,
    deleteUser,
    toggleUserStatus,
    exportUsers
  } = useUserManagement()

  // 查询参数
  const queryParams = reactive<UserQueryParams>({
    page: 1,
    pageSize: 20,
    keyword: '',
    department: '',
    status: '',
    role: '',
    sortBy: 'createTime',
    sortOrder: 'desc'
  })

  // 选中的用户
  const selectedUsers = ref<UserInfo[]>([])
  const hasSelectedUsers = computed(() => selectedUsers.value.length > 0)

  // 对话框状态
  const userDialogVisible = ref(false)
  const resetPasswordVisible = ref(false)
  const permissionsVisible = ref(false)
  const currentUser = ref<UserInfo | null>(null)
  const isEditMode = ref(false)

  /**
   * 处理查询
   */
  const handleSearch = async (): Promise<void> => {
    await searchUsers(queryParams)
  }

  /**
   * 重置查询
   */
  const resetSearch = (): void => {
    Object.assign(queryParams, {
      page: 1,
      pageSize: 20,
      keyword: '',
      department: '',
      status: '',
      role: '',
      sortBy: 'createTime',
      sortOrder: 'desc'
    })
    handleSearch()
  }

  /**
   * 处理选择变化
   */
  const handleSelectionChange = (selection: UserInfo[]): void => {
    selectedUsers.value = selection
  }

  /**
   * 处理排序变化
   */
  const handleSortChange = ({ prop, order }: { prop: string; order: string }): void => {
    queryParams.sortBy = prop as 'createTime' | 'updateTime' | 'lastLoginTime' | 'username'
    queryParams.sortOrder = order === 'ascending' ? 'asc' : 'desc'
    handleSearch()
  }

  /**
   * 处理分页变化
   */
  const handlePageChange = (page: number): void => {
    queryParams.page = page
    handleSearch()
  }

  /**
   * 处理页大小变化
   */
  const handleSizeChange = (pageSize: number): void => {
    queryParams.pageSize = pageSize
    queryParams.page = 1
    handleSearch()
  }

  /**
   * 显示新增用户对话框
   */
  const showCreateDialog = (): void => {
    currentUser.value = null
    isEditMode.value = false
    userDialogVisible.value = true
  }

  /**
   * 显示编辑用户对话框
   */
  const showEditDialog = (user: UserInfo): void => {
    currentUser.value = { ...user }
    isEditMode.value = true
    userDialogVisible.value = true
  }

  /**
   * 显示重置密码对话框
   */
  const showResetPasswordDialog = (user: UserInfo): void => {
    currentUser.value = { ...user }
    resetPasswordVisible.value = true
  }

  /**
   * 处理用户操作
   */
  const handleUserAction = async (command: string, user: UserInfo): Promise<void> => {
    switch (command) {
      case 'enable':
      case 'disable':
        await handleToggleStatus(user)
        break
      case 'permissions':
        showPermissionsDialog(user)
        break
      case 'logs':
        showUserLogs(user)
        break
      case 'delete':
        await handleDeleteUser(user)
        break
    }
  }

  /**
   * 处理批量操作
   */
  const handleBatchAction = async (command: string): Promise<void> => {
    switch (command) {
      case 'enable':
      case 'disable':
        await handleBatchToggleStatus(command === 'enable')
        break
      case 'export':
        await handleExportUsers()
        break
    }
  }

  /**
   * 切换用户状态
   */
  const handleToggleStatus = async (user: UserInfo): Promise<void> => {
    try {
      const action = user.status === 'active' ? '禁用' : '启用'
      await ElMessageBox.confirm(`确定要${action}用户"${user.realName}"吗？`, '确认操作')

      await toggleUserStatus(user.id, user.status === 'active' ? 'inactive' : 'active')
      ElMessage.success(`${action}成功`)
      await loadUsers()
    } catch (error: any) {
      if (error !== 'cancel') {
        ElMessage.error(`操作失败: ${error.message}`)
      }
    }
  }

  /**
   * 批量切换用户状态
   */
  const handleBatchToggleStatus = async (enable: boolean): Promise<void> => {
    try {
      const action = enable ? '启用' : '禁用'
      await ElMessageBox.confirm(
        `确定要${action}选中的 ${selectedUsers.value.length} 个用户吗？`,
        '确认操作'
      )

      // 这里应该调用批量API
      ElMessage.success(`批量${action}成功`)
      selectedUsers.value = []
      await loadUsers()
    } catch (error: any) {
      if (error !== 'cancel') {
        ElMessage.error(`批量操作失败: ${error.message}`)
      }
    }
  }

  /**
   * 删除用户
   */
  const handleDeleteUser = async (user: UserInfo): Promise<void> => {
    try {
      await ElMessageBox.confirm(
        `确定要删除用户"${user.realName}"吗？此操作不可恢复！`,
        '危险操作',
        { type: 'error' }
      )

      await deleteUser(user.id)
      ElMessage.success('删除成功')
      await loadUsers()
    } catch (error: any) {
      if (error !== 'cancel') {
        ElMessage.error(`删除失败: ${error.message}`)
      }
    }
  }

  /**
   * 导出用户数据
   */
  const handleExportUsers = async (): Promise<void> => {
    try {
      await exportUsers(queryParams)
      ElMessage.success('导出成功')
    } catch (error: any) {
      ElMessage.error(`导出失败: ${error.message}`)
    }
  }

  /**
   * 显示权限管理对话框
   */
  const showPermissionsDialog = (user: UserInfo): void => {
    currentUser.value = { ...user }
    permissionsVisible.value = true
  }

  /**
   * 显示用户操作日志
   */
  const showUserLogs = (user: UserInfo): void => {
    // 这里可以跳转到用户日志页面或显示日志对话框
    ElMessage.info('跳转到用户操作日志页面')
  }

  /**
   * 处理表单成功
   */
  const handleFormSuccess = (): void => {
    userDialogVisible.value = false
    loadUsers()
  }

  /**
   * 处理重置密码成功
   */
  const handleResetPasswordSuccess = (): void => {
    resetPasswordVisible.value = false
  }

  /**
   * 处理权限管理成功
   */
  const handlePermissionsSuccess = (): void => {
    permissionsVisible.value = false
    loadUsers()
  }

  /**
   * 获取角色名称
   */
  const getRoleName = (roleCode: string): string => {
    const role = roles.value.find(r => r.code === roleCode)
    return role?.name || roleCode
  }

  /**
   * 获取状态类型
   */
  const getStatusType = (status: string): string => {
    const types: Record<string, string> = {
      active: 'success',
      inactive: 'warning',
      locked: 'danger'
    }
    return types[status] || 'info'
  }

  /**
   * 获取状态文本
   */
  const getStatusText = (status: string): string => {
    const texts: Record<string, string> = {
      active: '启用',
      inactive: '禁用',
      locked: '锁定'
    }
    return texts[status] || status
  }

  /**
   * 格式化日期时间
   */
  const formatDateTime = (dateTime: string): string => {
    return new Date(dateTime).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 初始化
  onMounted(() => {
    loadUsers()
  })
</script>

<style lang="scss" scoped>
  .user-management {
    padding: 24px;
    min-height: calc(100vh - 60px);
    background-color: var(--color-bg-light);
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .page-description {
        margin: 0;
        color: var(--color-text-secondary);
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .search-card,
  .table-card {
    margin-bottom: 24px;
    border-radius: 8px;

    :deep(.el-card__body) {
      padding: 24px;
    }
  }

  .search-form {
    margin: 0;

    .el-form-item {
      margin-bottom: 0;
    }
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .table-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--color-text-primary);

      .total-count {
        font-size: 14px;
        font-weight: 400;
        color: var(--color-text-secondary);
      }
    }

    .table-tools {
      display: flex;
      gap: 8px;
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .user-avatar {
      flex-shrink: 0;
    }

    .user-details {
      min-width: 0;

      .user-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--color-text-primary);
        margin-bottom: 2px;
      }

      .username {
        font-size: 12px;
        color: var(--color-text-secondary);
      }
    }
  }

  .role-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .role-tag {
      font-size: 11px;
    }
  }

  .login-info {
    font-size: 12px;

    .login-ip {
      color: var(--color-text-placeholder);
      margin-top: 2px;
    }
  }

  .table-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
  }

  .text-muted {
    color: var(--color-text-placeholder);
    font-size: 12px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .user-management {
      padding: 16px;
    }

    .page-header {
      flex-direction: column;
      gap: 16px;
    }

    .header-actions {
      align-self: stretch;

      > * {
        flex: 1;
      }
    }

    .search-form {
      :deep(.el-form-item) {
        display: block;
        margin-bottom: 16px;

        .el-form-item__content {
          margin-left: 0 !important;
        }
      }
    }

    .table-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }

    :deep(.el-table) {
      font-size: 12px;

      .el-table__cell {
        padding: 8px 4px;
      }
    }

    .pagination-wrapper {
      :deep(.el-pagination) {
        justify-content: center;

        .el-pagination__sizes,
        .el-pagination__jump {
          display: none;
        }
      }
    }
  }
</style>
