/// <reference types="vitest" />
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver, VantResolver } from 'unplugin-vue-components/resolvers'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
        'vitest', // 添加Vitest API自动导入
        {
          'lodash-es': ['debounce', 'throttle', 'cloneDeep', 'merge'],
          'dayjs': [['default', 'dayjs']],
          'axios': [['default', 'axios']]
        }
      ],
      resolvers: [ElementPlusResolver()],
      dts: 'src/types/auto-imports.d.ts'
    }),
    Components({
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
          directives: true,
          version: '2.5.6'
        }),
        VantResolver({
          importStyle: false
        })
      ],
      dts: 'src/types/components.d.ts'
    })
  ],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@assets': resolve(__dirname, 'src/assets'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@composables': resolve(__dirname, 'src/composables'),
      '@api': resolve(__dirname, 'src/api'),
      '@types': resolve(__dirname, 'src/types'),
      '@tests': resolve(__dirname, 'tests')
    }
  },

  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @use "@/assets/styles/themes/variables.scss" as *;
          @use "@/assets/styles/base/mixins.scss" as *;
        `,
        api: 'modern-compiler'
      }
    }
  },

  test: {
    // 测试环境配置
    environment: 'jsdom',
    
    // 全局设置
    globals: true,
    
    // 测试文件匹配模式
    include: [
      'tests/unit/**/*.{test,spec}.{js,ts}',
      'src/**/__tests__/**/*.{test,spec}.{js,ts}',
      'src/**/*.{test,spec}.{js,ts}'
    ],
    
    // 排除文件
    exclude: [
      'node_modules',
      'dist',
      'cypress',
      'tests/e2e'
    ],
    
    // 设置文件
    setupFiles: ['tests/unit/setup.ts'],
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: 'coverage',
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.{js,ts}',
        'src/types/',
        'src/main.ts',
        'dist/',
        '.eslintrc.cjs',
        'stylelint.config.cjs'
      ],
      // 覆盖率阈值
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        },
        // 对关键模块要求更高覆盖率
        'src/components/base/': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90
        },
        'src/stores/': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        },
        'src/utils/': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        }
      }
    },
    
    // 并发测试
    threads: true,
    
    // 测试超时
    testTimeout: 10000,
    
    // 监听模式
    watch: false,
    
    // 报告器
    reporter: ['verbose', 'json', 'html'],
    
    // 输出目录
    outputFile: {
      json: 'test-results/results.json',
      html: 'test-results/results.html'
    },
    
    // 模拟配置
    clearMocks: true,
    restoreMocks: true,
    
    // 环境变量
    env: {
      NODE_ENV: 'test',
      VITE_APP_TITLE: 'IC CIM System Test',
      VITE_API_BASE_URL: 'http://localhost:8080',
      VITE_WS_BASE_URL: 'ws://localhost:8080'
    },
    
    // Vitest UI配置
    ui: false,
    
    // 调试配置
    inspectBrk: false,
    
    // 全局teardown
    globalSetup: 'tests/unit/globalSetup.ts'
  },

  define: {
    __VUE_OPTIONS_API__: 'false',
    __VUE_PROD_DEVTOOLS__: 'false'
  }
})