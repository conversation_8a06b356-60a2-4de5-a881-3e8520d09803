/**
 * 监控大屏样式
 * 专为大屏显示优化的样式定义
 */

// ============ 大屏基础变量 ============
:root {
  // 大屏专用间距
  --dashboard-spacing-xs: 4px;
  --dashboard-spacing-sm: 8px;
  --dashboard-spacing-md: 16px;
  --dashboard-spacing-lg: 24px;
  --dashboard-spacing-xl: 32px;
  --dashboard-spacing-2xl: 48px;
  
  // 大屏专用字体大小
  --dashboard-font-xs: 12px;
  --dashboard-font-sm: 14px;
  --dashboard-font-md: 16px;
  --dashboard-font-lg: 20px;
  --dashboard-font-xl: 24px;
  --dashboard-font-2xl: 32px;
  --dashboard-font-3xl: 40px;
  
  // 大屏专用颜色
  --dashboard-primary: #1890ff;
  --dashboard-success: #52c41a;
  --dashboard-warning: #faad14;
  --dashboard-danger: #f5222d;
  --dashboard-info: #13c2c2;
  
  // 大屏背景色
  --dashboard-bg-primary: #0a0e27;
  --dashboard-bg-secondary: #141b3c;
  --dashboard-bg-tertiary: #1e2a5a;
  --dashboard-bg-card: #1a1f37;
  --dashboard-bg-overlay: rgb(20 27 60 / 80%);
  
  // 大屏边框色
  --dashboard-border-primary: #2c3e5f;
  --dashboard-border-secondary: #34495e;
  --dashboard-border-accent: #3498db;
  
  // 大屏文字色
  --dashboard-text-primary: #fff;
  --dashboard-text-secondary: #b8c5d6;
  --dashboard-text-tertiary: #8892a5;
  --dashboard-text-accent: #3498db;
}

// ============ 大屏布局样式 ============
.dashboard-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
  color: var(--dashboard-text-primary);
  background: var(--dashboard-bg-primary);
  
  // 大屏专用背景纹理
  background-image: 
    radial-gradient(circle at 25% 25%, #1e3a8a 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, #1e40af 0%, transparent 50%);
  background-position: 0 0, 60px 60px;
  background-size: 100px 100px, 120px 120px;
  
  &::before {
    position: absolute;
    inset: 0;
    z-index: 0;
    pointer-events: none;
    content: '';
    background: linear-gradient(135deg, rgb(30 58 138 / 10%) 0%, rgb(30 64 175 / 5%) 100%);
  }
}

// ============ 大屏网格布局 ============
.dashboard-grid {
  position: relative;
  z-index: 1;
  display: grid;
  gap: var(--dashboard-spacing-lg);
  height: 100%;
  padding: var(--dashboard-spacing-lg);
  
  // 全屏大屏布局
  &--fullscreen {
    grid-template-rows: auto 1fr auto;
    grid-template-columns: repeat(12, 1fr);
    gap: var(--dashboard-spacing-xl);
    padding: var(--dashboard-spacing-xl);
  }
  
  // 标准大屏布局
  &--standard {
    grid-template-rows: auto 1fr;
    grid-template-columns: repeat(8, 1fr);
    gap: var(--dashboard-spacing-lg);
  }
  
  // 紧凑布局
  &--compact {
    grid-template-columns: repeat(6, 1fr);
    gap: var(--dashboard-spacing-md);
    padding: var(--dashboard-spacing-md);
  }
}

// ============ 大屏卡片样式 ============
.dashboard-card {
  position: relative;
  padding: var(--dashboard-spacing-lg);
  overflow: hidden;
  background: var(--dashboard-bg-card);
  border: 1px solid var(--dashboard-border-primary);
  border-radius: 12px;
  
  // 卡片发光效果
  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    background: linear-gradient(90deg, 
      transparent 0%, 
      var(--dashboard-border-accent) 50%, 
      transparent 100%
    );
    opacity: 0.6;
  }
  
  // 卡片悬停效果
  &:hover {
    border-color: var(--dashboard-border-accent);
    box-shadow: 
      0 8px 32px rgb(52 152 219 / 15%),
      inset 0 1px 0 rgb(255 255 255 / 10%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(-2px);
  }
  
  // 卡片标题
  &__title {
    display: flex;
    align-items: center;
    margin-bottom: var(--dashboard-spacing-md);
    font-size: var(--dashboard-font-lg);
    font-weight: 600;
    color: var(--dashboard-text-primary);
    
    &-icon {
      margin-right: var(--dashboard-spacing-sm);
      color: var(--dashboard-text-accent);
    }
  }
  
  // 卡片内容
  &__content {
    position: relative;
    z-index: 1;
  }
  
  // 卡片状态指示
  &--status {
    &.success {
      border-left: 4px solid var(--dashboard-success);
    }
    
    &.warning {
      border-left: 4px solid var(--dashboard-warning);
    }
    
    &.danger {
      border-left: 4px solid var(--dashboard-danger);
    }
    
    &.primary {
      border-left: 4px solid var(--dashboard-primary);
    }
  }
}

// ============ 大屏数据展示 ============
.dashboard-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  
  &__value {
    margin-bottom: var(--dashboard-spacing-xs);
    font-size: var(--dashboard-font-3xl);
    font-weight: 700;
    line-height: 1;
    color: var(--dashboard-text-primary);
    
    // 数字发光效果
    text-shadow: 0 0 20px rgb(52 152 219 / 50%);
    
    &--large {
      font-size: 4rem;
    }
    
    &--success {
      color: var(--dashboard-success);
      text-shadow: 0 0 20px rgb(82 196 26 / 50%);
    }
    
    &--warning {
      color: var(--dashboard-warning);
      text-shadow: 0 0 20px rgb(250 173 20 / 50%);
    }
    
    &--danger {
      color: var(--dashboard-danger);
      text-shadow: 0 0 20px rgb(245 34 45 / 50%);
    }
  }
  
  &__label {
    font-size: var(--dashboard-font-md);
    font-weight: 500;
    color: var(--dashboard-text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  
  &__unit {
    margin-left: var(--dashboard-spacing-xs);
    font-size: var(--dashboard-font-sm);
    color: var(--dashboard-text-tertiary);
  }
  
  &__trend {
    display: flex;
    align-items: center;
    margin-top: var(--dashboard-spacing-sm);
    font-size: var(--dashboard-font-sm);
    
    &-icon {
      margin-right: var(--dashboard-spacing-xs);
    }
    
    &--up {
      color: var(--dashboard-success);
    }
    
    &--down {
      color: var(--dashboard-danger);
    }
    
    &--stable {
      color: var(--dashboard-text-secondary);
    }
  }
}

// ============ 大屏图表容器 ============
.dashboard-chart {
  position: relative;
  width: 100%;
  height: 100%;
  
  &__container {
    width: 100%;
    height: 100%;
    padding: var(--dashboard-spacing-md);
    background: rgb(0 0 0 / 30%);
    border-radius: 8px;
  }
  
  &__title {
    position: absolute;
    top: var(--dashboard-spacing-md);
    left: var(--dashboard-spacing-md);
    z-index: 10;
    font-size: var(--dashboard-font-lg);
    font-weight: 600;
    color: var(--dashboard-text-primary);
  }
  
  &__subtitle {
    position: absolute;
    top: calc(var(--dashboard-spacing-md) + 28px);
    left: var(--dashboard-spacing-md);
    z-index: 10;
    font-size: var(--dashboard-font-sm);
    color: var(--dashboard-text-secondary);
  }
}

// ============ 大屏状态指示器 ============
.dashboard-status {
  display: flex;
  align-items: center;
  
  &__dot {
    position: relative;
    width: 12px;
    height: 12px;
    margin-right: var(--dashboard-spacing-sm);
    border-radius: 50%;
    
    &::before {
      position: absolute;
      inset: -2px;
      content: '';
      border-radius: 50%;
      animation: pulse 2s infinite;
    }
    
    &--running {
      background: var(--dashboard-success);
      
      &::before {
        background: var(--dashboard-success);
        opacity: 0.4;
      }
    }
    
    &--warning {
      background: var(--dashboard-warning);
      
      &::before {
        background: var(--dashboard-warning);
        opacity: 0.4;
      }
    }
    
    &--danger {
      background: var(--dashboard-danger);
      
      &::before {
        background: var(--dashboard-danger);
        opacity: 0.4;
      }
    }
    
    &--offline {
      background: #666;
    }
  }
  
  &__text {
    font-size: var(--dashboard-font-md);
    font-weight: 500;
    color: var(--dashboard-text-primary);
  }
}

// ============ 大屏进度条 ============
.dashboard-progress {
  position: relative;
  width: 100%;
  height: 8px;
  overflow: hidden;
  background: rgb(255 255 255 / 10%);
  border-radius: 4px;
  
  &__bar {
    position: relative;
    height: 100%;
    background: linear-gradient(90deg, 
      var(--dashboard-primary) 0%, 
      var(--dashboard-info) 100%
    );
    border-radius: 4px;
    transition: width 0.6s ease;
    
    &::after {
      position: absolute;
      inset: 0;
      content: '';
      background: linear-gradient(90deg, 
        transparent 0%, 
        rgb(255 255 255 / 30%) 50%, 
        transparent 100%
      );
      animation: shimmer 2s infinite;
    }
    
    &--success {
      background: linear-gradient(90deg, 
        var(--dashboard-success) 0%, 
        #67c23a 100%
      );
    }
    
    &--warning {
      background: linear-gradient(90deg, 
        var(--dashboard-warning) 0%, 
        #faad14 100%
      );
    }
    
    &--danger {
      background: linear-gradient(90deg, 
        var(--dashboard-danger) 0%, 
        #ff4d4f 100%
      );
    }
  }
  
  &__text {
    position: absolute;
    top: -24px;
    right: 0;
    font-size: var(--dashboard-font-sm);
    color: var(--dashboard-text-secondary);
  }
}

// ============ 大屏表格 ============
.dashboard-table {
  width: 100%;
  border-collapse: collapse;
  background: transparent;
  
  thead {
    th {
      padding: var(--dashboard-spacing-md);
      font-size: var(--dashboard-font-sm);
      font-weight: 600;
      color: var(--dashboard-text-secondary);
      text-transform: uppercase;
      letter-spacing: 1px;
      background: rgb(0 0 0 / 30%);
      border-bottom: 2px solid var(--dashboard-border-primary);
    }
  }
  
  tbody {
    tr {
      &:hover {
        background: rgb(52 152 219 / 10%);
      }
      
      &:nth-child(even) {
        background: rgb(0 0 0 / 10%);
      }
    }
    
    td {
      padding: var(--dashboard-spacing-md);
      font-size: var(--dashboard-font-md);
      color: var(--dashboard-text-primary);
      border-bottom: 1px solid var(--dashboard-border-primary);
    }
  }
}

// ============ 大屏动画效果 ============
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.3;
    transform: scale(1.2);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// ============ 大屏响应式适配 ============
// 4K显示器优化 (3840x2160)
@media (width >= 3840px) {
  :root {
    --dashboard-font-xs: 16px;
    --dashboard-font-sm: 18px;
    --dashboard-font-md: 22px;
    --dashboard-font-lg: 28px;
    --dashboard-font-xl: 36px;
    --dashboard-font-2xl: 48px;
    --dashboard-font-3xl: 64px;
    --dashboard-spacing-lg: 32px;
    --dashboard-spacing-xl: 48px;
    --dashboard-spacing-2xl: 64px;
  }
  
  .dashboard-card {
    border-radius: 16px;
  }
}

// 2K显示器优化 (2560x1440)
@media (width >= 2560px) and (width <= 3839px) {
  :root {
    --dashboard-font-xs: 14px;
    --dashboard-font-sm: 16px;
    --dashboard-font-md: 18px;
    --dashboard-font-lg: 24px;
    --dashboard-font-xl: 28px;
    --dashboard-font-2xl: 36px;
    --dashboard-font-3xl: 48px;
  }
}

// 标准1080p优化 (1920x1080)
@media (width >= 1920px) and (width <= 2559px) {
  :root {
    --dashboard-font-xs: 12px;
    --dashboard-font-sm: 14px;
    --dashboard-font-md: 16px;
    --dashboard-font-lg: 20px;
    --dashboard-font-xl: 24px;
    --dashboard-font-2xl: 32px;
    --dashboard-font-3xl: 40px;
  }
}

// 小屏幕适配
@media (width <= 1919px) {
  .dashboard-grid {
    &--fullscreen {
      grid-template-columns: repeat(8, 1fr);
    }
    
    &--standard {
      grid-template-columns: repeat(6, 1fr);
    }
  }
  
  .dashboard-metric {
    &__value {
      font-size: var(--dashboard-font-2xl);
      
      &--large {
        font-size: 3rem;
      }
    }
  }
}

// ============ 实用工具类 ============
.dashboard-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.dashboard-slide-in-left {
  animation: slideInFromLeft 0.6s ease-out;
}

.dashboard-slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.dashboard-text-glow {
  text-shadow: 0 0 10px currentcolor;
}

.dashboard-border-glow {
  box-shadow: 
    0 0 20px rgb(52 152 219 / 30%),
    inset 0 1px 0 rgb(255 255 255 / 10%);
}

.dashboard-glass {
  background: rgb(255 255 255 / 5%);
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 10%);
}

.dashboard-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgb(255 255 255 / 10%);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgb(52 152 219 / 60%);
    border-radius: 3px;
    
    &:hover {
      background: rgb(52 152 219 / 80%);
    }
  }
}