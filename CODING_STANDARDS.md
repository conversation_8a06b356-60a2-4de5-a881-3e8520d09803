# IC封测CIM系统 - 编码规范检查清单

## 🎯 开发前检查清单 (Pre-Development Checklist)

### Vue组件开发规范
- [ ] **组件命名**: 使用PascalCase，业务组件必须多词命名
- [ ] **组件结构**: `<template>` → `<script setup>` → `<style scoped>`顺序
- [ ] **Props定义**: 使用TypeScript接口定义Props类型
- [ ] **事件命名**: 使用kebab-case，如`@update:value`

### 样式开发规范
- [ ] **BEM命名**: 使用Block__Element--Modifier模式
- [ ] **CSS变量优先**: 必须使用主题变量，禁止硬编码颜色
- [ ] **响应式设计**: 移动优先设计
- [ ] **嵌套限制**: CSS选择器嵌套不超过3层
- [ ] **!important禁用**: 除非绝对必要，禁止使用!important
- [ ] **Scoped样式**: 使用:deep()处理样式穿透，不使用/deep/

### TypeScript规范
- [ ] **严格类型**: 禁止使用any类型
- [ ] **接口定义**: 所有API响应必须定义接口
- [ ] **导入导出**: 检查所有模块导入导出匹配

## 🔧 开发中检查清单 (During Development)

### 性能规范
- [ ] **组件懒加载**: 路由组件必须懒加载
- [ ] **图片优化**: 使用WebP格式，添加alt属性
- [ ] **API调用**: 避免重复调用，使用缓存机制

### 安全规范
- [ ] **XSS防护**: 用户输入必须转义
- [ ] **HTTPS**: 所有API调用使用HTTPS
- [ ] **敏感信息**: 禁止在代码中硬编码密钥

## ⚡ 开发后检查清单 (Post-Development)

### 代码质量
- [ ] **ESLint通过**: 无ESLint错误
- [ ] **类型检查通过**: npm run type-check无错误
- [ ] **样式检查通过**: npm run lint:style无错误
- [ ] **构建成功**: npm run build成功

### 功能验证
- [ ] **路由测试**: 所有路由可正常访问
- [ ] **响应式测试**: 在不同屏幕尺寸下正常显示
- [ ] **浏览器兼容**: Chrome/Firefox/Edge测试通过
- [ ] **错误处理**: 网络错误和异常情况正确处理

## 🚫 禁止的反模式 (Anti-Patterns)

### CSS反模式
```scss
// ❌ 禁止：硬编码颜色
.button {
  background-color: #ff0000 !important;
}

// ✅ 正确：使用CSS变量
.button {
  background-color: var(--color-primary);
}
```

### Vue反模式
```vue
<!-- ❌ 禁止：过度使用scoped穿透 -->
<style scoped>
.parent >>> .child {
  color: red !important;
}
</style>

<!-- ✅ 正确：合理使用深度选择器 -->
<style scoped>
.parent :deep(.child) {
  color: var(--color-text-primary);
}
</style>
```

### TypeScript反模式
```typescript
// ❌ 禁止：使用any类型
const data: any = await fetchData()

// ✅ 正确：定义具体类型
interface ApiResponse {
  code: number
  message: string
  data: unknown
}
const response: ApiResponse = await fetchData()
```

## 🔄 持续改进机制

### 代码审查要求
1. **每次提交前**: 运行完整的检查清单
2. **Pull Request**: 必须包含检查清单确认
3. **代码审查**: 重点检查规范遵循情况
4. **定期重构**: 每月清理技术债务

### 自动化检查
- **Pre-commit hooks**: 自动运行lint和type-check
- **CI/CD流水线**: 构建时强制检查
- **定期报告**: 每周生成代码质量报告