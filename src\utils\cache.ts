/**
 * IC封测CIM系统 - 数据缓存管理系统
 * Data Cache Management System for IC Packaging & Testing CIM System
 */

// Element Plus组件通过unplugin-auto-import自动导入

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  // 先进先出
  FIFO = 'fifo',
  // 最近最少使用
  LRU = 'lru',
  // 最不常用
  LFU = 'lfu',
  // 定时过期
  TTL = 'ttl',
  // 手动管理
  MANUAL = 'manual'
}

/**
 * 存储类型枚举
 */
export enum StorageType {
  MEMORY = 'memory',
  LOCAL_STORAGE = 'localStorage',
  SESSION_STORAGE = 'sessionStorage',
  INDEXED_DB = 'indexedDB'
}

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  key: string
  value: T
  createTime: number
  lastAccessTime: number
  accessCount: number
  expireTime?: number
  size: number
  tags?: string[]
  priority?: number
  compressed?: boolean
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  maxSize: number // 最大缓存大小（字节）
  maxItems: number // 最大缓存项数量
  defaultTTL: number // 默认过期时间（毫秒）
  strategy: CacheStrategy // 缓存策略
  storageType: StorageType // 存储类型
  enableCompression: boolean // 是否启用压缩
  enableEncryption: boolean // 是否启用加密
  keyPrefix: string // key前缀
  enableLogging: boolean // 是否启用日志
  autoCleanup: boolean // 是否自动清理
  cleanupInterval: number // 清理间隔（毫秒）
  compressionThreshold: number // 压缩阈值（字节）
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  totalSize: number
  totalItems: number
  hitCount: number
  missCount: number
  hitRate: number
  evictionCount: number
  lastCleanupTime?: number
  memoryUsage?: number
}

/**
 * 缓存查询选项接口
 */
export interface CacheQueryOptions {
  tags?: string[]
  keyPattern?: string | RegExp
  minPriority?: number
  maxAge?: number
  includeExpired?: boolean
}

/**
 * 数据压缩工具类
 */
class CompressionUtils {
  /**
   * 压缩数据
   */
  static compress(data: string): string {
    // 简单的LZ77算法实现
    const dict: Record<string, number> = {}
    const result: string[] = []
    let dictSize = 256
    let w = ''

    for (let i = 0; i < data.length; i++) {
      const c = data[i]
      const wc = w + c

      if (dict[wc] !== undefined) {
        w = wc
      } else {
        result.push(w.length === 1 ? w : String(dict[w]))
        dict[wc] = dictSize++
        w = c
      }
    }

    if (w) {
      result.push(w.length === 1 ? w : String(dict[w]))
    }

    return result.join('')
  }

  /**
   * 解压缩数据
   */
  static decompress(compressed: string): string {
    // 对应的解压缩实现
    return compressed // 简化实现
  }

  /**
   * 计算压缩率
   */
  static getCompressionRatio(original: string, compressed: string): number {
    return compressed.length / original.length
  }
}

/**
 * 数据加密工具类
 */
class EncryptionUtils {
  private static key = 'cim-cache-key-2024'

  /**
   * 简单加密
   */
  static encrypt(data: string): string {
    let encrypted = ''
    for (let i = 0; i < data.length; i++) {
      const keyChar = this.key.charCodeAt(i % this.key.length)
      const dataChar = data.charCodeAt(i)
      encrypted += String.fromCharCode((dataChar + keyChar) % 256)
    }
    return btoa(encrypted)
  }

  /**
   * 简单解密
   */
  static decrypt(encrypted: string): string {
    try {
      const data = atob(encrypted)
      let decrypted = ''
      for (let i = 0; i < data.length; i++) {
        const keyChar = this.key.charCodeAt(i % this.key.length)
        const dataChar = data.charCodeAt(i)
        decrypted += String.fromCharCode((dataChar - keyChar + 256) % 256)
      }
      return decrypted
    } catch {
      return encrypted
    }
  }
}

/**
 * 存储适配器接口
 */
interface StorageAdapter {
  get(key: string): string | null
  set(key: string, value: string): void
  remove(key: string): void
  clear(): void
  keys(): string[]
  size(): number
}

/**
 * 内存存储适配器
 */
class MemoryStorageAdapter implements StorageAdapter {
  private storage = new Map<string, string>()

  get(key: string): string | null {
    return this.storage.get(key) || null
  }

  set(key: string, value: string): void {
    this.storage.set(key, value)
  }

  remove(key: string): void {
    this.storage.delete(key)
  }

  clear(): void {
    this.storage.clear()
  }

  keys(): string[] {
    return Array.from(this.storage.keys())
  }

  size(): number {
    let size = 0
    this.storage.forEach(value => {
      size += value.length * 2 // 估算字符串字节大小
    })
    return size
  }
}

/**
 * LocalStorage适配器
 */
class LocalStorageAdapter implements StorageAdapter {
  get(key: string): string | null {
    try {
      return localStorage.getItem(key)
    } catch {
      return null
    }
  }

  set(key: string, value: string): void {
    try {
      localStorage.setItem(key, value)
    } catch (error) {
      console.error('LocalStorage set error:', error)
    }
  }

  remove(key: string): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('LocalStorage remove error:', error)
    }
  }

  clear(): void {
    try {
      // 只清理带有特定前缀的项
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('cim_cache_')) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.error('LocalStorage clear error:', error)
    }
  }

  keys(): string[] {
    try {
      return Object.keys(localStorage).filter(key => key.startsWith('cim_cache_'))
    } catch {
      return []
    }
  }

  size(): number {
    try {
      let size = 0
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('cim_cache_')) {
          const value = localStorage.getItem(key)
          size += (key.length + (value?.length || 0)) * 2
        }
      })
      return size
    } catch {
      return 0
    }
  }
}

/**
 * SessionStorage适配器
 */
class SessionStorageAdapter implements StorageAdapter {
  get(key: string): string | null {
    try {
      return sessionStorage.getItem(key)
    } catch {
      return null
    }
  }

  set(key: string, value: string): void {
    try {
      sessionStorage.setItem(key, value)
    } catch (error) {
      console.error('SessionStorage set error:', error)
    }
  }

  remove(key: string): void {
    try {
      sessionStorage.removeItem(key)
    } catch (error) {
      console.error('SessionStorage remove error:', error)
    }
  }

  clear(): void {
    try {
      const keys = Object.keys(sessionStorage)
      keys.forEach(key => {
        if (key.startsWith('cim_cache_')) {
          sessionStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.error('SessionStorage clear error:', error)
    }
  }

  keys(): string[] {
    try {
      return Object.keys(sessionStorage).filter(key => key.startsWith('cim_cache_'))
    } catch {
      return []
    }
  }

  size(): number {
    try {
      let size = 0
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('cim_cache_')) {
          const value = sessionStorage.getItem(key)
          size += (key.length + (value?.length || 0)) * 2
        }
      })
      return size
    } catch {
      return 0
    }
  }
}

/**
 * 缓存管理器类
 */
export class CacheManager {
  private config: Required<CacheConfig>
  private cache = new Map<string, CacheItem>()
  private storage: StorageAdapter
  private stats: CacheStats
  private cleanupTimer: number | null = null

  constructor(config?: Partial<CacheConfig>) {
    this.config = {
      maxSize: 50 * 1024 * 1024, // 50MB
      maxItems: 10000,
      defaultTTL: 30 * 60 * 1000, // 30分钟
      strategy: CacheStrategy.LRU,
      storageType: StorageType.MEMORY,
      enableCompression: false,
      enableEncryption: false,
      keyPrefix: 'cim_cache_',
      enableLogging: true,
      autoCleanup: true,
      cleanupInterval: 5 * 60 * 1000, // 5分钟
      compressionThreshold: 1024, // 1KB
      ...config
    }

    this.storage = this.createStorageAdapter()
    this.stats = {
      totalSize: 0,
      totalItems: 0,
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      evictionCount: 0
    }

    this.initializeFromStorage()

    if (this.config.autoCleanup) {
      this.startAutoCleanup()
    }

    this.log('Cache manager initialized', this.config)
  }

  /**
   * 设置缓存项
   */
  set<T>(
    key: string,
    value: T,
    options?: {
      ttl?: number
      tags?: string[]
      priority?: number
      skipPersist?: boolean
    }
  ): boolean {
    try {
      const now = Date.now()
      const fullKey = this.getFullKey(key)
      const expireTime = options?.ttl ? now + options.ttl : now + this.config.defaultTTL

      // 序列化值
      let serializedValue = JSON.stringify(value)
      let compressed = false

      // 压缩处理
      if (
        this.config.enableCompression &&
        serializedValue.length > this.config.compressionThreshold
      ) {
        serializedValue = CompressionUtils.compress(serializedValue)
        compressed = true
      }

      // 加密处理
      if (this.config.enableEncryption) {
        serializedValue = EncryptionUtils.encrypt(serializedValue)
      }

      const size = this.calculateSize(serializedValue)

      // 检查是否需要清理空间
      this.ensureSpace(size)

      const cacheItem: CacheItem<T> = {
        key: fullKey,
        value,
        createTime: now,
        lastAccessTime: now,
        accessCount: 0,
        expireTime,
        size,
        tags: options?.tags,
        priority: options?.priority,
        compressed
      }

      // 移除旧项
      if (this.cache.has(fullKey)) {
        this.removeFromStorage(fullKey)
      }

      // 添加到缓存
      this.cache.set(fullKey, cacheItem)

      // 持久化到存储
      if (!options?.skipPersist) {
        this.saveToStorage(fullKey, cacheItem, serializedValue)
      }

      this.updateStats()
      this.log('Cache item set', { key, size, compressed })

      return true
    } catch (error) {
      this.log('Failed to set cache item', error)
      return false
    }
  }

  /**
   * 获取缓存项
   */
  get<T>(key: string): T | null {
    const fullKey = this.getFullKey(key)
    let cacheItem = this.cache.get(fullKey)

    // 如果内存中没有，尝试从存储中加载
    if (!cacheItem) {
      cacheItem = this.loadFromStorage(fullKey)
      if (cacheItem) {
        this.cache.set(fullKey, cacheItem)
      }
    }

    if (!cacheItem) {
      this.stats.missCount++
      this.log('Cache miss', { key })
      return null
    }

    // 检查是否过期
    const now = Date.now()
    if (cacheItem.expireTime && now > cacheItem.expireTime) {
      this.remove(key)
      this.stats.missCount++
      this.log('Cache expired', { key })
      return null
    }

    // 更新访问信息
    cacheItem.lastAccessTime = now
    cacheItem.accessCount++

    this.stats.hitCount++
    this.log('Cache hit', { key })

    return cacheItem.value as T
  }

  /**
   * 移除缓存项
   */
  remove(key: string): boolean {
    const fullKey = this.getFullKey(key)
    const cacheItem = this.cache.get(fullKey)

    if (cacheItem) {
      this.cache.delete(fullKey)
      this.removeFromStorage(fullKey)
      this.updateStats()
      this.log('Cache item removed', { key })
      return true
    }

    return false
  }

  /**
   * 检查缓存项是否存在
   */
  has(key: string): boolean {
    const fullKey = this.getFullKey(key)
    const cacheItem = this.cache.get(fullKey)

    if (!cacheItem) {
      return false
    }

    // 检查是否过期
    const now = Date.now()
    if (cacheItem.expireTime && now > cacheItem.expireTime) {
      this.remove(key)
      return false
    }

    return true
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.storage.clear()
    this.stats = {
      totalSize: 0,
      totalItems: 0,
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      evictionCount: 0
    }
    this.log('Cache cleared')
  }

  /**
   * 查询缓存项
   */
  query(options: CacheQueryOptions): CacheItem[] {
    const results: CacheItem[] = []
    const now = Date.now()

    this.cache.forEach(item => {
      // 检查过期
      if (!options.includeExpired && item.expireTime && now > item.expireTime) {
        return
      }

      // 检查标签
      if (options.tags && options.tags.length > 0) {
        if (!item.tags || !options.tags.some(tag => item.tags!.includes(tag))) {
          return
        }
      }

      // 检查key模式
      if (options.keyPattern) {
        const pattern =
          typeof options.keyPattern === 'string'
            ? new RegExp(options.keyPattern)
            : options.keyPattern
        if (!pattern.test(item.key)) {
          return
        }
      }

      // 检查优先级
      if (options.minPriority && (!item.priority || item.priority < options.minPriority)) {
        return
      }

      // 检查年龄
      if (options.maxAge && now - item.createTime > options.maxAge) {
        return
      }

      results.push(item)
    })

    return results
  }

  /**
   * 批量设置
   */
  setMultiple<T>(items: Array<{ key: string; value: T; options?: any }>): number {
    let successCount = 0
    items.forEach(item => {
      if (this.set(item.key, item.value, item.options)) {
        successCount++
      }
    })
    return successCount
  }

  /**
   * 批量获取
   */
  getMultiple<T>(keys: string[]): Array<{ key: string; value: T | null }> {
    return keys.map(key => ({
      key,
      value: this.get<T>(key)
    }))
  }

  /**
   * 批量移除
   */
  removeMultiple(keys: string[]): number {
    let successCount = 0
    keys.forEach(key => {
      if (this.remove(key)) {
        successCount++
      }
    })
    return successCount
  }

  /**
   * 获取缓存统计
   */
  getStats(): CacheStats {
    this.updateStats()
    return { ...this.stats }
  }

  /**
   * 获取所有缓存键
   */
  keys(pattern?: string | RegExp): string[] {
    const keys: string[] = []
    const regex = pattern ? (typeof pattern === 'string' ? new RegExp(pattern) : pattern) : null

    this.cache.forEach((_, key) => {
      const shortKey = this.getShortKey(key)
      if (!regex || regex.test(shortKey)) {
        keys.push(shortKey)
      }
    })

    return keys
  }

  /**
   * 导出缓存数据
   */
  export(): string {
    const exportData = {
      config: this.config,
      items: Array.from(this.cache.entries()),
      stats: this.stats,
      exportTime: new Date().toISOString()
    }
    return JSON.stringify(exportData, null, 2)
  }

  /**
   * 导入缓存数据
   */
  import(data: string): boolean {
    try {
      const importData = JSON.parse(data)

      // 验证数据格式
      if (!importData.items || !Array.isArray(importData.items)) {
        throw new Error('Invalid import data format')
      }

      // 清空现有缓存
      this.clear()

      // 导入数据
      let importCount = 0
      importData.items.forEach(([key, item]: [string, CacheItem]) => {
        // 检查是否过期
        const now = Date.now()
        if (item.expireTime && now > item.expireTime) {
          return
        }

        this.cache.set(key, item)
        importCount++
      })

      this.updateStats()
      this.log('Cache data imported', { importCount })
      ElMessage.success(`导入缓存数据成功，共 ${importCount} 项`)

      return true
    } catch (error) {
      this.log('Failed to import cache data', error)
      ElMessage.error('导入缓存数据失败')
      return false
    }
  }

  /**
   * 手动清理过期项
   */
  cleanup(): number {
    const now = Date.now()
    let cleanupCount = 0

    this.cache.forEach((item, key) => {
      if (item.expireTime && now > item.expireTime) {
        this.cache.delete(key)
        this.removeFromStorage(key)
        cleanupCount++
      }
    })

    if (cleanupCount > 0) {
      this.updateStats()
      this.stats.lastCleanupTime = now
      this.log('Cache cleanup completed', { cleanupCount })
    }

    return cleanupCount
  }

  /**
   * 优化缓存
   */
  optimize(): void {
    // 清理过期项
    const cleanupCount = this.cleanup()

    // 根据策略清理项目
    this.evictItems()

    // 压缩存储
    this.compressStorage()

    this.log('Cache optimized', { cleanupCount })
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }

    this.clear()
    this.log('Cache manager destroyed')
  }

  // 私有方法

  private createStorageAdapter(): StorageAdapter {
    switch (this.config.storageType) {
      case StorageType.LOCAL_STORAGE:
        return new LocalStorageAdapter()
      case StorageType.SESSION_STORAGE:
        return new SessionStorageAdapter()
      case StorageType.MEMORY:
      default:
        return new MemoryStorageAdapter()
    }
  }

  private initializeFromStorage(): void {
    try {
      const keys = this.storage.keys()
      let loadCount = 0

      keys.forEach(key => {
        if (key.startsWith(this.config.keyPrefix)) {
          const cacheItem = this.loadFromStorage(key)
          if (cacheItem) {
            this.cache.set(key, cacheItem)
            loadCount++
          }
        }
      })

      this.updateStats()
      this.log('Cache initialized from storage', { loadCount })
    } catch (error) {
      this.log('Failed to initialize from storage', error)
    }
  }

  private saveToStorage(key: string, item: CacheItem, serializedValue: string): void {
    try {
      const storageItem = {
        ...item,
        serializedValue
      }
      this.storage.set(key, JSON.stringify(storageItem))
    } catch (error) {
      this.log('Failed to save to storage', error)
    }
  }

  private loadFromStorage(key: string): CacheItem | null {
    try {
      const data = this.storage.get(key)
      if (!data) return null

      const storageItem = JSON.parse(data)

      // 检查是否过期
      const now = Date.now()
      if (storageItem.expireTime && now > storageItem.expireTime) {
        this.storage.remove(key)
        return null
      }

      // 反序列化值
      let serializedValue = storageItem.serializedValue

      // 解密
      if (this.config.enableEncryption) {
        serializedValue = EncryptionUtils.decrypt(serializedValue)
      }

      // 解压缩
      if (storageItem.compressed) {
        serializedValue = CompressionUtils.decompress(serializedValue)
      }

      const value = JSON.parse(serializedValue)

      return {
        ...storageItem,
        value
      }
    } catch (error) {
      this.log('Failed to load from storage', error)
      return null
    }
  }

  private removeFromStorage(key: string): void {
    this.storage.remove(key)
  }

  private getFullKey(key: string): string {
    return `${this.config.keyPrefix}${key}`
  }

  private getShortKey(fullKey: string): string {
    return fullKey.replace(this.config.keyPrefix, '')
  }

  private calculateSize(data: string): number {
    return data.length * 2 // 估算字符串字节大小
  }

  private ensureSpace(newItemSize: number): void {
    const currentSize = this.stats.totalSize
    const currentItems = this.stats.totalItems

    // 检查大小限制
    if (currentSize + newItemSize > this.config.maxSize) {
      this.evictItems()
    }

    // 检查数量限制
    if (currentItems >= this.config.maxItems) {
      this.evictItems()
    }
  }

  private evictItems(): void {
    if (this.cache.size === 0) return

    const itemsToEvict: string[] = []
    const now = Date.now()

    switch (this.config.strategy) {
      case CacheStrategy.LRU:
        // 最近最少使用
        const lruItems = Array.from(this.cache.entries()).sort(
          ([, a], [, b]) => a.lastAccessTime - b.lastAccessTime
        )

        let evictSize = 0
        const targetSize = this.config.maxSize * 0.8 // 清理到80%

        for (const [key, item] of lruItems) {
          if (this.stats.totalSize - evictSize <= targetSize) break
          itemsToEvict.push(key)
          evictSize += item.size
        }
        break

      case CacheStrategy.LFU:
        // 最不常用
        const lfuItems = Array.from(this.cache.entries()).sort(
          ([, a], [, b]) => a.accessCount - b.accessCount
        )

        itemsToEvict.push(
          ...lfuItems.slice(0, Math.ceil(this.cache.size * 0.2)).map(([key]) => key)
        )
        break

      case CacheStrategy.FIFO:
        // 先进先出
        const fifoItems = Array.from(this.cache.entries()).sort(
          ([, a], [, b]) => a.createTime - b.createTime
        )

        itemsToEvict.push(
          ...fifoItems.slice(0, Math.ceil(this.cache.size * 0.2)).map(([key]) => key)
        )
        break

      case CacheStrategy.TTL:
        // 基于TTL清理即将过期的项
        this.cache.forEach((item, key) => {
          if (item.expireTime && item.expireTime - now < 60000) {
            // 1分钟内过期
            itemsToEvict.push(key)
          }
        })
        break
    }

    // 执行清理
    itemsToEvict.forEach(key => {
      this.cache.delete(key)
      this.removeFromStorage(key)
    })

    this.stats.evictionCount += itemsToEvict.length

    if (itemsToEvict.length > 0) {
      this.log('Items evicted', { count: itemsToEvict.length, strategy: this.config.strategy })
    }
  }

  private compressStorage(): void {
    // 实现存储压缩逻辑
    this.log('Storage compressed')
  }

  private updateStats(): void {
    let totalSize = 0
    let totalItems = 0

    this.cache.forEach(item => {
      totalSize += item.size
      totalItems++
    })

    this.stats.totalSize = totalSize
    this.stats.totalItems = totalItems
    this.stats.hitRate =
      this.stats.hitCount + this.stats.missCount > 0
        ? this.stats.hitCount / (this.stats.hitCount + this.stats.missCount)
        : 0

    if (performance && performance.memory) {
      this.stats.memoryUsage = performance.memory.usedJSHeapSize
    }
  }

  private startAutoCleanup(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  private log(message: string, data?: any): void {
    if (this.config.enableLogging) {
      const timestamp = new Date().toISOString()
      console.log(`[Cache ${timestamp}] ${message}`, data || '')
    }
  }
}

/**
 * 缓存工厂类
 */
export class CacheFactory {
  private static instances = new Map<string, CacheManager>()

  /**
   * 创建或获取缓存管理器实例
   */
  static getInstance(name: string, config?: Partial<CacheConfig>): CacheManager {
    if (!this.instances.has(name)) {
      this.instances.set(name, new CacheManager(config))
    }
    return this.instances.get(name)!
  }

  /**
   * 销毁缓存管理器实例
   */
  static destroyInstance(name: string): void {
    const instance = this.instances.get(name)
    if (instance) {
      instance.destroy()
      this.instances.delete(name)
    }
  }

  /**
   * 销毁所有实例
   */
  static destroyAll(): void {
    this.instances.forEach(instance => instance.destroy())
    this.instances.clear()
  }

  /**
   * 获取所有实例状态
   */
  static getAllStats(): Record<string, CacheStats> {
    const stats: Record<string, CacheStats> = {}
    this.instances.forEach((instance, name) => {
      stats[name] = instance.getStats()
    })
    return stats
  }
}

// 预定义的缓存管理器配置
export const CacheConfigs = {
  // API响应缓存
  API_CACHE: {
    maxSize: 20 * 1024 * 1024, // 20MB
    maxItems: 5000,
    defaultTTL: 10 * 60 * 1000, // 10分钟
    strategy: CacheStrategy.LRU,
    storageType: StorageType.MEMORY,
    enableCompression: true,
    keyPrefix: 'cim_api_cache_'
  },

  // 用户数据缓存
  USER_CACHE: {
    maxSize: 5 * 1024 * 1024, // 5MB
    maxItems: 1000,
    defaultTTL: 60 * 60 * 1000, // 1小时
    strategy: CacheStrategy.LRU,
    storageType: StorageType.LOCAL_STORAGE,
    enableEncryption: true,
    keyPrefix: 'cim_user_cache_'
  },

  // 设备状态缓存
  EQUIPMENT_CACHE: {
    maxSize: 10 * 1024 * 1024, // 10MB
    maxItems: 2000,
    defaultTTL: 30 * 1000, // 30秒
    strategy: CacheStrategy.TTL,
    storageType: StorageType.MEMORY,
    keyPrefix: 'cim_equipment_cache_'
  },

  // 报表缓存
  REPORT_CACHE: {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxItems: 500,
    defaultTTL: 4 * 60 * 60 * 1000, // 4小时
    strategy: CacheStrategy.LFU,
    storageType: StorageType.LOCAL_STORAGE,
    enableCompression: true,
    keyPrefix: 'cim_report_cache_'
  },

  // 图片缓存
  IMAGE_CACHE: {
    maxSize: 100 * 1024 * 1024, // 100MB
    maxItems: 1000,
    defaultTTL: 24 * 60 * 60 * 1000, // 24小时
    strategy: CacheStrategy.LRU,
    storageType: StorageType.LOCAL_STORAGE,
    keyPrefix: 'cim_image_cache_'
  }
} as const

// 导出预配置的缓存管理器实例
export const apiCache = CacheFactory.getInstance('api', CacheConfigs.API_CACHE)
export const userCache = CacheFactory.getInstance('user', CacheConfigs.USER_CACHE)
export const equipmentCache = CacheFactory.getInstance('equipment', CacheConfigs.EQUIPMENT_CACHE)
export const reportCache = CacheFactory.getInstance('report', CacheConfigs.REPORT_CACHE)
export const imageCache = CacheFactory.getInstance('image', CacheConfigs.IMAGE_CACHE)

// 默认导出
export default {
  CacheManager,
  CacheFactory,
  CacheStrategy,
  StorageType,
  CacheConfigs,
  apiCache,
  userCache,
  equipmentCache,
  reportCache,
  imageCache
}
