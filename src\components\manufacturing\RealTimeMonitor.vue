<template>
  <div class="real-time-monitor">
    <div class="monitor-header">
      <h3>实时生产监控</h3>
      <div class="monitor-controls">
        <el-select v-model="selectedLine" placeholder="选择产线" size="small" style="width: 150px">
          <el-option label="CP测试线" value="cp" />
          <el-option label="封装线" value="assembly" />
          <el-option label="FT测试线" value="ft" />
        </el-select>
        <el-button size="small" :class="{ 'is-active': autoRefresh }" @click="toggleAutoRefresh">
          <el-icon><Timer /></el-icon>
          {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
        </el-button>
        <el-button size="small" @click="refreshData">
          <el-icon><RefreshRight /></el-icon>
          手动刷新
        </el-button>
      </div>
    </div>

    <!-- 实时KPI指标 -->
    <div class="kpi-dashboard">
      <div class="kpi-row">
        <div class="kpi-card production">
          <div class="kpi-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="kpi-content">
            <div class="kpi-value">{{ formatNumber(realTimeData.hourlyOutput) }}</div>
            <div class="kpi-label">小时产出</div>
            <div class="kpi-trend" :class="realTimeData.outputTrend > 0 ? 'positive' : 'negative'">
              {{ realTimeData.outputTrend > 0 ? '+' : '' }}{{ realTimeData.outputTrend.toFixed(1) }}%
            </div>
          </div>
        </div>

        <div class="kpi-card yield">
          <div class="kpi-icon">
            <el-icon><PieChart /></el-icon>
          </div>
          <div class="kpi-content">
            <div class="kpi-value">{{ realTimeData.currentYield.toFixed(2) }}%</div>
            <div class="kpi-label">实时良率</div>
            <div class="kpi-target">目标: {{ realTimeData.targetYield.toFixed(1) }}%</div>
          </div>
        </div>

        <div class="kpi-card efficiency">
          <div class="kpi-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="kpi-content">
            <div class="kpi-value">{{ realTimeData.oeeValue.toFixed(1) }}%</div>
            <div class="kpi-label">OEE效率</div>
            <div class="kpi-breakdown">
              <span>A: {{ realTimeData.availability.toFixed(1) }}%</span>
              <span>P: {{ realTimeData.performance.toFixed(1) }}%</span>
              <span>Q: {{ realTimeData.quality.toFixed(1) }}%</span>
            </div>
          </div>
        </div>

        <div class="kpi-card alerts">
          <div class="kpi-icon">
            <el-icon :class="{ 'alert-active': activeAlerts > 0 }"><Bell /></el-icon>
          </div>
          <div class="kpi-content">
            <div class="kpi-value" :class="{ 'alert-count': activeAlerts > 0 }">{{ activeAlerts }}</div>
            <div class="kpi-label">活跃报警</div>
            <div class="kpi-details">{{ criticalAlerts }}条严重</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备状态概览 -->
    <div class="equipment-overview">
      <h4>设备状态概览</h4>
      <div class="equipment-grid">
        <div 
          v-for="equipment in filteredEquipment" 
          :key="equipment.id"
          class="equipment-item"
          :class="`status-${equipment.status}`"
          @click="viewEquipmentDetails(equipment)"
        >
          <div class="equipment-header">
            <span class="equipment-name">{{ equipment.name }}</span>
            <el-tag :type="getStatusType(equipment.status)" size="small">
              {{ getStatusText(equipment.status) }}
            </el-tag>
          </div>
          
          <div class="equipment-metrics">
            <div class="metric">
              <span class="metric-label">稼动率:</span>
              <span class="metric-value">{{ equipment.utilization?.toFixed(1) || 0 }}%</span>
            </div>
            <div class="metric">
              <span class="metric-label">良率:</span>
              <span class="metric-value">{{ equipment.yield?.toFixed(1) || 0 }}%</span>
            </div>
            <div class="metric">
              <span class="metric-label">产能:</span>
              <span class="metric-value">{{ equipment.throughput || 0 }} UPH</span>
            </div>
          </div>
          
          <div class="equipment-progress">
            <el-progress 
              :percentage="equipment.utilization || 0" 
              :stroke-width="4"
              :show-text="false"
              :color="getProgressColor(equipment.utilization || 0)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 实时趋势图 -->
    <div class="trend-charts">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-container">
            <h4>产量趋势</h4>
            <div id="output-trend-chart" style="width: 100%; height: 200px"></div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container">
            <h4>良率趋势</h4>
            <div id="yield-trend-chart" style="width: 100%; height: 200px"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 报警列表 -->
    <div v-if="recentAlarms.length > 0" class="alarms-section">
      <h4>最新报警</h4>
      <div class="alarms-list">
        <div 
          v-for="alarm in recentAlarms" 
          :key="alarm.id"
          class="alarm-item"
          :class="`severity-${alarm.severity}`"
        >
          <div class="alarm-icon">
            <el-icon><WarningFilled /></el-icon>
          </div>
          <div class="alarm-content">
            <div class="alarm-message">{{ alarm.message }}</div>
            <div class="alarm-details">
              <span class="alarm-equipment">{{ alarm.equipmentName }}</span>
              <span class="alarm-time">{{ formatAlarmTime(alarm.timestamp) }}</span>
            </div>
          </div>
          <div class="alarm-actions">
            <el-button size="small" link @click="acknowledgeAlarm(alarm)">
              确认
            </el-button>
            <el-button size="small" link type="primary" @click="viewAlarmDetails(alarm)">
              详情
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备详情弹窗 -->
    <el-dialog 
      v-model="equipmentDialogVisible" 
      :title="`设备详情 - ${selectedEquipment?.name}`"
      width="60%"
    >
      <div v-if="selectedEquipment" class="equipment-details">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="设备型号">{{ selectedEquipment.model }}</el-descriptions-item>
          <el-descriptions-item label="工作站">{{ selectedEquipment.station }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedEquipment.status)">
              {{ getStatusText(selectedEquipment.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="稼动率">{{ selectedEquipment.utilization?.toFixed(1) }}%</el-descriptions-item>
          <el-descriptions-item label="OEE">{{ selectedEquipment.oee?.toFixed(1) }}%</el-descriptions-item>
          <el-descriptions-item label="良率">{{ selectedEquipment.yield?.toFixed(1) }}%</el-descriptions-item>
          <el-descriptions-item label="产能">{{ selectedEquipment.throughput }} UPH</el-descriptions-item>
          <el-descriptions-item label="最后更新">{{ formatTime(selectedEquipment.lastUpdated) }}</el-descriptions-item>
          <el-descriptions-item label="累计产量">{{ formatNumber(selectedEquipment.totalOutput || 0) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="equipment-chart">
          <h4>设备运行趋势</h4>
          <div id="equipment-detail-chart" style="width: 100%; height: 300px"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import type { Equipment } from '@/types/manufacturing'
  import { 
    Timer, 
    RefreshRight, 
    TrendCharts, 
    PieChart, 
    Bell, 
    WarningFilled 
  } from '@element-plus/icons-vue'

  interface RealTimeData {
    hourlyOutput: number
    outputTrend: number
    currentYield: number
    targetYield: number
    oeeValue: number
    availability: number
    performance: number
    quality: number
  }

  interface Alarm {
    id: string
    equipmentId: string
    equipmentName: string
    severity: 'critical' | 'warning' | 'info'
    message: string
    timestamp: string
    acknowledged: boolean
  }

  const props = defineProps<{
    productionLine: string
  }>()

  // 响应式数据
  const selectedLine = ref('cp')
  const autoRefresh = ref(true)
  const equipmentDialogVisible = ref(false)
  const selectedEquipment = ref<Equipment | null>(null)
  
  const realTimeData = ref<RealTimeData>({
    hourlyOutput: 12450,
    outputTrend: 2.3,
    currentYield: 94.8,
    targetYield: 95.0,
    oeeValue: 87.5,
    availability: 92.1,
    performance: 95.8,
    quality: 99.2
  })

  const equipmentList = ref<Equipment[]>([
    {
      id: 'eq-001',
      name: 'ATE-001',
      model: 'Advantest T5581',
      station: 'CP-ST01',
      status: 'running',
      utilization: 95.2,
      oee: 92.1,
      yield: 94.8,
      throughput: 1250,
      totalOutput: 125600,
      lastUpdated: new Date().toISOString()
    },
    {
      id: 'eq-002', 
      name: 'ATE-002',
      model: 'Advantest T5581',
      station: 'CP-ST02',
      status: 'idle',
      utilization: 78.3,
      oee: 85.4,
      yield: 96.2,
      throughput: 980,
      totalOutput: 98400,
      lastUpdated: new Date().toISOString()
    },
    {
      id: 'eq-003',
      name: 'Handler-001', 
      model: 'Multitest MT8580',
      station: 'FT-ST01',
      status: 'alarm',
      utilization: 45.1,
      oee: 62.8,
      yield: 92.1,
      throughput: 560,
      totalOutput: 56200,
      lastUpdated: new Date().toISOString()
    }
  ])

  const recentAlarms = ref<Alarm[]>([
    {
      id: 'alarm-001',
      equipmentId: 'eq-003',
      equipmentName: 'Handler-001',
      severity: 'critical',
      message: '分选机卡料，需要立即处理',
      timestamp: new Date().toISOString(),
      acknowledged: false
    },
    {
      id: 'alarm-002', 
      equipmentId: 'eq-001',
      equipmentName: 'ATE-001',
      severity: 'warning',
      message: '探针卡使用次数接近上限',
      timestamp: new Date(Date.now() - 300000).toISOString(),
      acknowledged: false
    }
  ])

  // 计算属性
  const filteredEquipment = computed(() => {
    // 根据选中的产线过滤设备
    return equipmentList.value.filter(eq => {
      if (selectedLine.value === 'cp') return eq.name.includes('ATE')
      if (selectedLine.value === 'ft') return eq.name.includes('Handler')
      return true
    })
  })

  const activeAlerts = computed(() => 
    recentAlarms.value.filter(alarm => !alarm.acknowledged).length
  )

  const criticalAlerts = computed(() => 
    recentAlarms.value.filter(alarm => 
      !alarm.acknowledged && alarm.severity === 'critical'
    ).length
  )

  // 方法
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('zh-CN').format(num)
  }

  const formatTime = (timeStr: string): string => {
    return new Date(timeStr).toLocaleString('zh-CN')
  }

  const formatAlarmTime = (timeStr: string): string => {
    const now = new Date()
    const alarmTime = new Date(timeStr)
    const diff = now.getTime() - alarmTime.getTime()
    
    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
    return alarmTime.toLocaleTimeString('zh-CN')
  }

  const getStatusType = (status: string) => {
    const typeMap = {
      running: 'success',
      idle: 'warning', 
      alarm: 'danger',
      offline: 'info'
    }
    return typeMap[status as keyof typeof typeMap] || 'info'
  }

  const getStatusText = (status: string): string => {
    const textMap = {
      running: '运行中',
      idle: '空闲',
      alarm: '报警',
      offline: '离线'
    }
    return textMap[status as keyof typeof textMap] || '未知'
  }

  const getProgressColor = (value: number): string => {
    if (value >= 90) return '#67c23a'
    if (value >= 70) return '#e6a23c'
    return '#f56c6c'
  }

  const toggleAutoRefresh = () => {
    autoRefresh.value = !autoRefresh.value
    if (autoRefresh.value) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }

  const refreshData = () => {
    // 模拟数据更新
    realTimeData.value.hourlyOutput += Math.floor((Math.random() - 0.5) * 200)
    realTimeData.value.currentYield += (Math.random() - 0.5) * 0.5
    realTimeData.value.oeeValue += (Math.random() - 0.5) * 1.0
    
    // 更新设备数据
    equipmentList.value.forEach(eq => {
      eq.utilization = Math.max(0, Math.min(100, (eq.utilization || 0) + (Math.random() - 0.5) * 5))
      eq.yield = Math.max(0, Math.min(100, (eq.yield || 0) + (Math.random() - 0.5) * 2))
      eq.lastUpdated = new Date().toISOString()
    })
    
    ElMessage.success('数据已更新')
  }

  const viewEquipmentDetails = (equipment: Equipment) => {
    selectedEquipment.value = equipment
    equipmentDialogVisible.value = true
  }

  const acknowledgeAlarm = (alarm: Alarm) => {
    alarm.acknowledged = true
    ElMessage.success(`报警 ${alarm.id} 已确认`)
  }

  const viewAlarmDetails = (alarm: Alarm) => {
    ElMessage.info('查看报警详情功能开发中')
  }

  // 自动刷新逻辑
  let refreshTimer: NodeJS.Timeout | null = null

  const startAutoRefresh = () => {
    if (refreshTimer) return
    
    refreshTimer = setInterval(() => {
      refreshData()
    }, 5000) // 5秒刷新一次
  }

  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  // 生命周期
  onMounted(() => {
    if (autoRefresh.value) {
      startAutoRefresh()
    }
  })

  onUnmounted(() => {
    stopAutoRefresh()
  })

  // 监听产线切换
  watch(selectedLine, () => {
    refreshData()
  })
</script>

<style lang="scss" scoped>
  .real-time-monitor {
    padding: var(--spacing-4);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);
  }

  .monitor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);

    h3 {
      margin: 0;
      color: var(--color-text-primary);
    }

    .monitor-controls {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;

      .is-active {
        background-color: var(--color-primary);
        border-color: var(--color-primary);
        color: white;
      }
    }
  }

  .kpi-dashboard {
    margin-bottom: var(--spacing-6);
  }

  .kpi-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
  }

  .kpi-card {
    display: flex;
    align-items: center;
    padding: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);
    border: 1px solid var(--color-border-light);

    .kpi-icon {
      margin-right: var(--spacing-3);
      font-size: 2rem;
      
      &.alert-active {
        color: var(--color-danger);
        animation: pulse 1s infinite;
      }
    }

    .kpi-content {
      flex: 1;
    }

    .kpi-value {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: var(--spacing-1);

      &.alert-count {
        color: var(--color-danger);
      }
    }

    .kpi-label {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-1);
    }

    .kpi-trend {
      font-size: var(--font-size-sm);
      font-weight: 600;

      &.positive {
        color: var(--color-success);
      }

      &.negative {
        color: var(--color-danger);
      }
    }

    .kpi-target {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .kpi-breakdown {
      display: flex;
      gap: var(--spacing-2);
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
    }

    .kpi-details {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    &.production .kpi-icon { color: var(--color-primary); }
    &.yield .kpi-icon { color: var(--color-success); }
    &.efficiency .kpi-icon { color: var(--color-warning); }
    &.alerts .kpi-icon { color: var(--color-info); }
  }

  .equipment-overview {
    margin-bottom: var(--spacing-6);

    h4 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-3);
  }

  .equipment-item {
    padding: var(--spacing-3);
    border-radius: var(--radius-base);
    border: 1px solid var(--color-border-light);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-md);
      border-color: var(--color-primary);
    }

    &.status-running {
      border-left: 4px solid var(--color-success);
    }

    &.status-idle {
      border-left: 4px solid var(--color-warning);
    }

    &.status-alarm {
      border-left: 4px solid var(--color-danger);
    }

    &.status-offline {
      border-left: 4px solid var(--color-info);
    }

    .equipment-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-3);

      .equipment-name {
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }

    .equipment-metrics {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
      margin-bottom: var(--spacing-3);

      .metric {
        display: flex;
        justify-content: space-between;
        font-size: var(--font-size-sm);

        .metric-label {
          color: var(--color-text-secondary);
        }

        .metric-value {
          font-weight: 500;
          color: var(--color-text-primary);
        }
      }
    }
  }

  .trend-charts {
    margin-bottom: var(--spacing-6);

    .chart-container {
      padding: var(--spacing-4);
      background: var(--color-bg-secondary);
      border-radius: var(--radius-base);

      h4 {
        margin: 0 0 var(--spacing-3) 0;
        color: var(--color-text-primary);
      }
    }
  }

  .alarms-section {
    h4 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .alarms-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .alarm-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-3);
    border-radius: var(--radius-base);
    border: 1px solid var(--color-border-light);

    &.severity-critical {
      border-left: 4px solid var(--color-danger);
      background: rgba(245, 108, 108, 0.05);
    }

    &.severity-warning {
      border-left: 4px solid var(--color-warning);
      background: rgba(230, 162, 60, 0.05);
    }

    &.severity-info {
      border-left: 4px solid var(--color-info);
      background: rgba(144, 147, 153, 0.05);
    }

    .alarm-icon {
      margin-right: var(--spacing-3);
      color: var(--color-danger);
    }

    .alarm-content {
      flex: 1;

      .alarm-message {
        font-weight: 500;
        color: var(--color-text-primary);
        margin-bottom: var(--spacing-1);
      }

      .alarm-details {
        display: flex;
        gap: var(--spacing-3);
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    .alarm-actions {
      display: flex;
      gap: var(--spacing-1);
    }
  }

  .equipment-details {
    .equipment-chart {
      margin-top: var(--spacing-4);
      padding-top: var(--spacing-4);
      border-top: 1px solid var(--color-border-light);

      h4 {
        margin: 0 0 var(--spacing-3) 0;
        color: var(--color-text-primary);
      }
    }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  @media (width <= 768px) {
    .kpi-row {
      grid-template-columns: 1fr;
    }

    .equipment-grid {
      grid-template-columns: 1fr;
    }

    .monitor-header {
      flex-direction: column;
      gap: var(--spacing-3);
      align-items: stretch;

      .monitor-controls {
        justify-content: center;
      }
    }
  }
</style>