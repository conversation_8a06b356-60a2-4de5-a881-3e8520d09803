# 实时监控中心模块需求规格书

## 1. 模块概述

### 1.1 模块目标
构建综合性实时监控中心，提供全局生产看板、工序监控、设备状态监控、环境监控等多维度可视化展示，实现5-10秒高频数据刷新，支持大屏显示和移动端访问。

### 1.2 核心功能
- 全局生产看板
- 工序监控看板  
- 设备状态监控
- 环境监控看板

## 2. 功能需求详细描述

### 2.1 全局生产看板

#### 2.1.1 关键指标展示
**功能要求**：
- **订单完成率**：实时计算当日/当月订单完成情况
- **各工序产能**：CP测试、封装、FT测试产能动态展示  
- **良率指标**：各工序实时良率和趋势曲线
- **设备OEE**：关键设备综合效率实时显示
- **在制品数量**：各工序在制品数量和分布
- **异常汇总**：设备故障、质量异常分级显示

**验收标准**：
- 数据刷新频率5-10秒
- 支持4K大屏显示
- 指标计算准确率>99%
- 同时支持10个大屏显示

#### 2.1.2 生产趋势分析
**功能要求**：
- **产量趋势**：日/周/月产量趋势图表
- **良率趋势**：各产品线良率变化趋势
- **效率对比**：计划vs实际效率对比
- **异常统计**：异常类型和频次统计
- **历史对比**：同比环比数据对比

**验收标准**：
- 图表响应时间<3秒
- 支持1年历史数据展示
- 趋势计算准确率>95%

### 2.2 工序监控看板

#### 2.2.1 CP测试监控
**功能要求**：
- **探针台状态**：各探针台工作状态实时显示
- **测试进度**：当前测试批次进度和预计完成时间
- **良率监控**：实时良率计算和异常预警
- **晶圆图显示**：当前测试晶圆的缺陷分布图
- **设备参数**：测试设备关键参数曲线显示

**验收标准**：
- 晶圆图实时更新<1分钟
- 良率计算实时性<30秒
- 支持同时显示20台设备状态

#### 2.2.2 封装工艺监控
**功能要求**：
- **工艺参数**：键合温度、压力等参数实时曲线
- **设备状态**：贴片机、键合机、塑封机状态
- **工序完成率**：各封装工序完成情况
- **质量监控**：键合质量、塑封质量实时监控
- **异常报警**：工艺参数超限自动报警

**验收标准**：
- 参数采集频率≥1Hz
- 报警响应时间<30秒
- 工序完成率计算准确率>99%

#### 2.2.3 FT测试监控  
**功能要求**：
- **测试站利用率**：各测试站工作负荷显示
- **测试类型分布**：不同测试项目分布统计
- **分级结果**：产品分级结果实时统计
- **异常检出**：测试异常和不良品检出率
- **插座寿命**：测试插座使用寿命监控

**验收标准**：
- 利用率计算实时性<5分钟
- 分级统计准确率>99.5%
- 插座寿命预测准确率>90%

### 2.3 设备状态监控

#### 2.3.1 设备运行状态
**功能要求**：
- **状态总览**：所有设备运行/停机状态一览
- **地图显示**：车间设备位置可视化地图
- **状态统计**：运行率、故障率、维护率统计
- **负荷分析**：设备负荷分布和瓶颈分析
- **历史趋势**：设备状态历史趋势分析

**验收标准**：
- 设备状态更新<5秒
- 支持500+设备同时监控  
- 地图响应时间<2秒
- 统计准确率>98%

#### 2.3.2 关键参数监控
**功能要求**：
- **参数曲线**：设备关键参数实时曲线图
- **阈值报警**：参数超限自动报警提示
- **参数对比**：多设备参数对比分析
- **异常检测**：基于模式识别的异常检测
- **预测维护**：基于参数趋势的维护预测

**验收标准**：
- 参数采集延迟<2秒
- 异常检测准确率>85%
- 预测准确率>70%
- 支持100+参数同时监控

### 2.4 环境监控看板

#### 2.4.1 洁净室环境监控
**功能要求**：
- **温湿度监控**：各区域温湿度实时监控
- **气压监控**：洁净室微压差监控
- **洁净度监控**：空气洁净度等级监控
- **风速监控**：层流风速和换气次数监控
- **历史曲线**：环境参数历史变化曲线

**验收标准**：
- 环境数据更新<1分钟
- 监控精度：温度±0.1°C，湿度±1%RH
- 报警响应时间<1分钟
- 数据记录完整率>99.9%

#### 2.4.2 公用工程监控
**功能要求**：
- **气源监控**：氮气、压缩空气压力纯度监控
- **电力监控**：电压、电流、功耗实时监控
- **给排水监控**：纯水、废水系统监控
- **消防安全**：消防设施状态监控
- **能耗分析**：各区域能耗统计分析

**验收标准**：
- 公用工程监控覆盖率>95%
- 能耗计算准确率>98%
- 异常检测及时率>95%

## 3. 技术要求

### 3.1 可视化技术
- **前端框架**：Vue.js 3 + TypeScript
- **图表库**：ECharts、D3.js
- **3D可视化**：Three.js
- **响应式设计**：适配多种屏幕尺寸

### 3.2 数据刷新机制  
- **WebSocket**：实时数据推送
- **数据缓存**：Redis缓存高频数据
- **增量更新**：仅传输变化数据
- **离线处理**：网络中断时的数据缓存

### 3.3 性能要求
- **数据刷新**：5-10秒全局数据刷新
- **并发用户**：支持100并发用户查看
- **响应时间**：页面切换<2秒
- **内存占用**：单页面内存<500MB

## 4. 接口规范

### 4.1 WebSocket接口
- **ws://server/monitor/global**：全局数据推送
- **ws://server/monitor/equipment**：设备状态推送  
- **ws://server/monitor/environment**：环境数据推送

### 4.2 REST API接口
- **GET /api/monitor/dashboard/global**：获取全局看板数据
- **GET /api/monitor/equipment/{id}/status**：获取设备状态
- **GET /api/monitor/environment/realtime**：获取环境实时数据

## 5. 用户界面设计

### 5.1 大屏显示界面
- **布局**：网格化布局，可拖拽调整
- **主题**：深色主题，适合7×24小时显示
- **字体**：大号字体，远距离可读
- **颜色**：状态颜色标准化（绿色正常、黄色警告、红色异常）

### 5.2 移动端界面
- **响应式**：自适应手机、平板屏幕
- **触控优化**：适合触控操作的界面元素
- **离线功能**：关键数据的离线查看
- **推送通知**：异常情况的推送提醒

---

*此需求文档版本：V1.0*