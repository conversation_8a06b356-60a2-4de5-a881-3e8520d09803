<template>
  <div class="permission-management">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Key /></el-icon>
        权限管理
      </h1>
      <p class="page-description">系统权限控制和访问管理</p>
    </div>

    <div class="page-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>权限配置</span>
            <el-button type="primary" size="small">
              <el-icon><Plus /></el-icon>
              新增权限
            </el-button>
          </div>
        </template>

        <div class="coming-soon">
          <el-empty description="权限管理功能开发中">
            <template #image>
              <el-icon size="64"><Lock /></el-icon>
            </template>
            <el-button type="primary">返回用户管理</el-button>
          </el-empty>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  // 权限管理页面 - 开发中
  console.log('权限管理页面已加载')
</script>

<style lang="scss" scoped>
  .permission-management {
    padding: var(--spacing-4);
  }

  .page-header {
    margin-bottom: var(--spacing-6);

    .page-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
    }

    .page-description {
      margin: 0;
      color: var(--color-text-secondary);
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .coming-soon {
    padding: var(--spacing-8);
    text-align: center;
  }
</style>