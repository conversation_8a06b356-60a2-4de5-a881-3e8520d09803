<template>
  <div class="organization-structure">
    <div class="organization-structure__header">
      <h2 class="organization-structure__title">组织架构管理</h2>
      <div class="organization-structure__toolbar">
        <el-button type="primary"
@click="handleAddDepartment" :icon="Plus"
>
新增部门
</el-button>
        <el-button
:icon="Expand" @click="handleExpandAll"
>
          {{ isExpandAll ? '收起全部' : '展开全部' }}
        </el-button>
        <el-button :icon="Refresh"
@click="handleRefresh"
>
刷新
</el-button>
        <el-button :icon="Share"
@click="handleOrgChart"
>
组织架构图
</el-button>
      </div>
    </div>

    <div class="organization-structure__content">
      <el-row :gutter="20">
        <!-- 左侧：部门树形结构 -->
        <el-col :span="8">
          <el-card class="organization-structure__tree-card">
            <template #header>
              <div class="organization-structure__card-header">
                <span>部门结构</span>
                <el-input
                  v-model="filterText"
                  placeholder="搜索部门..."
                  size="small"
                  clearable
                  style="width: 200px"
                  :prefix-icon="Search"
                />
              </div>
            </template>

            <el-tree
              ref="deptTreeRef"
              :data="departmentTree"
              :props="treeProps"
              :filter-node-method="filterNode"
              :expand-on-click-node="false"
              :default-expand-all="isExpandAll"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
              @node-contextmenu="handleNodeRightClick"
            >
              <template #default="{ node, data }">
                <div class="organization-structure__tree-node">
                  <div class="organization-structure__node-content">
                    <el-icon class="organization-structure__node-icon">
                      <OfficeBuilding v-if="data.type === 'management'" />
                      <Factory v-else-if="data.type === 'production'" />
                      <Tools v-else-if="data.type === 'technical'" />
                      <Service v-else />
                    </el-icon>
                    <span class="organization-structure__node-label">{{ data.name }}</span>
                    <el-tag
                      :type="getDepartmentTypeTag(data.type)"
                      size="small"
                      class="organization-structure__node-tag"
                    >
                      {{ getDepartmentTypeName(data.type) }}
                    </el-tag>
                  </div>
                  <div class="organization-structure__node-actions">
                    <el-button
                      type="text"
                      size="small"
                      :icon="Edit"
                      @click.stop="handleEditDepartment(data)"
                    />
                    <el-button
                      type="text"
                      size="small"
                      :icon="Plus"
                      @click.stop="handleAddChildDepartment(data)"
                    />
                    <el-dropdown @command="command => handleDepartmentCommand(command, data)">
                      <el-button
type="text" size="small"
:icon="MoreFilled" @click.stop
/>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="positions" :icon="User">
                            管理岗位
                          </el-dropdown-item>
                          <el-dropdown-item command="employees" :icon="Avatar">
                            查看员工
                          </el-dropdown-item>
                          <el-dropdown-item command="permissions" :icon="Key">
                            权限配置
                          </el-dropdown-item>
                          <el-dropdown-item command="costCenter" :icon="Money">
                            成本中心
                          </el-dropdown-item>
                          <el-dropdown-item divided command="delete" :icon="Delete">
                            <span style="color: var(--el-color-danger)">删除部门</span>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </template>
            </el-tree>
          </el-card>
        </el-col>

        <!-- 右侧：详细信息和管理 -->
        <el-col :span="16">
          <div class="organization-structure__main">
            <!-- 部门详情 -->
            <el-card v-if="selectedDepartment" class="organization-structure__detail-card">
              <template #header>
                <div class="organization-structure__detail-header">
                  <div class="organization-structure__detail-title">
                    <el-icon>
                      <OfficeBuilding v-if="selectedDepartment.type === 'management'" />
                      <Factory v-else-if="selectedDepartment.type === 'production'" />
                      <Tools v-else-if="selectedDepartment.type === 'technical'" />
                      <Service v-else />
                    </el-icon>
                    <span>{{ selectedDepartment.name }}</span>
                    <el-tag
                      :type="getDepartmentTypeTag(selectedDepartment.type)"
                      class="organization-structure__detail-tag"
                    >
                      {{ getDepartmentTypeName(selectedDepartment.type) }}
                    </el-tag>
                  </div>
                  <div class="organization-structure__detail-actions">
                    <el-button size="small" @click="handleEditDepartment(selectedDepartment)">
                      编辑
                    </el-button>
                    <el-button
                      size="small"
                      type="primary"
                      @click="handleManagePositions(selectedDepartment)"
                    >
                      管理岗位
                    </el-button>
                  </div>
                </div>
              </template>

              <div class="organization-structure__detail-content">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="organization-structure__info-item">
                      <label>部门编码：</label>
                      <span>{{ selectedDepartment.code }}</span>
                    </div>
                    <div class="organization-structure__info-item">
                      <label>部门类型：</label>
                      <span>{{ getDepartmentTypeName(selectedDepartment.type) }}</span>
                    </div>
                    <div class="organization-structure__info-item">
                      <label>层级：</label>
                      <span>第{{ selectedDepartment.level }}级</span>
                    </div>
                    <div class="organization-structure__info-item">
                      <label>负责人：</label>
                      <span>{{ getManagerName(selectedDepartment.managerId) }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="organization-structure__info-item">
                      <label>办公地点：</label>
                      <span>{{ selectedDepartment.location || '-' }}</span>
                    </div>
                    <div class="organization-structure__info-item">
                      <label>部门电话：</label>
                      <span>{{ selectedDepartment.phoneNumber || '-' }}</span>
                    </div>
                    <div class="organization-structure__info-item">
                      <label>部门邮箱：</label>
                      <span>{{ selectedDepartment.email || '-' }}</span>
                    </div>
                    <div class="organization-structure__info-item">
                      <label>成本中心：</label>
                      <span>{{ selectedDepartment.costCenterCode || '-' }}</span>
                    </div>
                  </el-col>
                </el-row>

                <div
                  class="organization-structure__info-item organization-structure__info-item--full"
                >
                  <label>部门描述：</label>
                  <p>{{ selectedDepartment.description || '暂无描述' }}</p>
                </div>

                <div
                  class="organization-structure__info-item organization-structure__info-item--full"
                >
                  <label>主要职责：</label>
                  <ul class="organization-structure__responsibilities">
                    <li v-for="resp in selectedDepartment.responsibilities" :key="resp">
                      {{ resp }}
                    </li>
                  </ul>
                </div>
              </div>
            </el-card>

            <!-- 统计信息 -->
            <el-row :gutter="20" class="organization-structure__stats">
              <el-col :span="6">
                <el-card class="organization-structure__stat-card">
                  <div class="organization-structure__stat-content">
                    <div class="organization-structure__stat-number">
                      {{ departmentStats.total }}
                    </div>
                    <div class="organization-structure__stat-label">总部门数</div>
                  </div>
                  <el-icon class="organization-structure__stat-icon">
                    <OfficeBuilding />
                  </el-icon>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="organization-structure__stat-card">
                  <div class="organization-structure__stat-content">
                    <div class="organization-structure__stat-number">
                      {{ positionStats.total }}
                    </div>
                    <div class="organization-structure__stat-label">总岗位数</div>
                  </div>
                  <el-icon class="organization-structure__stat-icon">
                    <User />
                  </el-icon>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="organization-structure__stat-card">
                  <div class="organization-structure__stat-content">
                    <div class="organization-structure__stat-number">
                      {{ employeeStats.total }}
                    </div>
                    <div class="organization-structure__stat-label">总员工数</div>
                  </div>
                  <el-icon class="organization-structure__stat-icon">
                    <Avatar />
                  </el-icon>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="organization-structure__stat-card">
                  <div class="organization-structure__stat-content">
                    <div class="organization-structure__stat-number">
                      {{ employeeStats.vacant }}
                    </div>
                    <div class="organization-structure__stat-label">空缺岗位</div>
                  </div>
                  <el-icon class="organization-structure__stat-icon">
                    <QuestionFilled />
                  </el-icon>
                </el-card>
              </el-col>
            </el-row>

            <!-- Tabs页签：岗位管理、员工管理、权限配置 -->
            <el-card class="organization-structure__tabs-card">
              <el-tabs v-model="activeTab" @tab-change="handleTabChange">
                <!-- 岗位管理 -->
                <el-tab-pane label="岗位管理" name="positions">
                  <div class="organization-structure__positions">
                    <div class="organization-structure__positions-toolbar">
                      <el-button
                        type="primary"
                        size="small"
                        :disabled="!selectedDepartment"
                        @click="handleAddPosition"
                      >
                        新增岗位
                      </el-button>
                      <el-input
                        v-model="positionSearch"
                        placeholder="搜索岗位..."
                        size="small"
                        clearable
                        style="width: 200px"
                        :prefix-icon="Search"
                      />
                    </div>
                    <el-table
                      :data="filteredPositions"
                      size="small"
                      @row-click="handlePositionClick"
                    >
                      <el-table-column prop="code" label="岗位编码" width="120" />
                      <el-table-column prop="name" label="岗位名称" width="150" />
                      <el-table-column prop="level" label="岗位级别" width="100">
                        <template #default="scope">
                          <el-tag
:type="getPositionLevelTag(scope.row.level)" size="small"
>
                            {{ getPositionLevelName(scope.row.level) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="人员配置" width="100">
                        <template #default="scope">
                          <span>{{ scope.row.occupiedCount }}/{{ scope.row.headcount }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="isKeyPosition" label="关键岗位" width="80">
                        <template #default="scope">
                          <el-tag
:type="scope.row.isKeyPosition ? 'danger' : ''" size="small"
>
                            {{ scope.row.isKeyPosition ? '是' : '否' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="status" label="状态" width="80">
                        <template #default="scope">
                          <el-tag
                            :type="scope.row.status === 'active' ? 'success' : 'info'"
                            size="small"
                          >
                            {{ scope.row.status === 'active' ? '启用' : '停用' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="150">
                        <template #default="scope">
                          <el-button
                            type="text"
                            size="small"
                            @click="handleEditPosition(scope.row)"
                          >
                            编辑
                          </el-button>
                          <el-button
                            type="text"
                            size="small"
                            @click="handlePositionEmployees(scope.row)"
                          >
                            人员
                          </el-button>
                          <el-button
                            type="text"
                            size="small"
                            style="color: var(--el-color-danger)"
                            @click="handleDeletePosition(scope.row)"
                          >
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>

                <!-- 员工管理 -->
                <el-tab-pane label="员工管理" name="employees">
                  <div class="organization-structure__employees">
                    <div class="organization-structure__employees-toolbar">
                      <el-button
                        type="primary"
                        size="small"
                        :disabled="!selectedDepartment"
                        @click="handleAddEmployee"
                      >
                        新增员工
                      </el-button>
                      <el-input
                        v-model="employeeSearch"
                        placeholder="搜索员工..."
                        size="small"
                        clearable
                        style="width: 200px"
                        :prefix-icon="Search"
                      />
                    </div>
                    <el-table :data="filteredEmployees" size="small">
                      <el-table-column prop="employeeNumber" label="工号" width="100" />
                      <el-table-column prop="name" label="姓名" width="100" />
                      <el-table-column prop="englishName" label="英文名" width="120" />
                      <el-table-column label="岗位" width="120">
                        <template #default="scope">
                          {{ getPositionName(scope.row.positionId) }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="email" label="邮箱" width="180" />
                      <el-table-column prop="hireDate" label="入职日期" width="100">
                        <template #default="scope">
                          {{ formatDate(scope.row.hireDate) }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="status" label="状态" width="80">
                        <template #default="scope">
                          <el-tag
:type="getEmployeeStatusTag(scope.row.status)" size="small"
>
                            {{ getEmployeeStatusName(scope.row.status) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="120">
                        <template #default="scope">
                          <el-button
                            type="text"
                            size="small"
                            @click="handleEditEmployee(scope.row)"
                          >
                            编辑
                          </el-button>
                          <el-button
                            type="text"
                            size="small"
                            @click="handleViewEmployee(scope.row)"
                          >
                            详情
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>

                <!-- 权限配置 -->
                <el-tab-pane label="权限配置" name="permissions">
                  <div class="organization-structure__permissions">
                    <div class="organization-structure__permissions-toolbar">
                      <el-button
                        type="primary"
                        size="small"
                        :disabled="!selectedDepartment"
                        @click="handleConfigPermissions"
                      >
                        配置权限
                      </el-button>
                      <el-select
                        v-model="permissionScope"
                        size="small"
                        style="width: 150px"
                        @change="handlePermissionScopeChange"
                      >
                        <el-option label="部门权限" value="department" />
                        <el-option label="岗位权限" value="position" />
                        <el-option label="个人权限" value="employee" />
                      </el-select>
                    </div>
                    <div class="organization-structure__permissions-content">
                      <el-row :gutter="20">
                        <el-col :span="12">
                          <h4>权限列表</h4>
                          <el-tree
                            ref="permissionTreeRef"
                            :data="permissionTree"
                            :props="permissionTreeProps"
                            show-checkbox
                            node-key="id"
                            :default-checked-keys="checkedPermissions"
                            @check="handlePermissionCheck"
                          >
                            <template #default="{ node, data }">
                              <div class="organization-structure__permission-node">
                                <el-icon>
                                  <Menu v-if="data.type === 'menu'" />
                                  <Operation v-else-if="data.type === 'function'" />
                                  <DataLine v-else />
                                </el-icon>
                                <span>{{ data.name }}</span>
                              </div>
                            </template>
                          </el-tree>
                        </el-col>
                        <el-col :span="12">
                          <h4>数据权限范围</h4>
                          <el-radio-group v-model="dataScopeType" @change="handleDataScopeChange">
                            <el-radio label="all">全部数据</el-radio>
                            <el-radio label="department">本部门数据</el-radio>
                            <el-radio label="self">个人数据</el-radio>
                            <el-radio label="custom">自定义范围</el-radio>
                          </el-radio-group>
                          <div v-if="dataScopeType === 'custom'" style="margin-top: 10px">
                            <el-tree
                              ref="dataScopeTreeRef"
                              :data="departmentTree"
                              :props="treeProps"
                              show-checkbox
                              node-key="id"
                              :default-checked-keys="customDataScope"
                              @check="handleDataScopeTreeCheck"
                            />
                          </div>
                        </el-col>
                      </el-row>
                      <div class="organization-structure__permissions-actions">
                        <el-button type="primary" @click="handleSavePermissions">
                          保存权限配置
                        </el-button>
                        <el-button @click="handleResetPermissions">
重置
</el-button>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 部门管理对话框 -->
    <DepartmentDialog
      v-model:visible="departmentDialogVisible"
      :department="currentDepartment"
      :parent-department="parentDepartment"
      :departments="flatDepartments"
      :employees="employees"
      @success="handleDepartmentSuccess"
    />

    <!-- 岗位管理对话框 -->
    <PositionDialog
      v-model:visible="positionDialogVisible"
      :position="currentPosition"
      :department="selectedDepartment"
      :positions="positions"
      @success="handlePositionSuccess"
    />

    <!-- 员工管理对话框 -->
    <EmployeeDialog
      v-model:visible="employeeDialogVisible"
      :employee="currentEmployee"
      :departments="flatDepartments"
      :positions="positions"
      :employees="employees"
      @success="handleEmployeeSuccess"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue'
  import { ElMessage, ElMessageBox, ElTree } from 'element-plus'
  import {
    Plus,
    Edit,
    Delete,
    Refresh,
    Search,
    Expand,
    Share,
    MoreFilled,
    OfficeBuilding,
    Factory,
    Tools,
    Service,
    User,
    Avatar,
    Key,
    Money,
    QuestionFilled,
    Menu,
    Operation,
    DataLine
  } from '@element-plus/icons-vue'
  import type {
    Department,
    Position,
    Employee,
    Permission,
    DepartmentType,
    PositionLevel,
    EmployeeStatus
  } from '@/types/organization'
  import { organizationMockData } from '@/utils/mockData/organization'
  import DepartmentDialog from './components/DepartmentDialog.vue'
  import PositionDialog from './components/PositionDialog.vue'
  import EmployeeDialog from './components/EmployeeDialog.vue'

  // 组件引用
  const deptTreeRef = ref<InstanceType<typeof ElTree>>()
  const permissionTreeRef = ref<InstanceType<typeof ElTree>>()
  const dataScopeTreeRef = ref<InstanceType<typeof ElTree>>()

  // 数据状态
  const departments = ref<Department[]>([])
  const positions = ref<Position[]>([])
  const employees = ref<Employee[]>([])
  const permissions = ref<Permission[]>([])

  // 界面状态
  const filterText = ref('')
  const isExpandAll = ref(false)
  const selectedDepartment = ref<Department | null>(null)
  const activeTab = ref('positions')

  // 搜索过滤
  const positionSearch = ref('')
  const employeeSearch = ref('')

  // 权限配置
  const permissionScope = ref('department')
  const dataScopeType = ref('department')
  const checkedPermissions = ref<string[]>([])
  const customDataScope = ref<string[]>([])

  // 对话框状态
  const departmentDialogVisible = ref(false)
  const positionDialogVisible = ref(false)
  const employeeDialogVisible = ref(false)

  // 当前编辑数据
  const currentDepartment = ref<Department | null>(null)
  const currentPosition = ref<Position | null>(null)
  const currentEmployee = ref<Employee | null>(null)
  const parentDepartment = ref<Department | null>(null)

  // 树形组件配置
  const treeProps = {
    children: 'children',
    label: 'name',
    value: 'id'
  }

  const permissionTreeProps = {
    children: 'children',
    label: 'name',
    value: 'id'
  }

  // 计算属性
  const departmentTree = computed(() => {
    return buildDepartmentTree(departments.value)
  })

  const flatDepartments = computed(() => {
    return flattenDepartments(departments.value)
  })

  const filteredPositions = computed(() => {
    let result = positions.value

    // 按部门过滤
    if (selectedDepartment.value) {
      result = result.filter(pos => pos.departmentId === selectedDepartment.value!.id)
    }

    // 按关键词搜索
    if (positionSearch.value) {
      const keyword = positionSearch.value.toLowerCase()
      result = result.filter(
        pos => pos.name.toLowerCase().includes(keyword) || pos.code.toLowerCase().includes(keyword)
      )
    }

    return result
  })

  const filteredEmployees = computed(() => {
    let result = employees.value

    // 按部门过滤
    if (selectedDepartment.value) {
      result = result.filter(emp => emp.departmentId === selectedDepartment.value!.id)
    }

    // 按关键词搜索
    if (employeeSearch.value) {
      const keyword = employeeSearch.value.toLowerCase()
      result = result.filter(
        emp =>
          emp.name.toLowerCase().includes(keyword) ||
          emp.employeeNumber.toLowerCase().includes(keyword) ||
          emp.email.toLowerCase().includes(keyword)
      )
    }

    return result
  })

  const permissionTree = computed(() => {
    return permissions.value
  })

  // 统计数据
  const departmentStats = computed(() => ({
    total: flatDepartments.value.length,
    active: flatDepartments.value.filter(d => d.status === 'active').length,
    inactive: flatDepartments.value.filter(d => d.status === 'inactive').length
  }))

  const positionStats = computed(() => ({
    total: positions.value.length,
    occupied: positions.value.reduce((sum, pos) => sum + pos.occupiedCount, 0),
    vacant: positions.value.reduce((sum, pos) => sum + (pos.headcount - pos.occupiedCount), 0)
  }))

  const employeeStats = computed(() => ({
    total: employees.value.length,
    active: employees.value.filter(e => e.status === 'active').length,
    vacant: positionStats.value.vacant
  }))

  // 工具函数
  const buildDepartmentTree = (depts: Department[]): Department[] => {
    const map = new Map<string, Department>()
    const roots: Department[] = []

    // 创建映射
    depts.forEach(dept => {
      map.set(dept.id, { ...dept, children: [] })
    })

    // 构建树形结构
    depts.forEach(dept => {
      const node = map.get(dept.id)!
      if (dept.parentId && map.has(dept.parentId)) {
        const parent = map.get(dept.parentId)!
        parent.children!.push(node)
      } else {
        roots.push(node)
      }
    })

    return roots
  }

  const flattenDepartments = (depts: Department[]): Department[] => {
    const result: Department[] = []

    const flatten = (items: Department[]) => {
      items.forEach(item => {
        result.push(item)
        if (item.children && item.children.length > 0) {
          flatten(item.children)
        }
      })
    }

    flatten(depts)
    return result
  }

  const getDepartmentTypeName = (type: DepartmentType): string => {
    const typeMap = {
      production: '生产部门',
      technical: '技术部门',
      support: '支持部门',
      management: '管理部门'
    }
    return typeMap[type] || type
  }

  const getDepartmentTypeTag = (type: DepartmentType): string => {
    const tagMap = {
      production: 'success',
      technical: 'primary',
      support: 'warning',
      management: 'info'
    }
    return tagMap[type] || ''
  }

  const getPositionLevelName = (level: PositionLevel): string => {
    const levelMap = {
      director: '总监级',
      manager: '经理级',
      supervisor: '主管级',
      specialist: '专员级',
      operator: '操作员级'
    }
    return levelMap[level] || level
  }

  const getPositionLevelTag = (level: PositionLevel): string => {
    const tagMap = {
      director: 'danger',
      manager: 'warning',
      supervisor: 'primary',
      specialist: 'success',
      operator: 'info'
    }
    return tagMap[level] || ''
  }

  const getEmployeeStatusName = (status: EmployeeStatus): string => {
    const statusMap = {
      active: '在职',
      inactive: '离职',
      probation: '试用期',
      suspended: '停职'
    }
    return statusMap[status] || status
  }

  const getEmployeeStatusTag = (status: EmployeeStatus): string => {
    const tagMap = {
      active: 'success',
      inactive: 'info',
      probation: 'warning',
      suspended: 'danger'
    }
    return tagMap[status] || ''
  }

  const getManagerName = (managerId?: string): string => {
    if (!managerId) return '-'
    const manager = employees.value.find(emp => emp.id === managerId)
    return manager ? manager.name : '-'
  }

  const getPositionName = (positionId: string): string => {
    const position = positions.value.find(pos => pos.id === positionId)
    return position ? position.name : '-'
  }

  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleDateString('zh-CN')
  }

  // 事件处理
  const filterNode = (value: string, data: Department) => {
    if (!value) return true
    return (
      data.name.toLowerCase().includes(value.toLowerCase()) ||
      data.code.toLowerCase().includes(value.toLowerCase())
    )
  }

  const handleNodeClick = (data: Department) => {
    selectedDepartment.value = data
  }

  const handleNodeRightClick = (event: MouseEvent, data: Department) => {
    // 右键菜单处理
    event.preventDefault()
    console.log('Right click on:', data.name)
  }

  const handleExpandAll = () => {
    isExpandAll.value = !isExpandAll.value
  }

  const handleRefresh = () => {
    loadData()
    ElMessage.success('数据已刷新')
  }

  const handleOrgChart = () => {
    // 打开组织架构图
    ElMessage.info('组织架构图功能开发中...')
  }

  // 部门管理
  const handleAddDepartment = () => {
    currentDepartment.value = null
    parentDepartment.value = null
    departmentDialogVisible.value = true
  }

  const handleAddChildDepartment = (parent: Department) => {
    currentDepartment.value = null
    parentDepartment.value = parent
    departmentDialogVisible.value = true
  }

  const handleEditDepartment = (department: Department) => {
    currentDepartment.value = department
    parentDepartment.value = department.parentId
      ? flatDepartments.value.find(d => d.id === department.parentId) || null
      : null
    departmentDialogVisible.value = true
  }

  const handleDepartmentCommand = (command: string, department: Department) => {
    switch (command) {
      case 'positions':
        handleManagePositions(department)
        break
      case 'employees':
        selectedDepartment.value = department
        activeTab.value = 'employees'
        break
      case 'permissions':
        selectedDepartment.value = department
        activeTab.value = 'permissions'
        break
      case 'costCenter':
        ElMessage.info('成本中心管理功能开发中...')
        break
      case 'delete':
        handleDeleteDepartment(department)
        break
    }
  }

  const handleDeleteDepartment = (department: Department) => {
    ElMessageBox.confirm(`确认删除部门"${department.name}"吗？删除后将无法恢复。`, '确认删除', {
      type: 'warning',
      confirmButtonText: '确认删除',
      cancelButtonText: '取消'
    })
      .then(() => {
        // 删除逻辑
        const index = departments.value.findIndex(d => d.id === department.id)
        if (index > -1) {
          departments.value.splice(index, 1)
          ElMessage.success('删除成功')
        }
      })
      .catch(() => {
        // 取消删除
      })
  }

  const handleDepartmentSuccess = () => {
    departmentDialogVisible.value = false
    loadData()
    ElMessage.success('操作成功')
  }

  // 岗位管理
  const handleManagePositions = (department: Department) => {
    selectedDepartment.value = department
    activeTab.value = 'positions'
  }

  const handleAddPosition = () => {
    currentPosition.value = null
    positionDialogVisible.value = true
  }

  const handleEditPosition = (position: Position) => {
    currentPosition.value = position
    positionDialogVisible.value = true
  }

  const handlePositionClick = (position: Position) => {
    // 点击岗位行
    console.log('Position clicked:', position.name)
  }

  const handlePositionEmployees = (position: Position) => {
    // 查看岗位员工
    ElMessage.info(`查看岗位"${position.name}"的员工`)
  }

  const handleDeletePosition = (position: Position) => {
    ElMessageBox.confirm(`确认删除岗位"${position.name}"吗？`, '确认删除', {
      type: 'warning'
    }).then(() => {
      const index = positions.value.findIndex(p => p.id === position.id)
      if (index > -1) {
        positions.value.splice(index, 1)
        ElMessage.success('删除成功')
      }
    })
  }

  const handlePositionSuccess = () => {
    positionDialogVisible.value = false
    loadData()
    ElMessage.success('操作成功')
  }

  // 员工管理
  const handleAddEmployee = () => {
    currentEmployee.value = null
    employeeDialogVisible.value = true
  }

  const handleEditEmployee = (employee: Employee) => {
    currentEmployee.value = employee
    employeeDialogVisible.value = true
  }

  const handleViewEmployee = (employee: Employee) => {
    ElMessage.info(`查看员工"${employee.name}"的详细信息`)
  }

  const handleEmployeeSuccess = () => {
    employeeDialogVisible.value = false
    loadData()
    ElMessage.success('操作成功')
  }

  // Tab切换
  const handleTabChange = (tabName: string) => {
    activeTab.value = tabName
  }

  // 权限配置
  const handleConfigPermissions = () => {
    ElMessage.info('配置权限功能')
  }

  const handlePermissionScopeChange = (scope: string) => {
    permissionScope.value = scope
    // 重新加载权限数据
    loadPermissionData()
  }

  const handlePermissionCheck = (data: Permission, checked: boolean) => {
    // 权限选择处理
    console.log('Permission check:', data.name, checked)
  }

  const handleDataScopeChange = (scope: string) => {
    dataScopeType.value = scope
  }

  const handleDataScopeTreeCheck = (data: Department, checked: boolean) => {
    // 数据范围选择处理
    console.log('Data scope check:', data.name, checked)
  }

  const handleSavePermissions = () => {
    ElMessage.success('权限配置已保存')
  }

  const handleResetPermissions = () => {
    checkedPermissions.value = []
    customDataScope.value = []
    ElMessage.info('已重置权限配置')
  }

  // 数据加载
  const loadData = () => {
    // 加载模拟数据
    departments.value = organizationMockData.departments
    positions.value = organizationMockData.positions
    employees.value = organizationMockData.employees
  }

  const loadPermissionData = () => {
    permissions.value = organizationMockData.permissions
  }

  // 监听过滤条件变化
  watch(filterText, val => {
    deptTreeRef.value?.filter(val)
  })

  // 组件挂载
  onMounted(() => {
    loadData()
    loadPermissionData()
  })
</script>

<style lang="scss" scoped>
  .organization-structure {
    min-height: 100vh;
    padding: var(--spacing-4);
    background: var(--color-bg-primary);

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      background: var(--color-bg-elevated);
      border-radius: var(--radius-base);
      box-shadow: var(--shadow-sm);
    }

    &__title {
      margin: 0;
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-text-primary);
    }

    &__toolbar {
      display: flex;
      gap: var(--spacing-2);
    }

    &__content {
      min-height: calc(100vh - 160px);
    }

    &__tree-card {
      height: calc(100vh - 200px);

      :deep(.el-card__body) {
        height: calc(100% - 60px);
        padding: 0;
        overflow-y: auto;
      }
    }

    &__card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    &__tree-node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--radius-sm);

      &:hover {
        background: var(--color-bg-soft);
      }
    }

    &__node-content {
      display: flex;
      flex: 1;
      gap: var(--spacing-2);
      align-items: center;
    }

    &__node-icon {
      color: var(--color-primary);
    }

    &__node-label {
      font-weight: var(--font-weight-medium);
    }

    &__node-tag {
      margin-left: var(--spacing-2);
    }

    &__node-actions {
      display: flex;
      gap: var(--spacing-1);
      opacity: 0;
      transition: opacity 0.2s ease;

      .organization-structure__tree-node:hover & {
        opacity: 1;
      }
    }

    &__main {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    &__detail-card {
      :deep(.el-card__header) {
        border-bottom: 1px solid var(--color-border-light);
      }
    }

    &__detail-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    &__detail-title {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
    }

    &__detail-tag {
      margin-left: var(--spacing-2);
    }

    &__detail-actions {
      display: flex;
      gap: var(--spacing-2);
    }

    &__detail-content {
      padding: var(--spacing-4);
    }

    &__info-item {
      display: flex;
      margin-bottom: var(--spacing-3);

      &--full {
        flex-direction: column;
        margin-top: var(--spacing-4);
      }

      label {
        min-width: 80px;
        margin-right: var(--spacing-2);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-secondary);
      }

      span {
        color: var(--color-text-primary);
      }

      p {
        margin: var(--spacing-2) 0 0 0;
        line-height: 1.6;
        color: var(--color-text-primary);
      }
    }

    &__responsibilities {
      padding-left: var(--spacing-4);
      margin: var(--spacing-2) 0 0 0;

      li {
        margin-bottom: var(--spacing-1);
        color: var(--color-text-primary);
      }
    }

    &__stats {
      margin-bottom: var(--spacing-4);
    }

    &__stat-card {
      :deep(.el-card__body) {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-4);
      }
    }

    &__stat-content {
      display: flex;
      flex-direction: column;
    }

    &__stat-number {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      line-height: 1;
      color: var(--color-primary);
    }

    &__stat-label {
      margin-top: var(--spacing-1);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    &__stat-icon {
      font-size: var(--font-size-2xl);
      color: var(--color-primary);
      opacity: 0.3;
    }

    &__tabs-card {
      :deep(.el-tabs__header) {
        margin-bottom: var(--spacing-4);
      }

      :deep(.el-tabs__content) {
        padding: 0 var(--spacing-4);
      }
    }

    &__positions,
    &__employees {
      &-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-4);
      }
    }

    &__permissions {
      &-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-4);
      }

      &-content {
        padding: var(--spacing-4);
        background: var(--color-bg-soft);
        border-radius: var(--radius-base);

        h4 {
          margin: 0 0 var(--spacing-3) 0;
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--color-text-primary);
        }
      }

      &-actions {
        padding-top: var(--spacing-4);
        margin-top: var(--spacing-4);
        text-align: right;
        border-top: 1px solid var(--color-border-light);
      }
    }

    &__permission-node {
      display: flex;
      gap: var(--spacing-2);
      align-items: center;

      .el-icon {
        color: var(--color-primary);
      }
    }

    // 响应式设计
    @media (width <= 1200px) {
      :deep(.el-col) {
        &:first-child {
          margin-bottom: var(--spacing-4);
        }
      }
    }

    @media (width <= 768px) {
      padding: var(--spacing-2);

      &__header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;
      }

      &__toolbar {
        flex-wrap: wrap;
        justify-content: center;
      }

      &__tree-card {
        height: 400px;
      }

      &__detail-header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;
      }

      &__detail-title {
        justify-content: center;
      }

      &__stats {
        :deep(.el-col) {
          margin-bottom: var(--spacing-2);
        }
      }
    }
  }

  // 深色主题适配
  @media (prefers-color-scheme: dark) {
    .organization-structure {
      &__tree-node {
        &:hover {
          background: var(--color-bg-muted);
        }
      }
    }
  }
</style>
