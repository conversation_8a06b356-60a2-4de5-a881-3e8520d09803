<template>
  <div class="login-container">
    <div class="login-card">
      <!-- 系统标题和Logo -->
      <div class="login-header">
        <div class="logo">
          <svg viewBox="0 0 100 100" fill="currentColor">
            <circle cx="50" cy="50" r="50" fill="#3682da"/>
            <path d="M25 25 L75 75 M75 25 L25 75" stroke="#ffffff" stroke-width="8" stroke-linecap="round"/>
          </svg>
        </div>
        <h1 class="system-title">IC封测CIM系统</h1>
        <p class="system-subtitle">专业制造执行系统</p>
      </div>

      <!-- 错误提示 -->
      <el-alert
        v-if="hasLoginError && loginError"
        :title="loginError"
        type="error"
        :closable="true"
        class="login-error"
        show-icon
        @close="authStore.clearErrors"
      />

      <!-- 登录表单 -->
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
        @submit.prevent="handleSubmit"
      >
        <!-- 用户名 -->
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>

        <!-- 密码 -->
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>

        <!-- 记住我 -->
        <el-form-item class="remember-me">
          <el-checkbox v-model="loginForm.rememberMe">记住用户名</el-checkbox>
        </el-form-item>

        <!-- 登录按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="isLoggingIn"
            :disabled="!canAttemptLogin"
            @click="handleSubmit"
          >
            <span v-if="isLoggingIn">登录中...</span>
            <span v-else>登录</span>
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 快速登录提示 -->
      <div class="login-hint">
        <p>演示账户：admin / 123456</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { useAuthStore } from '@/stores/auth'
  import type { FormInstance, FormRules } from 'element-plus'

  // 页面状态
  const router = useRouter()
  const authStore = useAuthStore()

  // 表单引用
  const loginFormRef = ref<FormInstance>()

  // 登录表单数据
  const loginForm = reactive({
    username: '',
    password: '',
    rememberMe: false
  })

  // 认证状态
  const isLoggingIn = computed(() => authStore.isLoggingIn)
  const hasLoginError = computed(() => authStore.hasLoginError)
  const loginError = computed(() => authStore.loginError)
  const canAttemptLogin = computed(() => authStore.canAttemptLogin)

  // 表单验证规则
  const loginRules: FormRules = {
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
  }

  /**
   * 登录提交
   */
  const handleSubmit = async (): Promise<void> => {
    if (!loginFormRef.value) return

    try {
      const valid = await loginFormRef.value.validate()
      if (valid) {
        const success = await authStore.login(loginForm)

        if (success) {
          // 保存记住我选项
          if (loginForm.rememberMe) {
            localStorage.setItem('rememberedUsername', loginForm.username)
          } else {
            localStorage.removeItem('rememberedUsername')
          }

          // 跳转到首页或之前访问的页面
          const redirect = (router.currentRoute.value.query.redirect as string) || '/'
          await router.replace(redirect)
        }
      }
    } catch (error) {
      console.error('Login form validation failed:', error)
    }
  }

  /**
   * 初始化
   */
  onMounted(() => {
    // 恢复记住的用户名
    const rememberedUsername = localStorage.getItem('rememberedUsername')
    if (rememberedUsername) {
      loginForm.username = rememberedUsername
      loginForm.rememberMe = true
    }

    // 自动聚焦到第一个空输入框
    nextTick(() => {
      if (!loginForm.username) {
        const usernameInput = document.querySelector(
          'input[placeholder="请输入用户名"]'
        ) as HTMLInputElement
        usernameInput?.focus()
      } else {
        const passwordInput = document.querySelector('input[type="password"]') as HTMLInputElement
        passwordInput?.focus()
      }
    })
  })
</script>

<style lang="scss" scoped>
  // IC封测CIM系统 - 极简登录页面
  // 严格遵循项目设计系统和极简主义原则

  .login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: var(--spacing-6);
    background: #fafbfc;
  }

  .login-card {
    width: 100%;
    max-width: 380px;
    padding: var(--spacing-12);
    background: var(--color-bg-primary);
    border-radius: var(--radius-lg);
    border: none;
    box-shadow: none;
  }

  // ===== 登录头部 =====
  .login-header {
    text-align: center;
    margin-bottom: var(--spacing-12);

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      margin: 0 auto var(--spacing-4) auto;

      svg {
        width: 48px;
        height: 48px;
      }
    }

    .system-title {
      margin: 0 0 var(--spacing-1) 0;
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-text-primary);
      letter-spacing: -0.3px;
    }

    .system-subtitle {
      margin: 0;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      font-weight: var(--font-weight-normal);
    }
  }

  // ===== 错误提示 =====
  .login-error {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-base);
    border: none;
    background: rgba(239, 68, 68, 0.05);

    :deep(.el-alert__content) {
      .el-alert__title {
        color: var(--color-text-primary);
        font-weight: var(--font-weight-normal);
        font-size: var(--font-size-sm);
      }
    }

    :deep(.el-alert__icon) {
      color: rgba(239, 68, 68, 0.6);
    }
  }

  // ===== 登录表单 =====
  .login-form {
    .el-form-item {
      margin-bottom: var(--spacing-8);
    }

    // 输入框样式优化 - 纯极简无边框设计
    :deep(.el-input) {
      .el-input__wrapper {
        background-color: transparent;
        border: none;
        border-bottom: 1px solid var(--color-border-base);
        border-radius: 0;
        box-shadow: none;
        padding: var(--spacing-3) 0;
        transition: all var(--transition-normal);

        &:hover {
          border-bottom-color: var(--color-primary);
        }

        &.is-focus {
          border-bottom-color: var(--color-primary);
          box-shadow: none;
        }
      }

      .el-input__inner {
        color: var(--color-text-primary);
        font-size: var(--font-size-base);
        padding-left: var(--spacing-6);

        &::placeholder {
          color: var(--color-text-tertiary);
          font-weight: var(--font-weight-normal);
        }
      }

      // 前缀图标样式
      .el-input__prefix {
        .el-input__prefix-inner {
          color: var(--color-text-tertiary);
        }
      }

      // 后缀图标样式
      .el-input__suffix {
        .el-input__suffix-inner {
          color: var(--color-text-tertiary);
        }
      }
    }

    // 复选框样式优化 - 极简风格
    :deep(.el-checkbox) {
      .el-checkbox__input {
        .el-checkbox__inner {
          background-color: transparent;
          border: 1px solid var(--color-border-base);
          border-radius: var(--radius-sm);

          &:hover {
            border-color: var(--color-primary);
          }
        }

        &.is-checked .el-checkbox__inner {
          background-color: var(--color-primary);
          border-color: var(--color-primary);
        }
      }

      .el-checkbox__label {
        color: var(--color-text-secondary);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-normal);
      }
    }

    // 记住我
    .remember-me {
      margin-bottom: var(--spacing-6);

      :deep(.el-form-item__content) {
        justify-content: flex-start;
      }
    }

    // 登录按钮 - 极简扁平风格
    .login-button {
      width: 100%;
      height: 48px;
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      border-radius: var(--radius-base);
      border: none;
      background: var(--color-primary);
      color: var(--color-text-inverse);
      box-shadow: none;
      transition: all var(--transition-normal);

      &:hover {
        background: var(--color-primary-hover);
        transform: none;
      }

      &:active {
        background: var(--color-primary-active);
      }

      &:disabled {
        background: var(--color-bg-disabled);
        color: var(--color-text-disabled);
      }
    }
  }

  // ===== 登录提示 =====
  .login-hint {
    margin-top: var(--spacing-6);
    padding: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);
    text-align: center;

    p {
      margin: 0;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
  }

  // ===== 响应式设计 =====
  @media (max-width: 480px) {
    .login-container {
      padding: var(--spacing-3);
    }

    .login-card {
      padding: var(--spacing-6);
    }

    .login-header {
      margin-bottom: var(--spacing-6);

      .logo {
        width: 48px;
        height: 48px;

        svg {
          width: 24px;
          height: 24px;
        }
      }

      .system-title {
        font-size: var(--font-size-xl);
      }
    }
  }
</style>
