<template>
  <div class="login-container">
    <div class="login-card">
      <!-- 系统标题和Logo -->
      <div class="login-header">
        <div class="logo">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"
            />
          </svg>
        </div>
        <h1 class="system-title">IC封测CIM系统</h1>
        <p class="system-subtitle">专业制造执行系统</p>
      </div>

      <!-- 错误提示 -->
      <el-alert
        v-if="hasLoginError && loginError"
        :title="loginError"
        type="error"
        :closable="true"
        class="login-error"
        show-icon
        @close="authStore.clearErrors"
      />

      <!-- 登录表单 -->
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
        @submit.prevent="handleSubmit"
      >
        <!-- 用户名 -->
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>

        <!-- 密码 -->
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>

        <!-- 记住我 -->
        <el-form-item class="remember-me">
          <el-checkbox v-model="loginForm.rememberMe">记住用户名</el-checkbox>
        </el-form-item>

        <!-- 登录按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="isLoggingIn"
            :disabled="!canAttemptLogin"
            @click="handleSubmit"
          >
            <span v-if="isLoggingIn">登录中...</span>
            <span v-else>登录</span>
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 快速登录提示 -->
      <div class="login-hint">
        <p>演示账户：admin / 123456</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { useAuthStore } from '@/stores/auth'
  import type { FormInstance, FormRules } from 'element-plus'

  // 页面状态
  const router = useRouter()
  const authStore = useAuthStore()

  // 表单引用
  const loginFormRef = ref<FormInstance>()

  // 登录表单数据
  const loginForm = reactive({
    username: '',
    password: '',
    rememberMe: false
  })

  // 认证状态
  const isLoggingIn = computed(() => authStore.isLoggingIn)
  const hasLoginError = computed(() => authStore.hasLoginError)
  const loginError = computed(() => authStore.loginError)
  const canAttemptLogin = computed(() => authStore.canAttemptLogin)

  // 表单验证规则
  const loginRules: FormRules = {
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
  }

  /**
   * 登录提交
   */
  const handleSubmit = async (): Promise<void> => {
    if (!loginFormRef.value) return

    try {
      const valid = await loginFormRef.value.validate()
      if (valid) {
        const success = await authStore.login(loginForm)

        if (success) {
          // 保存记住我选项
          if (loginForm.rememberMe) {
            localStorage.setItem('rememberedUsername', loginForm.username)
          } else {
            localStorage.removeItem('rememberedUsername')
          }

          // 跳转到首页或之前访问的页面
          const redirect = (router.currentRoute.value.query.redirect as string) || '/'
          await router.replace(redirect)
        }
      }
    } catch (error) {
      console.error('Login form validation failed:', error)
    }
  }

  /**
   * 初始化
   */
  onMounted(() => {
    // 恢复记住的用户名
    const rememberedUsername = localStorage.getItem('rememberedUsername')
    if (rememberedUsername) {
      loginForm.username = rememberedUsername
      loginForm.rememberMe = true
    }

    // 自动聚焦到第一个空输入框
    nextTick(() => {
      if (!loginForm.username) {
        const usernameInput = document.querySelector(
          'input[placeholder="请输入用户名"]'
        ) as HTMLInputElement
        usernameInput?.focus()
      } else {
        const passwordInput = document.querySelector('input[type="password"]') as HTMLInputElement
        passwordInput?.focus()
      }
    })
  })
</script>

<style lang="scss" scoped>
  // IC封测CIM系统 - 极简登录页面
  // 严格遵循项目设计系统和极简主义原则

  .login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: var(--spacing-4);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  .login-card {
    width: 100%;
    max-width: 420px;
    padding: var(--spacing-10);
    background: var(--color-bg-primary);
    border-radius: var(--radius-xl);
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.1),
      0 1px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
  }

  // ===== 登录头部 =====
  .login-header {
    text-align: center;
    margin-bottom: var(--spacing-10);

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 72px;
      height: 72px;
      margin: 0 auto var(--spacing-5) auto;
      color: var(--color-text-inverse);
      background: linear-gradient(135deg, var(--color-primary), #40a9ff);
      border-radius: var(--radius-full);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        inset: -2px;
        background: linear-gradient(135deg, #40a9ff, var(--color-primary), #73d13d);
        border-radius: var(--radius-full);
        z-index: -1;
        opacity: 0.6;
        filter: blur(4px);
      }

      svg {
        width: 36px;
        height: 36px;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
      }
    }

    .system-title {
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
      letter-spacing: -0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .system-subtitle {
      margin: 0;
      font-size: var(--font-size-base);
      color: var(--color-text-secondary);
      font-weight: var(--font-weight-medium);
      opacity: 0.8;
    }
  }

  // ===== 错误提示 =====
  .login-error {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-base);
    border: none;
    background: rgba(245, 108, 108, 0.1);

    :deep(.el-alert__content) {
      .el-alert__title {
        color: var(--color-danger);
        font-weight: var(--font-weight-medium);
      }
    }

    :deep(.el-alert__icon) {
      color: var(--color-danger);
    }
  }

  // ===== 登录表单 =====
  .login-form {
    .el-form-item {
      margin-bottom: var(--spacing-6);
    }

    // 输入框样式优化 - 移除蓝色边框，采用极简设计
    :deep(.el-input) {
      .el-input__wrapper {
        background-color: var(--color-bg-secondary);
        border: 1px solid transparent;
        border-radius: var(--radius-base);
        box-shadow: none;
        transition: all 0.2s ease;

        &:hover {
          border-color: var(--color-border-light);
        }

        &.is-focus {
          border-color: var(--color-primary);
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }

      .el-input__inner {
        color: var(--color-text-primary);

        &::placeholder {
          color: var(--color-text-placeholder);
        }
      }

      // 前缀图标样式
      .el-input__prefix {
        .el-input__prefix-inner {
          color: var(--color-text-secondary);
        }
      }

      // 后缀图标样式
      .el-input__suffix {
        .el-input__suffix-inner {
          color: var(--color-text-secondary);
        }
      }
    }

    // 复选框样式优化
    :deep(.el-checkbox) {
      .el-checkbox__input {
        .el-checkbox__inner {
          background-color: var(--color-bg-secondary);
          border-color: var(--color-border-light);

          &:hover {
            border-color: var(--color-primary);
          }
        }

        &.is-checked .el-checkbox__inner {
          background-color: var(--color-primary);
          border-color: var(--color-primary);
        }
      }

      .el-checkbox__label {
        color: var(--color-text-secondary);
        font-size: var(--font-size-sm);
      }
    }

    // 记住我
    .remember-me {
      margin-bottom: var(--spacing-4);

      :deep(.el-form-item__content) {
        justify-content: flex-start;
      }
    }

    // 登录按钮
    .login-button {
      width: 100%;
      height: 44px;
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      border-radius: var(--radius-base);
      border: none;
      background: linear-gradient(135deg, var(--color-primary), #40a9ff);
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
      transition: all 0.2s ease;

      &:hover {
        background: linear-gradient(135deg, #40a9ff, var(--color-primary));
        box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
      }

      &:disabled {
        background: var(--color-bg-disabled);
        box-shadow: none;
        transform: none;
      }
    }
  }

  // ===== 登录提示 =====
  .login-hint {
    margin-top: var(--spacing-6);
    padding: var(--spacing-4);
    background: var(--color-bg-secondary);
    border-radius: var(--radius-base);
    text-align: center;

    p {
      margin: 0;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
  }

  // ===== 响应式设计 =====
  @media (max-width: 480px) {
    .login-container {
      padding: var(--spacing-3);
    }

    .login-card {
      padding: var(--spacing-6);
    }

    .login-header {
      margin-bottom: var(--spacing-6);

      .logo {
        width: 48px;
        height: 48px;

        svg {
          width: 24px;
          height: 24px;
        }
      }

      .system-title {
        font-size: var(--font-size-xl);
      }
    }
  }
</style>
