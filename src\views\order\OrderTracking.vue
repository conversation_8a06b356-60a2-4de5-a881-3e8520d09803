<!-- IC封测CIM系统 - 订单跟踪页面 -->
<template>
  <div class="order-tracking-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">
            订单跟踪
          </h2>
          <p class="page-subtitle">
            生产全流程可视化跟踪监控
          </p>
        </div>
        <div class="header-actions">
          <el-button
            :icon="Refresh"
            :loading="refreshing"
            type="primary"
            size="default"
            @click="refreshData"
          >
            刷新数据
          </el-button>
          <el-button
            :icon="Download"
            size="default"
            @click="exportReport"
          >
            导出报告
          </el-button>
          <el-button
            :icon="Share"
            size="default"
            @click="shareProgress"
          >
            客户共享
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card
      class="search-card"
      shadow="never"
    >
      <el-form
        :model="searchForm"
        inline
        class="search-form"
        @submit.prevent="handleSearch"
      >
        <el-form-item label="订单编号">
          <el-input
            v-model="searchForm.orderNumber"
            placeholder="输入订单编号"
            clearable
            style="width: 200px"
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="生产阶段">
          <el-select
            v-model="searchForm.currentStage"
            placeholder="选择生产阶段"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="stage in productionStageOptions"
              :key="stage.value"
              :label="stage.label"
              :value="stage.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态筛选">
          <el-select
            v-model="searchForm.statusFilter"
            placeholder="选择状态"
            clearable
            style="width: 150px"
          >
            <el-option
              label="按时进行"
              value="onSchedule"
            />
            <el-option
              label="延迟订单"
              value="delayed"
            />
            <el-option
              label="有异常"
              value="hasEvents"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="Search"
            @click="handleSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="RefreshLeft"
            @click="resetSearch"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="16">
        <el-col
          v-for="stat in statsCards"
          :key="stat.key"
          :span="6"
        >
          <el-card
            class="stat-card"
            shadow="hover"
          >
            <div class="stat-content">
              <div
                class="stat-icon"
                :style="{ backgroundColor: stat.color + '20', color: stat.color }"
              >
                <component :is="stat.icon" />
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ stat.value }}
                </div>
                <div class="stat-label">
                  {{ stat.label }}
                </div>
              </div>
              <div
                class="stat-trend"
                :class="stat.trend"
              >
                <span class="trend-text">{{ stat.change }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <el-row :gutter="16">
        <!-- 左侧订单列表 -->
        <el-col :span="8">
          <el-card
            class="order-list-card"
            shadow="never"
          >
            <template #header>
              <div class="card-header">
                <span class="card-title">跟踪订单列表</span>
                <el-badge
                  :value="filteredOrders.length"
                  type="primary"
                />
              </div>
            </template>

            <div
              v-loading="loading"
              class="order-list"
            >
              <div
                v-for="order in paginatedOrders"
                :key="order.id"
                class="order-item"
                :class="{ active: selectedOrderId === order.order.id }"
                @click="selectOrder(order.order.id)"
              >
                <div class="order-header">
                  <div class="order-number">
                    {{ order.orderNumber }}
                  </div>
                  <div
                    class="order-status"
                    :class="getStatusClass(order.productionProgress.isOnSchedule)"
                  >
                    {{ order.productionProgress.isOnSchedule ? '按时' : '延迟' }}
                  </div>
                </div>
                <div class="order-info">
                  <div class="customer-name">
                    {{ order.order.customer.name }}
                  </div>
                  <div class="product-info">
                    {{ order.order.productInfo.productName }}
                  </div>
                </div>
                <div class="progress-info">
                  <div class="progress-bar">
                    <el-progress
                      :percentage="order.productionProgress.overallProgress"
                      :stroke-width="6"
                      :show-text="false"
                      :color="getProgressColor(order.productionProgress.overallProgress)"
                    />
                  </div>
                  <div class="progress-text">
                    {{ order.productionProgress.overallProgress }}% -
                    {{ getStageText(order.productionProgress.currentStage) }}
                  </div>
                </div>
                <div class="order-indicators">
                  <el-tag
                    v-if="order.events.some(e => e.status === 'open')"
                    type="danger"
                    size="small"
                    class="indicator-tag"
                  >
                    {{ order.events.filter(e => e.status === 'open').length }}个异常
                  </el-tag>
                  <el-tag
                    v-if="order.productionProgress.delayDays"
                    type="warning"
                    size="small"
                    class="indicator-tag"
                  >
                    延迟{{ order.productionProgress.delayDays }}天
                  </el-tag>
                </div>
              </div>

              <!-- 分页 -->
              <div
                v-if="filteredOrders.length > pageSize"
                class="pagination-wrapper"
              >
                <el-pagination
                  v-model:current-page="currentPage"
                  :page-size="pageSize"
                  :total="filteredOrders.length"
                  layout="prev, pager, next"
                  small
                  background
                />
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧详情区域 -->
        <el-col :span="16">
          <div
            v-if="selectedTrackingDetail"
            class="detail-area"
          >
            <!-- 订单基本信息 -->
            <el-card
              class="order-info-card"
              shadow="never"
            >
              <template #header>
                <div class="card-header">
                  <span class="card-title">
                    订单信息 - {{ selectedTrackingDetail.orderNumber }}
                  </span>
                  <div class="header-tags">
                    <el-tag :type="getOrderStatusType(selectedTrackingDetail.order.status)">
                      {{ getOrderStatusText(selectedTrackingDetail.order.status) }}
                    </el-tag>
                    <el-tag :type="getPriorityType(selectedTrackingDetail.order.priority)">
                      {{ getPriorityText(selectedTrackingDetail.order.priority) }}
                    </el-tag>
                  </div>
                </div>
              </template>

              <el-row :gutter="16">
                <el-col :span="12">
                  <div class="info-item">
                    <label>客户名称:</label>
                    <span>{{ selectedTrackingDetail.order.customer.name }}</span>
                  </div>
                  <div class="info-item">
                    <label>产品名称:</label>
                    <span>{{ selectedTrackingDetail.order.productInfo.productName }}</span>
                  </div>
                  <div class="info-item">
                    <label>封装类型:</label>
                    <span>{{ selectedTrackingDetail.order.productInfo.packageType }}</span>
                  </div>
                  <div class="info-item">
                    <label>订单数量:</label>
                    <span>
                      {{ selectedTrackingDetail.order.productInfo.quantity.toLocaleString() }} K pcs
                    </span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>计划交期:</label>
                    <span>
                      {{ formatDate(selectedTrackingDetail.order.schedule.deliveryDate) }}
                    </span>
                  </div>
                  <div class="info-item">
                    <label>预计完成:</label>
                    <span
                      :class="{
                        'text-danger': !selectedTrackingDetail.productionProgress.isOnSchedule
                      }"
                    >
                      {{
                        formatDate(
                          selectedTrackingDetail.productionProgress.estimatedCompletionTime
                        )
                      }}
                    </span>
                  </div>
                  <div class="info-item">
                    <label>总体进度:</label>
                    <span class="progress-value">
                      {{ selectedTrackingDetail.productionProgress.overallProgress }}%
                    </span>
                  </div>
                  <div class="info-item">
                    <label>目标良率:</label>
                    <span>{{ selectedTrackingDetail.order.qualityInfo.yieldRequirement }}%</span>
                  </div>
                </el-col>
              </el-row>
            </el-card>

            <!-- 生产进度时间轴 -->
            <el-card
              class="timeline-card"
              shadow="never"
            >
              <template #header>
                <div class="card-header">
                  <span class="card-title">生产进度时间轴</span>
                  <span class="timeline-progress">
                    整体进度: {{ selectedTrackingDetail.productionProgress.overallProgress }}%
                  </span>
                </div>
              </template>

              <div class="production-timeline">
                <el-timeline>
                  <el-timeline-item
                    v-for="(stage, index) in selectedTrackingDetail.productionProgress.stages"
                    :key="stage.stage"
                    :timestamp="getStageTimestamp(stage)"
                    placement="top"
                    :type="getTimelineType(stage.status)"
                    :icon="getStageIcon(stage.status)"
                    :size="stage.status === 'in_progress' ? 'large' : 'normal'"
                  >
                    <div class="timeline-content">
                      <div class="stage-header">
                        <h4 class="stage-name">
                          {{ stage.stageName }}
                        </h4>
                        <div class="stage-status">
                          <el-tag
                            :type="getStageStatusType(stage.status)"
                            size="small"
                          >
                            {{ getStageStatusText(stage.status) }}
                          </el-tag>
                          <span class="stage-progress">{{ stage.progress }}%</span>
                        </div>
                      </div>

                      <div class="stage-details">
                        <el-row :gutter="16">
                          <el-col :span="12">
                            <div
                              v-if="stage.assignedEquipment"
                              class="detail-item"
                            >
                              <el-icon><Tools /></el-icon>
                              <span>设备: {{ stage.assignedEquipment }}</span>
                            </div>
                            <div
                              v-if="stage.assignedOperator"
                              class="detail-item"
                            >
                              <el-icon><User /></el-icon>
                              <span>操作员: {{ stage.assignedOperator }}</span>
                            </div>
                          </el-col>
                          <el-col :span="12">
                            <div
                              v-if="stage.yield"
                              class="detail-item"
                            >
                              <el-icon><TrendCharts /></el-icon>
                              <span>良率: {{ stage.yield }}%</span>
                            </div>
                            <div
                              v-if="stage.duration"
                              class="detail-item"
                            >
                              <el-icon><Clock /></el-icon>
                              <span>
                                耗时: {{ Math.floor(stage.duration / 60) }}h
                                {{ stage.duration % 60 }}min
                              </span>
                            </div>
                          </el-col>
                        </el-row>
                      </div>

                      <div
                        v-if="stage.progress > 0"
                        class="stage-progress-bar"
                      >
                        <el-progress
                          :percentage="stage.progress"
                          :stroke-width="4"
                          :color="getProgressColor(stage.progress)"
                        />
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-card>

            <!-- 实时数据和图表 -->
            <el-row
              :gutter="16"
              class="data-charts-row"
            >
              <el-col :span="12">
                <el-card
                  class="realtime-data-card"
                  shadow="never"
                >
                  <template #header>
                    <span class="card-title">实时生产数据</span>
                  </template>

                  <div class="realtime-data">
                    <div class="data-grid">
                      <div class="data-item">
                        <div class="data-label">
                          当前完成
                        </div>
                        <div class="data-value primary">
                          {{ selectedTrackingDetail.realTimeData.currentQuantity.toLocaleString() }}
                        </div>
                        <div class="data-unit">
                          pcs
                        </div>
                      </div>
                      <div class="data-item">
                        <div class="data-label">
                          剩余数量
                        </div>
                        <div class="data-value">
                          {{
                            selectedTrackingDetail.realTimeData.remainingQuantity.toLocaleString()
                          }}
                        </div>
                        <div class="data-unit">
                          pcs
                        </div>
                      </div>
                      <div class="data-item">
                        <div class="data-label">
                          实际良率
                        </div>
                        <div
                          class="data-value"
                          :class="{
                            success:
                              selectedTrackingDetail.realTimeData.actualYield >=
                              selectedTrackingDetail.realTimeData.targetYield,
                            warning:
                              selectedTrackingDetail.realTimeData.actualYield <
                              selectedTrackingDetail.realTimeData.targetYield
                          }"
                        >
                          {{ selectedTrackingDetail.realTimeData.actualYield }}%
                        </div>
                        <div class="data-unit">
                          目标: {{ selectedTrackingDetail.realTimeData.targetYield }}%
                        </div>
                      </div>
                      <div class="data-item">
                        <div class="data-label">
                          缺陷率
                        </div>
                        <div class="data-value warning">
                          {{ selectedTrackingDetail.realTimeData.defectRate }}
                        </div>
                        <div class="data-unit">
                          ppm
                        </div>
                      </div>
                      <div class="data-item">
                        <div class="data-label">
                          重工数量
                        </div>
                        <div class="data-value">
                          {{ selectedTrackingDetail.realTimeData.reworkQuantity.toLocaleString() }}
                        </div>
                        <div class="data-unit">
                          pcs
                        </div>
                      </div>
                      <div class="data-item">
                        <div class="data-label">
                          报废数量
                        </div>
                        <div class="data-value danger">
                          {{ selectedTrackingDetail.realTimeData.scrapQuantity.toLocaleString() }}
                        </div>
                        <div class="data-unit">
                          pcs
                        </div>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-col>

              <el-col :span="12">
                <el-card
                  class="progress-chart-card"
                  shadow="never"
                >
                  <template #header>
                    <span class="card-title">进度分析图表</span>
                  </template>

                  <div class="chart-container">
                    <div
                      ref="progressChartRef"
                      class="progress-chart"
                    />
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <!-- 异常事件和质量检测 -->
            <el-row
              :gutter="16"
              class="events-quality-row"
            >
              <el-col :span="12">
                <el-card
                  class="events-card"
                  shadow="never"
                >
                  <template #header>
                    <div class="card-header">
                      <span class="card-title">异常事件</span>
                      <el-badge
                        v-if="selectedTrackingDetail.events.filter(e => e.status === 'open').length > 0"
                        :value="
                          selectedTrackingDetail.events.filter(e => e.status === 'open').length
                        "
                        type="danger"
                        "
                      />
                    </div>
                  </template>

                  <div class="events-list">
                    <div
                      v-for="event in selectedTrackingDetail.events.slice(0, 5)"
                      :key="event.id"
                      class="event-item"
                      :class="getSeverityClass(event.severity)"
                    >
                      <div class="event-header">
                        <div class="event-title">
                          {{ event.title }}
                        </div>
                        <div class="event-time">
                          {{ formatDateTime(event.occurredAt) }}
                        </div>
                      </div>
                      <div class="event-description">
                        {{ event.description }}
                      </div>
                      <div class="event-footer">
                        <el-tag
                          :type="getSeverityTagType(event.severity)"
                          size="small"
                        >
                          {{ getSeverityText(event.severity) }}
                        </el-tag>
                        <el-tag
                          :type="getEventStatusType(event.status)"
                          size="small"
                        >
                          {{ getEventStatusText(event.status) }}
                        </el-tag>
                      </div>
                    </div>

                    <div
                      v-if="selectedTrackingDetail.events.length === 0"
                      class="no-events"
                    >
                      <el-empty
                        description="暂无异常事件"
                        :image-size="80"
                      />
                    </div>
                  </div>
                </el-card>
              </el-col>

              <el-col :span="12">
                <el-card
                  class="quality-card"
                  shadow="never"
                >
                  <template #header>
                    <span class="card-title">质量检测节点</span>
                  </template>

                  <div class="quality-checkpoints">
                    <div
                      v-for="checkpoint in selectedTrackingDetail.qualityCheckpoints"
                      :key="checkpoint.id"
                      class="checkpoint-item"
                      :class="getCheckpointStatusClass(checkpoint.status)"
                    >
                      <div class="checkpoint-header">
                        <div class="checkpoint-name">
                          {{ checkpoint.checkpointName }}
                        </div>
                        <el-tag
                          :type="getCheckpointStatusType(checkpoint.status)"
                          size="small"
                        >
                          {{ getCheckpointStatusText(checkpoint.status) }}
                        </el-tag>
                      </div>
                      <div class="checkpoint-type">
                        {{ checkpoint.checkType }}
                      </div>
                      <div
                        v-if="checkpoint.inspector"
                        class="checkpoint-inspector"
                      >
                        检验员: {{ checkpoint.inspector }}
                      </div>
                      <div
                        v-if="checkpoint.checkTime"
                        class="checkpoint-time"
                      >
                        检验时间: {{ formatDateTime(checkpoint.checkTime) }}
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <!-- 客户通知记录 -->
            <el-card
              class="notifications-card"
              shadow="never"
            >
              <template #header>
                <div class="card-header">
                  <span class="card-title">客户通知记录</span>
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Message"
                    @click="sendNotification"
                  >
                    发送通知
                  </el-button>
                </div>
              </template>

              <el-table
                :data="selectedTrackingDetail.notifications"
                stripe
                size="small"
                class="notifications-table"
              >
                <el-table-column
                  prop="sentAt"
                  label="发送时间"
                  width="140"
                >
                  <template #default="{ row }">
                    {{ formatDateTime(row.sentAt) }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="notificationType"
                  label="类型"
                  width="80"
                >
                  <template #default="{ row }">
                    <el-tag
                      :type="getNotificationTypeColor(row.notificationType)"
                      size="small"
                    >
                      {{ getNotificationTypeText(row.notificationType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="subject"
                  label="主题"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="recipientName"
                  label="接收人"
                  width="100"
                />
                <el-table-column
                  prop="status"
                  label="状态"
                  width="80"
                >
                  <template #default="{ row }">
                    <el-tag
                      :type="getNotificationStatusColor(row.status)"
                      size="small"
                    >
                      {{ getNotificationStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  label="操作"
                  width="100"
                >
                  <template #default="{ row }">
                    <el-button
                      type="text"
                      size="small"
                      @click="viewNotification(row)"
                    >
                      查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>

            <!-- 物流信息 -->
            <el-card
              v-if="selectedTrackingDetail.logistics"
              class="logistics-card"
              shadow="never"
            >
              <template #header>
                <span class="card-title">物流信息</span>
              </template>

              <el-row :gutter="16">
                <el-col :span="12">
                  <h4>包装信息</h4>
                  <div class="logistics-info">
                    <div class="info-item">
                      <label>包装类型:</label>
                      <span>{{ selectedTrackingDetail.logistics.packaging.packagingType }}</span>
                    </div>
                    <div class="info-item">
                      <label>包装数量:</label>
                      <span>
                        {{ selectedTrackingDetail.logistics.packaging.packageQuantity }} 包
                      </span>
                    </div>
                    <div class="info-item">
                      <label>托盘数量:</label>
                      <span>
                        {{ selectedTrackingDetail.logistics.packaging.palletQuantity }} 托盘
                      </span>
                    </div>
                    <div
                      v-if="selectedTrackingDetail.logistics.packaging.packagingDate"
                      class="info-item"
                    >
                      <label>包装日期:</label>
                      <span>
                        {{ formatDate(selectedTrackingDetail.logistics.packaging.packagingDate) }}
                      </span>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <h4>发货信息</h4>
                  <div class="logistics-info">
                    <div class="info-item">
                      <label>发货方式:</label>
                      <span>{{ selectedTrackingDetail.logistics.shipping.shippingMethod }}</span>
                    </div>
                    <div class="info-item">
                      <label>承运商:</label>
                      <span>{{ selectedTrackingDetail.logistics.shipping.carrier }}</span>
                    </div>
                    <div
                      v-if="selectedTrackingDetail.logistics.shipping.trackingNumber"
                      class="info-item"
                    >
                      <label>物流单号:</label>
                      <span class="tracking-number">
                        {{ selectedTrackingDetail.logistics.shipping.trackingNumber }}
                      </span>
                      <el-button
                        type="text"
                        size="small"
                        @click="trackPackage"
                      >
                        跟踪
                      </el-button>
                    </div>
                    <div
                      v-if="selectedTrackingDetail.logistics.shipping.shippedDate"
                      class="info-item"
                    >
                      <label>发货日期:</label>
                      <span>
                        {{ formatDate(selectedTrackingDetail.logistics.shipping.shippedDate) }}
                      </span>
                    </div>
                    <div
                      v-if="selectedTrackingDetail.logistics.shipping.estimatedArrival"
                      class="info-item"
                    >
                      <label>预计到达:</label>
                      <span>
                        {{ formatDate(selectedTrackingDetail.logistics.shipping.estimatedArrival) }}
                      </span>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-card>
          </div>

          <!-- 未选择订单的提示 -->
          <div
            v-else
            class="no-selection"
          >
            <el-empty
              description="请从左侧列表选择一个订单查看详细跟踪信息"
              :image-size="120"
            />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 通知发送对话框 -->
    <el-dialog
      v-model="notificationDialogVisible"
      title="发送客户通知"
      width="600px"
      @close="resetNotificationForm"
    >
      <el-form
        :model="notificationForm"
        label-width="100px"
      >
        <el-form-item label="通知类型">
          <el-radio-group v-model="notificationForm.type">
            <el-radio label="email">
              邮件
            </el-radio>
            <el-radio label="sms">
              短信
            </el-radio>
            <el-radio label="portal">
              客户门户
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="接收人">
          <el-input
            v-model="notificationForm.recipient"
            placeholder="输入接收人姓名"
          />
        </el-form-item>
        <el-form-item label="联系方式">
          <el-input
            v-model="notificationForm.contact"
            placeholder="输入邮箱或手机号"
          />
        </el-form-item>
        <el-form-item label="通知主题">
          <el-input
            v-model="notificationForm.subject"
            placeholder="输入通知主题"
          />
        </el-form-item>
        <el-form-item label="通知内容">
          <el-input
            v-model="notificationForm.content"
            type="textarea"
            rows="4"
            placeholder="输入通知内容"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="notificationDialogVisible = false">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitNotification"
        >
          发送
        </el-button>
      </template>
    </el-dialog>

    <!-- 通知详情对话框 -->
    <el-dialog
      v-model="notificationDetailVisible"
      title="通知详情"
      width="500px"
    >
      <div
        v-if="currentNotification"
        class="notification-detail"
      >
        <div class="detail-item">
          <label>发送时间:</label>
          <span>{{ formatDateTime(currentNotification.sentAt) }}</span>
        </div>
        <div class="detail-item">
          <label>通知类型:</label>
          <span>{{ getNotificationTypeText(currentNotification.notificationType) }}</span>
        </div>
        <div class="detail-item">
          <label>接收人:</label>
          <span>{{ currentNotification.recipientName }}</span>
        </div>
        <div class="detail-item">
          <label>联系方式:</label>
          <span>{{ currentNotification.recipientContact }}</span>
        </div>
        <div class="detail-item">
          <label>主题:</label>
          <span>{{ currentNotification.subject }}</span>
        </div>
        <div class="detail-item full">
          <label>内容:</label>
          <div class="content-text">
            {{ currentNotification.content }}
          </div>
        </div>
        <div
          v-if="currentNotification.response"
          class="detail-item"
        >
          <label>客户回复:</label>
          <div class="response-text">
            {{ currentNotification.response }}
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'
  import * as echarts from 'echarts'
  import {
    Search,
    RefreshLeft,
    Refresh,
    Download,
    Share,
    Tools,
    User,
    TrendCharts,
    Clock,
    Message,
    View,
    Checking,
    Loading,
    CircleCheck,
    Warning,
    CircleClose,
    Clock as ClockIcon
  } from '@element-plus/icons-vue'
  import type {
    OrderTrackingDetail,
    ProductionStage,
    StageStatus,
    EventSeverity,
    CustomerNotification
  } from '@/types/order'
  import {
    allOrderTrackingDetails,
    getOrderTrackingDetail,
    trackingStats
  } from '@/utils/mockData/orderTracking'

  // 响应式数据
  const loading = ref(false)
  const refreshing = ref(false)
  const submitting = ref(false)
  const selectedOrderId = ref<string>('')
  const currentPage = ref(1)
  const pageSize = 10
  const progressChartRef = ref<HTMLElement>()
  let progressChart: echarts.ECharts | null = null

  // 搜索表单
  const searchForm = ref({
    orderNumber: '',
    currentStage: '',
    statusFilter: ''
  })

  // 通知表单
  const notificationDialogVisible = ref(false)
  const notificationDetailVisible = ref(false)
  const currentNotification = ref<CustomerNotification | null>(null)
  const notificationForm = ref({
    type: 'email',
    recipient: '',
    contact: '',
    subject: '',
    content: ''
  })

  // 生产阶段选项
  const productionStageOptions = [
    { value: 'material_ready', label: '物料准备' },
    { value: 'cp_testing', label: 'CP测试' },
    { value: 'dicing', label: '晶圆切割' },
    { value: 'die_attach', label: '贴片' },
    { value: 'wire_bond', label: '线键合' },
    { value: 'molding', label: '塑封' },
    { value: 'marking', label: '打标' },
    { value: 'trim_form', label: '切筋成型' },
    { value: 'ft_testing', label: '最终测试' },
    { value: 'burn_in', label: '老化测试' },
    { value: 'final_qc', label: '最终质检' },
    { value: 'packaging', label: '包装' },
    { value: 'shipping', label: '发货' }
  ]

  // 统计卡片数据
  const statsCards = computed(() => [
    {
      key: 'total',
      label: '跟踪订单',
      value: trackingStats.totalTracking,
      color: '#3682da',
      icon: 'Document',
      change: '+8.2%',
      trend: 'up'
    },
    {
      key: 'onSchedule',
      label: '按时进行',
      value: trackingStats.onSchedule,
      color: '#10b981',
      icon: 'CircleCheck',
      change: '+12.5%',
      trend: 'up'
    },
    {
      key: 'delayed',
      label: '延迟订单',
      value: trackingStats.delayed,
      color: '#f59e0b',
      icon: 'Warning',
      change: '-5.3%',
      trend: 'down'
    },
    {
      key: 'avgProgress',
      label: '平均进度',
      value: trackingStats.averageProgress + '%',
      color: '#8b5cf6',
      icon: 'TrendCharts',
      change: '+15.7%',
      trend: 'up'
    }
  ])

  // 过滤后的订单列表
  const filteredOrders = computed(() => {
    let filtered = allOrderTrackingDetails

    // 订单编号过滤
    if (searchForm.value.orderNumber) {
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(searchForm.value.orderNumber.toLowerCase())
      )
    }

    // 生产阶段过滤
    if (searchForm.value.currentStage) {
      filtered = filtered.filter(
        order => order.productionProgress.currentStage === searchForm.value.currentStage
      )
    }

    // 状态过滤
    if (searchForm.value.statusFilter) {
      switch (searchForm.value.statusFilter) {
        case 'onSchedule':
          filtered = filtered.filter(order => order.productionProgress.isOnSchedule)
          break
        case 'delayed':
          filtered = filtered.filter(order => !order.productionProgress.isOnSchedule)
          break
        case 'hasEvents':
          filtered = filtered.filter(order => order.events.some(event => event.status === 'open'))
          break
      }
    }

    return filtered
  })

  // 分页后的订单列表
  const paginatedOrders = computed(() => {
    const start = (currentPage.value - 1) * pageSize
    const end = start + pageSize
    return filteredOrders.value.slice(start, end)
  })

  // 选中的订单跟踪详情
  const selectedTrackingDetail = computed(() => {
    return selectedOrderId.value ? getOrderTrackingDetail(selectedOrderId.value) : null
  })

  // 方法定义
  const selectOrder = (orderId: string) => {
    selectedOrderId.value = orderId
    nextTick(() => {
      initProgressChart()
    })
  }

  const handleSearch = () => {
    currentPage.value = 1
  }

  const resetSearch = () => {
    searchForm.value = {
      orderNumber: '',
      currentStage: '',
      statusFilter: ''
    }
    currentPage.value = 1
  }

  const refreshData = async () => {
    refreshing.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      ElMessage.success('数据已刷新')
    } catch (error) {
      ElMessage.error('刷新失败')
    } finally {
      refreshing.value = false
    }
  }

  const exportReport = () => {
    if (!selectedTrackingDetail.value) {
      ElMessage.warning('请先选择一个订单')
      return
    }

    // 模拟导出功能
    ElMessage.success('报告导出功能开发中')
  }

  const shareProgress = () => {
    if (!selectedTrackingDetail.value) {
      ElMessage.warning('请先选择一个订单')
      return
    }

    // 模拟分享功能
    navigator.clipboard.writeText(
      `${window.location.origin}/tracking/${selectedTrackingDetail.value.orderNumber}`
    )
    ElMessage.success('分享链接已复制到剪贴板')
  }

  const sendNotification = () => {
    if (!selectedTrackingDetail.value) {
      ElMessage.warning('请先选择一个订单')
      return
    }

    // 预填充客户信息
    notificationForm.value.recipient =
      selectedTrackingDetail.value.order.customer.contact?.name || ''
    notificationForm.value.contact =
      selectedTrackingDetail.value.order.customer.contact?.email || ''
    notificationDialogVisible.value = true
  }

  const submitNotification = async () => {
    submitting.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      ElMessage.success('通知发送成功')
      notificationDialogVisible.value = false
    } catch (error) {
      ElMessage.error('发送失败')
    } finally {
      submitting.value = false
    }
  }

  const resetNotificationForm = () => {
    notificationForm.value = {
      type: 'email',
      recipient: '',
      contact: '',
      subject: '',
      content: ''
    }
  }

  const viewNotification = (notification: CustomerNotification) => {
    currentNotification.value = notification
    notificationDetailVisible.value = true
  }

  const trackPackage = () => {
    ElMessage.success('物流跟踪功能开发中')
  }

  // 初始化进度图表
  const initProgressChart = () => {
    if (!progressChartRef.value || !selectedTrackingDetail.value) return

    if (progressChart) {
      progressChart.dispose()
    }

    progressChart = echarts.init(progressChartRef.value)

    const stages = selectedTrackingDetail.value.productionProgress.stages
    const stageNames = stages.map(stage => stage.stageName)
    const progressData = stages.map(stage => stage.progress)

    const option = {
      title: {
        text: '各阶段进度',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          const data = params[0]
          return `${data.name}<br/>进度: ${data.value}%`
        }
      },
      xAxis: {
        type: 'category',
        data: stageNames,
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 10
        }
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          type: 'bar',
          data: progressData.map((value, index) => ({
            value,
            itemStyle: {
              color: value === 100 ? '#10b981' : value > 0 ? '#3682da' : '#e5e7eb'
            }
          }))
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      }
    }

    progressChart.setOption(option)
  }

  // 辅助函数
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const getStatusClass = (isOnSchedule: boolean) => {
    return isOnSchedule ? 'status-success' : 'status-warning'
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return '#10b981'
    if (progress >= 80) return '#3682da'
    if (progress >= 60) return '#f59e0b'
    if (progress > 0) return '#8b5cf6'
    return '#e5e7eb'
  }

  const getStageText = (stage: ProductionStage) => {
    const stageMap: Record<ProductionStage, string> = {
      [ProductionStage.MATERIAL_READY]: '物料准备',
      [ProductionStage.CP_TESTING]: 'CP测试',
      [ProductionStage.DICING]: '晶圆切割',
      [ProductionStage.DIE_ATTACH]: '贴片',
      [ProductionStage.WIRE_BOND]: '线键合',
      [ProductionStage.MOLDING]: '塑封',
      [ProductionStage.MARKING]: '打标',
      [ProductionStage.TRIM_FORM]: '切筋成型',
      [ProductionStage.FT_TESTING]: '最终测试',
      [ProductionStage.BURN_IN]: '老化测试',
      [ProductionStage.FINAL_QC]: '最终质检',
      [ProductionStage.PACKAGING]: '包装',
      [ProductionStage.SHIPPING]: '发货'
    }
    return stageMap[stage] || stage
  }

  const getOrderStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: '待确认',
      confirmed: '已确认',
      processing: '生产中',
      testing: '测试中',
      completed: '已完成',
      cancelled: '已取消',
      on_hold: '暂停'
    }
    return statusMap[status] || status
  }

  const getOrderStatusType = (status: string) => {
    const typeMap: Record<string, string> = {
      pending: 'info',
      confirmed: 'success',
      processing: 'warning',
      testing: 'primary',
      completed: 'success',
      cancelled: 'danger',
      on_hold: 'warning'
    }
    return typeMap[status] || 'info'
  }

  const getPriorityText = (priority: string) => {
    const priorityMap: Record<string, string> = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    }
    return priorityMap[priority] || priority
  }

  const getPriorityType = (priority: string) => {
    const typeMap: Record<string, string> = {
      low: 'info',
      medium: 'primary',
      high: 'warning',
      urgent: 'danger'
    }
    return typeMap[priority] || 'info'
  }

  const getStageTimestamp = (stage: any) => {
    if (stage.actualStartTime) {
      return formatDateTime(stage.actualStartTime)
    }
    if (stage.plannedStartTime) {
      return '计划: ' + formatDateTime(stage.plannedStartTime)
    }
    return ''
  }

  const getTimelineType = (status: StageStatus) => {
    const typeMap: Record<StageStatus, string> = {
      [StageStatus.COMPLETED]: 'success',
      [StageStatus.IN_PROGRESS]: 'primary',
      [StageStatus.DELAYED]: 'warning',
      [StageStatus.BLOCKED]: 'danger',
      [StageStatus.REWORK]: 'warning',
      [StageStatus.PENDING]: 'info',
      [StageStatus.SKIPPED]: 'info'
    }
    return typeMap[status] || 'info'
  }

  const getStageIcon = (status: StageStatus) => {
    const iconMap: Record<StageStatus, any> = {
      [StageStatus.COMPLETED]: CircleCheck,
      [StageStatus.IN_PROGRESS]: Loading,
      [StageStatus.DELAYED]: Warning,
      [StageStatus.BLOCKED]: CircleClose,
      [StageStatus.REWORK]: Refresh,
      [StageStatus.PENDING]: ClockIcon,
      [StageStatus.SKIPPED]: View
    }
    return iconMap[status] || ClockIcon
  }

  const getStageStatusType = (status: StageStatus) => {
    const typeMap: Record<StageStatus, string> = {
      [StageStatus.COMPLETED]: 'success',
      [StageStatus.IN_PROGRESS]: 'primary',
      [StageStatus.DELAYED]: 'warning',
      [StageStatus.BLOCKED]: 'danger',
      [StageStatus.REWORK]: 'warning',
      [StageStatus.PENDING]: 'info',
      [StageStatus.SKIPPED]: 'info'
    }
    return typeMap[status] || 'info'
  }

  const getStageStatusText = (status: StageStatus) => {
    const textMap: Record<StageStatus, string> = {
      [StageStatus.COMPLETED]: '已完成',
      [StageStatus.IN_PROGRESS]: '进行中',
      [StageStatus.DELAYED]: '延迟',
      [StageStatus.BLOCKED]: '阻塞',
      [StageStatus.REWORK]: '重工',
      [StageStatus.PENDING]: '等待中',
      [StageStatus.SKIPPED]: '跳过'
    }
    return textMap[status] || status
  }

  const getSeverityClass = (severity: EventSeverity) => {
    return `event-${severity}`
  }

  const getSeverityTagType = (severity: EventSeverity) => {
    const typeMap: Record<EventSeverity, string> = {
      [EventSeverity.LOW]: 'info',
      [EventSeverity.MEDIUM]: 'warning',
      [EventSeverity.HIGH]: 'danger',
      [EventSeverity.CRITICAL]: 'danger'
    }
    return typeMap[severity] || 'info'
  }

  const getSeverityText = (severity: EventSeverity) => {
    const textMap: Record<EventSeverity, string> = {
      [EventSeverity.LOW]: '低',
      [EventSeverity.MEDIUM]: '中',
      [EventSeverity.HIGH]: '高',
      [EventSeverity.CRITICAL]: '严重'
    }
    return textMap[severity] || severity
  }

  const getEventStatusType = (status: string) => {
    const typeMap: Record<string, string> = {
      open: 'danger',
      investigating: 'warning',
      resolved: 'success',
      closed: 'info'
    }
    return typeMap[status] || 'info'
  }

  const getEventStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      open: '未处理',
      investigating: '调查中',
      resolved: '已解决',
      closed: '已关闭'
    }
    return textMap[status] || status
  }

  const getCheckpointStatusClass = (status: string) => {
    return `checkpoint-${status}`
  }

  const getCheckpointStatusType = (status: string) => {
    const typeMap: Record<string, string> = {
      pending: 'info',
      in_progress: 'primary',
      passed: 'success',
      failed: 'danger',
      waived: 'warning'
    }
    return typeMap[status] || 'info'
  }

  const getCheckpointStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: '待检',
      in_progress: '检验中',
      passed: '通过',
      failed: '不通过',
      waived: '豁免'
    }
    return textMap[status] || status
  }

  const getNotificationTypeText = (type: string) => {
    const textMap: Record<string, string> = {
      email: '邮件',
      sms: '短信',
      portal: '门户',
      phone: '电话'
    }
    return textMap[type] || type
  }

  const getNotificationTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      email: 'primary',
      sms: 'success',
      portal: 'warning',
      phone: 'info'
    }
    return colorMap[type] || 'info'
  }

  const getNotificationStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      sent: '已发送',
      delivered: '已送达',
      read: '已读',
      failed: '失败'
    }
    return textMap[status] || status
  }

  const getNotificationStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      sent: 'primary',
      delivered: 'success',
      read: 'info',
      failed: 'danger'
    }
    return colorMap[status] || 'info'
  }

  // 生命周期
  onMounted(() => {
    // 默认选择第一个订单
    if (filteredOrders.value.length > 0) {
      selectOrder(filteredOrders.value[0].order.id)
    }
  })

  onUnmounted(() => {
    if (progressChart) {
      progressChart.dispose()
    }
  })

  // 定时刷新数据
  let refreshTimer: NodeJS.Timeout
  onMounted(() => {
    refreshTimer = setInterval(() => {
      // 模拟实时数据更新
      if (selectedTrackingDetail.value) {
        // 这里可以调用API更新数据
      }
    }, 30000) // 30秒刷新一次
  })

  onUnmounted(() => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }
  })
</script>

<style lang="scss" scoped>
  .order-tracking-page {
    min-height: 100vh;
    padding: 20px;
    background-color: var(--color-bg-light);

    .page-header {
      margin-bottom: 20px;

      .header-content {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;

        .header-left {
          .page-title {
            margin: 0 0 4px;
            font-size: 24px;
            font-weight: 600;
            color: var(--color-text-primary);
          }

          .page-subtitle {
            margin: 0;
            font-size: 14px;
            color: var(--color-text-secondary);
          }
        }

        .header-actions {
          display: flex;
          gap: 12px;
        }
      }
    }

    .search-card {
      margin-bottom: 20px;

      .search-form {
        .el-form-item {
          margin-bottom: 0;
        }
      }
    }

    .stats-overview {
      margin-bottom: 20px;

      .stat-card {
        height: 100px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 8px 25px rgb(0 0 0 / 10%);
          transform: translateY(-2px);
        }

        .stat-content {
          display: flex;
          align-items: center;
          height: 100%;
          padding: 0;

          .stat-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            margin-right: 16px;
            font-size: 24px;
            border-radius: 50%;
          }

          .stat-info {
            flex: 1;

            .stat-value {
              font-size: 24px;
              font-weight: 600;
              line-height: 1;
              color: var(--color-text-primary);
            }

            .stat-label {
              margin-top: 4px;
              font-size: 14px;
              color: var(--color-text-secondary);
            }
          }

          .stat-trend {
            text-align: right;

            .trend-text {
              font-size: 12px;
              font-weight: 500;
            }

            &.up .trend-text {
              color: #10b981;
            }

            &.down .trend-text {
              color: #ef4444;
            }

            &.stable .trend-text {
              color: #6b7280;
            }
          }
        }
      }
    }

    .main-content {
      .order-list-card {
        height: calc(100vh - 300px);
        min-height: 600px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .card-title {
            font-size: 16px;
            font-weight: 600;
          }
        }

        .order-list {
          height: calc(100% - 40px);
          overflow-y: auto;

          .order-item {
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            border: 1px solid var(--color-border-light);
            border-radius: 8px;
            transition: all 0.2s ease;

            &:hover {
              border-color: var(--color-primary);
              box-shadow: 0 2px 8px rgb(59 130 246 / 10%);
            }

            &.active {
              background-color: var(--color-primary-light);
              border-color: var(--color-primary);
            }

            .order-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 8px;

              .order-number {
                font-weight: 600;
                color: var(--color-text-primary);
              }

              .order-status {
                padding: 2px 8px;
                font-size: 12px;
                border-radius: 12px;

                &.status-success {
                  color: #16a34a;
                  background-color: #dcfce7;
                }

                &.status-warning {
                  color: #d97706;
                  background-color: #fef3c7;
                }
              }
            }

            .order-info {
              margin-bottom: 8px;

              .customer-name {
                margin-bottom: 4px;
                font-size: 13px;
                color: var(--color-text-secondary);
              }

              .product-info {
                font-size: 13px;
                color: var(--color-text-secondary);
              }
            }

            .progress-info {
              margin-bottom: 8px;

              .progress-bar {
                margin-bottom: 4px;
              }

              .progress-text {
                font-size: 12px;
                color: var(--color-text-secondary);
              }
            }

            .order-indicators {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;

              .indicator-tag {
                font-size: 10px;
              }
            }
          }

          .pagination-wrapper {
            margin-top: 16px;
            text-align: center;
          }
        }
      }

      .detail-area {
        .order-info-card {
          margin-bottom: 16px;

          .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .card-title {
              font-size: 16px;
              font-weight: 600;
            }

            .header-tags {
              display: flex;
              gap: 8px;
            }
          }

          .info-item {
            display: flex;
            margin-bottom: 8px;

            label {
              width: 80px;
              font-size: 14px;
              color: var(--color-text-secondary);
            }

            span {
              font-size: 14px;
              color: var(--color-text-primary);

              &.progress-value {
                font-weight: 600;
                color: var(--color-primary);
              }

              &.text-danger {
                color: var(--color-danger);
              }
            }
          }
        }

        .timeline-card {
          margin-bottom: 16px;

          .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .timeline-progress {
              font-size: 14px;
              font-weight: 600;
              color: var(--color-primary);
            }
          }

          .production-timeline {
            .timeline-content {
              .stage-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;

                .stage-name {
                  margin: 0;
                  font-size: 16px;
                  font-weight: 600;
                  color: var(--color-text-primary);
                }

                .stage-status {
                  display: flex;
                  gap: 8px;
                  align-items: center;

                  .stage-progress {
                    font-size: 14px;
                    font-weight: 600;
                    color: var(--color-primary);
                  }
                }
              }

              .stage-details {
                margin-bottom: 12px;

                .detail-item {
                  display: flex;
                  gap: 8px;
                  align-items: center;
                  margin-bottom: 4px;
                  font-size: 14px;
                  color: var(--color-text-secondary);
                }
              }

              .stage-progress-bar {
                margin-top: 8px;
              }
            }
          }
        }

        .data-charts-row {
          margin-bottom: 16px;

          .realtime-data-card {
            .realtime-data {
              .data-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;

                .data-item {
                  padding: 12px;
                  text-align: center;
                  background-color: var(--color-bg-light);
                  border-radius: 8px;

                  .data-label {
                    margin-bottom: 4px;
                    font-size: 12px;
                    color: var(--color-text-secondary);
                  }

                  .data-value {
                    margin-bottom: 2px;
                    font-size: 20px;
                    font-weight: 600;
                    color: var(--color-text-primary);

                    &.primary {
                      color: var(--color-primary);
                    }

                    &.success {
                      color: var(--color-success);
                    }

                    &.warning {
                      color: var(--color-warning);
                    }

                    &.danger {
                      color: var(--color-danger);
                    }
                  }

                  .data-unit {
                    font-size: 10px;
                    color: var(--color-text-secondary);
                  }
                }
              }
            }
          }

          .progress-chart-card {
            .chart-container {
              .progress-chart {
                width: 100%;
                height: 260px;
              }
            }
          }
        }

        .events-quality-row {
          margin-bottom: 16px;

          .events-card {
            .events-list {
              max-height: 300px;
              overflow-y: auto;

              .event-item {
                padding: 12px;
                margin-bottom: 8px;
                border-left: 4px solid #e5e7eb;
                border-radius: 8px;

                &.event-low {
                  border-left-color: #6b7280;
                }

                &.event-medium {
                  border-left-color: #f59e0b;
                }

                &.event-high {
                  border-left-color: #ef4444;
                }

                &.event-critical {
                  background-color: #fef2f2;
                  border-left-color: #dc2626;
                }

                .event-header {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-bottom: 6px;

                  .event-title {
                    font-weight: 600;
                    color: var(--color-text-primary);
                  }

                  .event-time {
                    font-size: 12px;
                    color: var(--color-text-secondary);
                  }
                }

                .event-description {
                  margin-bottom: 8px;
                  font-size: 14px;
                  color: var(--color-text-secondary);
                }

                .event-footer {
                  display: flex;
                  gap: 8px;
                }
              }

              .no-events {
                padding: 40px 0;
                text-align: center;
              }
            }
          }

          .quality-card {
            .quality-checkpoints {
              max-height: 300px;
              overflow-y: auto;

              .checkpoint-item {
                padding: 12px;
                margin-bottom: 8px;
                border: 1px solid var(--color-border-light);
                border-radius: 8px;

                &.checkpoint-passed {
                  background-color: #f0fdf4;
                  border-color: var(--color-success);
                }

                &.checkpoint-failed {
                  background-color: #fef2f2;
                  border-color: var(--color-danger);
                }

                &.checkpoint-in_progress {
                  background-color: #eff6ff;
                  border-color: var(--color-primary);
                }

                .checkpoint-header {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-bottom: 6px;

                  .checkpoint-name {
                    font-weight: 600;
                    color: var(--color-text-primary);
                  }
                }

                .checkpoint-type {
                  margin-bottom: 4px;
                  font-size: 12px;
                  color: var(--color-text-secondary);
                }

                .checkpoint-inspector,
                .checkpoint-time {
                  margin-bottom: 2px;
                  font-size: 12px;
                  color: var(--color-text-secondary);
                }
              }
            }
          }
        }

        .notifications-card {
          margin-bottom: 16px;

          .notifications-table {
            .el-table__row {
              cursor: pointer;

              &:hover {
                background-color: var(--color-bg-light);
              }
            }
          }
        }

        .logistics-card {
          .logistics-info {
            .info-item {
              display: flex;
              margin-bottom: 8px;

              label {
                width: 80px;
                font-size: 14px;
                color: var(--color-text-secondary);
              }

              span {
                font-size: 14px;
                color: var(--color-text-primary);

                &.tracking-number {
                  font-family: monospace;
                  font-weight: 600;
                }
              }
            }
          }

          h4 {
            margin: 0 0 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--color-text-primary);
          }
        }
      }

      .no-selection {
        display: flex;
        align-items: center;
        justify-content: center;
        height: calc(100vh - 300px);
        min-height: 400px;
      }
    }
  }

  // 通知详情对话框样式
  .notification-detail {
    .detail-item {
      display: flex;
      margin-bottom: 12px;

      &.full {
        flex-direction: column;
      }

      label {
        flex-shrink: 0;
        width: 80px;
        margin-right: 12px;
        font-size: 14px;
        color: var(--color-text-secondary);
      }

      span {
        font-size: 14px;
        color: var(--color-text-primary);
      }

      .content-text,
      .response-text {
        padding: 8px;
        margin-top: 8px;
        font-size: 14px;
        color: var(--color-text-primary);
        background-color: var(--color-bg-light);
        border-radius: 4px;
      }

      .response-text {
        background-color: var(--color-primary-light);
      }
    }
  }

  // 响应式样式
  @media (width <= 1200px) {
    .main-content {
      .el-row {
        flex-direction: column;

        .el-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }

      .order-list-card {
        height: auto;
        min-height: 400px;
      }

      .data-charts-row,
      .events-quality-row {
        .el-row {
          flex-direction: row;
        }
      }
    }
  }

  @media (width <= 768px) {
    .order-tracking-page {
      padding: 12px;

      .stats-overview {
        .el-row {
          flex-direction: column;
          gap: 12px;

          .el-col {
            width: 100%;
          }
        }
      }

      .data-charts-row,
      .events-quality-row {
        .el-row {
          flex-direction: column;
        }
      }

      .realtime-data {
        .data-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
</style>
