<template>
  <div class="process-parameter-table">
    <div class="process-parameter-table__header">
      <h3>工艺参数监控</h3>
      <div class="process-parameter-table__actions">
        <CButton size="small" @click="refreshData">
          <el-icon><RefreshRight /></el-icon>
          刷新
        </CButton>
        <CButton size="small" type="primary" @click="openSettingsDialog">
          <el-icon><Setting /></el-icon>
          设置
        </CButton>
      </div>
    </div>

    <el-table
:data="parameters" :loading="loading"
class="parameter-table" stripe border
>
      <el-table-column prop="name" label="参数名称" min-width="120">
        <template #default="{ row }">
          <div class="parameter-name">
            <el-icon
:color="getParameterStatusColor(row.status)" class="parameter-status-icon"
>
              <CircleCheckFilled />
            </el-icon>
            {{ row.name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="currentValue" label="当前值" width="100" align="center">
        <template #default="{ row }">
          <span :class="getValueClass(row)">
            {{ formatValue(row.currentValue, row.unit) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="targetValue" label="目标值" width="100" align="center">
        <template #default="{ row }">
          {{ formatValue(row.targetValue, row.unit) }}
        </template>
      </el-table-column>

      <el-table-column label="规格范围" width="120" align="center">
        <template #default="{ row }">
          <div class="spec-range">
            {{ formatValue(row.minLimit, row.unit) }} ~ {{ formatValue(row.maxLimit, row.unit) }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="偏差" width="80" align="center">
        <template #default="{ row }">
          <span :class="getDeviationClass(row)">{{ calculateDeviation(row) }}%</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="趋势图" width="120" align="center">
        <template #default="{ row }">
          <div
class="trend-chart" @click="showTrendDialog(row)"
:id="`trend-${row.id}`"
>
            <el-icon><TrendCharts /></el-icon>
            查看趋势
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="100" align="center">
        <template #default="{ row }">
          <el-dropdown trigger="click">
            <CButton size="small" link>
              更多
              <el-icon><ArrowDown /></el-icon>
            </CButton>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="editParameter(row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item @click="viewHistory(row)">
                  <el-icon><Clock /></el-icon>
                  历史记录
                </el-dropdown-item>
                <el-dropdown-item @click="exportData(row)">
                  <el-icon><Download /></el-icon>
                  导出数据
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 趋势图弹窗 -->
    <el-dialog
      v-model="trendDialogVisible"
      :title="`${selectedParameter?.name} 趋势分析`"
      width="80%"
      top="5vh"
    >
      <div class="trend-dialog-content">
        <div
id="trend-chart" style="width: 100%; height: 400px"
/>
      </div>
    </el-dialog>

    <!-- 参数设置弹窗 -->
    <el-dialog v-model="settingsDialogVisible" title="参数设置" width="60%">
      <div class="settings-dialog-content">
        <el-form :model="settingsForm" label-width="120px">
          <el-form-item label="刷新频率">
            <el-select v-model="settingsForm.refreshInterval">
              <el-option label="5秒" :value="5000" />
              <el-option label="10秒" :value="10000" />
              <el-option label="30秒" :value="30000" />
              <el-option label="60秒" :value="60000" />
            </el-select>
          </el-form-item>
          <el-form-item label="报警阈值">
            <el-input-number
v-model="settingsForm.alarmThreshold" :min="1"
:max="50" :step="1"
/>
            <span style="margin-left: 8px">%</span>
          </el-form-item>
          <el-form-item label="显示数量">
            <el-input-number
v-model="settingsForm.displayCount" :min="10"
:max="100" :step="10"
/>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <CButton @click="settingsDialogVisible = false">取消</CButton>
        <CButton type="primary"
@click="saveSettings"
>
保存
</CButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import type { ProcessParameter } from '@/types/manufacturing'
  import CButton from '@/components/base/CButton.vue'
  import {
    RefreshRight,
    Setting,
    CircleCheckFilled,
    TrendCharts,
    ArrowDown,
    Edit,
    Clock,
    Download
  } from '@element-plus/icons-vue'

  interface Props {
    parameters: ProcessParameter[]
    loading?: boolean
  }

  interface Emits {
    (e: 'refresh'): void
    (e: 'edit-parameter', parameter: ProcessParameter): void
    (e: 'view-history', parameter: ProcessParameter): void
    (e: 'export-data', parameter: ProcessParameter): void
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false
  })

  const emit = defineEmits<Emits>()

  const trendDialogVisible = ref(false)
  const settingsDialogVisible = ref(false)
  const selectedParameter = ref<ProcessParameter | null>(null)

  const settingsForm = reactive({
    refreshInterval: 10000,
    alarmThreshold: 10,
    displayCount: 50
  })

  const getParameterStatusColor = (status: string): string => {
    const colorMap = {
      normal: '#67c23a',
      warning: '#e6a23c',
      alarm: '#f56c6c'
    }
    return colorMap[status as keyof typeof colorMap] || '#909399'
  }

  const getValueClass = (parameter: ProcessParameter): string => {
    if (parameter.status === 'alarm') return 'value-alarm'
    if (parameter.status === 'warning') return 'value-warning'
    return 'value-normal'
  }

  const getDeviationClass = (parameter: ProcessParameter): string => {
    const deviation = Math.abs(calculateDeviation(parameter))
    if (deviation > 10) return 'deviation-high'
    if (deviation > 5) return 'deviation-medium'
    return 'deviation-low'
  }

  const getStatusTagType = (status: string) => {
    const typeMap = {
      normal: 'success',
      warning: 'warning',
      alarm: 'danger'
    }
    return typeMap[status as keyof typeof typeMap] || 'info'
  }

  const getStatusText = (status: string): string => {
    const textMap = {
      normal: '正常',
      warning: '警告',
      alarm: '报警'
    }
    return textMap[status as keyof typeof textMap] || '未知'
  }

  const formatValue = (value: number, unit: string): string => {
    return `${value.toFixed(2)}${unit}`
  }

  const calculateDeviation = (parameter: ProcessParameter): number => {
    if (parameter.targetValue === 0) return 0
    return ((parameter.currentValue - parameter.targetValue) / parameter.targetValue) * 100
  }

  const refreshData = () => {
    emit('refresh')
  }

  const openSettingsDialog = () => {
    settingsDialogVisible.value = true
  }

  const saveSettings = () => {
    // 保存设置逻辑
    settingsDialogVisible.value = false
    ElMessage.success('设置已保存')
  }

  const showTrendDialog = (parameter: ProcessParameter) => {
    selectedParameter.value = parameter
    trendDialogVisible.value = true

    // 延迟渲染图表，确保DOM已更新
    nextTick(() => {
      renderTrendChart(parameter)
    })
  }

  const renderTrendChart = (parameter: ProcessParameter) => {
    // 这里集成ECharts或其他图表库
    console.log('渲染趋势图表:', parameter.name)
  }

  const editParameter = (parameter: ProcessParameter) => {
    emit('edit-parameter', parameter)
  }

  const viewHistory = (parameter: ProcessParameter) => {
    emit('view-history', parameter)
  }

  const exportData = (parameter: ProcessParameter) => {
    emit('export-data', parameter)
  }
</script>

<style lang="scss" scoped>
  .process-parameter-table {
    padding: var(--spacing-4);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-4);

      h3 {
        margin: 0;
        color: var(--color-text-primary);
      }
    }

    &__actions {
      display: flex;
      gap: var(--spacing-2);
    }
  }

  .parameter-table {
    --el-table-border-color: var(--color-border-light);
    --el-table-bg-color: var(--color-bg-primary);
    --el-table-tr-bg-color: var(--color-bg-primary);
    --el-table-expanded-cell-bg-color: var(--color-bg-secondary);
  }

  .parameter-name {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;

    .parameter-status-icon {
      font-size: var(--font-size-sm);
    }
  }

  .spec-range {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  .value-normal {
    font-weight: 600;
    color: var(--color-success);
  }

  .value-warning {
    font-weight: 600;
    color: var(--color-warning);
  }

  .value-alarm {
    font-weight: 600;
    color: var(--color-danger);
    animation: pulse 1s infinite;
  }

  .deviation-low {
    color: var(--color-success);
  }

  .deviation-medium {
    color: var(--color-warning);
  }

  .deviation-high {
    font-weight: 600;
    color: var(--color-danger);
  }

  .trend-chart {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }

  .trend-dialog-content {
    padding: var(--spacing-4);
  }

  .settings-dialog-content {
    padding: var(--spacing-4);
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.5;
    }
  }

  @media (width <= 768px) {
    .process-parameter-table {
      &__header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;
      }

      &__actions {
        justify-content: center;
      }
    }
  }
</style>
