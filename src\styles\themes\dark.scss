// IC封测CIM系统 - 深色主题
// 极简主义深色主题色彩系统

.dark-theme {
  // ===== 主色调 - 柔和蓝色系 =====
  --color-primary: #3b82f6;           // 主品牌色
  --color-primary-light: #60a5fa;     // 主色-浅
  --color-primary-dark: #2563eb;      // 主色-深
  --color-primary-hover: #60a5fa;     // 悬停色
  --color-primary-active: #2563eb;    // 激活色

  // ===== 功能色彩 - 深色优化版本 =====
  --color-success: #34d399;           // 成功色
  --color-success-light: #6ee7b7;
  --color-success-dark: #10b981;
  --color-warning: #fbbf24;           // 警告色
  --color-warning-light: #fcd34d;
  --color-warning-dark: #f59e0b;
  --color-error: #f87171;             // 错误色
  --color-error-light: #fca5a5;
  --color-error-dark: #ef4444;
  --color-info: #94a3b8;              // 信息色
  --color-info-light: #cbd5e1;
  --color-info-dark: #64748b;

  // ===== 中性色阶 - 深色模式文字 =====
  --color-text-primary: #f8fafc;      // 主要文字
  --color-text-secondary: #cbd5e1;    // 次要文字  
  --color-text-tertiary: #94a3b8;     // 第三级文字
  --color-text-disabled: #64748b;     // 禁用文字
  --color-text-white: #fff;        // 白色文字
  --color-text-inverse: #111827;      // 反色文字

  // ===== 背景色 - 深色背景系 =====
  --color-bg-primary: #0f172a;        // 主背景 (深蓝黑)
  --color-bg-secondary: #1e293b;      // 次要背景
  --color-bg-tertiary: #334155;       // 第三背景
  --color-bg-hover: #475569;          // 悬停背景
  --color-bg-active: #64748b;         // 激活背景
  --color-bg-disabled: #1e293b;       // 禁用背景
  --color-bg-mask: rgb(0 0 0 / 70%); // 遮罩背景

  // ===== 边框色 - 深色边框 =====
  --color-border-light: #334155;      // 轻边框
  --color-border-base: #475569;       // 基础边框  
  --color-border-dark: #64748b;       // 深边框
  --color-border-darker: #94a3b8;     // 更深边框
  --color-border-focus: #3b82f6;      // 焦点边框

  // ===== 阴影系统 - 深色阴影 =====
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 15%);
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 20%);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 30%), 0 1px 2px 0 rgb(0 0 0 / 15%);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 30%), 0 2px 4px -1px rgb(0 0 0 / 15%);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 30%), 0 4px 6px -2px rgb(0 0 0 / 15%);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 40%), 0 10px 10px -5px rgb(0 0 0 / 20%);

  // ===== 表格特定颜色 =====
  --color-table-header-bg: #1e293b;
  --color-table-border: #475569;
  --color-table-hover: #334155;
  --color-table-selected: #1e40af;

  // ===== 表单特定颜色 =====
  --color-input-bg: #1e293b;
  --color-input-border: #475569;
  --color-input-focus: #3b82f6;
  --color-input-placeholder: #94a3b8;

  // ===== 按钮特定颜色 =====
  --color-btn-secondary-bg: #1e293b;
  --color-btn-secondary-border: #475569;
  --color-btn-secondary-hover: #334155;

  // ===== 卡片特定颜色 =====
  --color-card-bg: #1e293b;
  --color-card-border: #475569;
  --color-card-shadow: var(--shadow-lg);

  // ===== 导航特定颜色 =====
  --color-nav-bg: #1e293b;
  --color-nav-item-hover: #334155;
  --color-nav-item-active: #1e40af;
  --color-nav-text: #cbd5e1;
  --color-nav-text-active: #60a5fa;

  // ===== 侧边栏特定颜色 =====
  --color-sidebar-bg: #1e293b;
  --color-sidebar-border: #475569;
  --color-sidebar-item-hover: #334155;
  --color-sidebar-item-active: #1e40af;

  // ===== IC封测专业色彩重新定义 =====
  --color-wafer: var(--color-wafer-dark);
  --color-die-pass: var(--color-die-pass-dark);
  --color-die-fail: var(--color-die-fail-dark);
}