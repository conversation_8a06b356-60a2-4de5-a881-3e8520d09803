# IC封测CIM系统数据标准和规范

## 1. 行业标准符合性

### 1.1 SEMI半导体标准
IC封测行业必须严格遵循SEMI（Semiconductor Equipment and Materials International）标准：

#### SECS/GEM协议标准
- **SEMI E4**: SECS-I通信标准
- **SEMI E5**: SECS-II消息协议
- **SEMI E30**: GEM通用设备模型
- **SEMI E37**: HSMS高速消息服务
- **SEMI E40**: 处理管理规范
- **SEMI E90**: 设备状态模型

```sql
-- SECS/GEM消息数据表设计
CREATE TABLE secs_gem_messages (
    message_id BIGINT PRIMARY KEY,
    equipment_id VARCHAR(20) NOT NULL COMMENT '设备ID',
    session_id INT NOT NULL COMMENT '会话ID',
    stream_function VARCHAR(10) NOT NULL COMMENT '消息类型(S1F1, S1F2等)',
    message_direction ENUM('HOST_TO_EQUIPMENT', 'EQUIPMENT_TO_HOST'),
    message_data JSON COMMENT '消息数据(JSON格式)',
    transaction_id BIGINT COMMENT '事务ID',
    message_timestamp TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3),
    INDEX idx_equipment_time (equipment_id, message_timestamp),
    INDEX idx_stream_function (stream_function, message_timestamp)
) COMMENT='SECS/GEM消息记录表';
```

#### STDF标准测试数据格式
- **STDF V4**: 标准测试数据文件格式
- 支持测试数据的标准化存储和交换

```sql
-- STDF标准测试数据表
CREATE TABLE stdf_test_data (
    test_data_id BIGINT PRIMARY KEY,
    lot_id VARCHAR(50) NOT NULL COMMENT 'Lot编号',
    wafer_id VARCHAR(50) COMMENT 'Wafer编号(CP测试)',
    die_x SMALLINT COMMENT 'Die X坐标',
    die_y SMALLINT COMMENT 'Die Y坐标',
    device_id VARCHAR(100) COMMENT '器件标识',
    test_head TINYINT COMMENT '测试头编号',
    site_number TINYINT COMMENT '测试Site编号',
    test_number INT NOT NULL COMMENT '测试项编号',
    test_name VARCHAR(100) NOT NULL COMMENT '测试项名称',
    test_result DECIMAL(15,6) COMMENT '测试结果',
    test_unit VARCHAR(20) COMMENT '测试单位',
    test_flag TINYINT COMMENT '测试标志(0-Pass, 1-Fail)',
    low_limit DECIMAL(15,6) COMMENT '下限',
    high_limit DECIMAL(15,6) COMMENT '上限',
    test_time TIMESTAMP(3) NOT NULL COMMENT '测试时间',
    temperature DECIMAL(5,2) COMMENT '测试温度',
    stdf_file_path VARCHAR(500) COMMENT 'STDF文件路径',
    INDEX idx_lot_wafer (lot_id, wafer_id),
    INDEX idx_test_time (test_time),
    INDEX idx_test_number (test_number, test_flag)
) COMMENT='STDF标准测试数据表';
```

### 1.2 JEDEC固体技术协会标准
- **JEDEC JESD30**: IC封装轮廓标准
- **JEDEC JESD51**: 热特性测试标准  
- **JEDEC JESD22**: 环境应力测试标准

```sql
-- JEDEC标准规格数据表
CREATE TABLE jedec_package_specs (
    spec_id VARCHAR(30) PRIMARY KEY,
    package_type VARCHAR(50) NOT NULL COMMENT '封装类型',
    jedec_outline VARCHAR(20) COMMENT 'JEDEC轮廓编号(如MO-220)',
    body_length DECIMAL(8,3) COMMENT '封装体长度(mm)',
    body_width DECIMAL(8,3) COMMENT '封装体宽度(mm)', 
    body_thickness DECIMAL(8,3) COMMENT '封装体厚度(mm)',
    lead_pitch DECIMAL(6,3) COMMENT '引脚间距(mm)',
    lead_count SMALLINT COMMENT '引脚数量',
    thermal_resistance DECIMAL(8,3) COMMENT '热阻值(°C/W)',
    moisture_level ENUM('1','2','2A','3','4','5','5A','6') COMMENT 'MSL湿敏等级',
    jedec_std_version VARCHAR(20) COMMENT 'JEDEC标准版本',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) COMMENT='JEDEC封装标准规格表';
```

### 1.3 IATF16949汽车行业质量标准
- **ISO/TS 16949:2016**: 汽车行业质量管理体系
- **AIAG核心工具**: APQP、PPAP、FMEA、MSA、SPC

```sql
-- IATF16949质量记录表
CREATE TABLE iatf16949_quality_records (
    record_id BIGINT PRIMARY KEY,
    document_type ENUM('APQP','PPAP','FMEA','MSA','SPC','CONTROL_PLAN') NOT NULL,
    product_id VARCHAR(50) NOT NULL COMMENT '产品编号',
    document_number VARCHAR(100) NOT NULL COMMENT '文档编号',
    revision VARCHAR(10) NOT NULL COMMENT '版本号',
    approval_status ENUM('DRAFT','REVIEW','APPROVED','OBSOLETE') DEFAULT 'DRAFT',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    reviewed_by VARCHAR(50) COMMENT '审核人',
    approved_by VARCHAR(50) COMMENT '批准人',
    effective_date DATE COMMENT '生效日期',
    document_content JSON COMMENT '文档内容(JSON格式)',
    attachment_files TEXT COMMENT '附件文件路径',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_product_type (product_id, document_type),
    INDEX idx_approval_status (approval_status, effective_date)
) COMMENT='IATF16949质量文档记录表';
```

### 1.4 AEC-Q100汽车电子可靠性标准
- **AEC-Q100**: 汽车IC可靠性认证标准
- **AEC-Q104**: 汽车多芯片模块可靠性标准

```sql
-- AEC-Q100可靠性测试记录表
CREATE TABLE aecq100_reliability_tests (
    test_id BIGINT PRIMARY KEY,
    product_id VARCHAR(50) NOT NULL COMMENT '产品编号',
    test_category ENUM(
        'HTOL',      -- 高温工作寿命测试
        'TC',        -- 温度循环测试  
        'HTS',       -- 高温存储测试
        'LTS',       -- 低温存储测试
        'THB',       -- 温湿度偏压测试
        'ESD_HBM',   -- 静电放电-人体模型
        'ESD_MM',    -- 静电放电-机器模型
        'ESD_CDM',   -- 静电放电-充电器件模型
        'LATCH_UP',  -- 闭锁效应测试
        'EM'         -- 电迁移测试
    ) NOT NULL,
    test_condition VARCHAR(200) COMMENT '测试条件',
    sample_size INT NOT NULL COMMENT '样本数量',
    test_duration_hours INT COMMENT '测试时长(小时)',
    temperature_celsius DECIMAL(5,1) COMMENT '测试温度',
    humidity_percent DECIMAL(4,1) COMMENT '湿度百分比',
    voltage_v DECIMAL(8,3) COMMENT '测试电压',
    pass_count INT COMMENT '通过数量',
    fail_count INT COMMENT '失效数量', 
    test_result ENUM('PASS','FAIL','ONGOING') COMMENT '测试结果',
    failure_analysis TEXT COMMENT '失效分析',
    test_start_time TIMESTAMP COMMENT '测试开始时间',
    test_end_time TIMESTAMP COMMENT '测试结束时间',
    INDEX idx_product_test (product_id, test_category),
    INDEX idx_test_result (test_result, test_end_time)
) COMMENT='AEC-Q100可靠性测试记录表';
```

## 2. 数据命名规范

### 2.1 表命名规范
- **前缀规范**: 使用模块前缀，如`ic_`表示IC封测专用表
- **分隔符**: 使用下划线`_`分隔单词
- **语言**: 使用英文命名，避免拼音
- **描述性**: 表名应能清楚表达表的用途

```sql
-- 正确的表命名示例
ic_product_specifications     -- IC产品规格表
ic_wafer_lot_tracking        -- 晶圆批次追踪表
ic_test_program_versions     -- 测试程序版本表
ic_package_bom_details       -- 封装BOM明细表

-- 错误的命名示例
tbl_product                  -- 缺少前缀，不够具体
ic产品规格                   -- 使用中文
icProductSpecs               -- 驼峰命名不符合规范
```

### 2.2 字段命名规范
- **主键**: 统一使用`表名单数_id`格式
- **外键**: 使用`关联表名单数_id`格式  
- **时间字段**: 使用`_time`或`_date`后缀
- **状态字段**: 使用`_status`或`_flag`后缀
- **数量字段**: 使用`_count`或`_quantity`后缀

```sql
-- 字段命名示例
CREATE TABLE ic_test_results (
    test_result_id BIGINT PRIMARY KEY,        -- 主键
    product_id VARCHAR(50),                   -- 外键
    wafer_id VARCHAR(50),                     -- 外键
    test_start_time TIMESTAMP,                -- 时间字段
    test_completion_time TIMESTAMP,           -- 时间字段
    test_status ENUM('PASS','FAIL'),         -- 状态字段
    die_count INT,                           -- 数量字段
    pass_count INT,                          -- 数量字段
    temperature_celsius DECIMAL(5,1),         -- 带单位的字段
    voltage_v DECIMAL(8,3)                   -- 带单位的字段
);
```

### 2.3 数据类型标准

#### 数值类型标准
```sql
-- 整数类型使用场景
TINYINT     -- 小范围整数(-128~127)，如状态标志
SMALLINT    -- 中等整数(-32768~32767)，如坐标、数量
INT         -- 标准整数，如ID、计数器
BIGINT      -- 大整数，如主键ID、时间戳

-- 小数类型使用场景  
DECIMAL(15,6)  -- 高精度测试数据，如电压、电流
DECIMAL(8,3)   -- 物理尺寸，如长宽高(mm)
DECIMAL(5,2)   -- 温度数据，如-55.25°C
DECIMAL(4,1)   -- 百分比数据，如良率99.9%
```

#### 字符类型标准
```sql
-- 字符类型使用场景
CHAR(10)          -- 固定长度编码，如产品编号
VARCHAR(50)       -- 变长短字符串，如名称、编号
VARCHAR(200)      -- 变长中等字符串，如描述
TEXT              -- 长文本，如备注、说明
JSON              -- JSON数据，如配置参数
```

#### 时间类型标准
```sql
-- 时间类型使用场景
DATE              -- 日期，如生效日期
TIME              -- 时间，如班次时间
DATETIME          -- 日期时间，如创建时间
TIMESTAMP         -- 时间戳，如最后更新时间
TIMESTAMP(3)      -- 毫秒级时间戳，如测试时间
```

## 3. 数据完整性约束

### 3.1 主键约束
- 所有表必须有主键
- 主键使用雪花算法生成的BIGINT类型
- 主键字段名统一为`表名单数_id`

### 3.2 外键约束
- 明确定义表间关系
- 使用CASCADE删除时需要谨慎
- 重要的关联关系必须建立外键约束

```sql
-- 外键约束示例
ALTER TABLE ic_test_results 
ADD CONSTRAINT fk_test_product 
FOREIGN KEY (product_id) REFERENCES ic_products(product_id)
ON DELETE RESTRICT ON UPDATE CASCADE;
```

### 3.3 CHECK约束
- 用于数据范围验证
- 枚举值验证
- 业务规则验证

```sql
-- CHECK约束示例
CREATE TABLE ic_temperature_test (
    test_id BIGINT PRIMARY KEY,
    temperature DECIMAL(5,1) CHECK (temperature BETWEEN -55.0 AND 200.0),
    test_result ENUM('PASS','FAIL') NOT NULL,
    voltage DECIMAL(8,3) CHECK (voltage >= 0),
    CONSTRAINT chk_temperature_range 
        CHECK (temperature IN (-40, 25, 85, 125, 150) OR 
               test_type = 'CUSTOM_TEMPERATURE')
);
```

## 4. 索引设计规范

### 4.1 主键索引
- 自动创建，无需额外设计
- 使用雪花算法保证全局唯一性和趋势递增

### 4.2 唯一索引
- 业务唯一性约束
- 防止重复数据

```sql
-- 唯一索引示例
CREATE UNIQUE INDEX uk_lot_wafer_die 
ON ic_wafer_map (lot_id, wafer_id, die_x, die_y);
```

### 4.3 复合索引
- 多字段组合查询优化
- 字段顺序按查询频率和选择性排列

```sql
-- 复合索引示例
CREATE INDEX idx_test_multi 
ON ic_test_data (product_id, test_time, temperature, test_result);
```

### 4.4 函数索引
- 支持复杂查询条件优化

```sql
-- 函数索引示例
CREATE INDEX idx_test_date 
ON ic_test_data ((DATE(test_time)));
```

## 5. 数据安全规范

### 5.1 敏感数据加密
- 客户专有数据必须加密存储
- 使用AES-256加密算法

```sql
-- 敏感数据加密字段
CREATE TABLE ic_customer_products (
    product_id BIGINT PRIMARY KEY,
    customer_id VARCHAR(50) NOT NULL,
    product_name_encrypted VARBINARY(256) COMMENT '加密的产品名称',
    specification_encrypted TEXT COMMENT '加密的产品规格',
    encryption_key_id VARCHAR(32) COMMENT '加密密钥ID',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5.2 数据脱敏规范
- 非生产环境必须进行数据脱敏
- 保持数据格式和统计特性

```sql
-- 数据脱敏示例
UPDATE ic_customer_products_test 
SET customer_name = CONCAT('CUSTOMER_', LPAD(ROW_NUMBER() OVER(), 4, '0')),
    contact_email = CONCAT('test', FLOOR(RAND() * 10000), '@example.com');
```

## 6. 数据质量控制

### 6.1 数据验证规则
- 必填字段验证
- 数据格式验证
- 数据范围验证
- 业务逻辑验证

```sql
-- 数据验证触发器示例
DELIMITER //
CREATE TRIGGER tr_validate_test_data 
BEFORE INSERT ON ic_test_data
FOR EACH ROW
BEGIN
    -- 验证测试结果在合理范围内
    IF NEW.test_result < NEW.low_limit OR NEW.test_result > NEW.high_limit THEN
        SET NEW.test_flag = 1; -- 标记为失败
    ELSE
        SET NEW.test_flag = 0; -- 标记为通过
    END IF;
    
    -- 验证温度范围
    IF NEW.temperature NOT IN (-40, 25, 85, 125, 150) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '无效的测试温度';
    END IF;
END//
DELIMITER ;
```

### 6.2 数据一致性检查
- 定期执行数据一致性检查
- 自动修复或报告数据异常

```sql
-- 数据一致性检查存储过程
DELIMITER //
CREATE PROCEDURE sp_check_data_consistency()
BEGIN
    DECLARE inconsistent_count INT DEFAULT 0;
    
    -- 检查BOM数据一致性
    SELECT COUNT(*) INTO inconsistent_count
    FROM ic_bom_details b
    LEFT JOIN ic_materials m ON b.material_id = m.material_id
    WHERE m.material_id IS NULL;
    
    IF inconsistent_count > 0 THEN
        INSERT INTO system_alerts (alert_type, alert_message, alert_time)
        VALUES ('DATA_CONSISTENCY', 
                CONCAT('发现', inconsistent_count, '条BOM数据不一致'), 
                NOW());
    END IF;
END//
DELIMITER ;
```

## 7. 版本控制与变更管理

### 7.1 数据库版本控制
- 使用Flyway或Liquibase进行数据库版本管理
- 所有结构变更必须有脚本记录

```sql
-- 版本控制信息表
CREATE TABLE database_version_history (
    version_id INT PRIMARY KEY AUTO_INCREMENT,
    version_number VARCHAR(20) NOT NULL COMMENT '版本号',
    description TEXT COMMENT '变更描述',
    script_file VARCHAR(200) COMMENT '脚本文件名',
    applied_by VARCHAR(50) COMMENT '执行人',
    applied_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    success BOOLEAN DEFAULT TRUE COMMENT '执行是否成功'
);
```

### 7.2 变更审批流程
- 所有生产环境变更必须经过审批
- 变更前必须备份
- 变更后必须验证

---

*本文档规定了IC封测CIM系统的数据标准和规范，确保系统符合行业标准和最佳实践。*
*版本: V1.0 | 更新时间: 2025年*