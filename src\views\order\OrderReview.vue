<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
  import {
    Check,
    Close,
    ArrowRight,
    Warning,
    InfoFilled,
    Document,
    Clock,
    User,
    Star
  } from '@element-plus/icons-vue'

  // 引入 OSAT 行业相关类型定义
  import {
    OrderReview,
    OrderReviewTask,
    OrderReviewStatus,
    OrderReviewDepartment,
    ReviewResult,
    RiskLevel,
    RiskItem,
    ReviewDecision,
    TechnicalReviewDetail,
    CapacityReviewDetail,
    QualityReviewDetail,
    SupplyChainReviewDetail,
    FinanceReviewDetail
  } from '@/types/order'
  import { useOrderStore } from '@/stores/order'

  // 路由和状态管理
  const router = useRouter()
  const route = useRoute()
  const orderStore = useOrderStore()

  // 页面响应式状态
  const loading = ref(false)
  const currentOrderReview = ref<OrderReview | null>(null)
  const activeTab = ref<OrderReviewDepartment>(OrderReviewDepartment.TECHNICAL)
  const showRiskDialog = ref(false)
  const showDecisionDialog = ref(false)

  // 表单引用
  const technicalFormRef = ref()
  const capacityFormRef = ref()
  const qualityFormRef = ref()
  const supplyChainFormRef = ref()
  const financeFormRef = ref()

  // 评审表单数据
  const technicalForm = reactive<TechnicalReviewDetail>({
    processCompatibility: ReviewResult.PASS,
    equipmentCapability: ReviewResult.PASS,
    dftRequirement: ReviewResult.PASS,
    testSolution: ReviewResult.PASS,
    yieldFeasibility: ReviewResult.PASS,
    riskLevel: RiskLevel.LOW
  })

  const capacityForm = reactive<CapacityReviewDetail>({
    cpLineCapacity: ReviewResult.PASS,
    cpUtilization: 0,
    assemblyCapacity: ReviewResult.PASS,
    assemblyUtilization: 0,
    ftLineCapacity: ReviewResult.PASS,
    ftUtilization: 0,
    equipmentScheduling: ReviewResult.PASS,
    capacityRisk: RiskLevel.LOW
  })

  const qualityForm = reactive<QualityReviewDetail>({
    customerStandards: ReviewResult.PASS,
    iatf16949Compliance: ReviewResult.PASS,
    reliabilityRequirement: ReviewResult.PASS,
    testCapability: ReviewResult.PASS,
    qualityRisk: RiskLevel.LOW
  })

  const supplyChainForm = reactive<SupplyChainReviewDetail>({
    waferSupply: ReviewResult.PASS,
    waferLeadTime: 0,
    substrateSupply: ReviewResult.PASS,
    substrateLeadTime: 0,
    materialBom: ReviewResult.PASS,
    inventoryBuffer: 0,
    supplyRisk: RiskLevel.LOW,
    totalLeadTime: 0
  })

  const financeForm = reactive<FinanceReviewDetail>({
    creditRisk: ReviewResult.PASS,
    pricingReasonable: ReviewResult.PASS,
    margin: 0,
    paymentTerms: ReviewResult.PASS,
    exchangeRateRisk: ReviewResult.PASS,
    cashFlowImpact: 0,
    financialRisk: RiskLevel.LOW,
    profitabilityAssessment: 'good'
  })

  // 页面加载初始化
  onMounted(async () => {
    await loadOrderReviewData()
  })

  const loadOrderReviewData = async () => {
    loading.value = true
    try {
      const orderId = route.params.orderId as string
      if (!orderId) {
        ElMessage.error('订单ID缺失')
        return
      }

      // 获取订单评审数据（模拟数据）
      currentOrderReview.value = await getMockOrderReviewData(orderId)
    } catch (error) {
      ElMessage.error('加载订单评审信息失败')
    } finally {
      loading.value = false
    }
  }

  // 获取模拟订单评审数据
  const getMockOrderReviewData = async (orderId: string): Promise<OrderReview> => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    return {
      id: 'review-' + orderId,
      orderId: orderId,
      order: {
        id: orderId,
        orderNumber: 'ORD-20240101-001',
        customerId: 'CUST001',
        customer: {
          id: 'CUST001',
          name: '某汽车电子公司',
          code: 'AUTO_ELEC_001',
          contact: {
            name: '张工程师',
            phone: '13800138000',
            email: '<EMAIL>'
          }
        },
        productInfo: {
          productName: 'MCU主控芯片',
          productCode: 'MCU-ARM-001',
          packageType: 'QFP',
          quantity: 100,
          waferSize: 8,
          dieSize: '5.2x5.2mm',
          leadCount: 144,
          specifications: 'ARM Cortex-M4 32位微控制器，工作频率180MHz，1MB Flash，256KB RAM'
        },
        pricing: {
          unitPrice: 25.6,
          totalAmount: 2560000,
          currency: 'CNY',
          paymentTerms: '30天付款'
        },
        schedule: {
          orderDate: '2024-01-01',
          deliveryDate: '2024-03-15',
          confirmedDate: '2024-01-05'
        },
        status: 'confirmed',
        priority: 'high',
        progress: {
          overall: 0,
          cpTesting: 0,
          assembly: 0,
          ftTesting: 0,
          packaging: 0
        },
        qualityInfo: {
          yieldRequirement: 99.5,
          qualityLevel: 'A' as const
        },
        createdAt: '2024-01-01T08:00:00Z',
        updatedAt: '2024-01-05T14:30:00Z',
        createdBy: 'sales001'
      } as any,
      reviewStatus: OrderReviewStatus.IN_PROGRESS,
      initiatedBy: 'manager001',
      initiatedAt: '2024-01-05T15:00:00Z',
      reviewTasks: [
        {
          id: 'task-technical',
          orderId: orderId,
          department: OrderReviewDepartment.TECHNICAL,
          departmentName: '技术部',
          assignedTo: '李技术经理',
          status: 'pending',
          priority: 'high',
          dueDate: '2024-01-08',
          overallComments: ''
        },
        {
          id: 'task-capacity',
          orderId: orderId,
          department: OrderReviewDepartment.CAPACITY,
          departmentName: '产能部',
          assignedTo: '王产能经理',
          status: 'pending',
          priority: 'high',
          dueDate: '2024-01-08',
          overallComments: ''
        },
        {
          id: 'task-quality',
          orderId: orderId,
          department: OrderReviewDepartment.QUALITY,
          departmentName: '质量部',
          assignedTo: '赵质量经理',
          status: 'pending',
          priority: 'high',
          dueDate: '2024-01-08',
          overallComments: ''
        },
        {
          id: 'task-supply-chain',
          orderId: orderId,
          department: OrderReviewDepartment.SUPPLY_CHAIN,
          departmentName: '供应链部',
          assignedTo: '钱供应链经理',
          status: 'pending',
          priority: 'high',
          dueDate: '2024-01-08',
          overallComments: ''
        },
        {
          id: 'task-finance',
          orderId: orderId,
          department: OrderReviewDepartment.FINANCE,
          departmentName: '财务部',
          assignedTo: '孙财务经理',
          status: 'pending',
          priority: 'high',
          dueDate: '2024-01-08',
          overallComments: ''
        }
      ] as OrderReviewTask[],
      riskAssessment: {
        overallRisk: RiskLevel.MEDIUM,
        riskItems: [
          {
            id: 'risk-001',
            category: OrderReviewDepartment.TECHNICAL,
            riskType: '工艺风险',
            description: '新工艺需要验证，可能影响良率',
            probability: 30,
            impact: RiskLevel.MEDIUM,
            mitigation: '提前进行工艺验证和小批试产',
            responsible: '李技术经理',
            status: 'identified'
          },
          {
            id: 'risk-002',
            category: OrderReviewDepartment.CAPACITY,
            riskType: '产能风险',
            description: 'CP测试设备利用率较高，可能影响交期',
            probability: 40,
            impact: RiskLevel.MEDIUM,
            mitigation: '安排夜班生产，增加设备利用时间',
            responsible: '王产能经理',
            status: 'identified'
          }
        ] as RiskItem[],
        riskMatrix: [
          { department: OrderReviewDepartment.TECHNICAL, riskLevel: RiskLevel.MEDIUM, score: 6 },
          { department: OrderReviewDepartment.CAPACITY, riskLevel: RiskLevel.MEDIUM, score: 7 },
          { department: OrderReviewDepartment.QUALITY, riskLevel: RiskLevel.LOW, score: 3 },
          { department: OrderReviewDepartment.SUPPLY_CHAIN, riskLevel: RiskLevel.LOW, score: 4 },
          { department: OrderReviewDepartment.FINANCE, riskLevel: RiskLevel.LOW, score: 2 }
        ]
      },
      progress: {
        completedTasks: 0,
        totalTasks: 5,
        percentage: 0,
        averageScore: 0,
        criticalIssues: 2
      },
      timeline: [
        {
          event: '订单评审启动',
          timestamp: '2024-01-05T15:00:00Z',
          actor: 'manager001',
          description: '订单确认后自动启动评审流程',
          category: 'milestone'
        },
        {
          event: '分配评审任务',
          timestamp: '2024-01-05T15:05:00Z',
          actor: 'system',
          description: '已分配给各部门经理进行评审',
          category: 'task'
        }
      ],
      updatedAt: '2024-01-05T15:05:00Z'
    }
  }

  // 获取部门中文名称
  const getDepartmentName = (department: OrderReviewDepartment): string => {
    const departmentNames = {
      [OrderReviewDepartment.TECHNICAL]: '技术部',
      [OrderReviewDepartment.CAPACITY]: '产能部',
      [OrderReviewDepartment.QUALITY]: '质量部',
      [OrderReviewDepartment.SUPPLY_CHAIN]: '供应链部',
      [OrderReviewDepartment.FINANCE]: '财务部'
    }
    return departmentNames[department]
  }

  // 获取评审结果标签类型
  const getReviewResultType = (result: ReviewResult): string => {
    const types = {
      [ReviewResult.PASS]: 'success',
      [ReviewResult.CONDITIONAL_PASS]: 'warning',
      [ReviewResult.FAIL]: 'danger'
    }
    return types[result]
  }

  // 获取风险等级标签类型
  const getRiskLevelType = (level: RiskLevel): string => {
    const types = {
      [RiskLevel.LOW]: 'success',
      [RiskLevel.MEDIUM]: 'warning',
      [RiskLevel.HIGH]: 'danger',
      [RiskLevel.CRITICAL]: 'danger'
    }
    return types[level]
  }

  // 提交部门评审
  const submitDepartmentReview = async (department: OrderReviewDepartment) => {
    try {
      loading.value = true

      // 模拟提交评审
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新任务状态
      const task = currentOrderReview.value?.reviewTasks.find(t => t.department === department)
      if (task) {
        task.status = 'completed'
        task.completedAt = new Date().toISOString()

        // 根据部门更新对应的评审详情
        switch (department) {
          case OrderReviewDepartment.TECHNICAL:
            task.technicalDetail = { ...technicalForm }
            break
          case OrderReviewDepartment.CAPACITY:
            task.capacityDetail = { ...capacityForm }
            break
          case OrderReviewDepartment.QUALITY:
            task.qualityDetail = { ...qualityForm }
            break
          case OrderReviewDepartment.SUPPLY_CHAIN:
            task.supplyChainDetail = { ...supplyChainForm }
            break
          case OrderReviewDepartment.FINANCE:
            task.financeDetail = { ...financeForm }
            break
        }
      }

      // 更新进度
      if (currentOrderReview.value) {
        const completedTasks = currentOrderReview.value.reviewTasks.filter(
          t => t.status === 'completed'
        ).length
        currentOrderReview.value.progress.completedTasks = completedTasks
        currentOrderReview.value.progress.percentage = Math.round(
          (completedTasks / currentOrderReview.value.progress.totalTasks) * 100
        )
      }

      ElMessage.success(`${getDepartmentName(department)}评审提交成功`)

      // 添加时间线记录
      if (currentOrderReview.value) {
        currentOrderReview.value.timeline.push({
          event: `${getDepartmentName(department)}评审完成`,
          timestamp: new Date().toISOString(),
          actor: '当前用户',
          description: `${getDepartmentName(department)}评审已完成并提交`,
          category: 'task'
        })
      }
    } catch (error) {
      ElMessage.error(`${getDepartmentName(department)}评审提交失败`)
    } finally {
      loading.value = false
    }
  }

  // 最终决策
  const makeDecision = async (decision: 'approved' | 'conditionally_approved' | 'rejected') => {
    try {
      loading.value = true

      // 模拟决策处理
      await new Promise(resolve => setTimeout(resolve, 1000))

      if (currentOrderReview.value) {
        currentOrderReview.value.reviewStatus =
          decision === 'approved'
            ? OrderReviewStatus.APPROVED
            : decision === 'conditionally_approved'
              ? OrderReviewStatus.CONDITIONALLY_APPROVED
              : OrderReviewStatus.REJECTED

        currentOrderReview.value.completedAt = new Date().toISOString()

        // 添加决策记录
        currentOrderReview.value.decision = {
          id: 'decision-' + Date.now(),
          orderId: currentOrderReview.value.orderId,
          decisionBy: '总经理',
          decisionAt: new Date().toISOString(),
          decision: decision,
          overallRisk: currentOrderReview.value.riskAssessment.overallRisk,
          businessJustification:
            decision === 'approved'
              ? '各项评审通过，风险可控'
              : decision === 'conditionally_approved'
                ? '有条件批准，需要关注风险项'
                : '风险过高，不建议接单',
          nextActions: [],
          reviewNotes: '评审完成'
        } as ReviewDecision

        // 添加时间线记录
        currentOrderReview.value.timeline.push({
          event: '订单评审决策',
          timestamp: new Date().toISOString(),
          actor: '总经理',
          description: `订单评审决策：${
            decision === 'approved'
              ? '批准'
              : decision === 'conditionally_approved'
                ? '有条件批准'
                : '拒绝'
          }`,
          category: 'decision'
        })
      }

      const statusText =
        decision === 'approved'
          ? '批准'
          : decision === 'conditionally_approved'
            ? '有条件批准'
            : '拒绝'
      ElMessage.success(`订单评审已${statusText}`)

      showDecisionDialog.value = false
    } catch (error) {
      ElMessage.error('订单决策失败')
    } finally {
      loading.value = false
    }
  }

  // 计算评审进度
  const reviewProgress = computed(() => {
    if (!currentOrderReview.value) return 0
    return currentOrderReview.value.progress.percentage
  })

  // 检查是否可以做最终决策
  const canMakeDecision = computed(() => {
    if (!currentOrderReview.value) return false
    return (
      currentOrderReview.value.progress.completedTasks ===
      currentOrderReview.value.progress.totalTasks
    )
  })

  // 获取当前部门任务
  const getCurrentTask = (department: OrderReviewDepartment) => {
    return currentOrderReview.value?.reviewTasks.find(t => t.department === department)
  }
</script>

<template>
  <div
v-loading="loading" class="order-review-container"
>
    <!-- 订单基本信息卡片 -->
    <el-card class="order-info-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>订单评审 - {{ currentOrderReview?.order.orderNumber }}</h3>
            <el-tag
              size="large"
              :type="
                currentOrderReview?.reviewStatus === OrderReviewStatus.PENDING
                  ? 'info'
                  : currentOrderReview?.reviewStatus === OrderReviewStatus.IN_PROGRESS
                    ? 'warning'
                    : currentOrderReview?.reviewStatus === OrderReviewStatus.APPROVED
                      ? 'success'
                      : currentOrderReview?.reviewStatus ===
                        OrderReviewStatus.CONDITIONALLY_APPROVED
                        ? 'warning'
                        : 'danger'
              "
            >
              <el-icon><InfoFilled /></el-icon>
              {{
                currentOrderReview?.reviewStatus === OrderReviewStatus.PENDING
                  ? '待评审'
                  : currentOrderReview?.reviewStatus === OrderReviewStatus.IN_PROGRESS
                    ? '评审中'
                    : currentOrderReview?.reviewStatus === OrderReviewStatus.APPROVED
                      ? '已批准'
                      : currentOrderReview?.reviewStatus ===
                        OrderReviewStatus.CONDITIONALLY_APPROVED
                        ? '有条件批准'
                        : '已拒绝'
              }}
            </el-tag>
          </div>
          <div class="header-right">
            <el-button
type="primary" @click="showRiskDialog = true"
:icon="Warning"
>
              风险评估
            </el-button>
            <el-button
              v-if="canMakeDecision"
              type="success"
              :icon="Check"
              @click="showDecisionDialog = true"
            >
              最终决策
            </el-button>
          </div>
        </div>
      </template>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-descriptions title="订单信息" :column="1" border>
            <el-descriptions-item label="客户名称">
              <el-text strong>
                {{ currentOrderReview?.order.customer.name }}
              </el-text>
            </el-descriptions-item>
            <el-descriptions-item label="产品名称">
              {{ currentOrderReview?.order.productInfo.productName }}
            </el-descriptions-item>
            <el-descriptions-item label="产品代码">
              <el-tag>{{ currentOrderReview?.order.productInfo.productCode }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="封装类型">
              <el-tag type="info">
                {{ currentOrderReview?.order.productInfo.packageType }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="数量">
              <el-text strong>{{ currentOrderReview?.order.productInfo.quantity }} K pcs</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="交期">
              <el-text
                :type="
                  new Date(currentOrderReview?.order.schedule.deliveryDate || '') < new Date()
                    ? 'danger'
                    : 'success'
                "
              >
                {{ currentOrderReview?.order.schedule.deliveryDate }}
              </el-text>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="12">
          <el-descriptions title="技术规格" :column="1" border>
            <el-descriptions-item label="晶圆尺寸">
              {{ currentOrderReview?.order.productInfo.waferSize }}"
            </el-descriptions-item>
            <el-descriptions-item label="芯片尺寸">
              {{ currentOrderReview?.order.productInfo.dieSize }}
            </el-descriptions-item>
            <el-descriptions-item label="引脚数量">
              {{ currentOrderReview?.order.productInfo.leadCount }} pins
            </el-descriptions-item>
            <el-descriptions-item label="良率要求">
              <el-text strong>
                {{ currentOrderReview?.order.qualityInfo.yieldRequirement }}%
              </el-text>
            </el-descriptions-item>
            <el-descriptions-item label="质量等级">
              <el-tag
                :type="
                  currentOrderReview?.order.qualityInfo.qualityLevel === 'A' ? 'success' : 'warning'
                "
              >
                {{ currentOrderReview?.order.qualityInfo.qualityLevel }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="订单金额">
              <el-text strong style="color: var(--el-color-primary)">
                ¥{{ (currentOrderReview?.order.pricing.totalAmount || 0).toLocaleString() }}
              </el-text>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </el-card>

    <!-- 评审进度卡片 -->
    <el-card class="review-progress-card">
      <template #header>
        <div class="progress-header">
          <span>评审进度</span>
          <el-text>
            {{ currentOrderReview?.progress.completedTasks }}/{{
              currentOrderReview?.progress.totalTasks
            }}
            已完成
          </el-text>
        </div>
      </template>

      <el-steps
:active="Math.floor(reviewProgress / 20)" finish-status="success"
align-center
>
        <el-step
          v-for="task in currentOrderReview?.reviewTasks"
          :key="task.id"
          :title="task.departmentName"
          :description="task.assignedTo"
          :status="
            task.status === 'completed'
              ? 'success'
              : task.status === 'in_progress'
                ? 'process'
                : 'wait'
          "
          :icon="task.status === 'completed' ? Check : task.status === 'in_progress' ? Clock : User"
        />
      </el-steps>

      <div class="progress-details">
        <el-progress
          :percentage="reviewProgress"
          :status="reviewProgress === 100 ? 'success' : 'warning'"
          striped
          striped-flow
        />
        <div class="progress-stats">
          <el-statistic
            title="平均评分"
            :value="currentOrderReview?.progress.averageScore || 0"
            :precision="1"
          />
          <el-statistic
            title="关键问题"
            :value="currentOrderReview?.progress.criticalIssues || 0"
          />
          <el-statistic
            title="整体风险"
            :value="currentOrderReview?.riskAssessment.overallRisk || 'low'"
            :value-style="{
              color:
                getRiskLevelType(
                  currentOrderReview?.riskAssessment.overallRisk || RiskLevel.LOW
                ) === 'success'
                  ? '#67c23a'
                  : getRiskLevelType(
                    currentOrderReview?.riskAssessment.overallRisk || RiskLevel.LOW
                  ) === 'warning'
                    ? '#e6a23c'
                    : '#f56c6c'
            }"
          />
        </div>
      </div>
    </el-card>

    <!-- 评审标签页 -->
    <el-card class="review-tabs-card">
      <el-tabs
        v-model="activeTab"
        type="border-card"
        @tab-change="name => (activeTab = name as OrderReviewDepartment)"
      >
        <!-- 技术评审 -->
        <el-tab-pane
          :label="getDepartmentName(OrderReviewDepartment.TECHNICAL)"
          :name="OrderReviewDepartment.TECHNICAL"
        >
          <div class="department-review">
            <div class="review-header">
              <h4>技术评审</h4>
              <div class="review-status">
                <el-text>
                  负责人: {{ getCurrentTask(OrderReviewDepartment.TECHNICAL)?.assignedTo }}
                </el-text>
                <el-tag
                  :type="
                    getCurrentTask(OrderReviewDepartment.TECHNICAL)?.status === 'completed'
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{
                    getCurrentTask(OrderReviewDepartment.TECHNICAL)?.status === 'completed'
                      ? '已完成'
                      : '待完成'
                  }}
                </el-tag>
              </div>
            </div>

            <el-form
              ref="technicalFormRef"
              :model="technicalForm"
              label-width="140px"
              :disabled="getCurrentTask(OrderReviewDepartment.TECHNICAL)?.status === 'completed'"
            >
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="工艺兼容性">
                    <el-select
                      v-model="technicalForm.processCompatibility"
                      placeholder="评估工艺兼容性"
                    >
                      <el-option :value="ReviewResult.PASS" label="通过" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="有条件通过" />
                      <el-option :value="ReviewResult.FAIL" label="不通过" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备能力">
                    <el-select
                      v-model="technicalForm.equipmentCapability"
                      placeholder="评估设备能力"
                    >
                      <el-option :value="ReviewResult.PASS" label="通过" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="有条件通过" />
                      <el-option :value="ReviewResult.FAIL" label="不通过" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="DFT要求">
                    <el-select v-model="technicalForm.dftRequirement" placeholder="评估DFT要求">
                      <el-option :value="ReviewResult.PASS" label="满足" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="部分满足" />
                      <el-option :value="ReviewResult.FAIL" label="不满足" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="测试方案">
                    <el-select v-model="technicalForm.testSolution" placeholder="评估测试方案">
                      <el-option :value="ReviewResult.PASS" label="可行" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="需要调整" />
                      <el-option :value="ReviewResult.FAIL" label="不可行" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="良率可行性">
                    <el-select
                      v-model="technicalForm.yieldFeasibility"
                      placeholder="评估良率可行性"
                    >
                      <el-option :value="ReviewResult.PASS" label="可达成" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="有挑战" />
                      <el-option :value="ReviewResult.FAIL" label="无法达成" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="风险等级">
                    <el-select v-model="technicalForm.riskLevel" placeholder="评估技术风险">
                      <el-option :value="RiskLevel.LOW" label="低风险" />
                      <el-option :value="RiskLevel.MEDIUM" label="中风险" />
                      <el-option :value="RiskLevel.HIGH" label="高风险" />
                      <el-option :value="RiskLevel.CRITICAL" label="严重风险" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="开发时间(天)">
                    <el-input-number
                      v-model="technicalForm.estimatedDevelopmentTime"
                      :min="0"
                      placeholder="预计开发时间"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="资本投资(万元)">
                    <el-input-number
                      v-model="technicalForm.requiredCapitalInvestment"
                      :min="0"
                      :step="10"
                      placeholder="需要的资本投资"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="工艺备注">
                <el-input
                  v-model="technicalForm.processNotes"
                  type="textarea"
                  :rows="2"
                  placeholder="工艺兼容性备注"
                />
              </el-form-item>

              <el-form-item label="设备备注">
                <el-input
                  v-model="technicalForm.equipmentNotes"
                  type="textarea"
                  :rows="2"
                  placeholder="设备能力备注"
                />
              </el-form-item>

              <el-form-item
                v-if="getCurrentTask(OrderReviewDepartment.TECHNICAL)?.status !== 'completed'"
              >
                <el-button
                  type="primary"
                  :loading="loading"
                  @click="submitDepartmentReview(OrderReviewDepartment.TECHNICAL)"
                >
                  提交技术评审
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 产能评审 -->
        <el-tab-pane
          :label="getDepartmentName(OrderReviewDepartment.CAPACITY)"
          :name="OrderReviewDepartment.CAPACITY"
        >
          <div class="department-review">
            <div class="review-header">
              <h4>产能评审</h4>
              <div class="review-status">
                <el-text>
                  负责人: {{ getCurrentTask(OrderReviewDepartment.CAPACITY)?.assignedTo }}
                </el-text>
                <el-tag
                  :type="
                    getCurrentTask(OrderReviewDepartment.CAPACITY)?.status === 'completed'
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{
                    getCurrentTask(OrderReviewDepartment.CAPACITY)?.status === 'completed'
                      ? '已完成'
                      : '待完成'
                  }}
                </el-tag>
              </div>
            </div>

            <el-form
              ref="capacityFormRef"
              :model="capacityForm"
              label-width="140px"
              :disabled="getCurrentTask(OrderReviewDepartment.CAPACITY)?.status === 'completed'"
            >
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="CP产线产能">
                    <el-select
                      v-model="capacityForm.cpLineCapacity"
                      placeholder="评估CP测试产线产能"
                    >
                      <el-option :value="ReviewResult.PASS" label="充足" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="紧张" />
                      <el-option :value="ReviewResult.FAIL" label="不足" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="CP利用率(%)">
                    <el-slider v-model="capacityForm.cpUtilization" :max="100" show-input />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="封装产线产能">
                    <el-select
                      v-model="capacityForm.assemblyCapacity"
                      placeholder="评估封装产线产能"
                    >
                      <el-option :value="ReviewResult.PASS" label="充足" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="紧张" />
                      <el-option :value="ReviewResult.FAIL" label="不足" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="封装利用率(%)">
                    <el-slider v-model="capacityForm.assemblyUtilization" :max="100" show-input />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="FT产线产能">
                    <el-select
                      v-model="capacityForm.ftLineCapacity"
                      placeholder="评估FT测试产线产能"
                    >
                      <el-option :value="ReviewResult.PASS" label="充足" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="紧张" />
                      <el-option :value="ReviewResult.FAIL" label="不足" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="FT利用率(%)">
                    <el-slider v-model="capacityForm.ftUtilization" :max="100" show-input />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="设备排程">
                    <el-select
                      v-model="capacityForm.equipmentScheduling"
                      placeholder="评估设备排程"
                    >
                      <el-option :value="ReviewResult.PASS" label="可排程" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="需调整" />
                      <el-option :value="ReviewResult.FAIL" label="无法排程" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="产能风险">
                    <el-select v-model="capacityForm.capacityRisk" placeholder="评估产能风险">
                      <el-option :value="RiskLevel.LOW" label="低风险" />
                      <el-option :value="RiskLevel.MEDIUM" label="中风险" />
                      <el-option :value="RiskLevel.HIGH" label="高风险" />
                      <el-option :value="RiskLevel.CRITICAL" label="严重风险" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="推荐生产窗口">
                <el-input
                  v-model="capacityForm.recommendedProductionWindow"
                  placeholder="推荐的生产时间窗口"
                />
              </el-form-item>

              <el-form-item label="产能备注">
                <el-input
                  v-model="capacityForm.cpNotes"
                  type="textarea"
                  :rows="3"
                  placeholder="产能评审详细说明"
                />
              </el-form-item>

              <el-form-item
                v-if="getCurrentTask(OrderReviewDepartment.CAPACITY)?.status !== 'completed'"
              >
                <el-button
                  type="primary"
                  :loading="loading"
                  @click="submitDepartmentReview(OrderReviewDepartment.CAPACITY)"
                >
                  提交产能评审
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 质量评审 -->
        <el-tab-pane
          :label="getDepartmentName(OrderReviewDepartment.QUALITY)"
          :name="OrderReviewDepartment.QUALITY"
        >
          <div class="department-review">
            <div class="review-header">
              <h4>质量评审</h4>
              <div class="review-status">
                <el-text>
                  负责人: {{ getCurrentTask(OrderReviewDepartment.QUALITY)?.assignedTo }}
                </el-text>
                <el-tag
                  :type="
                    getCurrentTask(OrderReviewDepartment.QUALITY)?.status === 'completed'
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{
                    getCurrentTask(OrderReviewDepartment.QUALITY)?.status === 'completed'
                      ? '已完成'
                      : '待完成'
                  }}
                </el-tag>
              </div>
            </div>

            <el-form
              ref="qualityFormRef"
              :model="qualityForm"
              label-width="140px"
              :disabled="getCurrentTask(OrderReviewDepartment.QUALITY)?.status === 'completed'"
            >
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="客户标准">
                    <el-select
                      v-model="qualityForm.customerStandards"
                      placeholder="评估客户质量标准"
                    >
                      <el-option :value="ReviewResult.PASS" label="完全符合" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="基本符合" />
                      <el-option :value="ReviewResult.FAIL" label="不符合" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="IATF16949合规">
                    <el-select
                      v-model="qualityForm.iatf16949Compliance"
                      placeholder="评估IATF16949合规性"
                    >
                      <el-option :value="ReviewResult.PASS" label="完全合规" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="基本合规" />
                      <el-option :value="ReviewResult.FAIL" label="不合规" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="可靠性要求">
                    <el-select
                      v-model="qualityForm.reliabilityRequirement"
                      placeholder="评估可靠性要求"
                    >
                      <el-option :value="ReviewResult.PASS" label="可满足" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="有挑战" />
                      <el-option :value="ReviewResult.FAIL" label="无法满足" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="测试能力">
                    <el-select v-model="qualityForm.testCapability" placeholder="评估测试能力">
                      <el-option :value="ReviewResult.PASS" label="充分" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="基本够用" />
                      <el-option :value="ReviewResult.FAIL" label="不足" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="器件认证">
                    <el-switch
                      v-model="qualityForm.qualificationRequirement"
                      active-text="需要"
                      inactive-text="不需要"
                    />
                  </el-form-item>
                </el-col>
                <el-col
v-if="qualityForm.qualificationRequirement" :span="12"
>
                  <el-form-item label="认证时间(天)">
                    <el-input-number
                      v-model="qualityForm.qualificationTime"
                      :min="0"
                      placeholder="器件认证所需时间"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="质量风险">
                <el-select v-model="qualityForm.qualityRisk" placeholder="评估质量风险">
                  <el-option :value="RiskLevel.LOW" label="低风险" />
                  <el-option :value="RiskLevel.MEDIUM" label="中风险" />
                  <el-option :value="RiskLevel.HIGH" label="高风险" />
                  <el-option :value="RiskLevel.CRITICAL" label="严重风险" />
                </el-select>
              </el-form-item>

              <el-form-item label="质量控制计划">
                <el-input
                  v-model="qualityForm.proposedQualityPlan"
                  type="textarea"
                  :rows="3"
                  placeholder="质量控制计划和措施"
                />
              </el-form-item>

              <el-form-item label="质量备注">
                <el-input
                  v-model="qualityForm.standardsNotes"
                  type="textarea"
                  :rows="2"
                  placeholder="质量评审详细说明"
                />
              </el-form-item>

              <el-form-item
                v-if="getCurrentTask(OrderReviewDepartment.QUALITY)?.status !== 'completed'"
              >
                <el-button
                  type="primary"
                  :loading="loading"
                  @click="submitDepartmentReview(OrderReviewDepartment.QUALITY)"
                >
                  提交质量评审
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 供应链评审 -->
        <el-tab-pane
          :label="getDepartmentName(OrderReviewDepartment.SUPPLY_CHAIN)"
          :name="OrderReviewDepartment.SUPPLY_CHAIN"
        >
          <div class="department-review">
            <div class="review-header">
              <h4>供应链评审</h4>
              <div class="review-status">
                <el-text>
                  负责人: {{ getCurrentTask(OrderReviewDepartment.SUPPLY_CHAIN)?.assignedTo }}
                </el-text>
                <el-tag
                  :type="
                    getCurrentTask(OrderReviewDepartment.SUPPLY_CHAIN)?.status === 'completed'
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{
                    getCurrentTask(OrderReviewDepartment.SUPPLY_CHAIN)?.status === 'completed'
                      ? '已完成'
                      : '待完成'
                  }}
                </el-tag>
              </div>
            </div>

            <el-form
              ref="supplyChainFormRef"
              :model="supplyChainForm"
              label-width="140px"
              :disabled="getCurrentTask(OrderReviewDepartment.SUPPLY_CHAIN)?.status === 'completed'"
            >
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="晶圆供应">
                    <el-select v-model="supplyChainForm.waferSupply" placeholder="评估晶圆供应能力">
                      <el-option :value="ReviewResult.PASS" label="可保障" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="有风险" />
                      <el-option :value="ReviewResult.FAIL" label="无法保障" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="晶圆交期(天)">
                    <el-input-number
                      v-model="supplyChainForm.waferLeadTime"
                      :min="0"
                      placeholder="晶圆供应交期"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="基板供应">
                    <el-select
                      v-model="supplyChainForm.substrateSupply"
                      placeholder="评估基板供应能力"
                    >
                      <el-option :value="ReviewResult.PASS" label="可保障" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="有风险" />
                      <el-option :value="ReviewResult.FAIL" label="无法保障" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="基板交期(天)">
                    <el-input-number
                      v-model="supplyChainForm.substrateLeadTime"
                      :min="0"
                      placeholder="基板供应交期"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="材料BOM">
                    <el-select v-model="supplyChainForm.materialBom" placeholder="评估材料BOM">
                      <el-option :value="ReviewResult.PASS" label="完整可用" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="需要补充" />
                      <el-option :value="ReviewResult.FAIL" label="不完整" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="安全库存(天)">
                    <el-input-number
                      v-model="supplyChainForm.inventoryBuffer"
                      :min="0"
                      placeholder="安全库存天数"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="替代供应商">
                    <el-switch
                      v-model="supplyChainForm.alternativeSources"
                      active-text="有"
                      inactive-text="无"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="总交期(天)">
                    <el-input-number
                      v-model="supplyChainForm.totalLeadTime"
                      :min="0"
                      placeholder="总供应链交期"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="供应链风险">
                    <el-select v-model="supplyChainForm.supplyRisk" placeholder="评估供应链风险">
                      <el-option :value="RiskLevel.LOW" label="低风险" />
                      <el-option :value="RiskLevel.MEDIUM" label="中风险" />
                      <el-option :value="RiskLevel.HIGH" label="高风险" />
                      <el-option :value="RiskLevel.CRITICAL" label="严重风险" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="成本影响(%)">
                    <el-input-number
                      v-model="supplyChainForm.costImpact"
                      :min="-100"
                      :max="100"
                      :step="0.1"
                      placeholder="成本变化百分比"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="供应链备注">
                <el-input
                  v-model="supplyChainForm.waferNotes"
                  type="textarea"
                  :rows="3"
                  placeholder="供应链评审详细说明"
                />
              </el-form-item>

              <el-form-item
                v-if="getCurrentTask(OrderReviewDepartment.SUPPLY_CHAIN)?.status !== 'completed'"
              >
                <el-button
                  type="primary"
                  :loading="loading"
                  @click="submitDepartmentReview(OrderReviewDepartment.SUPPLY_CHAIN)"
                >
                  提交供应链评审
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 财务评审 -->
        <el-tab-pane
          :label="getDepartmentName(OrderReviewDepartment.FINANCE)"
          :name="OrderReviewDepartment.FINANCE"
        >
          <div class="department-review">
            <div class="review-header">
              <h4>财务评审</h4>
              <div class="review-status">
                <el-text>
                  负责人: {{ getCurrentTask(OrderReviewDepartment.FINANCE)?.assignedTo }}
                </el-text>
                <el-tag
                  :type="
                    getCurrentTask(OrderReviewDepartment.FINANCE)?.status === 'completed'
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{
                    getCurrentTask(OrderReviewDepartment.FINANCE)?.status === 'completed'
                      ? '已完成'
                      : '待完成'
                  }}
                </el-tag>
              </div>
            </div>

            <el-form
              ref="financeFormRef"
              :model="financeForm"
              label-width="140px"
              :disabled="getCurrentTask(OrderReviewDepartment.FINANCE)?.status === 'completed'"
            >
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="信用风险">
                    <el-select v-model="financeForm.creditRisk" placeholder="评估客户信用风险">
                      <el-option :value="ReviewResult.PASS" label="低风险" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="中风险" />
                      <el-option :value="ReviewResult.FAIL" label="高风险" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="信用额度(万元)">
                    <el-input-number
                      v-model="financeForm.creditLimit"
                      :min="0"
                      :step="10"
                      placeholder="客户信用额度"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="定价合理性">
                    <el-select v-model="financeForm.pricingReasonable" placeholder="评估定价合理性">
                      <el-option :value="ReviewResult.PASS" label="合理" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="偏低" />
                      <el-option :value="ReviewResult.FAIL" label="不合理" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="毛利率(%)">
                    <el-input-number
                      v-model="financeForm.margin"
                      :min="0"
                      :max="100"
                      :step="0.1"
                      placeholder="预期毛利率"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="收款条件">
                    <el-select v-model="financeForm.paymentTerms" placeholder="评估收款条件">
                      <el-option :value="ReviewResult.PASS" label="可接受" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="需要改善" />
                      <el-option :value="ReviewResult.FAIL" label="不可接受" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="汇率风险">
                    <el-select v-model="financeForm.exchangeRateRisk" placeholder="评估汇率风险">
                      <el-option :value="ReviewResult.PASS" label="无风险" />
                      <el-option :value="ReviewResult.CONDITIONAL_PASS" label="有风险" />
                      <el-option :value="ReviewResult.FAIL" label="高风险" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="现金流影响(万元)">
                    <el-input-number
                      v-model="financeForm.cashFlowImpact"
                      :step="10"
                      placeholder="对现金流的影响"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="盈利评估">
                    <el-select
                      v-model="financeForm.profitabilityAssessment"
                      placeholder="评估盈利能力"
                    >
                      <el-option value="excellent" label="优秀" />
                      <el-option value="good" label="良好" />
                      <el-option value="acceptable" label="可接受" />
                      <el-option value="poor" label="较差" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="财务风险">
                <el-select v-model="financeForm.financialRisk" placeholder="评估财务风险">
                  <el-option :value="RiskLevel.LOW" label="低风险" />
                  <el-option :value="RiskLevel.MEDIUM" label="中风险" />
                  <el-option :value="RiskLevel.HIGH" label="高风险" />
                  <el-option :value="RiskLevel.CRITICAL" label="严重风险" />
                </el-select>
              </el-form-item>

              <el-form-item label="财务备注">
                <el-input
                  v-model="financeForm.creditNotes"
                  type="textarea"
                  :rows="3"
                  placeholder="财务评审详细说明"
                />
              </el-form-item>

              <el-form-item
                v-if="getCurrentTask(OrderReviewDepartment.FINANCE)?.status !== 'completed'"
              >
                <el-button
                  type="primary"
                  :loading="loading"
                  @click="submitDepartmentReview(OrderReviewDepartment.FINANCE)"
                >
                  提交财务评审
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 评审时间线 -->
    <el-card class="timeline-card">
      <template #header>
        <h4>评审时间线</h4>
      </template>

      <el-timeline>
        <el-timeline-item
          v-for="item in currentOrderReview?.timeline"
          :key="item.timestamp"
          :timestamp="new Date(item.timestamp).toLocaleString()"
          :type="
            item.category === 'milestone'
              ? 'primary'
              : item.category === 'decision'
                ? 'success'
                : item.category === 'issue'
                  ? 'danger'
                  : 'info'
          "
        >
          <el-card class="timeline-card-content">
            <div class="timeline-header">
              <h5>{{ item.event }}</h5>
              <el-tag
                :type="
                  item.category === 'milestone'
                    ? 'primary'
                    : item.category === 'decision'
                      ? 'success'
                      : 'info'
                "
              >
                {{ item.actor }}
              </el-tag>
            </div>
            <p>{{ item.description }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 风险评估对话框 -->
    <el-dialog
v-model="showRiskDialog" title="风险评估"
width="80%" align-center
>
      <div class="risk-assessment">
        <el-row :gutter="24">
          <el-col :span="12">
            <h4>整体风险矩阵</h4>
            <el-table
:data="currentOrderReview?.riskAssessment.riskMatrix" border
height="300"
>
              <el-table-column prop="department" label="部门" width="120">
                <template #default="{ row }">
                  {{ getDepartmentName(row.department) }}
                </template>
              </el-table-column>
              <el-table-column prop="riskLevel" label="风险等级" width="120">
                <template #default="{ row }">
                  <el-tag :type="getRiskLevelType(row.riskLevel)">
                    {{
                      row.riskLevel === RiskLevel.LOW
                        ? '低'
                        : row.riskLevel === RiskLevel.MEDIUM
                          ? '中'
                          : row.riskLevel === RiskLevel.HIGH
                            ? '高'
                            : '严重'
                    }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="score" label="风险评分" width="100">
                <template #default="{ row }">
                  <el-rate
v-model="row.score" :max="10"
disabled show-score
/>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="12">
            <h4>风险项详情</h4>
            <div class="risk-items">
              <el-card
                v-for="risk in currentOrderReview?.riskAssessment.riskItems"
                :key="risk.id"
                class="risk-item-card"
                shadow="hover"
              >
                <div class="risk-item-header">
                  <el-tag :type="getRiskLevelType(risk.impact)">
                    {{ risk.riskType }}
                  </el-tag>
                  <el-text type="info">
                    {{ getDepartmentName(risk.category) }}
                  </el-text>
                </div>
                <p class="risk-description">
                  {{ risk.description }}
                </p>
                <div class="risk-details">
                  <el-text size="small">发生概率: {{ risk.probability }}%</el-text>
                  <el-text size="small">负责人: {{ risk.responsible }}</el-text>
                </div>
                <div class="risk-mitigation">
                  <el-text
size="small" type="success">缓解措施: {{ risk.mitigation }}</el-text>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
      </div>

      <template #footer>
        <el-button @click="showRiskDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 最终决策对话框 -->
    <el-dialog
v-model="showDecisionDialog" title="订单评审最终决策"
width="50%" align-center
>
      <div class="decision-summary">
        <el-alert
          :title="`评审完成度: ${reviewProgress}%`"
          type="info"
          show-icon
          :closable="false"
        />

        <el-divider />

        <h4>评审结果汇总</h4>
        <el-table
          :data="currentOrderReview?.reviewTasks.filter(t => t.status === 'completed')"
          border
        >
          <el-table-column prop="departmentName" label="部门" width="120" />
          <el-table-column prop="result" label="评审结果" width="120">
            <template #default="{ row }">
              <el-tag :type="getReviewResultType(row.result || ReviewResult.PASS)">
                {{
                  row.result === ReviewResult.PASS
                    ? '通过'
                    : row.result === ReviewResult.CONDITIONAL_PASS
                      ? '有条件通过'
                      : '不通过'
                }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="completedAt" label="完成时间">
            <template #default="{ row }">
              {{ row.completedAt ? new Date(row.completedAt).toLocaleString() : '-' }}
            </template>
          </el-table-column>
        </el-table>

        <el-divider />

        <div class="decision-actions">
          <el-space :size="16">
            <el-button
              type="success"
              size="large"
              :loading="loading"
              @click="makeDecision('approved')"
            >
              <el-icon><Check /></el-icon>
              批准订单
            </el-button>
            <el-button
              type="warning"
              size="large"
              :loading="loading"
              @click="makeDecision('conditionally_approved')"
            >
              <el-icon><Warning /></el-icon>
              有条件批准
            </el-button>
            <el-button
              type="danger"
              size="large"
              :loading="loading"
              @click="makeDecision('rejected')"
            >
              <el-icon><Close /></el-icon>
              拒绝订单
            </el-button>
          </el-space>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDecisionDialog = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  .order-review-container {
    min-height: 100vh;
    padding: var(--spacing-4);
    background-color: var(--color-bg-page);

    .order-info-card,
    .review-progress-card,
    .review-tabs-card,
    .timeline-card {
      margin-bottom: var(--spacing-4);

      :deep(.el-card__header) {
        background-color: var(--color-bg-primary);
        border-bottom: 1px solid var(--color-border-light);
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-left {
        display: flex;
        gap: var(--spacing-3);
        align-items: center;

        h3 {
          margin: 0;
          font-weight: 600;
          color: var(--color-text-primary);
        }
      }

      .header-right {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    .progress-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }

    .progress-details {
      margin-top: var(--spacing-4);

      .progress-stats {
        display: flex;
        justify-content: space-around;
        padding: var(--spacing-3);
        margin-top: var(--spacing-4);
        background-color: var(--color-bg-secondary);
        border-radius: var(--radius-base);
      }
    }

    .department-review {
      .review-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: var(--spacing-2);
        margin-bottom: var(--spacing-4);
        border-bottom: 1px solid var(--color-border-light);

        h4 {
          margin: 0;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .review-status {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
        }
      }

      .el-form {
        :deep(.el-form-item__label) {
          font-weight: 500;
          color: var(--color-text-primary);
        }

        :deep(.el-input__wrapper) {
          border-radius: var(--radius-small);
        }

        :deep(.el-select) {
          width: 100%;
        }

        :deep(.el-slider__runway) {
          height: 6px;
        }
      }
    }

    .timeline-card {
      .timeline-card-content {
        padding: var(--spacing-2);
        border: none;
        box-shadow: none;

        .timeline-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--spacing-1);

          h5 {
            margin: 0;
            font-weight: 600;
            color: var(--color-text-primary);
          }
        }

        p {
          margin: 0;
          font-size: 14px;
          line-height: 1.4;
          color: var(--color-text-secondary);
        }
      }
    }

    .risk-assessment {
      .risk-items {
        max-height: 400px;
        overflow-y: auto;

        .risk-item-card {
          margin-bottom: var(--spacing-2);

          .risk-item-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-2);
          }

          .risk-description {
            margin: var(--spacing-2) 0;
            font-weight: 500;
            color: var(--color-text-primary);
          }

          .risk-details {
            display: flex;
            justify-content: space-between;
            margin: var(--spacing-2) 0;
          }

          .risk-mitigation {
            padding: var(--spacing-2);
            background-color: var(--color-success-light-9);
            border-left: 3px solid var(--color-success);
            border-radius: var(--radius-small);
          }
        }
      }
    }

    .decision-summary {
      .decision-actions {
        margin-top: var(--spacing-4);
        text-align: center;
      }

      .el-table {
        margin: var(--spacing-3) 0;
      }
    }

    // 响应式设计
    @media (width <= 768px) {
      padding: var(--spacing-2);

      .card-header {
        flex-direction: column;
        gap: var(--spacing-2);
        align-items: stretch;

        .header-left,
        .header-right {
          justify-content: center;
        }
      }

      .progress-details .progress-stats {
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .review-header {
        flex-direction: column;
        gap: var(--spacing-2);
        align-items: stretch !important;
      }

      .decision-actions {
        .el-space {
          flex-direction: column;

          .el-button {
            width: 100%;
          }
        }
      }
    }

    // 主题适配
    :deep(.el-steps) {
      .el-step__title {
        color: var(--color-text-primary);
      }

      .el-step__description {
        color: var(--color-text-secondary);
      }
    }

    :deep(.el-tabs) {
      .el-tabs__header {
        background-color: var(--color-bg-primary);
      }

      .el-tabs__item {
        color: var(--color-text-primary);

        &.is-active {
          color: var(--color-primary);
        }
      }
    }

    :deep(.el-timeline) {
      padding-left: 0;

      .el-timeline-item__timestamp {
        color: var(--color-text-secondary);
      }
    }

    :deep(.el-descriptions) {
      .el-descriptions__label {
        font-weight: 500;
        color: var(--color-text-primary);
      }

      .el-descriptions__content {
        color: var(--color-text-primary);
      }
    }
  }
</style>
