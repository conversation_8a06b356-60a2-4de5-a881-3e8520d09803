# 工艺开发管理模块设计

## 1. 模块概述

### 1.1 模块定位
工艺开发管理模块是IC封测CIM系统的核心技术模块，专门管理从工艺设计到量产转移的完整工艺开发过程。该模块涵盖Die Attach、Wire Bond、Molding、Trim & Form等IC封装关键工艺的开发、验证、优化和标准化，是确保产品质量和生产效率的技术保障。

### 1.2 IC封测工艺开发特点
- **工艺复杂多样**：涉及Die Attach、Wire Bond、Molding、Trim&Form、Marking等多个专业工艺
- **参数精密控制**：工艺参数需要控制在微米和毫秒级别
- **材料依赖性强**：不同材料组合影响工艺参数选择
- **设备耦合度高**：工艺参数与设备性能紧密关联
- **质量要求严格**：需要满足JEDEC、AEC-Q100等严格标准
- **经验依赖性强**：工艺优化需要大量经验数据支撑

### 1.3 核心业务价值
- **缩短工艺开发周期**：通过标准化和经验复用，减少30-50%的开发时间
- **提高工艺稳定性**：通过DOE和统计分析，确保工艺Cpk≥1.33
- **降低开发成本**：通过仿真和预测，减少实际试验成本
- **知识资产积累**：建立企业级工艺知识库，形成竞争优势
- **质量保证体系**：确保工艺开发符合行业标准和客户要求

### 1.4 应用场景覆盖
```
工艺开发管理应用场景
├── Die Attach工艺开发
│   ├── 贴片材料选择与验证
│   ├── 贴片温度曲线优化
│   ├── 贴片压力控制
│   └── Die倾斜度控制
├── Wire Bond工艺开发
│   ├── 键合参数优化
│   ├── 键合线选择
│   ├── 键合强度测试
│   └── 键合可靠性验证
├── Molding工艺开发
│   ├── 塑封料选择
│   ├── 塑封温度控制
│   ├── 塑封压力优化
│   └── 翘曲度控制
├── Trim & Form工艺开发
│   ├── 切筋参数设置
│   ├── 引脚成形控制
│   ├── 电镀工艺优化
│   └── 外观检查标准
├── 工艺仿真分析
│   ├── 热仿真分析
│   ├── 应力仿真分析
│   ├── 流动仿真分析
│   └── 可靠性仿真
├── DOE实验设计
│   ├── 因子筛选实验
│   ├── 响应面优化
│   ├── 稳健性设计
│   └── 容差分析
├── 工艺验证与确认
│   ├── 工艺能力研究
│   ├── 生产确认试验
│   ├── 长期稳定性验证
│   └── 客户认证支持
└── 工艺标准化
    ├── 标准作业程序制定
    ├── 工艺规范文件
    ├── 检验标准制定
    └── 培训教材编制
```

## 2. IC封测工艺专业架构设计

### 2.1 技术架构
```
工艺开发管理模块架构
├── 工艺开发项目管理        # 工艺开发全生命周期管理
├── 多工艺集成开发平台      # Die Attach/Wire Bond/Molding等集成开发
├── 工艺仿真分析引擎        # 热/应力/流动等多物理场仿真
├── DOE实验设计系统         # 统计学实验设计和分析
├── 工艺参数优化引擎        # 基于AI的工艺参数优化
├── 工艺验证管理系统        # 工艺能力和可靠性验证
├── 工艺知识库系统          # 工艺经验和知识管理
├── 工艺标准化平台          # 工艺文件和标准管理
├── 设备工艺集成接口        # 与制造设备的深度集成
└── 质量统计分析中心        # SPC和工艺质量分析
```

### 2.2 核心数据模型

#### 2.2.1 工艺开发项目管理
```sql
-- 工艺开发项目表
CREATE TABLE ic_process_development_projects (
    project_id VARCHAR(30) PRIMARY KEY,
    npi_project_id VARCHAR(30),              -- 关联NPI项目ID
    project_code VARCHAR(50) UNIQUE,         -- 工艺开发项目编码
    project_name VARCHAR(200),               -- 项目名称
    product_code VARCHAR(100),               -- 产品编码
    package_type VARCHAR(50),                -- 封装类型
    development_phase ENUM('concept','design','development','verification','validation','release'), -- 开发阶段
    priority ENUM('low','normal','high','urgent'), -- 优先级
    complexity ENUM('simple','medium','complex','very_complex'), -- 复杂度
    process_engineer VARCHAR(20),            -- 工艺工程师
    project_manager VARCHAR(20),             -- 项目经理
    target_cpk DECIMAL(4,2) DEFAULT 1.33,   -- 目标Cpk值
    target_yield DECIMAL(5,2) DEFAULT 99.0, -- 目标良率%
    target_takt_time INT,                    -- 目标节拍时间(秒)
    development_budget DECIMAL(12,2),        -- 开发预算
    actual_cost DECIMAL(12,2),              -- 实际成本
    planned_start_date DATE,                 -- 计划开始日期
    planned_end_date DATE,                   -- 计划结束日期
    actual_start_date DATE,                  -- 实际开始日期
    actual_end_date DATE,                    -- 实际结束日期
    project_status ENUM('planning','developing','testing','validating','completed','cancelled'), -- 项目状态
    risk_level ENUM('low','medium','high','critical'), -- 风险等级
    customer_requirements JSON,              -- 客户要求
    technical_challenges TEXT,               -- 技术挑战
    success_criteria JSON,                   -- 成功标准
    lessons_learned TEXT,                    -- 经验教训
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_npi_project (npi_project_id),
    INDEX idx_engineer_status (process_engineer, project_status),
    INDEX idx_package_type (package_type),
    INDEX idx_priority_dates (priority, planned_end_date)
);

-- 工艺步骤定义表
CREATE TABLE ic_process_steps (
    step_id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30),                  -- 工艺开发项目ID
    step_sequence INT,                       -- 工艺步骤序号
    step_name VARCHAR(100),                  -- 工艺步骤名称
    step_type ENUM('die_attach','wire_bond','molding','deflash','trim_form','marking','inspection'), -- 工艺类型
    step_category ENUM('preparation','main_process','inspection','rework'), -- 步骤分类
    equipment_type VARCHAR(50),              -- 设备类型
    standard_cycle_time INT,                 -- 标准周期时间(秒)
    process_temperature_min DECIMAL(8,2),   -- 工艺温度范围-最小值(°C)
    process_temperature_max DECIMAL(8,2),   -- 工艺温度范围-最大值(°C)
    process_pressure_min DECIMAL(10,3),     -- 工艺压力范围-最小值(Pa)
    process_pressure_max DECIMAL(10,3),     -- 工艺压力范围-最大值(Pa)
    material_consumption JSON,               -- 材料消耗定义
    quality_checkpoints JSON,               -- 质量检查点
    critical_parameters JSON,               -- 关键参数定义
    process_window JSON,                     -- 工艺窗口定义
    failure_modes JSON,                      -- 失效模式定义
    control_plan JSON,                       -- 控制计划
    is_critical_step BOOLEAN DEFAULT FALSE, -- 是否关键工艺步骤
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    
    INDEX idx_project_sequence (project_id, step_sequence),
    INDEX idx_step_type (step_type),
    INDEX idx_critical_step (is_critical_step)
);

-- 工艺参数定义表
CREATE TABLE ic_process_parameters (
    parameter_id VARCHAR(30) PRIMARY KEY,
    step_id VARCHAR(30),                     -- 工艺步骤ID
    parameter_name VARCHAR(100),             -- 参数名称
    parameter_code VARCHAR(50),              -- 参数编码
    parameter_category ENUM('temperature','pressure','time','speed','position','force','voltage','current'), -- 参数分类
    data_type ENUM('integer','decimal','boolean','string','enum'), -- 数据类型
    unit_of_measure VARCHAR(20),             -- 计量单位
    nominal_value DECIMAL(12,6),             -- 标准值
    lower_spec_limit DECIMAL(12,6),          -- 下规格限
    upper_spec_limit DECIMAL(12,6),          -- 上规格限
    lower_control_limit DECIMAL(12,6),       -- 下控制限
    upper_control_limit DECIMAL(12,6),       -- 上控制限
    target_cpk DECIMAL(4,2) DEFAULT 1.33,   -- 目标Cpk值
    current_cpk DECIMAL(4,2),               -- 当前Cpk值
    criticality ENUM('critical','major','minor'), -- 重要程度
    control_method ENUM('spc','limit_check','trend_monitor','manual'), -- 控制方法
    sampling_frequency ENUM('each','hourly','shift','daily','weekly'), -- 采样频率
    measurement_method VARCHAR(200),         -- 测量方法
    measurement_equipment VARCHAR(100),      -- 测量设备
    parameter_dependencies JSON,             -- 参数依赖关系
    optimization_history JSON,              -- 优化历史
    is_key_parameter BOOLEAN DEFAULT FALSE, -- 是否关键参数
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_step_parameter (step_id, parameter_code),
    INDEX idx_parameter_category (parameter_category),
    INDEX idx_key_parameter (is_key_parameter),
    INDEX idx_criticality (criticality)
);
```

#### 2.2.2 DOE实验设计管理
```sql
-- DOE实验设计表
CREATE TABLE ic_doe_experiments (
    experiment_id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30),                  -- 工艺开发项目ID
    experiment_code VARCHAR(50) UNIQUE,     -- 实验编码
    experiment_name VARCHAR(200),           -- 实验名称
    experiment_type ENUM('screening','optimization','verification','robustness'), -- 实验类型
    design_type ENUM('full_factorial','fractional_factorial','response_surface','taguchi','latin_square'), -- 设计类型
    experiment_objective TEXT,               -- 实验目的
    experiment_hypothesis TEXT,              -- 实验假设
    factor_count INT,                        -- 因子数量
    level_count INT,                         -- 水平数量
    run_count INT,                          -- 试验次数
    block_count INT DEFAULT 1,              -- 区组数量
    replicate_count INT DEFAULT 1,          -- 重复次数
    randomization_seed INT,                  -- 随机化种子
    center_point_runs INT DEFAULT 0,        -- 中心点试验次数
    design_resolution VARCHAR(10),          -- 设计分辨率(如IV, V等)
    confounding_pattern TEXT,               -- 混杂模式
    statistical_power DECIMAL(5,3),         -- 统计功效
    significance_level DECIMAL(4,3) DEFAULT 0.05, -- 显著水平
    minimum_detectable_effect DECIMAL(8,4), -- 最小可检出效应
    experiment_duration_hours INT,          -- 实验持续时间(小时)
    estimated_cost DECIMAL(10,2),          -- 预估成本
    actual_cost DECIMAL(10,2),             -- 实际成本
    experiment_status ENUM('design','approved','running','completed','cancelled'), -- 实验状态
    start_date DATE,                        -- 开始日期
    end_date DATE,                          -- 结束日期
    principal_investigator VARCHAR(20),     -- 主要研究者
    experiment_team JSON,                   -- 实验团队
    safety_requirements TEXT,               -- 安全要求
    resource_requirements JSON,             -- 资源需求
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_project_experiment (project_id, experiment_code),
    INDEX idx_experiment_type (experiment_type),
    INDEX idx_status_dates (experiment_status, start_date),
    INDEX idx_investigator (principal_investigator)
);

-- DOE实验因子表
CREATE TABLE ic_doe_factors (
    factor_id VARCHAR(30) PRIMARY KEY,
    experiment_id VARCHAR(30),               -- DOE实验ID
    factor_sequence INT,                     -- 因子序号
    factor_name VARCHAR(100),                -- 因子名称
    factor_code VARCHAR(20),                 -- 因子编码(A, B, C等)
    process_parameter_id VARCHAR(30),       -- 关联工艺参数ID
    factor_type ENUM('quantitative','qualitative','mixture'), -- 因子类型
    factor_role ENUM('input','nuisance','blocking'), -- 因子作用
    controllability ENUM('easy','difficult','hard_to_change'), -- 控制难易程度
    cost_to_change ENUM('low','medium','high'), -- 变更成本
    measurement_precision DECIMAL(10,6),    -- 测量精度
    factor_levels JSON,                      -- 因子水平定义
    level_coding JSON,                       -- 水平编码
    factor_constraints TEXT,                 -- 因子约束条件
    practical_significance DECIMAL(8,4),    -- 实际显著差异
    is_hard_to_change BOOLEAN DEFAULT FALSE, -- 是否难变因子
    factor_interaction_potential JSON,       -- 交互作用可能性
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    
    INDEX idx_experiment_sequence (experiment_id, factor_sequence),
    INDEX idx_parameter_factor (process_parameter_id),
    INDEX idx_factor_type (factor_type),
    INDEX idx_controllability (controllability)
);

-- DOE实验响应表
CREATE TABLE ic_doe_responses (
    response_id VARCHAR(30) PRIMARY KEY,
    experiment_id VARCHAR(30),               -- DOE实验ID
    response_sequence INT,                   -- 响应序号
    response_name VARCHAR(100),              -- 响应名称
    response_code VARCHAR(20),               -- 响应编码(Y1, Y2等)
    measurement_unit VARCHAR(20),            -- 测量单位
    response_type ENUM('continuous','discrete','attribute','count'), -- 响应类型
    optimization_goal ENUM('maximize','minimize','target','none'), -- 优化目标
    target_value DECIMAL(12,6),             -- 目标值
    importance_weight DECIMAL(5,3) DEFAULT 1.0, -- 重要性权重
    measurement_method VARCHAR(200),         -- 测量方法
    measurement_precision DECIMAL(10,6),    -- 测量精度
    measurement_repeatability DECIMAL(8,4), -- 测量重现性
    transformation_method ENUM('none','log','sqrt','inverse','box_cox'), -- 数据变换方法
    outlier_criteria JSON,                  -- 异常值判定准则
    response_limits JSON,                   -- 响应限制范围
    business_impact_description TEXT,       -- 业务影响描述
    is_primary_response BOOLEAN DEFAULT FALSE, -- 是否主要响应
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    
    INDEX idx_experiment_sequence (experiment_id, response_sequence),
    INDEX idx_response_type (response_type),
    INDEX idx_optimization_goal (optimization_goal),
    INDEX idx_primary_response (is_primary_response)
);

-- DOE试验运行表
CREATE TABLE ic_doe_runs (
    run_id VARCHAR(30) PRIMARY KEY,
    experiment_id VARCHAR(30),               -- DOE实验ID
    run_sequence INT,                        -- 试验序号
    standard_order INT,                      -- 标准次序
    random_order INT,                        -- 随机次序
    block_number INT,                        -- 区组号
    replicate_number INT,                    -- 重复号
    run_type ENUM('factorial','center_point','axial','augment'), -- 试验类型
    factor_settings JSON,                    -- 因子设定值
    actual_factor_values JSON,               -- 实际因子值
    response_values JSON,                    -- 响应值
    run_date DATE,                          -- 试验日期
    start_time TIME,                        -- 开始时间
    end_time TIME,                          -- 结束时间
    operator VARCHAR(20),                    -- 操作员
    equipment_used VARCHAR(100),             -- 使用设备
    environmental_conditions JSON,           -- 环境条件
    run_status ENUM('planned','running','completed','failed','invalidated'), -- 试验状态
    run_notes TEXT,                         -- 试验备注
    data_quality_flag ENUM('good','questionable','invalid'), -- 数据质量标志
    outlier_flag BOOLEAN DEFAULT FALSE,     -- 异常值标志
    exclusion_reason TEXT,                  -- 排除原因
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_experiment_run (experiment_id, run_sequence),
    INDEX idx_standard_order (experiment_id, standard_order),
    INDEX idx_random_order (experiment_id, random_order),
    INDEX idx_run_date (run_date),
    INDEX idx_run_status (run_status)
);
```

#### 2.2.3 工艺仿真分析
```sql
-- 仿真分析项目表
CREATE TABLE ic_simulation_projects (
    simulation_id VARCHAR(30) PRIMARY KEY,
    process_project_id VARCHAR(30),          -- 工艺开发项目ID
    simulation_code VARCHAR(50) UNIQUE,     -- 仿真编码
    simulation_name VARCHAR(200),           -- 仿真名称
    simulation_type ENUM('thermal','mechanical','flow','electromagnetic','multi_physics'), -- 仿真类型
    analysis_purpose ENUM('design_optimization','failure_analysis','process_window','sensitivity_analysis'), -- 分析目的
    simulation_software VARCHAR(50),         -- 仿真软件
    software_version VARCHAR(20),           -- 软件版本
    model_complexity ENUM('2d_simplified','2d_detailed','3d_simplified','3d_detailed'), -- 模型复杂度
    mesh_quality ENUM('coarse','medium','fine','very_fine'), -- 网格质量
    solver_type VARCHAR(50),                 -- 求解器类型
    convergence_criteria DECIMAL(12,8),     -- 收敛准则
    simulation_time_hours DECIMAL(8,2),     -- 仿真时间(小时)
    compute_resources_used JSON,            -- 计算资源使用情况
    model_geometry_source VARCHAR(200),     -- 几何模型来源
    material_properties JSON,               -- 材料属性定义
    boundary_conditions JSON,               -- 边界条件
    initial_conditions JSON,                -- 初始条件
    simulation_parameters JSON,             -- 仿真参数
    mesh_statistics JSON,                   -- 网格统计信息
    simulation_status ENUM('setup','running','completed','failed','cancelled'), -- 仿真状态
    start_timestamp TIMESTAMP,              -- 开始时间
    end_timestamp TIMESTAMP,                -- 结束时间
    simulation_engineer VARCHAR(20),        -- 仿真工程师
    validation_method TEXT,                 -- 验证方法
    experimental_correlation TEXT,          -- 实验关联性
    results_summary TEXT,                   -- 结果摘要
    recommendations TEXT,                   -- 建议
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_process_project (process_project_id),
    INDEX idx_simulation_type (simulation_type),
    INDEX idx_status_engineer (simulation_status, simulation_engineer),
    INDEX idx_simulation_dates (start_timestamp, end_timestamp)
);

-- 仿真结果表
CREATE TABLE ic_simulation_results (
    result_id VARCHAR(30) PRIMARY KEY,
    simulation_id VARCHAR(30),               -- 仿真项目ID
    result_category ENUM('temperature','stress','strain','displacement','velocity','pressure','electric_field'), -- 结果类别
    result_parameter VARCHAR(100),           -- 结果参数名称
    location_description VARCHAR(200),       -- 位置描述
    coordinate_x DECIMAL(12,6),             -- X坐标
    coordinate_y DECIMAL(12,6),             -- Y坐标
    coordinate_z DECIMAL(12,6),             -- Z坐标
    result_value DECIMAL(15,8),             -- 结果数值
    unit_of_measure VARCHAR(20),             -- 单位
    time_point DECIMAL(12,6),               -- 时间点
    result_type ENUM('minimum','maximum','average','peak','steady_state'), -- 结果类型
    critical_assessment ENUM('safe','marginal','critical','failure'), -- 临界评估
    design_margin DECIMAL(8,4),            -- 设计余量
    safety_factor DECIMAL(8,4),            -- 安全系数
    failure_criteria_met BOOLEAN,           -- 是否满足失效准则
    result_quality ENUM('excellent','good','acceptable','poor'), -- 结果质量
    uncertainty_range DECIMAL(8,4),         -- 不确定性范围
    confidence_level DECIMAL(5,2),          -- 置信度
    validation_status ENUM('validated','needs_validation','invalid'), -- 验证状态
    comparison_with_experiment DECIMAL(8,4), -- 与实验对比差异%
    engineering_significance TEXT,           -- 工程意义
    result_visualization_path VARCHAR(500), -- 结果可视化文件路径
    created_at TIMESTAMP,
    
    INDEX idx_simulation_category (simulation_id, result_category),
    INDEX idx_location (coordinate_x, coordinate_y, coordinate_z),
    INDEX idx_critical_assessment (critical_assessment),
    INDEX idx_validation_status (validation_status)
);
```

#### 2.2.4 工艺知识库管理
```sql
-- 工艺知识库表
CREATE TABLE ic_process_knowledge_base (
    knowledge_id VARCHAR(30) PRIMARY KEY,
    knowledge_code VARCHAR(50) UNIQUE,      -- 知识编码
    knowledge_title VARCHAR(200),           -- 知识标题
    knowledge_type ENUM('process_recipe','troubleshooting','best_practice','failure_mode','material_property'), -- 知识类型
    process_type ENUM('die_attach','wire_bond','molding','trim_form','marking','all'), -- 工艺类型
    package_types JSON,                      -- 适用封装类型
    knowledge_category ENUM('technical','quality','safety','efficiency','cost'), -- 知识分类
    knowledge_level ENUM('basic','intermediate','advanced','expert'), -- 知识等级
    applicability_scope ENUM('general','specific_product','specific_customer','specific_equipment'), -- 适用范围
    knowledge_content TEXT,                 -- 知识内容
    technical_description TEXT,             -- 技术描述
    implementation_steps TEXT,              -- 实施步骤
    prerequisites TEXT,                     -- 前提条件
    expected_benefits TEXT,                 -- 预期收益
    risks_and_limitations TEXT,             -- 风险和限制
    related_parameters JSON,                -- 相关参数
    equipment_requirements JSON,            -- 设备要求
    material_requirements JSON,             -- 材料要求
    skill_requirements JSON,                -- 技能要求
    validation_results JSON,               -- 验证结果
    success_cases JSON,                     -- 成功案例
    failure_cases JSON,                     -- 失败案例
    lessons_learned TEXT,                   -- 经验教训
    references TEXT,                        -- 参考资料
    attachments JSON,                       -- 附件列表
    tags JSON,                             -- 标签
    usage_frequency INT DEFAULT 0,          -- 使用频率
    effectiveness_rating DECIMAL(3,2),      -- 有效性评分
    user_feedback_rating DECIMAL(3,2),     -- 用户反馈评分
    last_validated_date DATE,              -- 最后验证日期
    validation_status ENUM('draft','validated','needs_review','obsolete'), -- 验证状态
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_by VARCHAR(20),
    updated_at TIMESTAMP,
    
    INDEX idx_knowledge_type_process (knowledge_type, process_type),
    INDEX idx_category_level (knowledge_category, knowledge_level),
    INDEX idx_tags (tags(100)),
    INDEX idx_validation_status (validation_status),
    INDEX idx_effectiveness_rating (effectiveness_rating DESC),
    FULLTEXT KEY ft_content (knowledge_content, technical_description, implementation_steps)
);

-- 工艺问题库表
CREATE TABLE ic_process_troubleshooting (
    issue_id VARCHAR(30) PRIMARY KEY,
    issue_code VARCHAR(50) UNIQUE,          -- 问题编码
    issue_title VARCHAR(200),               -- 问题标题
    process_type ENUM('die_attach','wire_bond','molding','trim_form','marking'), -- 工艺类型
    issue_category ENUM('quality_defect','equipment_malfunction','process_deviation','material_issue'), -- 问题分类
    severity_level ENUM('low','medium','high','critical'), -- 严重程度
    frequency ENUM('rare','occasional','frequent','continuous'), -- 发生频率
    symptom_description TEXT,               -- 症状描述
    root_cause_analysis TEXT,              -- 根因分析
    immediate_actions TEXT,                 -- 立即措施
    corrective_actions TEXT,               -- 纠正措施
    preventive_actions TEXT,               -- 预防措施
    verification_method TEXT,              -- 验证方法
    related_parameters JSON,               -- 相关参数
    diagnostic_steps JSON,                 -- 诊断步骤
    solution_effectiveness DECIMAL(5,2),   -- 解决方案有效性%
    average_resolution_time_hours DECIMAL(8,2), -- 平均解决时间(小时)
    cost_impact DECIMAL(10,2),             -- 成本影响
    quality_impact TEXT,                   -- 质量影响
    safety_considerations TEXT,             -- 安全考虑
    expert_contacts JSON,                  -- 专家联系方式
    similar_issues JSON,                   -- 相似问题
    knowledge_base_links JSON,             -- 知识库链接
    case_studies JSON,                     -- 案例研究
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_by VARCHAR(20),
    updated_at TIMESTAMP,
    
    INDEX idx_process_category (process_type, issue_category),
    INDEX idx_severity_frequency (severity_level, frequency),
    INDEX idx_effectiveness (solution_effectiveness DESC),
    FULLTEXT KEY ft_issue_content (symptom_description, root_cause_analysis, corrective_actions)
);
```

## 3. 工艺开发管理引擎

### 3.1 DOE实验设计引擎
```java
@Service
public class DOEExperimentDesignService {
    
    @Autowired
    private DOEExperimentRepository doeRepository;
    
    @Autowired
    private ProcessParameterRepository parameterRepository;
    
    @Autowired
    private StatisticalAnalysisService statisticalService;
    
    /**
     * 创建DOE实验设计
     */
    public ICDOEExperiment createDOEExperiment(DOEExperimentRequest request) {
        // 1. 验证实验设计参数
        validateExperimentDesign(request);
        
        // 2. 创建实验主记录
        ICDOEExperiment experiment = new ICDOEExperiment();
        experiment.setExperimentId(IdGenerator.generateId());
        experiment.setProjectId(request.getProjectId());
        experiment.setExperimentCode(generateExperimentCode(request.getProjectId()));
        experiment.setExperimentName(request.getExperimentName());
        experiment.setExperimentType(request.getExperimentType());
        experiment.setDesignType(request.getDesignType());
        experiment.setExperimentObjective(request.getObjective());
        experiment.setFactorCount(request.getFactors().size());
        experiment.setLevelCount(calculateMaxLevels(request.getFactors()));
        experiment.setSignificanceLevel(request.getSignificanceLevel());
        experiment.setStatisticalPower(request.getStatisticalPower());
        experiment.setExperimentStatus(ExperimentStatus.DESIGN);
        experiment.setPrincipalInvestigator(getCurrentUserId());
        
        experiment = doeRepository.save(experiment);
        
        // 3. 创建实验因子
        List<ICDOEFactor> factors = createExperimentFactors(experiment, request.getFactors());
        
        // 4. 创建响应变量
        List<ICDOEResponse> responses = createExperimentResponses(experiment, request.getResponses());
        
        // 5. 生成实验设计矩阵
        ExperimentDesignMatrix designMatrix = generateDesignMatrix(
            experiment.getDesignType(), factors, request.getDesignOptions());
        
        // 6. 创建试验运行计划
        List<ICDOERun> runs = createExperimentRuns(experiment, designMatrix);
        
        // 7. 计算实验统计属性
        ExperimentStatistics statistics = calculateExperimentStatistics(
            experiment, factors, responses, runs);
        
        // 更新实验统计信息
        experiment.setRunCount(runs.size());
        experiment.setDesignResolution(statistics.getDesignResolution());
        experiment.setConfoundingPattern(statistics.getConfoundingPattern());
        experiment.setMinimumDetectableEffect(statistics.getMinimumDetectableEffect());
        
        return doeRepository.save(experiment);
    }
    
    /**
     * 生成实验设计矩阵
     */
    private ExperimentDesignMatrix generateDesignMatrix(DesignType designType, 
                                                       List<ICDOEFactor> factors, 
                                                       DesignOptions options) {
        ExperimentDesignMatrix matrix = new ExperimentDesignMatrix();
        
        switch (designType) {
            case FULL_FACTORIAL:
                matrix = generateFullFactorialDesign(factors, options);
                break;
            case FRACTIONAL_FACTORIAL:
                matrix = generateFractionalFactorialDesign(factors, options);
                break;
            case RESPONSE_SURFACE:
                matrix = generateResponseSurfaceDesign(factors, options);
                break;
            case TAGUCHI:
                matrix = generateTaguchiDesign(factors, options);
                break;
            case LATIN_SQUARE:
                matrix = generateLatinSquareDesign(factors, options);
                break;
        }
        
        // 添加中心点
        if (options.getCenterPointRuns() > 0) {
            matrix.addCenterPointRuns(options.getCenterPointRuns());
        }
        
        // 随机化
        if (options.isRandomizeRuns()) {
            matrix.randomizeRunOrder(options.getRandomizationSeed());
        }
        
        // 区组化
        if (options.getBlockCount() > 1) {
            matrix.createBlocks(options.getBlockCount(), factors);
        }
        
        return matrix;
    }
    
    /**
     * 全因子实验设计
     */
    private ExperimentDesignMatrix generateFullFactorialDesign(List<ICDOEFactor> factors, 
                                                              DesignOptions options) {
        ExperimentDesignMatrix matrix = new ExperimentDesignMatrix();
        
        // 计算总试验次数
        int totalRuns = factors.stream()
            .mapToInt(factor -> factor.getFactorLevels().size())
            .reduce(1, (a, b) -> a * b);
        
        // 生成所有因子水平组合
        List<Map<String, Object>> combinations = generateAllCombinations(factors);
        
        for (int i = 0; i < combinations.size(); i++) {
            ExperimentRun run = new ExperimentRun();
            run.setStandardOrder(i + 1);
            run.setRunType(RunType.FACTORIAL);
            run.setFactorSettings(combinations.get(i));
            matrix.addRun(run);
        }
        
        matrix.setDesignType(DesignType.FULL_FACTORIAL);
        matrix.setRunCount(totalRuns);
        matrix.setDesignResolution("Full");
        
        return matrix;
    }
    
    /**
     * 响应面实验设计（中心复合设计）
     */
    private ExperimentDesignMatrix generateResponseSurfaceDesign(List<ICDOEFactor> factors, 
                                                                DesignOptions options) {
        ExperimentDesignMatrix matrix = new ExperimentDesignMatrix();
        int factorCount = factors.size();
        
        // 1. 生成2^k因子点
        List<Map<String, Object>> factorialPoints = generate2LevelFactorialPoints(factors);
        for (int i = 0; i < factorialPoints.size(); i++) {
            ExperimentRun run = new ExperimentRun();
            run.setStandardOrder(i + 1);
            run.setRunType(RunType.FACTORIAL);
            run.setFactorSettings(factorialPoints.get(i));
            matrix.addRun(run);
        }
        
        // 2. 生成轴向点（星点）
        double alpha = calculateAlphaValue(factorCount, options);
        List<Map<String, Object>> axialPoints = generateAxialPoints(factors, alpha);
        for (int i = 0; i < axialPoints.size(); i++) {
            ExperimentRun run = new ExperimentRun();
            run.setStandardOrder(factorialPoints.size() + i + 1);
            run.setRunType(RunType.AXIAL);
            run.setFactorSettings(axialPoints.get(i));
            matrix.addRun(run);
        }
        
        // 3. 生成中心点
        int centerPointRuns = options.getCenterPointRuns() > 0 ? 
            options.getCenterPointRuns() : calculateOptimalCenterPoints(factorCount);
        Map<String, Object> centerPoint = generateCenterPoint(factors);
        for (int i = 0; i < centerPointRuns; i++) {
            ExperimentRun run = new ExperimentRun();
            run.setStandardOrder(factorialPoints.size() + axialPoints.size() + i + 1);
            run.setRunType(RunType.CENTER_POINT);
            run.setFactorSettings(new HashMap<>(centerPoint));
            matrix.addRun(run);
        }
        
        matrix.setDesignType(DesignType.RESPONSE_SURFACE);
        matrix.setRunCount(factorialPoints.size() + axialPoints.size() + centerPointRuns);
        matrix.setDesignProperties(Map.of("alpha", alpha, "rotatable", true));
        
        return matrix;
    }
    
    /**
     * 实验数据分析
     */
    public DOEAnalysisResult analyzeDOEResults(String experimentId) {
        ICDOEExperiment experiment = doeRepository.findById(experimentId)
            .orElseThrow(() -> new EntityNotFoundException("DOE实验不存在"));
        
        // 1. 获取实验数据
        List<ICDOERun> runs = doeRunRepository.findByExperimentIdOrderByStandardOrder(experimentId);
        List<ICDOEResponse> responses = doeResponseRepository.findByExperimentId(experimentId);
        List<ICDOEFactor> factors = doeFactorRepository.findByExperimentIdOrderByFactorSequence(experimentId);
        
        DOEAnalysisResult analysisResult = new DOEAnalysisResult();
        analysisResult.setExperimentId(experimentId);
        analysisResult.setAnalysisDate(LocalDateTime.now());
        
        for (ICDOEResponse response : responses) {
            ResponseAnalysis responseAnalysis = analyzeResponse(response, runs, factors);
            analysisResult.addResponseAnalysis(responseAnalysis);
        }
        
        // 2. 总体模型分析
        OverallModelAnalysis overallAnalysis = analyzeOverallModel(responses, runs, factors);
        analysisResult.setOverallAnalysis(overallAnalysis);
        
        // 3. 优化分析
        if (hasOptimizationGoals(responses)) {
            OptimizationAnalysis optimization = performMultiResponseOptimization(
                responses, runs, factors);
            analysisResult.setOptimizationAnalysis(optimization);
        }
        
        // 4. 稳健性分析
        RobustnessAnalysis robustnessAnalysis = performRobustnessAnalysis(responses, runs, factors);
        analysisResult.setRobustnessAnalysis(robustnessAnalysis);
        
        return analysisResult;
    }
    
    /**
     * 单响应分析
     */
    private ResponseAnalysis analyzeResponse(ICDOEResponse response, 
                                           List<ICDOERun> runs, 
                                           List<ICDOEFactor> factors) {
        ResponseAnalysis analysis = new ResponseAnalysis();
        analysis.setResponseId(response.getResponseId());
        analysis.setResponseName(response.getResponseName());
        
        // 1. 数据预处理
        List<Double> responseData = extractResponseData(response, runs);
        if (response.getTransformationMethod() != TransformationMethod.NONE) {
            responseData = applyTransformation(responseData, response.getTransformationMethod());
        }
        
        // 2. 描述性统计
        DescriptiveStatistics descriptive = calculateDescriptiveStatistics(responseData);
        analysis.setDescriptiveStatistics(descriptive);
        
        // 3. 回归分析
        RegressionAnalysis regression = performRegressionAnalysis(response, runs, factors);
        analysis.setRegressionAnalysis(regression);
        
        // 4. 方差分析(ANOVA)
        ANOVAResult anova = performANOVAAnalysis(response, runs, factors);
        analysis.setAnovaResult(anova);
        
        // 5. 残差分析
        ResidualAnalysis residualAnalysis = performResidualAnalysis(regression);
        analysis.setResidualAnalysis(residualAnalysis);
        
        // 6. 影响图分析
        EffectsAnalysis effectsAnalysis = calculateEffectsAnalysis(response, runs, factors);
        analysis.setEffectsAnalysis(effectsAnalysis);
        
        // 7. 响应面分析（如适用）
        if (isResponseSurfaceDesign(runs)) {
            ResponseSurfaceAnalysis surfaceAnalysis = performResponseSurfaceAnalysis(
                response, runs, factors);
            analysis.setResponseSurfaceAnalysis(surfaceAnalysis);
        }
        
        return analysis;
    }
    
    /**
     * 多响应优化
     */
    private OptimizationAnalysis performMultiResponseOptimization(List<ICDOEResponse> responses, 
                                                                 List<ICDOERun> runs, 
                                                                 List<ICDOEFactor> factors) {
        OptimizationAnalysis optimization = new OptimizationAnalysis();
        
        // 1. 构建多响应优化模型
        MultiResponseModel model = buildMultiResponseModel(responses, runs, factors);
        
        // 2. 定义约束条件
        List<OptimizationConstraint> constraints = buildOptimizationConstraints(responses, factors);
        
        // 3. 执行多目标优化
        switch (responses.size()) {
            case 1:
                optimization = performSingleObjectiveOptimization(model, constraints);
                break;
            case 2:
                optimization = performBiObjectiveOptimization(model, constraints);
                break;
            default:
                optimization = performMultiObjectiveOptimization(model, constraints);
                break;
        }
        
        // 4. 敏感性分析
        SensitivityAnalysis sensitivity = performSensitivityAnalysis(
            optimization.getOptimalSolution(), model);
        optimization.setSensitivityAnalysis(sensitivity);
        
        // 5. 稳健性验证
        RobustnessVerification robustness = verifyOptimalSolutionRobustness(
            optimization.getOptimalSolution(), model);
        optimization.setRobustnessVerification(robustness);
        
        return optimization;
    }
}
```

### 3.2 工艺仿真分析引擎
```java
@Service
public class ProcessSimulationService {
    
    @Autowired
    private SimulationProjectRepository simulationRepository;
    
    @Autowired
    private SimulationResultRepository resultRepository;
    
    @Autowired
    private CADIntegrationService cadService;
    
    /**
     * 热仿真分析
     */
    public ThermalSimulationResult performThermalAnalysis(ThermalSimulationRequest request) {
        // 1. 创建仿真项目
        ICSimulationProject simulation = createSimulationProject(request, SimulationType.THERMAL);
        
        // 2. 几何模型准备
        GeometryModel geometry = prepareGeometryModel(request.getGeometrySource(), 
                                                     request.getSimplificationLevel());
        
        // 3. 材料属性定义
        List<MaterialProperty> materials = defineMaterialProperties(
            request.getMaterialDefinitions(), SimulationType.THERMAL);
        
        // 4. 边界条件设置
        ThermalBoundaryConditions boundaryConditions = setupThermalBoundaryConditions(request);
        
        // 5. 网格生成
        MeshGeneration mesh = generateMesh(geometry, request.getMeshQuality());
        simulation.setMeshStatistics(mesh.getStatistics());
        
        // 6. 求解器配置
        ThermalSolver solver = configureThermalSolver(request.getSolverSettings());
        
        // 7. 执行仿真计算
        simulation.setSimulationStatus(SimulationStatus.RUNNING);
        simulation.setStartTimestamp(LocalDateTime.now());
        simulationRepository.save(simulation);
        
        ThermalSimulationResult result = new ThermalSimulationResult();
        result.setSimulationId(simulation.getSimulationId());
        
        try {
            // 求解热传导方程
            SolutionField temperatureField = solver.solveHeatTransfer(
                geometry, materials, boundaryConditions, mesh);
            
            // 后处理分析
            result = processThermalResults(temperatureField, request.getAnalysisRequirements());
            
            // 关键位置温度提取
            List<TemperatureResult> keyPointTemperatures = extractKeyPointTemperatures(
                temperatureField, request.getKeyLocations());
            result.setKeyPointTemperatures(keyPointTemperatures);
            
            // 热路径分析
            ThermalPathAnalysis thermalPaths = analyzeThermalPaths(
                temperatureField, request.getHeatSources(), request.getHeatSinks());
            result.setThermalPathAnalysis(thermalPaths);
            
            // 热设计余量计算
            ThermalMarginAnalysis thermalMargin = calculateThermalMargin(
                keyPointTemperatures, request.getTemperatureLimits());
            result.setThermalMarginAnalysis(thermalMargin);
            
            // 保存仿真结果
            saveSimulationResults(simulation.getSimulationId(), result);
            
            simulation.setSimulationStatus(SimulationStatus.COMPLETED);
            simulation.setResultsSummary("热仿真完成，最高温度: " + 
                result.getMaxTemperature() + "°C");
            
        } catch (Exception e) {
            simulation.setSimulationStatus(SimulationStatus.FAILED);
            simulation.setResultsSummary("仿真失败: " + e.getMessage());
            log.error("热仿真失败", e);
            throw new SimulationExecutionException("热仿真执行失败", e);
        } finally {
            simulation.setEndTimestamp(LocalDateTime.now());
            simulationRepository.save(simulation);
        }
        
        return result;
    }
    
    /**
     * 应力仿真分析
     */
    public MechanicalSimulationResult performMechanicalAnalysis(MechanicalSimulationRequest request) {
        // 1. 创建仿真项目
        ICSimulationProject simulation = createSimulationProject(request, SimulationType.MECHANICAL);
        
        // 2. 结构模型准备
        StructuralModel structure = prepareStructuralModel(request);
        
        // 3. 材料属性定义（机械属性）
        List<MechanicalMaterialProperty> materials = defineMechanicalProperties(
            request.getMaterialDefinitions());
        
        // 4. 载荷和约束条件
        MechanicalBoundaryConditions boundaryConditions = setupMechanicalBoundaryConditions(request);
        
        // 5. 执行结构分析
        simulation.setSimulationStatus(SimulationStatus.RUNNING);
        simulation.setStartTimestamp(LocalDateTime.now());
        
        MechanicalSimulationResult result = new MechanicalSimulationResult();
        result.setSimulationId(simulation.getSimulationId());
        
        try {
            // 静力分析
            if (request.getAnalysisType().contains(AnalysisType.STATIC)) {
                StaticAnalysisResult staticResult = performStaticAnalysis(
                    structure, materials, boundaryConditions);
                result.setStaticAnalysisResult(staticResult);
            }
            
            // 模态分析
            if (request.getAnalysisType().contains(AnalysisType.MODAL)) {
                ModalAnalysisResult modalResult = performModalAnalysis(
                    structure, materials, boundaryConditions);
                result.setModalAnalysisResult(modalResult);
            }
            
            // 疲劳分析
            if (request.getAnalysisType().contains(AnalysisType.FATIGUE)) {
                FatigueAnalysisResult fatigueResult = performFatigueAnalysis(
                    structure, materials, boundaryConditions, request.getLoadingHistory());
                result.setFatigueAnalysisResult(fatigueResult);
            }
            
            // 翘曲分析（IC封装特有）
            WarpageAnalysisResult warpageResult = performWarpageAnalysis(
                structure, materials, request.getThermalProfile());
            result.setWarpageAnalysisResult(warpageResult);
            
            // 应力集中分析
            StressConcentrationAnalysis stressConcentration = analyzeStressConcentration(
                result.getStaticAnalysisResult(), request.getCriticalFeatures());
            result.setStressConcentrationAnalysis(stressConcentration);
            
            // 失效分析
            FailureAnalysis failureAnalysis = performFailureAnalysis(
                result, materials, request.getFailureCriteria());
            result.setFailureAnalysis(failureAnalysis);
            
            simulation.setSimulationStatus(SimulationStatus.COMPLETED);
            simulation.setResultsSummary("机械仿真完成，最大应力: " + 
                result.getMaxVonMisesStress() + " MPa");
            
        } catch (Exception e) {
            simulation.setSimulationStatus(SimulationStatus.FAILED);
            simulation.setResultsSummary("仿真失败: " + e.getMessage());
            throw new SimulationExecutionException("机械仿真执行失败", e);
        } finally {
            simulation.setEndTimestamp(LocalDateTime.now());
            simulationRepository.save(simulation);
        }
        
        return result;
    }
    
    /**
     * 塑封料流动仿真
     */
    public MoldingFlowSimulationResult performMoldingFlowAnalysis(MoldingFlowRequest request) {
        // 1. 创建仿真项目
        ICSimulationProject simulation = createSimulationProject(request, SimulationType.FLOW);
        
        MoldingFlowSimulationResult result = new MoldingFlowSimulationResult();
        result.setSimulationId(simulation.getSimulationId());
        
        try {
            // 2. 塑封料流体属性定义
            MoldCompoundProperties moldProperties = defineMoldCompoundProperties(
                request.getMoldCompoundType(), request.getCuringConditions());
            
            // 3. 模腔几何模型
            MoldCavityGeometry cavityGeometry = prepareMoldCavityGeometry(request.getCavityDesign());
            
            // 4. 流动边界条件
            FlowBoundaryConditions flowBoundaryConditions = setupFlowBoundaryConditions(
                request.getInjectionParameters(), request.getVentingDesign());
            
            // 5. 执行填充分析
            FillAnalysisResult fillResult = performFillAnalysis(
                cavityGeometry, moldProperties, flowBoundaryConditions);
            result.setFillAnalysisResult(fillResult);
            
            // 6. 固化分析
            CuringAnalysisResult curingResult = performCuringAnalysis(
                fillResult, moldProperties, request.getCuringProfile());
            result.setCuringAnalysisResult(curingResult);
            
            // 7. 缺陷预测
            DefectPredictionResult defectPrediction = predictMoldingDefects(
                fillResult, curingResult, request.getQualityRequirements());
            result.setDefectPredictionResult(defectPrediction);
            
            // 8. 工艺窗口分析
            ProcessWindowAnalysis processWindow = analyzeProcessWindow(
                result, request.getProcessVariabilities());
            result.setProcessWindowAnalysis(processWindow);
            
            // 9. 优化建议
            MoldingOptimizationRecommendations recommendations = generateOptimizationRecommendations(
                result, request.getOptimizationGoals());
            result.setOptimizationRecommendations(recommendations);
            
            simulation.setSimulationStatus(SimulationStatus.COMPLETED);
            simulation.setResultsSummary(String.format("塑封流动仿真完成，填充时间: %.2fs, 最大剪切速率: %.2f s⁻¹", 
                fillResult.getFillTime(), fillResult.getMaxShearRate()));
            
        } catch (Exception e) {
            simulation.setSimulationStatus(SimulationStatus.FAILED);
            simulation.setResultsSummary("仿真失败: " + e.getMessage());
            throw new SimulationExecutionException("塑封流动仿真执行失败", e);
        } finally {
            simulation.setEndTimestamp(LocalDateTime.now());
            simulationRepository.save(simulation);
        }
        
        return result;
    }
    
    /**
     * 仿真结果验证
     */
    public SimulationValidationResult validateSimulationResults(String simulationId, 
                                                               ExperimentalData experimentalData) {
        ICSimulationProject simulation = simulationRepository.findById(simulationId)
            .orElseThrow(() -> new EntityNotFoundException("仿真项目不存在"));
        
        SimulationValidationResult validation = new SimulationValidationResult();
        validation.setSimulationId(simulationId);
        validation.setValidationDate(LocalDateTime.now());
        
        // 1. 获取仿真结果
        List<ICSimulationResult> simulationResults = resultRepository.findBySimulationId(simulationId);
        
        // 2. 数据对比分析
        for (ICSimulationResult simResult : simulationResults) {
            ExperimentalDataPoint expData = experimentalData.findMatchingPoint(
                simResult.getLocationDescription(), simResult.getResultParameter());
            
            if (expData != null) {
                ValidationComparison comparison = new ValidationComparison();
                comparison.setParameter(simResult.getResultParameter());
                comparison.setLocation(simResult.getLocationDescription());
                comparison.setSimulationValue(simResult.getResultValue());
                comparison.setExperimentalValue(expData.getMeasuredValue());
                comparison.setAbsoluteError(Math.abs(simResult.getResultValue().doubleValue() 
                    - expData.getMeasuredValue().doubleValue()));
                comparison.setRelativeError(comparison.getAbsoluteError() / 
                    expData.getMeasuredValue().doubleValue() * 100);
                
                // 评估准确性
                if (comparison.getRelativeError() < 5.0) {
                    comparison.setAccuracyLevel(AccuracyLevel.EXCELLENT);
                } else if (comparison.getRelativeError() < 10.0) {
                    comparison.setAccuracyLevel(AccuracyLevel.GOOD);
                } else if (comparison.getRelativeError() < 20.0) {
                    comparison.setAccuracyLevel(AccuracyLevel.ACCEPTABLE);
                } else {
                    comparison.setAccuracyLevel(AccuracyLevel.POOR);
                }
                
                validation.addComparison(comparison);
            }
        }
        
        // 3. 统计分析
        ValidationStatistics statistics = calculateValidationStatistics(validation.getComparisons());
        validation.setStatistics(statistics);
        
        // 4. 模型可信度评估
        ModelCredibilityAssessment credibility = assessModelCredibility(validation);
        validation.setCredibilityAssessment(credibility);
        
        // 5. 改进建议
        List<ModelImprovementRecommendation> recommendations = generateImprovementRecommendations(
            validation, simulation);
        validation.setImprovementRecommendations(recommendations);
        
        return validation;
    }
}
```

---

*工艺开发管理模块为IC封测CIM系统提供了完整的工艺开发生命周期管理，包括DOE实验设计、工艺仿真分析、知识库管理等核心功能，确保工艺开发的科学性、效率性和知识积累*