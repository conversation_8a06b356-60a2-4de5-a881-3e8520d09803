/**
 * 组织架构管理相关的类型定义
 * IC封测工厂组织架构数据结构
 */

// 部门类型枚举
export enum DepartmentType {
  PRODUCTION = 'production', // 生产部门
  TECHNICAL = 'technical', // 技术部门
  SUPPORT = 'support', // 支持部门
  MANAGEMENT = 'management' // 管理部门
}

// 部门状态枚举
export enum DepartmentStatus {
  ACTIVE = 'active', // 活跃
  INACTIVE = 'inactive', // 停用
  PLANNED = 'planned' // 规划中
}

// 岗位级别枚举
export enum PositionLevel {
  DIRECTOR = 'director', // 总监级
  MANAGER = 'manager', // 经理级
  SUPERVISOR = 'supervisor', // 主管级
  SPECIALIST = 'specialist', // 专员级
  OPERATOR = 'operator' // 操作员级
}

// 员工状态枚举
export enum EmployeeStatus {
  ACTIVE = 'active', // 在职
  INACTIVE = 'inactive', // 离职
  PROBATION = 'probation', // 试用期
  SUSPENDED = 'suspended' // 停职
}

// 权限类型枚举
export enum PermissionType {
  MENU = 'menu', // 菜单权限
  FUNCTION = 'function', // 功能权限
  DATA = 'data' // 数据权限
}

// 部门信息接口
export interface Department {
  id: string // 部门ID
  code: string // 部门编码
  name: string // 部门名称
  type: DepartmentType // 部门类型
  level: number // 层级（1为顶级）
  parentId?: string // 父部门ID
  managerId?: string // 部门负责人ID
  description?: string // 部门描述
  responsibilities: string[] // 主要职责
  status: DepartmentStatus // 部门状态
  costCenterCode?: string // 成本中心代码
  location?: string // 办公地点
  phoneNumber?: string // 部门电话
  email?: string // 部门邮箱
  establishedDate: Date // 成立日期
  children?: Department[] // 子部门
  createdAt: Date // 创建时间
  updatedAt: Date // 更新时间
  createdBy: string // 创建人
  updatedBy: string // 更新人
}

// 岗位信息接口
export interface Position {
  id: string // 岗位ID
  code: string // 岗位编码
  name: string // 岗位名称
  departmentId: string // 所属部门ID
  level: PositionLevel // 岗位级别
  reportToId?: string // 汇报岗位ID
  description?: string // 岗位描述
  responsibilities: string[] // 岗位职责
  requirements: string[] // 任职要求
  skills: string[] // 技能要求
  salary?: {
    // 薪资范围
    min: number
    max: number
    currency: string
  }
  headcount: number // 编制人数
  occupiedCount: number // 已占用人数
  isKeyPosition: boolean // 是否关键岗位
  status: 'active' | 'inactive' | 'planned' // 岗位状态
  createdAt: Date // 创建时间
  updatedAt: Date // 更新时间
  createdBy: string // 创建人
  updatedBy: string // 更新人
}

// 员工信息接口
export interface Employee {
  id: string // 员工ID
  employeeNumber: string // 工号
  name: string // 姓名
  englishName?: string // 英文名
  departmentId: string // 部门ID
  positionId: string // 岗位ID
  directSupervisorId?: string // 直接主管ID
  email: string // 邮箱
  phoneNumber?: string // 手机号
  hireDate: Date // 入职日期
  status: EmployeeStatus // 员工状态
  avatar?: string // 头像URL
  gender: 'male' | 'female' // 性别
  birthDate?: Date // 出生日期
  education?: string // 学历
  workLocation: string // 工作地点
  emergencyContact?: {
    // 紧急联系人
    name: string
    relationship: string
    phoneNumber: string
  }
  createdAt: Date // 创建时间
  updatedAt: Date // 更新时间
}

// 权限配置接口
export interface Permission {
  id: string // 权限ID
  code: string // 权限编码
  name: string // 权限名称
  type: PermissionType // 权限类型
  parentId?: string // 父权限ID
  description?: string // 权限描述
  resource?: string // 资源路径
  action?: string // 操作动作
  children?: Permission[] // 子权限
  createdAt: Date // 创建时间
  updatedAt: Date // 更新时间
}

// 角色权限配置接口
export interface RolePermission {
  id: string // 配置ID
  departmentId?: string // 部门ID（部门权限）
  positionId?: string // 岗位ID（岗位权限）
  employeeId?: string // 员工ID（个人权限）
  permissions: string[] // 权限ID列表
  dataScope?: {
    // 数据权限范围
    type: 'all' | 'department' | 'self' | 'custom'
    departmentIds?: string[] // 自定义部门范围
  }
  isActive: boolean // 是否生效
  effectiveDate?: Date // 生效日期
  expiryDate?: Date // 失效日期
  createdAt: Date // 创建时间
  updatedAt: Date // 更新时间
  createdBy: string // 创建人
  updatedBy: string // 更新人
}

// 成本中心接口
export interface CostCenter {
  id: string // 成本中心ID
  code: string // 成本中心代码
  name: string // 成本中心名称
  type: 'production' | 'support' | 'management' // 类型
  departmentIds: string[] // 关联部门ID列表
  managerId: string // 负责人ID
  budget?: {
    // 预算信息
    annual: number // 年度预算
    currency: string // 币种
    year: number // 预算年度
  }
  description?: string // 描述
  isActive: boolean // 是否生效
  createdAt: Date // 创建时间
  updatedAt: Date // 更新时间
  createdBy: string // 创建人
  updatedBy: string // 更新人
}

// 组织架构变更记录接口
export interface OrganizationChange {
  id: string // 变更记录ID
  type: 'department' | 'position' | 'employee' | 'permission' // 变更类型
  action: 'create' | 'update' | 'delete' | 'move' // 变更动作
  targetId: string // 变更目标ID
  targetName: string // 变更目标名称
  oldValue?: any // 变更前值
  newValue?: any // 变更后值
  reason?: string // 变更原因
  approvedBy?: string // 审批人
  effectiveDate: Date // 生效日期
  createdAt: Date // 创建时间
  createdBy: string // 创建人
}

// 查询参数接口
export interface DepartmentQueryParams {
  keyword?: string // 关键词搜索
  type?: DepartmentType // 部门类型
  status?: DepartmentStatus // 部门状态
  parentId?: string // 父部门ID
  level?: number // 层级
  managerId?: string // 负责人ID
  page?: number // 页码
  pageSize?: number // 每页数量
}

export interface PositionQueryParams {
  keyword?: string // 关键词搜索
  departmentId?: string // 部门ID
  level?: PositionLevel // 岗位级别
  status?: string // 岗位状态
  isKeyPosition?: boolean // 是否关键岗位
  page?: number // 页码
  pageSize?: number // 每页数量
}

export interface EmployeeQueryParams {
  keyword?: string // 关键词搜索
  departmentId?: string // 部门ID
  positionId?: string // 岗位ID
  status?: EmployeeStatus // 员工状态
  hireYear?: number // 入职年份
  page?: number // 页码
  pageSize?: number // 每页数量
}

// 表单数据接口
export interface DepartmentFormData {
  name: string
  code: string
  type: DepartmentType
  parentId?: string
  managerId?: string
  description?: string
  responsibilities: string[]
  costCenterCode?: string
  location?: string
  phoneNumber?: string
  email?: string
}

export interface PositionFormData {
  name: string
  code: string
  departmentId: string
  level: PositionLevel
  reportToId?: string
  description?: string
  responsibilities: string[]
  requirements: string[]
  skills: string[]
  headcount: number
  isKeyPosition: boolean
}

export interface EmployeeFormData {
  name: string
  employeeNumber: string
  englishName?: string
  departmentId: string
  positionId: string
  directSupervisorId?: string
  email: string
  phoneNumber?: string
  hireDate: Date
  gender: 'male' | 'female'
  birthDate?: Date
  education?: string
  workLocation: string
  emergencyContact?: {
    name: string
    relationship: string
    phoneNumber: string
  }
}

// API响应接口
export interface DepartmentTreeResponse {
  code: number
  message: string
  data: Department[]
}

export interface PositionListResponse {
  code: number
  message: string
  data: {
    list: Position[]
    total: number
    page: number
    pageSize: number
  }
}

export interface EmployeeListResponse {
  code: number
  message: string
  data: {
    list: Employee[]
    total: number
    page: number
    pageSize: number
  }
}

export interface PermissionTreeResponse {
  code: number
  message: string
  data: Permission[]
}
