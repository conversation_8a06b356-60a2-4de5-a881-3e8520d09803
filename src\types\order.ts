// IC封测CIM系统 - 订单类型定义

// 订单状态枚举
export enum OrderStatus {
  PENDING = 'pending', // 待确认
  CONFIRMED = 'confirmed', // 已确认
  PROCESSING = 'processing', // 生产中
  TESTING = 'testing', // 测试中
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
  ON_HOLD = 'on_hold' // 暂停
}

// 订单优先级枚举
export enum OrderPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 封装类型枚举
export enum PackageType {
  QFP = 'QFP', // Quad Flat Package
  BGA = 'BGA', // Ball Grid Array
  CSP = 'CSP', // Chip Scale Package
  FC = 'FC', // Flip Chip
  SOP = 'SOP', // Small Outline Package
  TSOP = 'TSOP', // Thin Small Outline Package
  QFN = 'QFN', // Quad Flat No-leads
  DFN = 'DFN', // Dual Flat No-leads
  WLCSP = 'WLCSP', // Wafer Level Chip Scale Package
  SSOP = 'SSOP', // Shrink Small Outline Package
  TQFP = 'TQFP' // Thin Quad Flat Package
}

// 货币类型
export enum CurrencyType {
  CNY = 'CNY',
  USD = 'USD',
  EUR = 'EUR'
}

export interface Order {
  id: string
  orderNumber: string
  customerId: string
  customer: {
    id: string
    name: string
    code: string
    contact?: {
      name: string
      phone: string
      email: string
    }
  }
  productInfo: {
    productName: string
    productCode: string
    packageType: PackageType
    quantity: number // K pcs为单位
    waferSize: number // 晶圆尺寸（英寸）
    dieSize: string // 芯片尺寸
    leadCount: number // 引脚数量
    specifications: string // 规格说明
  }
  pricing: {
    unitPrice: number // 单价（元/K pcs）
    totalAmount: number // 总金额
    currency: CurrencyType
    paymentTerms: string // 付款条件
  }
  schedule: {
    orderDate: string // 下单日期
    confirmedDate?: string // 确认日期
    startDate?: string // 开始生产日期
    deliveryDate: string // 交期
    actualDeliveryDate?: string // 实际交付日期
  }
  status: OrderStatus
  priority: OrderPriority
  progress: {
    overall: number // 总进度（%）
    cpTesting: number // CP测试进度（%）
    assembly: number // 封装进度（%）
    ftTesting: number // FT测试进度（%）
    packaging: number // 包装进度（%）
  }
  qualityInfo: {
    yieldRequirement: number // 良率要求（%）
    currentYield?: number // 当前良率（%）
    defectRate?: number // 缺陷率（ppm）
    qualityLevel: 'A' | 'B' | 'C' | 'D'
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  notes?: string
  attachments?: Array<{
    id: string
    name: string
    url: string
    type: string
    uploadAt: string
  }>
}

// 订单查询参数
export interface OrderQueryParams {
  page?: number
  pageSize?: number
  orderNumber?: string
  customerId?: string
  customerName?: string
  status?: OrderStatus[]
  priority?: OrderPriority[]
  packageType?: PackageType[]
  orderDateRange?: [string, string]
  deliveryDateRange?: [string, string]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ===== 第一阶段专业功能：工作订单管理 =====

// 工作订单类型 - OSAT专业流程
export enum WorkOrderType {
  CP_TESTING = 'cp_testing', // CP晶圆测试
  ASSEMBLY = 'assembly', // 封装工艺
  FT_TESTING = 'ft_testing', // 最终测试
  PACKAGING = 'packaging', // 包装
  REWORK = 'rework' // 重工
}

// 工作订单状态
export enum WorkOrderStatus {
  PENDING = 'pending', // 等待开始
  IN_QUEUE = 'in_queue', // 排队中
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed', // 已完成
  ON_HOLD = 'on_hold', // 暂停
  CANCELLED = 'cancelled' // 取消
}

// 工作订单接口 - 第一阶段基础MES
export interface WorkOrder {
  id: string
  workOrderNumber: string
  parentOrderId: string // 关联的主订单ID
  type: WorkOrderType
  status: WorkOrderStatus
  priority: OrderPriority

  // OSAT专业信息
  productInfo: {
    lotId: string // 批次号
    quantity: number // 数量（pcs）
    packageType: PackageType
    waferSize: number // 晶圆尺寸
    diePerWafer?: number // 每片晶圆die数量
  }

  // 工艺参数
  processInfo: {
    equipmentId?: string // 指定设备ID
    recipeId?: string // Recipe配置ID
    testProgram?: string // 测试程序
    processTime: number // 预计加工时间（分钟）
    standardYield: number // 标准良率要求（%）
  }

  // 排产信息
  schedule: {
    plannedStartTime: string // 计划开始时间
    plannedEndTime: string // 计划结束时间
    actualStartTime?: string // 实际开始时间
    actualEndTime?: string // 实际结束时间
  }

  // 质量信息
  qualityResult?: {
    actualYield: number // 实际良率（%）
    defectCount: number // 缺陷数量
    reworkCount: number // 重工数量
    passedQuantity: number // 良品数量
    failedQuantity: number // 不良品数量
  }

  assignedOperator?: string // 指定操作员
  notes?: string
  createdAt: string
  updatedAt: string
}

// ===== 第一阶段专业功能：主生产计划 =====

// 生产计划接口 - OSAT优化
export interface ProductionPlan {
  id: string
  planNumber: string
  planName: string
  planPeriod: {
    startDate: string
    endDate: string
    planType: 'daily' | 'weekly' | 'monthly'
  }

  // 订单组
  orderGroups: Array<{
    groupId: string
    orders: string[] // 订单ID数组
    totalQuantity: number // 总数量
    packageTypes: PackageType[] // 封装类型
    priority: OrderPriority
  }>

  // 产能规划 - 基于测试和封装设备可用性
  capacityPlan: {
    cpTestingCapacity: number // CP测试产能（K pcs/日）
    assemblyCapacity: number // 封装产能（K pcs/日）
    ftTestingCapacity: number // FT测试产能（K pcs/日）
    bottleneckProcess?: string // 瓶颈工序
  }

  // 设备分配
  equipmentAllocation: Array<{
    processType: WorkOrderType
    equipmentIds: string[]
    allocatedHours: number // 分配工时
    utilization: number // 利用率（%）
  }>

  status: 'draft' | 'approved' | 'executing' | 'completed' | 'cancelled'
  approvedBy?: string
  createdAt: string
  updatedAt: string
}

// ===== 第一阶段专业功能：客户订单生命周期 =====

// ===== 第一阶段专业功能：客户询价管理 =====

// 询价状态枚举
export enum InquiryStatus {
  PENDING = 'pending', // 待评估
  EVALUATING = 'evaluating', // 评估中
  QUOTED = 'quoted', // 已报价
  CONFIRMED = 'confirmed', // 已确认
  REJECTED = 'rejected', // 已拒绝
  EXPIRED = 'expired' // 已过期
}

// 产品类型枚举 - IC应用领域
export enum ProductType {
  AUTOMOTIVE = 'automotive', // 汽车电子IC
  CONSUMER = 'consumer', // 消费电子IC
  COMMUNICATION = 'communication', // 通信IC
  INDUSTRIAL = 'industrial', // 工业IC
  MEDICAL = 'medical', // 医疗IC
  AEROSPACE = 'aerospace', // 航空航天IC
  COMPUTING = 'computing' // 计算IC
}

// 测试要求类型
export enum TestRequirement {
  CP_ONLY = 'cp_only', // 仅CP测试
  FT_ONLY = 'ft_only', // 仅FT测试
  CP_FT = 'cp_ft', // CP+FT测试
  RELIABILITY = 'reliability', // 可靠性测试
  CUSTOM = 'custom' // 定制测试
}

// 客户询价接口
export interface CustomerInquiry {
  id: string
  inquiryNumber: string // 询价编号
  customerId: string
  customer: {
    id: string
    name: string
    code: string
    contact: {
      name: string
      phone: string
      email: string
      department: string
    }
    industryType: ProductType // 客户行业类型
  }

  // 产品信息
  productInfo: {
    productName: string
    productCode?: string
    productType: ProductType // 产品应用类型
    packageType: PackageType
    quantity: number // K pcs为单位
    quantityLevel: 'K' | 'M' // K级(千)或M级(百万)
    waferSize: number // 晶圆尺寸（英寸）
    dieSize: string // 芯片尺寸
    leadCount: number // 引脚数量
    specifications: string // 详细规格说明
    datasheet?: string // 数据手册链接
  }

  // 测试要求
  testRequirements: {
    testType: TestRequirement
    cpTestRequirement?: string // CP测试要求
    ftTestRequirement?: string // FT测试要求
    reliabilityTest?: boolean // 是否需要可靠性测试
    customTestSpec?: string // 定制测试规格
    yieldRequirement: number // 良率要求（%）
  }

  // 时间要求
  schedule: {
    inquiryDate: string // 询价日期
    expectedQuoteDate: string // 期望报价日期
    targetDeliveryDate: string // 目标交期
    productionStartDate?: string // 期望开始生产日期
    urgencyLevel: OrderPriority // 紧急程度
  }

  // 商务信息
  businessInfo: {
    budgetRange?: {
      min: number
      max: number
      currency: CurrencyType
    }
    paymentTerms?: string // 期望付款条件
    contractDuration?: string // 合同期限
    volumeCommitment?: number // 年度数量承诺
  }

  // 询价状态管理
  status: InquiryStatus
  priority: OrderPriority
  assignedSalesManager?: string // 指定销售经理
  assignedEngineer?: string // 指定工程师

  // 评估进度
  evaluation: {
    technicalFeasibility?: 'feasible' | 'challenging' | 'not_feasible' // 技术可行性
    capacityAvailability?: 'available' | 'limited' | 'unavailable' // 产能可用性
    costEstimation?: {
      cpTestCost: number // CP测试成本
      assemblyCost: number // 封装成本
      ftTestCost: number // FT测试成本
      totalUnitCost: number // 总单位成本
    }
    riskAssessment?: 'low' | 'medium' | 'high' // 风险评估
    evaluationNotes?: string // 评估备注
  }

  // 报价信息
  quotation?: {
    quoteNumber: string // 报价单号
    unitPrice: number // 单价（元/K pcs）
    totalAmount: number // 总金额
    currency: CurrencyType
    validityPeriod: string // 报价有效期
    paymentTerms: string // 付款条件
    deliveryTerms: string // 交货条件
    quotedAt: string // 报价时间
    quotedBy: string // 报价人
  }

  createdAt: string
  updatedAt: string
  createdBy: string
  notes?: string
  attachments?: Array<{
    id: string
    name: string
    url: string
    type: string
    uploadAt: string
  }>
}

// 询价查询参数
export interface InquiryQueryParams {
  page?: number
  pageSize?: number
  inquiryNumber?: string
  customerId?: string
  customerName?: string
  status?: InquiryStatus[]
  priority?: OrderPriority[]
  productType?: ProductType[]
  packageType?: PackageType[]
  testType?: TestRequirement[]
  inquiryDateRange?: [string, string]
  expectedQuoteDateRange?: [string, string]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 询价列表响应
export interface InquiryListResponse {
  data: CustomerInquiry[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 创建询价数据
export interface CreateInquiryData {
  customerId: string
  productInfo: {
    productName: string
    productCode?: string
    productType: ProductType
    packageType: PackageType
    quantity: number
    quantityLevel: 'K' | 'M'
    waferSize: number
    dieSize: string
    leadCount: number
    specifications: string
    datasheet?: string
  }
  testRequirements: {
    testType: TestRequirement
    cpTestRequirement?: string
    ftTestRequirement?: string
    reliabilityTest?: boolean
    customTestSpec?: string
    yieldRequirement: number
  }
  schedule: {
    expectedQuoteDate: string
    targetDeliveryDate: string
    productionStartDate?: string
    urgencyLevel: OrderPriority
  }
  businessInfo?: {
    budgetRange?: {
      min: number
      max: number
      currency: CurrencyType
    }
    paymentTerms?: string
    contractDuration?: string
    volumeCommitment?: number
  }
  notes?: string
}

// 询价统计数据
export interface InquiryStats {
  totalInquiries: number // 总询价数
  pendingInquiries: number // 待评估询价
  quotedInquiries: number // 已报价询价
  confirmedInquiries: number // 已确认询价
  conversionRate: number // 转化率（%）
  averageQuoteTime: number // 平均报价时间（天）
  totalQuotedValue: number // 总报价金额
  monthlyInquiryTrend: Array<{
    month: string
    count: number
    value: number
  }>
}

// 订单生命周期阶段
export enum OrderLifecycleStage {
  INQUIRY = 'inquiry', // 询价
  QUOTATION = 'quotation', // 报价
  ORDER_PLACED = 'order_placed', // 下单
  ORDER_CONFIRMED = 'order_confirmed', // 确认
  IN_PRODUCTION = 'in_production', // 生产中
  QUALITY_CHECK = 'quality_check', // 质检
  PACKAGING = 'packaging', // 包装
  SHIPPING = 'shipping', // 发货
  DELIVERED = 'delivered', // 已交付
  CLOSED = 'closed' // 关闭
}

// 订单生命周期管理
export interface OrderLifecycle {
  orderId: string
  currentStage: OrderLifecycleStage
  stageHistory: Array<{
    stage: OrderLifecycleStage
    enteredAt: string
    exitedAt?: string
    duration?: number // 停留时间（小时）
    operator: string
    notes?: string
  }>

  // 关键时间节点
  keyMilestones: {
    orderReceived: string
    productionStarted?: string
    cpTestingCompleted?: string
    assemblyCompleted?: string
    ftTestingCompleted?: string
    qualityApproved?: string
    shipped?: string
    delivered?: string
  }

  // 封装类型规格 - OSAT专业要求
  packagingSpecifications: {
    packageType: PackageType
    packageDimensions: {
      length: number // 长度（mm）
      width: number // 宽度（mm）
      height: number // 厚度（mm）
    }
    leadPitch: number // 引脚间距（mm）
    thermalCharacteristics: {
      thermalResistance: number // 热阻（°C/W）
      powerDissipation: number // 功耗（W）
    }
    testSpecifications: {
      cpTestLimits: string // CP测试限值
      ftTestLimits: string // FT测试限值
      reliabilityRequirements: string // 可靠性要求
    }
  }
}

// 搜索表单
export interface OrderSearchForm {
  orderNumber: string
  customerId: string
  customerName: string
  status: OrderStatus[]
  priority: OrderPriority[]
  packageType: PackageType[]
  orderDateRange: [string, string] | null
  deliveryDateRange: [string, string] | null
}

// 创建订单数据
export interface CreateOrderData {
  customerId: string
  productInfo: {
    productName: string
    productCode: string
    packageType: PackageType
    quantity: number
    waferSize: number
    dieSize: string
    leadCount: number
    specifications: string
  }
  pricing: {
    unitPrice: number
    currency: CurrencyType
    paymentTerms: string
  }
  schedule: {
    deliveryDate: string
  }
  priority: OrderPriority
  qualityInfo: {
    yieldRequirement: number
    qualityLevel: 'A' | 'B' | 'C' | 'D'
  }
  notes?: string
}

// 订单列表响应
export interface OrderListResponse {
  data: Order[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 分页信息
export interface Pagination {
  current: number
  pageSize: number
  total: number
  showSizeChanger: boolean
  showQuickJumper: boolean
  showTotal: boolean
}

// 订单统计数据
export interface OrderStats {
  totalOrders: number // 总订单数
  processingOrders: number // 生产中订单
  completedOrders: number // 已完成订单
  overdueOrders: number // 逾期订单
  totalVolume: number // 总产能（K pcs）
  monthlyVolume: number // 月产能（K pcs）
  averageYield: number // 平均良率（%）
  onTimeDeliveryRate: number // 准时交付率（%）
}

export interface OrderStatCard {
  key: string
  label: string
  value: string | number
  detail: string
  trend: 'up' | 'down' | 'stable'
  changePercent: string
  trendClass: string
  iconClass: string
  icon: string
  color: string
}

// 搜索表单类型
export interface SearchForm {
  orderNumber: string
  customerId: string
  status: OrderStatus[]
  priority: OrderPriority[]
  dateRange: [string, string] | null
}

// 分页类型
export interface Pagination {
  current: number
  pageSize: number
  total: number
  showSizeChanger: boolean
  showQuickJumper: boolean
  showTotal: boolean
}

// ===== 订单跟踪专业功能 =====

// 生产阶段枚举 - OSAT专业流程
export enum ProductionStage {
  MATERIAL_READY = 'material_ready', // 物料准备
  CP_TESTING = 'cp_testing', // CP测试
  DICING = 'dicing', // 晶圆切割
  DIE_ATTACH = 'die_attach', // 贴片
  WIRE_BOND = 'wire_bond', // 线键合
  MOLDING = 'molding', // 塑封
  MARKING = 'marking', // 打标
  TRIM_FORM = 'trim_form', // 切筋成型
  FT_TESTING = 'ft_testing', // 最终测试
  BURN_IN = 'burn_in', // 老化测试
  FINAL_QC = 'final_qc', // 最终质检
  PACKAGING = 'packaging', // 包装
  SHIPPING = 'shipping' // 发货
}

// 阶段状态枚举
export enum StageStatus {
  PENDING = 'pending', // 等待中
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed', // 已完成
  DELAYED = 'delayed', // 延迟
  BLOCKED = 'blocked', // 阻塞
  REWORK = 'rework', // 重工
  SKIPPED = 'skipped' // 跳过
}

// 异常事件类型
export enum EventType {
  INFO = 'info', // 信息
  WARNING = 'warning', // 警告
  ERROR = 'error', // 错误
  ALERT = 'alert', // 告警
  MILESTONE = 'milestone' // 里程碑
}

// 异常事件严重程度
export enum EventSeverity {
  LOW = 'low', // 低
  MEDIUM = 'medium', // 中
  HIGH = 'high', // 高
  CRITICAL = 'critical' // 严重
}

// 生产阶段信息
export interface ProductionStageInfo {
  stage: ProductionStage
  stageName: string
  status: StageStatus
  progress: number // 进度百分比
  plannedStartTime?: string
  plannedEndTime?: string
  actualStartTime?: string
  actualEndTime?: string
  duration?: number // 实际用时（分钟）
  standardDuration: number // 标准用时（分钟）
  assignedEquipment?: string
  assignedOperator?: string
  yield?: number // 该阶段良率
  throughput?: number // 产出量
  notes?: string
}

// 异常事件
export interface ProductionEvent {
  id: string
  orderId: string
  stage: ProductionStage
  eventType: EventType
  severity: EventSeverity
  title: string
  description: string
  occurredAt: string
  resolvedAt?: string
  duration?: number // 持续时间（分钟）
  responsiblePerson?: string
  impact: string // 影响描述
  solution?: string // 解决方案
  preventiveAction?: string // 预防措施
  status: 'open' | 'investigating' | 'resolved' | 'closed'
  tags?: string[]
}

// 质量检测节点
export interface QualityCheckpoint {
  id: string
  checkpointName: string
  stage: ProductionStage
  checkType: 'IQC' | 'IPQC' | 'FQC' | 'OQC' | 'RELIABILITY'
  status: 'pending' | 'in_progress' | 'passed' | 'failed' | 'waived'
  checkParameters: Array<{
    parameter: string
    specification: string
    actualValue?: string
    result: 'pass' | 'fail' | 'na'
  }>
  inspector?: string
  checkTime?: string
  notes?: string
}

// 客户通知记录
export interface CustomerNotification {
  id: string
  orderId: string
  notificationType: 'email' | 'sms' | 'portal' | 'phone'
  recipientName: string
  recipientContact: string
  subject: string
  content: string
  sentAt: string
  status: 'sent' | 'delivered' | 'read' | 'failed'
  response?: string
  responseAt?: string
}

// 订单跟踪详细信息
export interface OrderTrackingDetail {
  id: string
  orderNumber: string
  order: Order

  // 生产进度信息
  productionProgress: {
    currentStage: ProductionStage
    overallProgress: number // 总体进度
    stages: ProductionStageInfo[]
    estimatedCompletionTime: string
    actualCompletionTime?: string
    isOnSchedule: boolean
    delayDays?: number
  }

  // 实时生产数据
  realTimeData: {
    currentQuantity: number // 当前完成数量
    remainingQuantity: number // 剩余数量
    actualYield: number // 实际良率
    targetYield: number // 目标良率
    defectRate: number // 缺陷率（ppm）
    reworkQuantity: number // 重工数量
    scrapQuantity: number // 报废数量
  }

  // 设备使用情况
  equipmentStatus: Array<{
    equipmentId: string
    equipmentName: string
    status: 'idle' | 'running' | 'maintenance' | 'error'
    utilization: number // 利用率
    currentRecipe?: string
    assignedStage: ProductionStage
  }>

  // 质量检测节点
  qualityCheckpoints: QualityCheckpoint[]

  // 异常事件记录
  events: ProductionEvent[]

  // 客户通知记录
  notifications: CustomerNotification[]

  // 物流信息
  logistics?: {
    packaging: {
      packagingType: string
      packageQuantity: number
      palletQuantity: number
      packagingDate?: string
    }
    shipping: {
      shippingMethod: string
      trackingNumber?: string
      shippedDate?: string
      estimatedArrival?: string
      actualArrival?: string
      carrier: string
    }
  }

  updatedAt: string
}

// 甘特图数据
export interface GanttChartData {
  orderId: string
  stages: Array<{
    stage: ProductionStage
    stageName: string
    plannedStart: string
    plannedEnd: string
    actualStart?: string
    actualEnd?: string
    progress: number
    status: StageStatus
    dependencies?: ProductionStage[]
  }>
}

// 生产统计数据
export interface ProductionStatistics {
  orderId: string
  period: 'daily' | 'weekly' | 'monthly'
  data: Array<{
    date: string
    plannedQuantity: number
    actualQuantity: number
    yieldRate: number
    defectCount: number
    reworkCount: number
    equipmentUtilization: number
  }>
}

// 订单跟踪查询参数
export interface OrderTrackingQuery {
  orderId?: string
  orderNumber?: string
  currentStage?: ProductionStage[]
  status?: StageStatus[]
  isDelayed?: boolean
  hasEvents?: boolean
  dateRange?: [string, string]
}

// ===== 订单评审专业功能 =====

// 评审部门枚举
export enum OrderReviewDepartment {
  TECHNICAL = 'technical', // 技术部
  CAPACITY = 'capacity', // 产能部
  QUALITY = 'quality', // 质量部
  SUPPLY_CHAIN = 'supply_chain', // 供应链部
  FINANCE = 'finance' // 财务部
}

// 评审状态枚举
export enum OrderReviewStatus {
  PENDING = 'pending', // 待评审
  IN_PROGRESS = 'in_progress', // 评审中
  APPROVED = 'approved', // 已批准
  CONDITIONALLY_APPROVED = 'conditionally_approved', // 有条件批准
  REJECTED = 'rejected', // 已拒绝
  ON_HOLD = 'on_hold' // 暂停评审
}

// 风险等级枚举
export enum RiskLevel {
  LOW = 'low', // 低风险
  MEDIUM = 'medium', // 中风险
  HIGH = 'high', // 高风险
  CRITICAL = 'critical' // 严重风险
}

// 评审结果枚举
export enum ReviewResult {
  PASS = 'pass', // 通过
  CONDITIONAL_PASS = 'conditional_pass', // 有条件通过
  FAIL = 'fail' // 不通过
}

// 技术评审详细信息
export interface TechnicalReviewDetail {
  processCompatibility: ReviewResult // 工艺兼容性
  processNotes?: string
  equipmentCapability: ReviewResult // 设备能力
  equipmentNotes?: string
  dftRequirement: ReviewResult // DFT要求
  dftNotes?: string
  testSolution: ReviewResult // 测试方案
  testNotes?: string
  yieldFeasibility: ReviewResult // 良率可行性
  yieldNotes?: string
  riskLevel: RiskLevel
  estimatedDevelopmentTime?: number // 预计开发时间（天）
  requiredCapitalInvestment?: number // 需要的资本投资
}

// 产能评审详细信息
export interface CapacityReviewDetail {
  cpLineCapacity: ReviewResult // CP测试产线产能
  cpNotes?: string
  cpUtilization: number // CP产线利用率（%）
  assemblyCapacity: ReviewResult // 封装产线产能
  assemblyNotes?: string
  assemblyUtilization: number // 封装产线利用率（%）
  ftLineCapacity: ReviewResult // FT测试产线产能
  ftNotes?: string
  ftUtilization: number // FT产线利用率（%）
  equipmentScheduling: ReviewResult // 设备排程
  schedulingNotes?: string
  bottleneckProcess?: ProductionStage // 瓶颈工序
  capacityRisk: RiskLevel
  recommendedProductionWindow?: string // 推荐生产窗口
}

// 质量评审详细信息
export interface QualityReviewDetail {
  customerStandards: ReviewResult // 客户标准
  standardsNotes?: string
  iatf16949Compliance: ReviewResult // IATF16949合规性
  iatfNotes?: string
  reliabilityRequirement: ReviewResult // 可靠性要求
  reliabilityNotes?: string
  testCapability: ReviewResult // 测试能力
  testCapabilityNotes?: string
  qualificationRequirement?: boolean // 是否需要器件认证
  qualificationTime?: number // 认证所需时间（天）
  qualityRisk: RiskLevel
  proposedQualityPlan?: string // 质量控制计划
}

// 供应链评审详细信息
export interface SupplyChainReviewDetail {
  waferSupply: ReviewResult // 晶圆供应
  waferNotes?: string
  waferLeadTime: number // 晶圆交期（天）
  substrateSupply: ReviewResult // 基板供应
  substrateNotes?: string
  substrateLeadTime: number // 基板交期（天）
  materialBom: ReviewResult // 材料BOM
  bomNotes?: string
  alternativeSources?: boolean // 是否有替代供应商
  inventoryBuffer: number // 安全库存（天）
  supplyRisk: RiskLevel
  totalLeadTime: number // 总交期（天）
  costImpact?: number // 成本影响（%）
}

// 财务评审详细信息
export interface FinanceReviewDetail {
  creditRisk: ReviewResult // 信用风险
  creditNotes?: string
  creditLimit?: number // 信用额度
  pricingReasonable: ReviewResult // 定价合理性
  pricingNotes?: string
  margin: number // 毛利率（%）
  paymentTerms: ReviewResult // 收款条件
  paymentNotes?: string
  exchangeRateRisk: ReviewResult // 汇率风险
  exchangeNotes?: string
  cashFlowImpact: number // 现金流影响
  financialRisk: RiskLevel
  profitabilityAssessment: 'excellent' | 'good' | 'acceptable' | 'poor'
}

// 部门评审任务
export interface OrderReviewTask {
  id: string
  orderId: string
  department: OrderReviewDepartment
  departmentName: string
  assignedTo: string // 评审负责人
  status: 'pending' | 'in_progress' | 'completed' | 'rejected'
  priority: OrderPriority
  dueDate: string // 评审截止日期
  startedAt?: string
  completedAt?: string
  result?: ReviewResult
  overallComments: string
  attachments?: Array<{
    id: string
    name: string
    url: string
    type: string
    uploadAt: string
  }>

  // 各部门专业评审详情
  technicalDetail?: TechnicalReviewDetail
  capacityDetail?: CapacityReviewDetail
  qualityDetail?: QualityReviewDetail
  supplyChainDetail?: SupplyChainReviewDetail
  financeDetail?: FinanceReviewDetail
}

// 风险识别项
export interface RiskItem {
  id: string
  category: OrderReviewDepartment
  riskType: string
  description: string
  probability: number // 发生概率（%）
  impact: RiskLevel
  mitigation: string // 缓解措施
  responsible: string // 负责人
  status: 'identified' | 'mitigating' | 'resolved' | 'accepted'
  targetDate?: string // 解决目标日期
}

// 评审决策记录
export interface ReviewDecision {
  id: string
  orderId: string
  decisionBy: string // 决策人
  decisionAt: string
  decision: 'approved' | 'conditionally_approved' | 'rejected'
  overallRisk: RiskLevel
  conditions?: string[] // 批准条件
  rejectionReasons?: string[] // 拒绝原因
  businessJustification: string // 商业理由
  nextActions: string[] // 后续行动
  reviewNotes: string
  approvalLimit?: number // 审批权限
}

// 订单评审完整信息
export interface OrderReview {
  id: string
  orderId: string
  order: Order
  reviewStatus: OrderReviewStatus
  initiatedBy: string
  initiatedAt: string
  completedAt?: string

  // 评审任务列表
  reviewTasks: OrderReviewTask[]

  // 风险评估
  riskAssessment: {
    overallRisk: RiskLevel
    riskItems: RiskItem[]
    riskMatrix: Array<{
      department: OrderReviewDepartment
      riskLevel: RiskLevel
      score: number
    }>
  }

  // 评审决策
  decision?: ReviewDecision

  // 评审进度
  progress: {
    completedTasks: number
    totalTasks: number
    percentage: number
    averageScore: number // 平均评分
    criticalIssues: number // 关键问题数量
  }

  // 时间节点
  timeline: Array<{
    event: string
    timestamp: string
    actor: string
    description: string
    category: 'milestone' | 'task' | 'decision' | 'issue'
  }>

  updatedAt: string
}

// 评审统计数据
export interface ReviewStatistics {
  totalReviews: number // 总评审数
  pendingReviews: number // 待评审数
  approvedReviews: number // 已批准数
  rejectedReviews: number // 已拒绝数
  averageReviewTime: number // 平均评审时间（天）
  approvalRate: number // 批准率（%）

  // 各部门评审效率
  departmentStats: Array<{
    department: OrderReviewDepartment
    averageTime: number // 平均评审时间（天）
    approvalRate: number // 部门批准率（%）
    workload: number // 工作负载
  }>

  // 风险分布
  riskDistribution: Array<{
    riskLevel: RiskLevel
    count: number
    percentage: number
  }>

  // 月度趋势
  monthlyTrend: Array<{
    month: string
    submitted: number
    approved: number
    rejected: number
    averageTime: number
  }>
}
