# 工作流引擎模块设计

## 1. 模块概述

### 1.1 模块定位
工作流引擎模块为CIM系统提供统一的业务流程管理能力，支持订单审批、质量审批、设备维护、异常处理等各种业务流程的建模、执行和监控。

### 1.2 复用价值
- **流程标准化**：统一的流程建模和执行标准
- **高复用性**：支持各种类型的审批和业务流程
- **提升效率**：可视化流程设计，快速配置新流程
- **过程透明**：完整的流程执行跟踪和监控

### 1.3 应用场景
```
工作流引擎复用场景
├── 订单管理流程
│   ├── 订单评审流程
│   ├── 订单变更审批
│   └── 交期调整审批
├── 质量管理流程  
│   ├── 不合格品评审
│   ├── 质量异常处理
│   └── 改进措施审批
├── 设备管理流程
│   ├── 维护工单审批
│   ├── 设备采购审批
│   └── 设备报废审批
├── 人员管理流程
│   ├── 请假审批流程
│   ├── 培训申请审批
│   └── 绩效评估流程
└── 系统管理流程
    ├── 权限申请审批
    ├── 系统配置变更
    └── 数据变更审批
```

## 2. 技术架构

### 2.1 架构设计
```
工作流引擎架构
├── 流程设计器              # 可视化流程建模
├── 流程引擎核心            # BPMN执行引擎
├── 任务管理中心            # 任务分配和执行
├── 表单引擎                # 动态表单生成
├── 规则引擎                # 条件判断和路由
├── 监控分析中心            # 流程监控和分析
└── 集成适配层              # 与业务系统集成
```

### 2.2 核心组件

#### 2.2.1 流程定义引擎
```java
@Entity
public class ProcessDefinition {
    @Id
    private String processId;           // 流程ID
    private String processKey;          // 流程标识
    private String processName;         // 流程名称
    private String category;            // 流程分类
    private Integer version;            // 版本号
    private String bpmnXml;            // BPMN流程定义
    private String formConfig;         // 表单配置
    private ProcessStatus status;       // 状态：draft/active/suspended
    private LocalDateTime deployTime;   // 部署时间
    private String deployBy;           // 部署人
}

@Entity
public class ProcessNode {
    @Id
    private String nodeId;             // 节点ID
    private String processId;          // 所属流程
    private String nodeName;           // 节点名称
    private NodeType nodeType;         // 节点类型
    private String assigneeType;       // 分配类型：user/role/expression
    private String assigneeValue;      // 分配值
    private String formKey;            // 表单标识
    private String conditions;         // 执行条件
    private Integer timeoutHours;      // 超时时间（小时）
    private String escalationRule;     // 升级规则
}
```

#### 2.2.2 流程实例管理
```java
@Entity
public class ProcessInstance {
    @Id
    private String instanceId;         // 实例ID
    private String processId;          // 流程定义ID
    private String businessKey;        // 业务关键字
    private String title;              // 流程标题
    private String initiator;          // 发起人
    private LocalDateTime startTime;   // 开始时间
    private LocalDateTime endTime;     // 结束时间
    private ProcessStatus status;      // 状态
    private String currentNode;        // 当前节点
    private Integer priority;          // 优先级
    private Map<String, Object> variables; // 流程变量
}

@Entity
public class TaskInstance {
    @Id
    private String taskId;             // 任务ID
    private String instanceId;         // 流程实例ID
    private String nodeId;             // 节点ID
    private String taskName;           // 任务名称
    private String assignee;           // 执行人
    private String candidateUsers;     // 候选用户
    private String candidateGroups;    // 候选组
    private LocalDateTime createTime;  // 创建时间
    private LocalDateTime claimTime;   // 签收时间
    private LocalDateTime completeTime; // 完成时间
    private LocalDateTime dueDate;     // 到期时间
    private TaskStatus status;         // 任务状态
    private String formData;           // 表单数据
    private String comments;           // 处理意见
}
```

## 3. 可视化流程设计器

### 3.1 BPMN支持
```javascript
// 基于bpmn.js的流程设计器
class ProcessDesigner {
    constructor(containerId) {
        this.viewer = new BpmnJS({
            container: containerId,
            keyboard: { bindTo: document },
            additionalModules: [
                customPropertiesPanel,
                customPalette,
                customContextPad
            ]
        });
    }
    
    // 创建新流程
    createNewProcess() {
        const bpmnXml = `
        <?xml version="1.0" encoding="UTF-8"?>
        <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL">
          <bpmn:process id="Process_1" isExecutable="true">
            <bpmn:startEvent id="StartEvent_1"/>
          </bpmn:process>
        </bpmn:definitions>`;
        
        return this.viewer.importXML(bpmnXml);
    }
    
    // 保存流程定义
    async saveProcess() {
        const { xml } = await this.viewer.saveXML({ format: true });
        const processDefinition = {
            processKey: this.processKey,
            processName: this.processName,
            category: this.category,
            bpmnXml: xml,
            formConfig: this.getFormConfig()
        };
        
        return this.processService.saveDefinition(processDefinition);
    }
}
```

### 3.2 节点类型扩展
```javascript
// 自定义节点类型
const customNodes = {
    // 审批节点
    'custom:approval-task': {
        name: '审批任务',
        icon: 'approval-icon',
        properties: ['assignee', 'dueDate', 'formKey', 'escalation']
    },
    
    // 系统任务节点
    'custom:system-task': {
        name: '系统任务',
        icon: 'system-icon', 
        properties: ['serviceClass', 'method', 'parameters']
    },
    
    // 通知节点
    'custom:notification-task': {
        name: '通知任务',
        icon: 'notification-icon',
        properties: ['recipients', 'template', 'channels']
    },
    
    // 条件网关
    'custom:condition-gateway': {
        name: '条件网关',
        icon: 'gateway-icon',
        properties: ['conditions', 'defaultPath']
    }
};
```

## 4. 流程引擎核心

### 4.1 流程执行引擎
```java
@Service
public class ProcessEngine {
    
    // 启动流程实例
    public ProcessInstance startProcess(String processKey, String businessKey, 
                                      String initiator, Map<String, Object> variables) {
        ProcessDefinition definition = getLatestProcessDefinition(processKey);
        
        ProcessInstance instance = new ProcessInstance();
        instance.setInstanceId(IdGenerator.generateId());
        instance.setProcessId(definition.getProcessId());
        instance.setBusinessKey(businessKey);
        instance.setInitiator(initiator);
        instance.setStartTime(LocalDateTime.now());
        instance.setStatus(ProcessStatus.RUNNING);
        instance.setVariables(variables);
        
        // 创建开始任务
        createStartTask(instance, definition);
        
        return processInstanceRepository.save(instance);
    }
    
    // 完成任务
    public void completeTask(String taskId, String userId, Map<String, Object> variables, 
                           String comments) {
        TaskInstance task = getTaskById(taskId);
        validateTaskAssignee(task, userId);
        
        // 更新任务状态
        task.setCompleteTime(LocalDateTime.now());
        task.setStatus(TaskStatus.COMPLETED);
        task.setComments(comments);
        
        // 更新流程变量
        ProcessInstance instance = getProcessInstance(task.getInstanceId());
        instance.getVariables().putAll(variables);
        
        // 流转到下一节点
        moveToNextNode(instance, task, variables);
        
        taskRepository.save(task);
    }
    
    // 流转到下一节点
    private void moveToNextNode(ProcessInstance instance, TaskInstance currentTask, 
                              Map<String, Object> variables) {
        ProcessDefinition definition = getProcessDefinition(instance.getProcessId());
        ProcessNode currentNode = getProcessNode(currentTask.getNodeId());
        
        List<ProcessNode> nextNodes = getNextNodes(definition, currentNode, variables);
        
        for (ProcessNode nextNode : nextNodes) {
            if (nextNode.getNodeType() == NodeType.END_EVENT) {
                // 流程结束
                instance.setStatus(ProcessStatus.COMPLETED);
                instance.setEndTime(LocalDateTime.now());
            } else {
                // 创建下一个任务
                createTask(instance, nextNode);
            }
        }
    }
}
```

### 4.2 任务分配策略
```java
@Component
public class TaskAssignmentStrategy {
    
    public List<String> assignTask(ProcessNode node, Map<String, Object> variables) {
        String assigneeType = node.getAssigneeType();
        String assigneeValue = node.getAssigneeValue();
        
        switch (assigneeType) {
            case "user":
                return Arrays.asList(assigneeValue);
                
            case "role":
                return userService.getUsersByRole(assigneeValue);
                
            case "expression":
                return evaluateExpression(assigneeValue, variables);
                
            case "department":
                String deptId = (String) variables.get("departmentId");
                return userService.getUsersByDepartment(deptId);
                
            case "manager":
                String userId = (String) variables.get("initiator");
                return Arrays.asList(userService.getDirectManager(userId));
                
            default:
                throw new IllegalArgumentException("Unknown assignee type: " + assigneeType);
        }
    }
    
    private List<String> evaluateExpression(String expression, Map<String, Object> variables) {
        // 使用SPEL表达式解析
        SpelExpressionParser parser = new SpelExpressionParser();
        Expression exp = parser.parseExpression(expression);
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariables(variables);
        
        Object result = exp.getValue(context);
        if (result instanceof String) {
            return Arrays.asList((String) result);
        } else if (result instanceof List) {
            return (List<String>) result;
        }
        
        return Collections.emptyList();
    }
}
```

## 5. 动态表单引擎

### 5.1 表单配置
```json
{
  "formKey": "order_approval_form",
  "formName": "订单审批表单",
  "layout": "vertical",
  "sections": [
    {
      "title": "订单基本信息",
      "fields": [
        {
          "name": "orderNo",
          "label": "订单编号",
          "type": "input",
          "readonly": true,
          "value": "${orderNo}"
        },
        {
          "name": "customerName", 
          "label": "客户名称",
          "type": "input",
          "readonly": true,
          "value": "${customerName}"
        },
        {
          "name": "totalAmount",
          "label": "订单金额",
          "type": "number",
          "readonly": true,
          "value": "${totalAmount}",
          "format": "currency"
        }
      ]
    },
    {
      "title": "审批信息",
      "fields": [
        {
          "name": "approvalResult",
          "label": "审批结果",
          "type": "radio",
          "required": true,
          "options": [
            {"value": "approve", "label": "同意"},
            {"value": "reject", "label": "拒绝"},
            {"value": "return", "label": "退回"}
          ]
        },
        {
          "name": "comments",
          "label": "审批意见",
          "type": "textarea",
          "required": true,
          "maxLength": 500
        }
      ]
    }
  ]
}
```

### 5.2 表单渲染引擎
```vue
<template>
  <div class="dynamic-form">
    <el-form :model="formData" :rules="formRules" ref="dynamicForm" label-width="120px">
      <div v-for="section in formConfig.sections" :key="section.title" class="form-section">
        <h3>{{ section.title }}</h3>
        <el-row :gutter="20">
          <el-col v-for="field in section.fields" :key="field.name" 
                  :span="field.colspan || 12">
            <el-form-item :label="field.label" :prop="field.name">
              <!-- 输入框 -->
              <el-input v-if="field.type === 'input'"
                       v-model="formData[field.name]"
                       :readonly="field.readonly"
                       :placeholder="field.placeholder"/>
              
              <!-- 数字输入 -->
              <el-input-number v-else-if="field.type === 'number'"
                              v-model="formData[field.name]"
                              :readonly="field.readonly"/>
              
              <!-- 单选框 -->
              <el-radio-group v-else-if="field.type === 'radio'"
                             v-model="formData[field.name]">
                <el-radio v-for="option in field.options" 
                         :key="option.value" 
                         :label="option.value">
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
              
              <!-- 文本域 -->
              <el-input v-else-if="field.type === 'textarea'"
                       type="textarea"
                       v-model="formData[field.name]"
                       :maxlength="field.maxLength"
                       show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    
    <div class="form-actions">
      <el-button @click="submitForm" type="primary">提交</el-button>
      <el-button @click="saveForm">保存</el-button>
      <el-button @click="cancelForm">取消</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DynamicForm',
  props: {
    formConfig: Object,
    taskId: String,
    processVariables: Object
  },
  data() {
    return {
      formData: {},
      formRules: {}
    };
  },
  created() {
    this.initFormData();
    this.buildValidationRules();
  },
  methods: {
    initFormData() {
      // 根据表单配置和流程变量初始化表单数据
      this.formConfig.sections.forEach(section => {
        section.fields.forEach(field => {
          if (field.value) {
            // 解析变量表达式
            this.formData[field.name] = this.parseExpression(field.value);
          }
        });
      });
    },
    
    parseExpression(expression) {
      // 简单的变量替换，实际项目中可使用更强大的表达式引擎
      return expression.replace(/\$\{(.+?)\}/g, (match, varName) => {
        return this.processVariables[varName] || '';
      });
    },
    
    buildValidationRules() {
      // 根据字段配置构建验证规则
      this.formConfig.sections.forEach(section => {
        section.fields.forEach(field => {
          if (field.required) {
            this.formRules[field.name] = [
              {required: true, message: `${field.label}不能为空`, trigger: 'blur'}
            ];
          }
        });
      });
    },
    
    submitForm() {
      this.$refs.dynamicForm.validate((valid) => {
        if (valid) {
          this.$emit('submit', {
            taskId: this.taskId,
            formData: this.formData
          });
        }
      });
    }
  }
};
</script>
```

## 6. 流程监控与分析

### 6.1 流程监控面板
```java
@RestController
@RequestMapping("/api/workflow/monitor")
public class ProcessMonitorController {
    
    @GetMapping("/dashboard")
    public ProcessDashboard getProcessDashboard() {
        ProcessDashboard dashboard = new ProcessDashboard();
        
        // 流程实例统计
        dashboard.setTotalInstances(processInstanceService.getTotalCount());
        dashboard.setRunningInstances(processInstanceService.getRunningCount());
        dashboard.setCompletedInstances(processInstanceService.getCompletedCount());
        
        // 任务统计
        dashboard.setPendingTasks(taskService.getPendingCount());
        dashboard.setOverdueTasks(taskService.getOverdueCount());
        dashboard.setCompletedTasksToday(taskService.getCompletedTodayCount());
        
        // 流程效率分析
        dashboard.setAverageProcessTime(processAnalysisService.getAverageProcessTime());
        dashboard.setProcessTrendData(processAnalysisService.getProcessTrend());
        
        return dashboard;
    }
    
    @GetMapping("/instances")
    public PageResult<ProcessInstanceVO> getProcessInstances(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String processKey,
            @RequestParam(required = false) String status) {
        
        return processInstanceService.getInstances(page, size, processKey, status);
    }
    
    @GetMapping("/tasks/pending")
    public List<TaskInstanceVO> getPendingTasks(@RequestParam String userId) {
        return taskService.getPendingTasks(userId);
    }
}
```

### 6.2 流程分析报表
```java
@Service
public class ProcessAnalysisService {
    
    public ProcessEfficiencyReport getEfficiencyReport(String processKey, 
                                                     LocalDate startDate, 
                                                     LocalDate endDate) {
        List<ProcessInstance> instances = processInstanceRepository
            .findByProcessKeyAndDateRange(processKey, startDate, endDate);
        
        ProcessEfficiencyReport report = new ProcessEfficiencyReport();
        
        // 完成率分析
        long totalCount = instances.size();
        long completedCount = instances.stream()
            .filter(i -> i.getStatus() == ProcessStatus.COMPLETED)
            .count();
        report.setCompletionRate((double) completedCount / totalCount * 100);
        
        // 平均处理时间
        OptionalDouble avgTime = instances.stream()
            .filter(i -> i.getEndTime() != null)
            .mapToLong(i -> Duration.between(i.getStartTime(), i.getEndTime()).toHours())
            .average();
        report.setAverageProcessTimeHours(avgTime.orElse(0));
        
        // 瓶颈节点分析
        Map<String, Long> nodeTimeStats = getNodeTimeStatistics(instances);
        report.setBottleneckNodes(findBottleneckNodes(nodeTimeStats));
        
        return report;
    }
    
    private Map<String, Long> getNodeTimeStatistics(List<ProcessInstance> instances) {
        // 分析各节点的平均处理时间
        return instances.stream()
            .flatMap(i -> getTaskInstances(i.getInstanceId()).stream())
            .filter(t -> t.getCompleteTime() != null)
            .collect(Collectors.groupingBy(
                TaskInstance::getNodeId,
                Collectors.averagingLong(t -> 
                    Duration.between(t.getCreateTime(), t.getCompleteTime()).toHours())
            ));
    }
}
```

## 7. 集成接口

### 7.1 业务系统集成
```java
@Component
public class WorkflowIntegrationService {
    
    // 订单审批完成后回调
    @EventListener
    public void handleOrderApprovalCompleted(ProcessCompletedEvent event) {
        if ("order_approval".equals(event.getProcessKey())) {
            String businessKey = event.getBusinessKey();
            Map<String, Object> variables = event.getVariables();
            
            if ("approve".equals(variables.get("approvalResult"))) {
                orderService.approveOrder(businessKey);
            } else {
                orderService.rejectOrder(businessKey, 
                    (String) variables.get("comments"));
            }
        }
    }
    
    // 质量异常处理完成回调
    @EventListener
    public void handleQualityExceptionProcessed(ProcessCompletedEvent event) {
        if ("quality_exception".equals(event.getProcessKey())) {
            String exceptionId = event.getBusinessKey();
            Map<String, Object> variables = event.getVariables();
            
            qualityService.updateExceptionStatus(exceptionId, 
                (String) variables.get("resolution"));
        }
    }
}
```

### 7.2 外部API接口
```java
@RestController
@RequestMapping("/api/workflow")
public class WorkflowApiController {
    
    @PostMapping("/process/start")
    public ApiResult<String> startProcess(@RequestBody StartProcessRequest request) {
        ProcessInstance instance = processEngine.startProcess(
            request.getProcessKey(),
            request.getBusinessKey(), 
            request.getInitiator(),
            request.getVariables()
        );
        return ApiResult.success(instance.getInstanceId());
    }
    
    @PostMapping("/task/{taskId}/complete")
    public ApiResult<Void> completeTask(@PathVariable String taskId,
                                       @RequestBody CompleteTaskRequest request) {
        processEngine.completeTask(
            taskId,
            request.getUserId(),
            request.getVariables(),
            request.getComments()
        );
        return ApiResult.success();
    }
    
    @GetMapping("/process/{instanceId}/status")
    public ApiResult<ProcessStatusResponse> getProcessStatus(@PathVariable String instanceId) {
        ProcessInstance instance = processInstanceService.getById(instanceId);
        ProcessStatusResponse response = new ProcessStatusResponse();
        response.setStatus(instance.getStatus());
        response.setCurrentNode(instance.getCurrentNode());
        response.setProgress(calculateProgress(instance));
        
        return ApiResult.success(response);
    }
}
```

## 8. 性能优化

### 8.1 数据库优化
```sql
-- 流程实例索引优化
CREATE INDEX idx_process_instance_status ON process_instances(status, start_time);
CREATE INDEX idx_process_instance_business ON process_instances(business_key);

-- 任务实例索引优化  
CREATE INDEX idx_task_assignee_status ON task_instances(assignee, status);
CREATE INDEX idx_task_due_date ON task_instances(due_date, status);
CREATE INDEX idx_task_process ON task_instances(instance_id, status);

-- 历史数据分区
CREATE TABLE task_instances_history PARTITION BY RANGE (complete_time) (
    PARTITION p202401 VALUES LESS THAN ('2024-02-01'),
    PARTITION p202402 VALUES LESS THAN ('2024-03-01'),
    -- 按月分区
);
```

### 8.2 缓存策略
```java
@Service
public class WorkflowCacheService {
    
    @Cacheable(value = "process-definitions", key = "#processKey")
    public ProcessDefinition getProcessDefinition(String processKey) {
        return processDefinitionRepository.findByProcessKey(processKey);
    }
    
    @Cacheable(value = "user-tasks", key = "#userId", expiry = 300)
    public List<TaskInstance> getUserPendingTasks(String userId) {
        return taskInstanceRepository.findByAssigneeAndStatus(userId, TaskStatus.PENDING);
    }
    
    @CacheEvict(value = "user-tasks", key = "#task.assignee")
    public void evictUserTasksCache(TaskInstance task) {
        // 任务状态变更时清除用户任务缓存
    }
}
```

---

*工作流引擎模块是CIM系统业务流程自动化的核心，为各业务模块提供统一、灵活、可配置的流程管理能力*