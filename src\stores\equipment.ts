/**
 * 设备管理状态管理
 * Equipment Management Store
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  Equipment,
  EquipmentStatusDetail,
  EquipmentAlarm,
  EquipmentEvent,
  Recipe,
  EquipmentConstant,
  MaintenancePlan,
  MaintenanceTask,
  SecsMessage,
  EquipmentStatistics,
  MaintenanceStatistics,
  EquipmentQueryParams,
  AlarmQueryParams,
  EventQueryParams,
  MaintenanceQueryParams
} from '@/types/equipment'
import { EquipmentStatus, EquipmentType } from '@/types/equipment'
import {
  equipmentApi,
  alarmApi,
  eventApi,
  recipeApi,
  constantApi,
  maintenanceApi,
  secsGemApi,
  equipmentControlApi
} from '@/api/equipment'

// 使用模拟数据进行开发
import {
  mockEquipmentList,
  mockEquipmentStatus,
  mockAlarms,
  mockEvents,
  mockRecipes,
  mockEquipmentConstants,
  mockMaintenancePlans,
  mockMaintenanceTasks,
  mockSecsMessages,
  mockEquipmentStatistics,
  mockMaintenanceStatistics
} from '@/utils/mockData/equipment'

export const useEquipmentStore = defineStore('equipment', () => {
  // 状态管理
  const equipment = ref<Equipment[]>([])
  const equipmentStatus = ref<EquipmentStatusDetail[]>([])
  const alarms = ref<EquipmentAlarm[]>([])
  const events = ref<EquipmentEvent[]>([])
  const recipes = ref<Recipe[]>([])
  const constants = ref<EquipmentConstant[]>([])
  const maintenancePlans = ref<MaintenancePlan[]>([])
  const maintenanceTasks = ref<MaintenanceTask[]>([])
  const secsMessages = ref<SecsMessage[]>([])
  const statistics = ref<EquipmentStatistics | null>(null)
  const maintenanceStatistics = ref<MaintenanceStatistics | null>(null)

  // 加载状态
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 分页信息
  const pagination = ref({
    current: 1,
    pageSize: 20,
    total: 0
  })

  // 查询参数
  const queryParams = ref<EquipmentQueryParams>({})
  const alarmQueryParams = ref<AlarmQueryParams>({})
  const eventQueryParams = ref<EventQueryParams>({})
  const maintenanceQueryParams = ref<MaintenanceQueryParams>({})

  // 计算属性
  const equipmentCount = computed(() => ({
    total: equipment.value.length,
    running: equipment.value.filter(eq => eq.status === EquipmentStatus.RUN).length,
    idle: equipment.value.filter(eq => eq.status === EquipmentStatus.IDLE).length,
    down: equipment.value.filter(eq => eq.status === EquipmentStatus.DOWN).length,
    pm: equipment.value.filter(eq => eq.status === EquipmentStatus.PM).length
  }))

  const activeAlarms = computed(() => alarms.value.filter(alarm => alarm.isActive))
  const criticalAlarms = computed(() =>
    activeAlarms.value.filter(alarm => alarm.severity === 'CRITICAL')
  )
  const majorAlarms = computed(() => activeAlarms.value.filter(alarm => alarm.severity === 'MAJOR'))

  const overdueTasks = computed(() =>
    maintenanceTasks.value.filter(
      task => task.status === 'SCHEDULED' && new Date(task.scheduledDate) < new Date()
    )
  )

  const equipmentByType = computed(() => {
    const byType: Record<string, Equipment[]> = {}
    equipment.value.forEach(eq => {
      if (!byType[eq.type]) {
        byType[eq.type] = []
      }
      byType[eq.type].push(eq)
    })
    return byType
  })

  // 设备管理actions
  const fetchEquipmentList = async (params?: EquipmentQueryParams) => {
    loading.value = true
    error.value = null
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟
        equipment.value = mockEquipmentList
        pagination.value.total = mockEquipmentList.length
      } else {
        const response = await equipmentApi.getEquipmentList(params || {})
        equipment.value = response.data.list
        pagination.value = {
          current: response.data.page,
          pageSize: response.data.limit,
          total: response.data.total
        }
      }
      queryParams.value = params || {}
    } catch (err) {
      error.value = '获取设备列表失败'
      console.error('Failed to fetch equipment list:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchEquipmentStatus = async (ids?: string[]) => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 200))
        equipmentStatus.value = mockEquipmentStatus
      } else {
        const response = await equipmentApi.getEquipmentStatus(ids)
        equipmentStatus.value = response.data
      }
    } catch (err) {
      console.error('Failed to fetch equipment status:', err)
    }
  }

  const fetchStatistics = async () => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 300))
        statistics.value = mockEquipmentStatistics
      } else {
        const response = await equipmentApi.getEquipmentStatistics()
        statistics.value = response.data
      }
    } catch (err) {
      console.error('Failed to fetch equipment statistics:', err)
    }
  }

  const createEquipment = async (data: Omit<Equipment, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    try {
      const response = await equipmentApi.createEquipment(data)
      equipment.value.push(response.data)
      return response.data
    } catch (err) {
      error.value = '创建设备失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateEquipment = async (id: string, data: Partial<Equipment>) => {
    loading.value = true
    try {
      const response = await equipmentApi.updateEquipment(id, data)
      const index = equipment.value.findIndex(eq => eq.id === id)
      if (index !== -1) {
        equipment.value[index] = response.data
      }
      return response.data
    } catch (err) {
      error.value = '更新设备失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteEquipment = async (id: string) => {
    loading.value = true
    try {
      await equipmentApi.deleteEquipment(id)
      equipment.value = equipment.value.filter(eq => eq.id !== id)
    } catch (err) {
      error.value = '删除设备失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 告警管理actions
  const fetchAlarms = async (params?: AlarmQueryParams) => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 300))
        alarms.value = mockAlarms
      } else {
        const response = await alarmApi.getAlarmList(params || {})
        alarms.value = response.data.list
      }
      alarmQueryParams.value = params || {}
    } catch (err) {
      console.error('Failed to fetch alarms:', err)
    }
  }

  const acknowledgeAlarm = async (id: string, operator: string) => {
    try {
      await alarmApi.acknowledgeAlarm(id, operator)
      const alarm = alarms.value.find(a => a.id === id)
      if (alarm) {
        alarm.acknowledgedBy = operator
        alarm.acknowledgedAt = new Date().toISOString()
      }
    } catch (err) {
      console.error('Failed to acknowledge alarm:', err)
      throw err
    }
  }

  const clearAlarm = async (id: string, operator: string) => {
    try {
      await alarmApi.clearAlarm(id, operator)
      const alarm = alarms.value.find(a => a.id === id)
      if (alarm) {
        alarm.isActive = false
        alarm.clearedAt = new Date().toISOString()
      }
    } catch (err) {
      console.error('Failed to clear alarm:', err)
      throw err
    }
  }

  // 事件日志actions
  const fetchEvents = async (params?: EventQueryParams) => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 200))
        events.value = mockEvents
      } else {
        const response = await eventApi.getEventList(params || {})
        events.value = response.data.list
      }
      eventQueryParams.value = params || {}
    } catch (err) {
      console.error('Failed to fetch events:', err)
    }
  }

  // Recipe管理actions
  const fetchRecipes = async (equipmentId?: string) => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 300))
        recipes.value = equipmentId
          ? mockRecipes.filter(r => r.equipmentId === equipmentId)
          : mockRecipes
      } else {
        const response = await recipeApi.getRecipeList(equipmentId)
        recipes.value = response.data.list
      }
    } catch (err) {
      console.error('Failed to fetch recipes:', err)
    }
  }

  const createRecipe = async (data: Omit<Recipe, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await recipeApi.createRecipe(data)
      recipes.value.push(response.data)
      return response.data
    } catch (err) {
      console.error('Failed to create recipe:', err)
      throw err
    }
  }

  const activateRecipe = async (recipeId: string, equipmentId: string) => {
    try {
      await recipeApi.activateRecipe(recipeId, equipmentId)
      // 更新Recipe状态
      recipes.value.forEach(recipe => {
        if (recipe.equipmentId === equipmentId) {
          recipe.isActive = recipe.id === recipeId
        }
      })
    } catch (err) {
      console.error('Failed to activate recipe:', err)
      throw err
    }
  }

  // 设备常量actions
  const fetchConstants = async (equipmentId: string) => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 200))
        constants.value = mockEquipmentConstants.filter(c => c.equipmentId === equipmentId)
      } else {
        const response = await constantApi.getConstantList(equipmentId)
        constants.value = response.data
      }
    } catch (err) {
      console.error('Failed to fetch constants:', err)
    }
  }

  const updateConstant = async (
    equipmentId: string,
    constantId: string,
    value: string | number | boolean
  ) => {
    try {
      await constantApi.updateConstant(equipmentId, constantId, value)
      const constant = constants.value.find(c => c.id === constantId)
      if (constant) {
        constant.value = value
      }
    } catch (err) {
      console.error('Failed to update constant:', err)
      throw err
    }
  }

  // 维护管理actions
  const fetchMaintenancePlans = async (equipmentId?: string) => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 300))
        maintenancePlans.value = equipmentId
          ? mockMaintenancePlans.filter(p => p.equipmentId === equipmentId)
          : mockMaintenancePlans
      } else {
        const response = await maintenanceApi.getMaintenancePlans(equipmentId)
        maintenancePlans.value = response.data
      }
    } catch (err) {
      console.error('Failed to fetch maintenance plans:', err)
    }
  }

  const fetchMaintenanceTasks = async (params?: MaintenanceQueryParams) => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 300))
        maintenanceTasks.value = mockMaintenanceTasks
      } else {
        const response = await maintenanceApi.getMaintenanceTasks(params || {})
        maintenanceTasks.value = response.data.list
      }
      maintenanceQueryParams.value = params || {}
    } catch (err) {
      console.error('Failed to fetch maintenance tasks:', err)
    }
  }

  const fetchMaintenanceStatistics = async () => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 200))
        maintenanceStatistics.value = mockMaintenanceStatistics
      } else {
        const response = await maintenanceApi.getMaintenanceStatistics()
        maintenanceStatistics.value = response.data
      }
    } catch (err) {
      console.error('Failed to fetch maintenance statistics:', err)
    }
  }

  const createMaintenanceTask = async (data: Omit<MaintenanceTask, 'id' | 'createdAt'>) => {
    try {
      const response = await maintenanceApi.createMaintenanceTask(data)
      maintenanceTasks.value.push(response.data)
      return response.data
    } catch (err) {
      console.error('Failed to create maintenance task:', err)
      throw err
    }
  }

  const startMaintenanceTask = async (id: string, operator: string) => {
    try {
      await maintenanceApi.startMaintenanceTask(id, operator)
      const task = maintenanceTasks.value.find(t => t.id === id)
      if (task) {
        task.status = 'IN_PROGRESS'
        task.actualStartTime = new Date().toISOString()
      }
    } catch (err) {
      console.error('Failed to start maintenance task:', err)
      throw err
    }
  }

  const completeMaintenanceTask = async (
    id: string,
    data: { operator: string; notes?: string; usedParts: any[] }
  ) => {
    try {
      await maintenanceApi.completeMaintenanceTask(id, data)
      const task = maintenanceTasks.value.find(t => t.id === id)
      if (task) {
        task.status = 'COMPLETED'
        task.actualEndTime = new Date().toISOString()
        task.completionNotes = data.notes
        task.usedParts = data.usedParts
      }
    } catch (err) {
      console.error('Failed to complete maintenance task:', err)
      throw err
    }
  }

  // SECS/GEM通信actions
  const fetchSecsMessages = async (equipmentId: string, params?: any) => {
    try {
      // 开发阶段使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 200))
        secsMessages.value = mockSecsMessages
      } else {
        const response = await secsGemApi.getSecsMessages(equipmentId, params)
        secsMessages.value = response.data.list
      }
    } catch (err) {
      console.error('Failed to fetch SECS messages:', err)
    }
  }

  const sendSecsMessage = async (
    equipmentId: string,
    data: { stream: number; function: number; data?: any }
  ) => {
    try {
      const response = await secsGemApi.sendSecsMessage(equipmentId, data)
      if (response.data) {
        secsMessages.value.unshift(response.data)
      }
      return response.data
    } catch (err) {
      console.error('Failed to send SECS message:', err)
      throw err
    }
  }

  // 设备控制actions
  const startEquipment = async (equipmentId: string, operator: string) => {
    try {
      await equipmentControlApi.startEquipment(equipmentId, operator)
      // 更新设备状态
      const eq = equipment.value.find(e => e.id === equipmentId)
      if (eq) {
        eq.status = EquipmentStatus.RUN
      }
      const status = equipmentStatus.value.find(s => s.equipmentId === equipmentId)
      if (status) {
        status.status = EquipmentStatus.RUN
        status.lastStatusChange = new Date().toISOString()
      }
    } catch (err) {
      console.error('Failed to start equipment:', err)
      throw err
    }
  }

  const stopEquipment = async (equipmentId: string, operator: string) => {
    try {
      await equipmentControlApi.stopEquipment(equipmentId, operator)
      // 更新设备状态
      const eq = equipment.value.find(e => e.id === equipmentId)
      if (eq) {
        eq.status = EquipmentStatus.IDLE
      }
      const status = equipmentStatus.value.find(s => s.equipmentId === equipmentId)
      if (status) {
        status.status = EquipmentStatus.IDLE
        status.lastStatusChange = new Date().toISOString()
      }
    } catch (err) {
      console.error('Failed to stop equipment:', err)
      throw err
    }
  }

  // 工具方法
  const getEquipmentById = (id: string) => equipment.value.find(eq => eq.id === id)
  const getEquipmentStatusById = (id: string) =>
    equipmentStatus.value.find(status => status.equipmentId === id)
  const getRecipesByEquipmentId = (equipmentId: string) =>
    recipes.value.filter(recipe => recipe.equipmentId === equipmentId)
  const getActiveAlarmsByEquipmentId = (equipmentId: string) =>
    alarms.value.filter(alarm => alarm.equipmentId === equipmentId && alarm.isActive)

  // 清除数据
  const clearData = () => {
    equipment.value = []
    equipmentStatus.value = []
    alarms.value = []
    events.value = []
    recipes.value = []
    constants.value = []
    maintenancePlans.value = []
    maintenanceTasks.value = []
    secsMessages.value = []
    statistics.value = null
    maintenanceStatistics.value = null
    error.value = null
  }

  // 实时数据更新(WebSocket)
  const handleRealTimeUpdate = (data: any) => {
    switch (data.type) {
      case 'EQUIPMENT_STATUS':
        const statusIndex = equipmentStatus.value.findIndex(s => s.equipmentId === data.equipmentId)
        if (statusIndex !== -1) {
          equipmentStatus.value[statusIndex] = {
            ...equipmentStatus.value[statusIndex],
            ...data.data
          }
        }
        // 同时更新设备表中的状态
        const equipmentIndex = equipment.value.findIndex(eq => eq.id === data.equipmentId)
        if (equipmentIndex !== -1) {
          equipment.value[equipmentIndex].status = data.data.status
        }
        break
      case 'ALARM':
        if (data.data.isActive) {
          // 新告警或更新现有告警
          const alarmIndex = alarms.value.findIndex(a => a.id === data.data.id)
          if (alarmIndex !== -1) {
            alarms.value[alarmIndex] = data.data
          } else {
            alarms.value.unshift(data.data)
          }
        } else {
          // 告警清除
          const alarm = alarms.value.find(a => a.id === data.data.id)
          if (alarm) {
            alarm.isActive = false
            alarm.clearedAt = data.timestamp
          }
        }
        break
      case 'EVENT':
        events.value.unshift(data.data)
        // 限制事件日志数量，保持最新的1000条
        if (events.value.length > 1000) {
          events.value = events.value.slice(0, 1000)
        }
        break
    }
  }

  return {
    // 状态
    equipment: readonly(equipment),
    equipmentStatus: readonly(equipmentStatus),
    alarms: readonly(alarms),
    events: readonly(events),
    recipes: readonly(recipes),
    constants: readonly(constants),
    maintenancePlans: readonly(maintenancePlans),
    maintenanceTasks: readonly(maintenanceTasks),
    secsMessages: readonly(secsMessages),
    statistics: readonly(statistics),
    maintenanceStatistics: readonly(maintenanceStatistics),
    loading: readonly(loading),
    error: readonly(error),
    pagination: readonly(pagination),
    queryParams: readonly(queryParams),

    // 计算属性
    equipmentCount,
    activeAlarms,
    criticalAlarms,
    majorAlarms,
    overdueTasks,
    equipmentByType,

    // 方法
    fetchEquipmentList,
    fetchEquipmentStatus,
    fetchStatistics,
    createEquipment,
    updateEquipment,
    deleteEquipment,
    fetchAlarms,
    acknowledgeAlarm,
    clearAlarm,
    fetchEvents,
    fetchRecipes,
    createRecipe,
    activateRecipe,
    fetchConstants,
    updateConstant,
    fetchMaintenancePlans,
    fetchMaintenanceTasks,
    fetchMaintenanceStatistics,
    createMaintenanceTask,
    startMaintenanceTask,
    completeMaintenanceTask,
    fetchSecsMessages,
    sendSecsMessage,
    startEquipment,
    stopEquipment,
    getEquipmentById,
    getEquipmentStatusById,
    getRecipesByEquipmentId,
    getActiveAlarmsByEquipmentId,
    clearData,
    handleRealTimeUpdate
  }
})
