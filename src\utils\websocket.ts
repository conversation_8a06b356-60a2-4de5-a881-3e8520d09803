/**
 * IC封测CIM系统 - WebSocket实时数据管理系统
 * WebSocket Real-time Data Management System for IC Packaging & Testing CIM System
 */

import { ElMessage, ElNotification } from 'element-plus'

/**
 * WebSocket连接状态枚举
 */
export enum WebSocketStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * WebSocket消息类型枚举
 */
export enum MessageType {
  // 系统消息
  HEARTBEAT = 'heartbeat',
  AUTH = 'auth',
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe',

  // 业务数据消息
  EQUIPMENT_STATUS = 'equipment_status',
  PRODUCTION_DATA = 'production_data',
  QUALITY_DATA = 'quality_data',
  ALARM_DATA = 'alarm_data',
  ENVIRONMENT_DATA = 'environment_data',
  INVENTORY_DATA = 'inventory_data',

  // SECS/GEM消息
  SECS_MESSAGE = 'secs_message',
  SECS_ALARM = 'secs_alarm',
  SECS_EVENT = 'secs_event',

  // 通知消息
  NOTIFICATION = 'notification',
  SYSTEM_EVENT = 'system_event'
}

/**
 * WebSocket消息接口
 */
export interface WebSocketMessage<T = any> {
  id?: string
  type: MessageType | string
  data: T
  timestamp: string
  source?: string
  target?: string
  priority?: 'low' | 'normal' | 'high' | 'critical'
}

/**
 * WebSocket连接配置接口
 */
export interface WebSocketConfig {
  url: string
  protocols?: string | string[]
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  heartbeatTimeout?: number
  enableLogging?: boolean
  authToken?: string
  enableCompression?: boolean
  messageQueueSize?: number
}

/**
 * 事件监听器接口
 */
export interface EventListener<T = any> {
  id: string
  type: MessageType | string
  handler: (data: T, message: WebSocketMessage<T>) => void
  filter?: (data: T, message: WebSocketMessage<T>) => boolean
  once?: boolean
  priority?: number
}

/**
 * 连接状态信息接口
 */
export interface ConnectionInfo {
  status: WebSocketStatus
  connected: boolean
  reconnectAttempts: number
  lastConnected?: Date
  lastDisconnected?: Date
  latency: number
  messageCount: {
    sent: number
    received: number
    failed: number
  }
}

/**
 * WebSocket管理器类
 */
export class WebSocketManager {
  private ws: WebSocket | null = null
  private config: Required<WebSocketConfig>
  private status: WebSocketStatus = WebSocketStatus.DISCONNECTED
  private listeners: Map<string, EventListener> = new Map()
  private messageQueue: WebSocketMessage[] = []
  private heartbeatTimer: number | null = null
  private heartbeatTimeoutTimer: number | null = null
  private reconnectTimer: number | null = null
  private reconnectAttempts = 0
  private connectionInfo: ConnectionInfo
  private lastHeartbeatTime = 0
  private isManualClose = false

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      heartbeatTimeout: 5000,
      enableLogging: true,
      enableCompression: false,
      messageQueueSize: 1000,
      ...config
    }

    this.connectionInfo = {
      status: WebSocketStatus.DISCONNECTED,
      connected: false,
      reconnectAttempts: 0,
      latency: 0,
      messageCount: {
        sent: 0,
        received: 0,
        failed: 0
      }
    }

    this.log('WebSocket Manager initialized', this.config)
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.log('WebSocket already connected')
      return
    }

    this.isManualClose = false
    this.setStatus(WebSocketStatus.CONNECTING)

    try {
      await this.createConnection()
    } catch (error) {
      this.log('Failed to connect WebSocket', error)
      throw error
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    this.isManualClose = true
    this.clearTimers()

    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect')
      this.ws = null
    }

    this.setStatus(WebSocketStatus.DISCONNECTED)
    this.log('WebSocket disconnected manually')
  }

  /**
   * 发送消息
   */
  send<T = any>(message: Omit<WebSocketMessage<T>, 'id' | 'timestamp'>): boolean {
    const fullMessage: WebSocketMessage<T> = {
      id: this.generateMessageId(),
      timestamp: new Date().toISOString(),
      ...message
    }

    // 如果未连接，加入消息队列
    if (!this.isConnected()) {
      this.enqueueMessage(fullMessage)
      this.log('Message queued (not connected)', fullMessage)
      return false
    }

    try {
      this.ws!.send(JSON.stringify(fullMessage))
      this.connectionInfo.messageCount.sent++
      this.log('Message sent', fullMessage)
      return true
    } catch (error) {
      this.connectionInfo.messageCount.failed++
      this.log('Failed to send message', error)
      this.enqueueMessage(fullMessage)
      return false
    }
  }

  /**
   * 添加事件监听器
   */
  on<T = any>(
    type: MessageType | string,
    handler: (data: T, message: WebSocketMessage<T>) => void,
    options?: {
      filter?: (data: T, message: WebSocketMessage<T>) => boolean
      once?: boolean
      priority?: number
    }
  ): string {
    const id = this.generateListenerId()
    const listener: EventListener<T> = {
      id,
      type,
      handler,
      filter: options?.filter,
      once: options?.once,
      priority: options?.priority || 0
    }

    this.listeners.set(id, listener)
    this.log('Event listener added', { id, type })
    return id
  }

  /**
   * 移除事件监听器
   */
  off(id: string): boolean {
    const removed = this.listeners.delete(id)
    if (removed) {
      this.log('Event listener removed', { id })
    }
    return removed
  }

  /**
   * 订阅数据类型
   */
  subscribe(type: MessageType | string, filters?: Record<string, any>): void {
    this.send({
      type: MessageType.SUBSCRIBE,
      data: { type, filters }
    })
    this.log('Subscribed to', { type, filters })
  }

  /**
   * 取消订阅数据类型
   */
  unsubscribe(type: MessageType | string): void {
    this.send({
      type: MessageType.UNSUBSCRIBE,
      data: { type }
    })
    this.log('Unsubscribed from', { type })
  }

  /**
   * 获取连接状态
   */
  getConnectionInfo(): ConnectionInfo {
    return { ...this.connectionInfo }
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  /**
   * 获取连接状态
   */
  getStatus(): WebSocketStatus {
    return this.status
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.disconnect()
    this.listeners.clear()
    this.messageQueue = []
    this.log('WebSocket Manager destroyed')
  }

  /**
   * 创建WebSocket连接
   */
  private async createConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.config.url, this.config.protocols)

        // 启用压缩
        if (this.config.enableCompression && 'binaryType' in this.ws) {
          this.ws.binaryType = 'arraybuffer'
        }

        const connectTimeout = setTimeout(() => {
          reject(new Error('Connection timeout'))
        }, 10000)

        this.ws.onopen = () => {
          clearTimeout(connectTimeout)
          this.onConnected()
          resolve()
        }

        this.ws.onmessage = event => {
          this.onMessage(event)
        }

        this.ws.onerror = error => {
          clearTimeout(connectTimeout)
          this.onError(error)
          reject(error)
        }

        this.ws.onclose = event => {
          clearTimeout(connectTimeout)
          this.onClosed(event)
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 连接成功处理
   */
  private onConnected(): void {
    this.setStatus(WebSocketStatus.CONNECTED)
    this.reconnectAttempts = 0
    this.connectionInfo.lastConnected = new Date()
    this.connectionInfo.reconnectAttempts = 0

    // 认证
    if (this.config.authToken) {
      this.send({
        type: MessageType.AUTH,
        data: { token: this.config.authToken }
      })
    }

    // 启动心跳
    this.startHeartbeat()

    // 发送队列中的消息
    this.flushMessageQueue()

    // 触发连接成功事件
    this.triggerEvent('connected', null)

    this.log('WebSocket connected successfully')
    ElMessage.success('实时数据连接已建立')
  }

  /**
   * 消息接收处理
   */
  private onMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      this.connectionInfo.messageCount.received++

      // 计算延迟
      if (message.timestamp) {
        const latency = Date.now() - new Date(message.timestamp).getTime()
        this.connectionInfo.latency = latency
      }

      // 处理特殊消息类型
      if (message.type === MessageType.HEARTBEAT) {
        this.handleHeartbeatResponse(message)
        return
      }

      // 触发对应的事件监听器
      this.triggerEvent(message.type, message.data, message)

      this.log('Message received', message)
    } catch (error) {
      this.log('Failed to parse message', error)
    }
  }

  /**
   * 错误处理
   */
  private onError(error: Event): void {
    this.setStatus(WebSocketStatus.ERROR)
    this.connectionInfo.messageCount.failed++

    this.log('WebSocket error', error)
    this.triggerEvent('error', error)
  }

  /**
   * 连接关闭处理
   */
  private onClosed(event: CloseEvent): void {
    this.setStatus(WebSocketStatus.DISCONNECTED)
    this.connectionInfo.lastDisconnected = new Date()
    this.clearTimers()

    this.log('WebSocket closed', { code: event.code, reason: event.reason })
    this.triggerEvent('disconnected', { code: event.code, reason: event.reason })

    // 如果不是手动关闭，则尝试重连
    if (!this.isManualClose && event.code !== 1000) {
      this.attemptReconnect()
    }
  }

  /**
   * 尝试重新连接
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.log('Max reconnect attempts reached')
      ElMessage.error('实时数据连接失败，已达到最大重连次数')
      return
    }

    this.reconnectAttempts++
    this.connectionInfo.reconnectAttempts = this.reconnectAttempts
    this.setStatus(WebSocketStatus.RECONNECTING)

    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
      30000
    )

    this.log(
      `Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts}) in ${delay}ms`
    )

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch(error => {
        this.log('Reconnect failed', error)
      })
    }, delay)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
    }

    this.heartbeatTimer = window.setInterval(() => {
      if (this.isConnected()) {
        this.lastHeartbeatTime = Date.now()

        this.send({
          type: MessageType.HEARTBEAT,
          data: { timestamp: this.lastHeartbeatTime }
        })

        // 设置心跳超时
        this.heartbeatTimeoutTimer = window.setTimeout(() => {
          this.log('Heartbeat timeout, closing connection')
          this.ws?.close(1001, 'Heartbeat timeout')
        }, this.config.heartbeatTimeout)
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 处理心跳响应
   */
  private handleHeartbeatResponse(message: WebSocketMessage): void {
    if (this.heartbeatTimeoutTimer) {
      clearTimeout(this.heartbeatTimeoutTimer)
      this.heartbeatTimeoutTimer = null
    }

    // 更新延迟
    if (message.data?.timestamp) {
      const latency = Date.now() - message.data.timestamp
      this.connectionInfo.latency = latency
    }
  }

  /**
   * 清理定时器
   */
  private clearTimers(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    if (this.heartbeatTimeoutTimer) {
      clearTimeout(this.heartbeatTimeoutTimer)
      this.heartbeatTimeoutTimer = null
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 设置连接状态
   */
  private setStatus(status: WebSocketStatus): void {
    this.status = status
    this.connectionInfo.status = status
    this.connectionInfo.connected = status === WebSocketStatus.CONNECTED
  }

  /**
   * 消息入队
   */
  private enqueueMessage(message: WebSocketMessage): void {
    if (this.messageQueue.length >= this.config.messageQueueSize) {
      this.messageQueue.shift() // 移除最旧的消息
    }
    this.messageQueue.push(message)
  }

  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()!
      try {
        this.ws!.send(JSON.stringify(message))
        this.connectionInfo.messageCount.sent++
        this.log('Queued message sent', message)
      } catch (error) {
        this.connectionInfo.messageCount.failed++
        this.log('Failed to send queued message', error)
        break
      }
    }
  }

  /**
   * 触发事件监听器
   */
  private triggerEvent<T = any>(type: string, data: T, message?: WebSocketMessage<T>): void {
    // 获取匹配的监听器并按优先级排序
    const matchingListeners = Array.from(this.listeners.values())
      .filter(listener => listener.type === type)
      .sort((a, b) => (b.priority || 0) - (a.priority || 0))

    const toRemove: string[] = []

    matchingListeners.forEach(listener => {
      try {
        // 应用过滤器
        if (listener.filter && message && !listener.filter(data, message)) {
          return
        }

        // 调用处理函数
        listener.handler(data, message || { type, data, timestamp: new Date().toISOString() })

        // 如果是一次性监听器，标记为待删除
        if (listener.once) {
          toRemove.push(listener.id)
        }
      } catch (error) {
        this.log('Event handler error', error)
      }
    })

    // 移除一次性监听器
    toRemove.forEach(id => this.listeners.delete(id))
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成监听器ID
   */
  private generateListenerId(): string {
    return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 日志记录
   */
  private log(message: string, data?: any): void {
    if (this.config.enableLogging) {
      const timestamp = new Date().toISOString()
      console.log(`[WebSocket ${timestamp}] ${message}`, data || '')
    }
  }
}

/**
 * WebSocket工具类
 */
export class WebSocketUtils {
  /**
   * 创建设备状态WebSocket管理器
   */
  static createEquipmentStatusManager(config?: Partial<WebSocketConfig>): WebSocketManager {
    return new WebSocketManager({
      url: `${this.getWebSocketBaseUrl()}/equipment`,
      heartbeatInterval: 15000,
      ...config
    })
  }

  /**
   * 创建生产数据WebSocket管理器
   */
  static createProductionDataManager(config?: Partial<WebSocketConfig>): WebSocketManager {
    return new WebSocketManager({
      url: `${this.getWebSocketBaseUrl()}/production`,
      heartbeatInterval: 10000,
      ...config
    })
  }

  /**
   * 创建质量数据WebSocket管理器
   */
  static createQualityDataManager(config?: Partial<WebSocketConfig>): WebSocketManager {
    return new WebSocketManager({
      url: `${this.getWebSocketBaseUrl()}/quality`,
      heartbeatInterval: 20000,
      ...config
    })
  }

  /**
   * 创建告警WebSocket管理器
   */
  static createAlarmManager(config?: Partial<WebSocketConfig>): WebSocketManager {
    return new WebSocketManager({
      url: `${this.getWebSocketBaseUrl()}/alarms`,
      heartbeatInterval: 5000,
      maxReconnectAttempts: 20,
      ...config
    })
  }

  /**
   * 创建SECS/GEM消息WebSocket管理器
   */
  static createSecsGemManager(config?: Partial<WebSocketConfig>): WebSocketManager {
    return new WebSocketManager({
      url: `${this.getWebSocketBaseUrl()}/secs-gem`,
      heartbeatInterval: 30000,
      enableCompression: true,
      messageQueueSize: 2000,
      ...config
    })
  }

  /**
   * 获取WebSocket基础URL
   */
  private static getWebSocketBaseUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    return `${protocol}//${host}/api/ws`
  }

  /**
   * 验证WebSocket消息
   */
  static validateMessage(message: any): message is WebSocketMessage {
    return (
      typeof message === 'object' &&
      message !== null &&
      typeof message.type === 'string' &&
      'data' in message &&
      typeof message.timestamp === 'string'
    )
  }

  /**
   * 创建标准消息
   */
  static createMessage<T = any>(
    type: MessageType | string,
    data: T,
    options?: {
      source?: string
      target?: string
      priority?: 'low' | 'normal' | 'high' | 'critical'
    }
  ): Omit<WebSocketMessage<T>, 'id' | 'timestamp'> {
    return {
      type,
      data,
      source: options?.source,
      target: options?.target,
      priority: options?.priority || 'normal'
    }
  }

  /**
   * 格式化延迟显示
   */
  static formatLatency(latency: number): string {
    if (latency < 1000) {
      return `${latency}ms`
    } else {
      return `${(latency / 1000).toFixed(1)}s`
    }
  }

  /**
   * 获取连接状态显示文本
   */
  static getStatusText(status: WebSocketStatus): string {
    const statusTexts = {
      [WebSocketStatus.CONNECTING]: '连接中...',
      [WebSocketStatus.CONNECTED]: '已连接',
      [WebSocketStatus.DISCONNECTED]: '未连接',
      [WebSocketStatus.RECONNECTING]: '重连中...',
      [WebSocketStatus.ERROR]: '连接错误'
    }
    return statusTexts[status] || '未知状态'
  }

  /**
   * 获取连接状态颜色
   */
  static getStatusColor(status: WebSocketStatus): string {
    const statusColors = {
      [WebSocketStatus.CONNECTING]: '#409EFF',
      [WebSocketStatus.CONNECTED]: '#67C23A',
      [WebSocketStatus.DISCONNECTED]: '#909399',
      [WebSocketStatus.RECONNECTING]: '#E6A23C',
      [WebSocketStatus.ERROR]: '#F56C6C'
    }
    return statusColors[status] || '#909399'
  }
}

/**
 * 全局WebSocket管理器实例
 */
export class GlobalWebSocketManager {
  private static instance: GlobalWebSocketManager
  private managers: Map<string, WebSocketManager> = new Map()

  static getInstance(): GlobalWebSocketManager {
    if (!GlobalWebSocketManager.instance) {
      GlobalWebSocketManager.instance = new GlobalWebSocketManager()
    }
    return GlobalWebSocketManager.instance
  }

  /**
   * 创建或获取WebSocket管理器
   */
  getManager(name: string, config?: WebSocketConfig): WebSocketManager {
    if (!this.managers.has(name)) {
      if (!config) {
        throw new Error(`WebSocket manager '${name}' not found and no config provided`)
      }
      this.managers.set(name, new WebSocketManager(config))
    }
    return this.managers.get(name)!
  }

  /**
   * 销毁WebSocket管理器
   */
  destroyManager(name: string): boolean {
    const manager = this.managers.get(name)
    if (manager) {
      manager.destroy()
      this.managers.delete(name)
      return true
    }
    return false
  }

  /**
   * 销毁所有管理器
   */
  destroyAll(): void {
    this.managers.forEach(manager => manager.destroy())
    this.managers.clear()
  }

  /**
   * 获取所有管理器状态
   */
  getAllStatus(): Record<string, ConnectionInfo> {
    const status: Record<string, ConnectionInfo> = {}
    this.managers.forEach((manager, name) => {
      status[name] = manager.getConnectionInfo()
    })
    return status
  }
}

// 导出全局实例
export const globalWebSocketManager = GlobalWebSocketManager.getInstance()

// 导出默认配置
export const defaultWebSocketConfig: Partial<WebSocketConfig> = {
  reconnectInterval: 5000,
  maxReconnectAttempts: 10,
  heartbeatInterval: 30000,
  heartbeatTimeout: 5000,
  enableLogging: true,
  enableCompression: false,
  messageQueueSize: 1000
}
