<template>
  <teleport to="body">
    <div v-if="visible" :class="modalClasses" @click="handleMaskClick">
      <div :class="dialogClasses" :style="dialogStyle" @click.stop>
        <!-- 头部 -->
        <div v-if="showHeader" class="c-modal__header">
          <slot name="header">
            <h3 class="c-modal__title">
              {{ title }}
            </h3>
          </slot>
          <button
v-if="closable" class="c-modal__close"
aria-label="关闭" @click="handleClose"
>
            ×
          </button>
        </div>

        <!-- 内容 -->
        <div class="c-modal__body">
          <slot />
        </div>

        <!-- 底部 -->
        <div v-if="showFooter" class="c-modal__footer">
          <slot name="footer">
            <c-button @click="handleCancel">
              {{ cancelText }}
            </c-button>
            <c-button
type="primary" :loading="confirmLoading"
@click="handleConfirm"
>
              {{ confirmText }}
            </c-button>
          </slot>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
  import { computed, nextTick, onMounted, onUnmounted, watch } from 'vue'

  export interface CModalProps {
    /** 是否显示 */
    visible?: boolean
    /** 标题 */
    title?: string
    /** 宽度 */
    width?: number | string
    /** 是否显示头部 */
    showHeader?: boolean
    /** 是否显示底部 */
    showFooter?: boolean
    /** 是否可关闭 */
    closable?: boolean
    /** 点击蒙层是否关闭 */
    maskClosable?: boolean
    /** 确定按钮文字 */
    confirmText?: string
    /** 取消按钮文字 */
    cancelText?: string
    /** 确定按钮loading */
    confirmLoading?: boolean
    /** 是否居中 */
    centered?: boolean
    /** 层级 */
    zIndex?: number
    /** 是否销毁子元素 */
    destroyOnClose?: boolean
  }

  const props = withDefaults(defineProps<CModalProps>(), {
    visible: false,
    title: '提示',
    width: 520,
    showHeader: true,
    showFooter: true,
    closable: true,
    maskClosable: true,
    confirmText: '确定',
    cancelText: '取消',
    confirmLoading: false,
    centered: true,
    zIndex: 1000,
    destroyOnClose: false
  })

  const emit = defineEmits<{
    'update:visible': [visible: boolean]
    confirm: []
    cancel: []
    close: []
  }>()

  // 计算属性
  const modalClasses = computed(() => [
    'c-modal',
    {
      'c-modal--centered': props.centered
    }
  ])

  const dialogClasses = computed(() => ['c-modal__dialog'])

  const dialogStyle = computed(() => {
    const style: any = {
      zIndex: props.zIndex
    }

    if (props.width) {
      style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
    }

    return style
  })

  // 方法
  const handleClose = () => {
    emit('update:visible', false)
    emit('close')
  }

  const handleConfirm = () => {
    emit('confirm')
  }

  const handleCancel = () => {
    emit('update:visible', false)
    emit('cancel')
  }

  const handleMaskClick = () => {
    if (props.maskClosable) {
      handleClose()
    }
  }

  const handleEscapeKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && props.visible && props.closable) {
      handleClose()
    }
  }

  // 阻止滚动
  const preventScroll = () => {
    document.body.style.overflow = 'hidden'
  }

  const restoreScroll = () => {
    document.body.style.overflow = ''
  }

  // 生命周期
  onMounted(() => {
    document.addEventListener('keydown', handleEscapeKey)
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleEscapeKey)
    restoreScroll()
  })

  // 监听visible变化
  watch(
    () => props.visible,
    visible => {
      if (visible) {
        nextTick(() => {
          preventScroll()
        })
      } else {
        restoreScroll()
      }
    },
    { immediate: true }
  )
</script>

<style lang="scss">
  .c-modal {
    position: fixed;
    inset: 0;
    z-index: 1000;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: var(--spacing-6) var(--spacing-4);
    overflow-y: auto;
    background: rgb(0 0 0 / 50%);

    &--centered {
      align-items: center;
    }
  }

  .c-modal__dialog {
    position: relative;
    display: flex;
    flex-direction: column;
    max-width: 90vw;
    max-height: 90vh;
    background: var(--color-bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);

    @include fade-in;
  }

  .c-modal__header {
    @include flex-between;
    flex-shrink: 0;
    padding: var(--spacing-5) var(--spacing-6);
    border-bottom: 1px solid var(--color-border-light);
  }

  .c-modal__title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
  }

  .c-modal__close {
    padding: var(--spacing-1);
    font-size: 20px;
    color: var(--color-text-tertiary);
    cursor: pointer;
    background: none;
    border: none;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);

    &:hover {
      color: var(--color-text-primary);
      background: var(--color-bg-hover);
    }
  }

  .c-modal__body {
    flex: 1;
    padding: var(--spacing-6);
    overflow-y: auto;

    @include scrollbar;
  }

  .c-modal__footer {
    @include flex-center;
    flex-shrink: 0;
    gap: var(--spacing-3);
    justify-content: flex-end;
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--color-border-light);
  }

  // 响应式
  @media (width <= 768px) {
    .c-modal {
      padding: var(--spacing-4) var(--spacing-2);
    }

    .c-modal__dialog {
      width: 100% !important;
      max-width: none;
      margin: 0;
    }

    .c-modal__header,
    .c-modal__body,
    .c-modal__footer {
      padding-right: var(--spacing-4);
      padding-left: var(--spacing-4);
    }

    .c-modal__footer {
      flex-direction: column-reverse;
      gap: var(--spacing-2);

      .c-button {
        width: 100%;
      }
    }
  }

  // IC封测专用样式
  .c-modal--wafer-detail {
    .c-modal__dialog {
      width: 800px;
      max-width: 95vw;
    }

    .wafer-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
    }

    .info-item {
      padding: var(--spacing-3);
      background: var(--color-bg-secondary);
      border-radius: var(--radius-base);

      .label {
        margin-bottom: var(--spacing-1);
        font-size: var(--font-size-xs);
        color: var(--color-text-secondary);
      }

      .value {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
      }
    }
  }

  .c-modal--test-result {
    .c-modal__dialog {
      width: 900px;
      max-width: 95vw;
    }

    .test-summary {
      display: flex;
      gap: var(--spacing-6);
      margin-bottom: var(--spacing-4);

      .summary-card {
        flex: 1;
        padding: var(--spacing-4);
        text-align: center;
        border-radius: var(--radius-base);

        &.pass {
          color: var(--color-success);
          background: var(--color-die-pass);
        }

        &.fail {
          color: var(--color-error);
          background: var(--color-die-fail);
        }

        .count {
          margin-bottom: var(--spacing-1);
          font-size: var(--font-size-2xl);
          font-weight: var(--font-weight-bold);
        }

        .label {
          font-size: var(--font-size-sm);
        }
      }
    }
  }
</style>
