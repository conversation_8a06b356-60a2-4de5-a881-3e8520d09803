# IC封测CIM系统 - D盘安装脚本
# 管理员权限运行此脚本

param(
    [switch]$CreateDirectories,
    [switch]$DownloadSoftware,
    [switch]$SetEnvironmentVariables,
    [switch]$InstallDocker,
    [switch]$All
)

# 如果没有指定参数，默认执行所有操作
if (-not ($CreateDirectories -or $DownloadSoftware -or $SetEnvironmentVariables -or $InstallDocker)) {
    $All = $true
}

Write-Host "=====================================" -ForegroundColor Cyan
Write-Host "  IC封测CIM系统 D盘安装脚本" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# 1. 创建目录结构
if ($CreateDirectories -or $All) {
    Write-Host "`n[1/4] 创建D盘目录结构..." -ForegroundColor Yellow
    
    $directories = @(
        "D:\Development\Java\jdk-17",
        "D:\Development\Java\maven", 
        "D:\Development\Redis",
        "D:\Development\Tools\IntelliJ-IDEA",
        "D:\Development\Tools\MySQL-Workbench",
        "D:\Development\Tools\Postman",
        "D:\Development\Databases\InfluxDB",
        "D:\Development\Databases\Elasticsearch",
        "D:\Development\Databases\Neo4j",
        "D:\Development\Middleware\RabbitMQ",
        "D:\Development\Middleware\Nginx",
        "D:\Development\Middleware\Prometheus",
        "D:\Development\Middleware\Grafana",
        "D:\Development\Data\mysql-data",
        "D:\Development\Data\redis-data",
        "D:\Development\Data\influxdb-data",
        "D:\Development\Data\elasticsearch-data",
        "D:\Development\Data\rabbitmq-data",
        "D:\Workspace\maven-repo",
        "D:\Workspace\npm-cache",
        "D:\Workspace\temp",
        "D:\Projects\JSCIM-System"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Host "  ✓ 创建目录: $dir" -ForegroundColor Green
        } else {
            Write-Host "  ◦ 目录已存在: $dir" -ForegroundColor Gray
        }
    }
}

# 2. 下载软件
if ($DownloadSoftware -or $All) {
    Write-Host "`n[2/4] 下载必要软件..." -ForegroundColor Yellow
    
    # 创建下载目录
    $downloadPath = "D:\Workspace\temp\downloads"
    if (!(Test-Path $downloadPath)) {
        New-Item -ItemType Directory -Path $downloadPath -Force | Out-Null
    }
    
    Write-Host "下载目录: $downloadPath" -ForegroundColor Cyan
    
    # 下载链接配置
    $downloads = @{
        "Java JDK 17" = @{
            "url" = "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.10%2B7/OpenJDK17U-jdk_x64_windows_hotspot_17.0.10_7.msi"
            "file" = "OpenJDK17U-jdk_x64_windows_hotspot_17.0.10_7.msi"
            "target" = "D:\Development\Java\jdk-17"
        }
        "Maven" = @{
            "url" = "https://dlcdn.apache.org/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.zip"
            "file" = "apache-maven-3.9.6-bin.zip" 
            "target" = "D:\Development\Java\maven"
        }
        "Redis Windows" = @{
            "url" = "https://github.com/microsoftarchive/redis/releases/download/win-3.0.504/Redis-x64-3.0.504.zip"
            "file" = "Redis-x64-3.0.504.zip"
            "target" = "D:\Development\Redis"
        }
        "IntelliJ IDEA Community" = @{
            "url" = "https://download.jetbrains.com/idea/ideaIC-2023.3.4.exe"
            "file" = "ideaIC-2023.3.4.exe"
            "target" = "D:\Development\Tools\IntelliJ-IDEA"
        }
        "MySQL Workbench" = @{
            "url" = "https://dev.mysql.com/get/Downloads/MySQLGUITools/mysql-workbench-community-8.0.36-winx64.msi"
            "file" = "mysql-workbench-community-8.0.36-winx64.msi"
            "target" = "D:\Development\Tools\MySQL-Workbench"
        }
    }
    
    foreach ($software in $downloads.Keys) {
        $info = $downloads[$software]
        $filePath = Join-Path $downloadPath $info.file
        
        Write-Host "`n  下载 $software..." -ForegroundColor Cyan
        Write-Host "    URL: $($info.url)" -ForegroundColor Gray
        Write-Host "    文件: $($info.file)" -ForegroundColor Gray
        
        if (!(Test-Path $filePath)) {
            try {
                Write-Host "    正在下载..." -ForegroundColor Yellow
                Invoke-WebRequest -Uri $info.url -OutFile $filePath -UserAgent "Mozilla/5.0"
                Write-Host "    ✓ 下载完成: $filePath" -ForegroundColor Green
            } catch {
                Write-Host "    ✗ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "    请手动下载: $($info.url)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "    ◦ 文件已存在: $filePath" -ForegroundColor Gray
        }
    }
    
    Write-Host "`n  安装说明：" -ForegroundColor Cyan
    Write-Host "  1. Java JDK: 运行MSI安装到 D:\Development\Java\jdk-17\" -ForegroundColor White
    Write-Host "  2. Maven: 解压ZIP到 D:\Development\Java\maven\" -ForegroundColor White  
    Write-Host "  3. Redis: 解压ZIP到 D:\Development\Redis\" -ForegroundColor White
    Write-Host "  4. IntelliJ IDEA: 运行EXE安装到 D:\Development\Tools\IntelliJ-IDEA\" -ForegroundColor White
    Write-Host "  5. MySQL Workbench: 运行MSI安装到 D:\Development\Tools\MySQL-Workbench\" -ForegroundColor White
}

# 3. 设置环境变量
if ($SetEnvironmentVariables -or $All) {
    Write-Host "`n[3/4] 配置环境变量..." -ForegroundColor Yellow
    
    try {
        # Java环境变量
        [Environment]::SetEnvironmentVariable("JAVA_HOME", "D:\Development\Java\jdk-17", "Machine")
        Write-Host "  ✓ 设置 JAVA_HOME = D:\Development\Java\jdk-17" -ForegroundColor Green
        
        # Maven环境变量
        [Environment]::SetEnvironmentVariable("MAVEN_HOME", "D:\Development\Java\maven", "Machine")
        Write-Host "  ✓ 设置 MAVEN_HOME = D:\Development\Java\maven" -ForegroundColor Green
        
        # Redis环境变量
        [Environment]::SetEnvironmentVariable("REDIS_HOME", "D:\Development\Redis", "Machine")
        Write-Host "  ✓ 设置 REDIS_HOME = D:\Development\Redis" -ForegroundColor Green
        
        # 更新PATH
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
        $newPaths = @(
            "D:\Development\Java\jdk-17\bin",
            "D:\Development\Java\maven\bin",
            "D:\Development\Redis"
        )
        
        foreach ($newPath in $newPaths) {
            if ($currentPath -notlike "*$newPath*") {
                $currentPath += ";$newPath"
            }
        }
        
        [Environment]::SetEnvironmentVariable("PATH", $currentPath, "Machine")
        Write-Host "  ✓ 更新 PATH 环境变量" -ForegroundColor Green
        
    } catch {
        Write-Host "  ✗ 设置环境变量失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "  请以管理员权限运行此脚本" -ForegroundColor Yellow
    }
}

# 4. Docker配置
if ($InstallDocker -or $All) {
    Write-Host "`n[4/4] 创建Docker Compose配置..." -ForegroundColor Yellow
    
    $dockerCompose = @"
version: '3.8'
services:
  redis:
    image: redis:7.2-alpine
    container_name: ic-cim-redis
    ports:
      - "6379:6379"
    volumes:
      - D:\Development\Data\redis-data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  influxdb:
    image: influxdb:2.7
    container_name: ic-cim-influxdb
    ports:
      - "8086:8086"
    volumes:
      - D:\Development\Data\influxdb-data:/var/lib/influxdb2
    environment:
      - INFLUXDB_DB=ic_cim
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=admin123
    restart: unless-stopped

  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: ic-cim-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - D:\Development\Data\rabbitmq-data:/var/lib/rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
    restart: unless-stopped

  elasticsearch:
    image: elasticsearch:8.12.0
    container_name: ic-cim-elasticsearch
    ports:
      - "9200:9200"
    volumes:
      - D:\Development\Data\elasticsearch-data:/usr/share/elasticsearch/data
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    restart: unless-stopped

  kibana:
    image: kibana:8.12.0
    container_name: ic-cim-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:v2.48.1
    container_name: ic-cim-prometheus
    ports:
      - "9090:9090"
    volumes:
      - D:\Development\Data\prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.3.3
    container_name: ic-cim-grafana
    ports:
      - "3000:3000"
    volumes:
      - D:\Development\Data\grafana-data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    restart: unless-stopped
"@

    $dockerComposeFile = "D:\Projects\JSCIM-System\docker-compose.yml"
    $dockerCompose | Out-File -FilePath $dockerComposeFile -Encoding UTF8
    Write-Host "  ✓ 创建 Docker Compose 配置: $dockerComposeFile" -ForegroundColor Green
    
    # 创建启动脚本
    $startScript = @"
@echo off
echo 启动IC封测CIM系统Docker容器...
cd /d D:\Projects\JSCIM-System
docker-compose up -d
echo.
echo 容器启动完成！访问地址：
echo Redis: localhost:6379
echo InfluxDB: http://localhost:8086 (admin/admin123)
echo RabbitMQ: http://localhost:15672 (admin/admin123)
echo Elasticsearch: http://localhost:9200
echo Kibana: http://localhost:5601
echo Prometheus: http://localhost:9090
echo Grafana: http://localhost:3000 (admin/admin123)
pause
"@
    
    $startScriptFile = "D:\Projects\JSCIM-System\start-services.bat"
    $startScript | Out-File -FilePath $startScriptFile -Encoding UTF8
    Write-Host "  ✓ 创建启动脚本: $startScriptFile" -ForegroundColor Green
}

Write-Host "`n=====================================" -ForegroundColor Cyan
Write-Host "           安装脚本执行完成" -ForegroundColor Cyan  
Write-Host "=====================================" -ForegroundColor Cyan

Write-Host "`n下一步操作：" -ForegroundColor Yellow
Write-Host "1. 手动安装下载的软件到指定D盘目录" -ForegroundColor White
Write-Host "2. 重启命令行使环境变量生效" -ForegroundColor White
Write-Host "3. 运行验证命令检查安装结果" -ForegroundColor White
Write-Host "4. 使用Docker启动中间件服务" -ForegroundColor White

Write-Host "`n验证命令：" -ForegroundColor Yellow
Write-Host "java -version" -ForegroundColor Cyan
Write-Host "mvn --version" -ForegroundColor Cyan  
Write-Host "docker --version" -ForegroundColor Cyan
Write-Host "docker-compose --version" -ForegroundColor Cyan

Write-Host "`n启动服务：" -ForegroundColor Yellow
Write-Host "cd D:\Projects\JSCIM-System" -ForegroundColor Cyan
Write-Host "docker-compose up -d" -ForegroundColor Cyan