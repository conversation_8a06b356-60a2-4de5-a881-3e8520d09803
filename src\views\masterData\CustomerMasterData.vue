<template>
  <div class="customer-master-data">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="page-title">
        <h2>客户主数据管理</h2>
        <p class="page-description">IC设计公司客户标准化主数据维护，为业务流程提供准确数据源</p>
      </div>
      <div class="page-actions">
        <el-button type="success" @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button type="info" @click="exportCustomers">
          <el-icon><Download /></el-icon>
          数据导出
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增客户
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索客户名称、编码、英文名"
              clearable
              @clear="resetSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>

          <el-col :span="4">
            <el-select
              v-model="searchForm.type"
              placeholder="客户类型"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="option in CUSTOMER_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-col>

          <el-col :span="4">
            <el-select
              v-model="searchForm.level"
              placeholder="客户等级"
              clearable
              multiple
              collapse-tags
            >
              <el-option
                v-for="option in CUSTOMER_LEVEL_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-col>

          <el-col :span="4">
            <el-select
              v-model="searchForm.status"
              placeholder="客户状态"
              clearable
              multiple
              collapse-tags
            >
              <el-option
                v-for="option in CUSTOMER_STATUS_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-col>

          <el-col :span="6">
            <el-button type="primary" @click="searchCustomers">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button
type="text" @click="showAdvancedSearch = !showAdvancedSearch"
>
              {{ showAdvancedSearch ? '收起' : '高级筛选' }}
              <el-icon>
                <component :is="showAdvancedSearch ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </el-col>
        </el-row>

        <!-- 高级搜索 -->
        <div v-show="showAdvancedSearch" class="advanced-search">
          <el-divider content-position="left">高级筛选</el-divider>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select
                v-model="searchForm.scale"
                placeholder="客户规模"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="option in CUSTOMER_SCALE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-col>

            <el-col :span="6">
              <el-select
                v-model="searchForm.creditLevel"
                placeholder="信用等级"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="option in CREDIT_LEVEL_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-col>

            <el-col :span="6">
              <el-select
                v-model="searchForm.applicationFields"
                placeholder="应用领域"
                clearable
                multiple
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="option in APPLICATION_FIELD_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-col>

            <el-col :span="6">
              <el-date-picker
                v-model="searchForm.createdDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="创建开始日期"
                end-placeholder="创建结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <!-- 数据统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-item">
              <div class="stats-icon primary">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ customerStats.total }}
                </div>
                <div class="stats-label">总客户数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-item">
              <div class="stats-icon success">
                <el-icon><CircleCheckFilled /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ customerStats.active }}
                </div>
                <div class="stats-label">活跃客户</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-item">
              <div class="stats-icon warning">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ customerStats.strategic }}
                </div>
                <div class="stats-label">战略客户</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-item">
              <div class="stats-icon info">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ customerStats.thisMonth }}
                </div>
                <div class="stats-label">本月新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 客户列表表格 -->
    <el-card shadow="never" class="table-card">
      <template #header>
        <div class="table-header">
          <span class="table-title">客户列表 ({{ pagination.total }})</span>
          <div class="table-actions">
            <el-checkbox v-model="showOnlyFavorites">只显示收藏客户</el-checkbox>
            <el-button-group>
              <el-button
                size="small"
                :type="tableView === 'table' ? 'primary' : ''"
                @click="tableView = 'table'"
              >
                <el-icon><Menu /></el-icon>
              </el-button>
              <el-button
                size="small"
                :type="tableView === 'card' ? 'primary' : ''"
                @click="tableView = 'card'"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-show="tableView === 'table'">
        <el-table
          v-loading="loading"
          :data="customerList"
          stripe
          border
          height="600"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="50" />
          <el-table-column label="收藏" width="60" align="center">
            <template #default="{ row }">
              <el-button
text @click.stop="toggleFavorite(row)"
>
                <el-icon :style="{ color: row.isFavorite ? '#f56c6c' : '#c0c4cc' }">
                  <StarFilled v-if="row.isFavorite" />
                  <Star v-else />
                </el-icon>
              </el-button>
            </template>
          </el-table-column>

          <el-table-column prop="code" label="客户编码" width="120" fixed="left" />

          <el-table-column label="客户信息" width="300" fixed="left">
            <template #default="{ row }">
              <div class="customer-info">
                <div class="customer-avatar">
                  <el-avatar
                    :size="40"
                    :src="row.logo"
                    :style="{ backgroundColor: getCustomerColor(row.type) }"
                  >
                    {{ row.shortName?.charAt(0) || row.name.charAt(0) }}
                  </el-avatar>
                </div>
                <div class="customer-details">
                  <div class="customer-name">
                    {{ row.name }}
                  </div>
                  <div class="customer-english">
                    {{ row.englishName }}
                  </div>
                  <div class="customer-short">
                    {{ row.shortName }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="类型/等级" width="120" align="center">
            <template #default="{ row }">
              <div class="type-level-tags">
                <el-tag
size="small" :type="getCustomerTypeTagType(row.type)"
>
                  {{ getCustomerTypeLabel(row.type) }}
                </el-tag>
                <el-tag
                  size="small"
                  :color="getCustomerLevelColor(row.level)"
                  style="margin-top: 4px; color: white"
                >
                  {{ getCustomerLevelLabel(row.level) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag
:type="getStatusTagType(row.status)" size="small"
>
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="规模" width="100" align="center">
            <template #default="{ row }">
              <el-tag size="small" type="info">
                {{ getScaleLabel(row.scale) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="信用等级" width="100" align="center">
            <template #default="{ row }">
              <el-tag
                size="small"
                :color="getCreditLevelColor(row.creditLevel)"
                style="color: white"
              >
                {{ row.creditLevel }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="应用领域" width="150">
            <template #default="{ row }">
              <div class="application-tags">
                <el-tag
                  v-for="(field, index) in row.applicationFields.slice(0, 2)"
                  :key="field"
                  size="small"
                  :type="index % 2 === 0 ? 'success' : 'warning'"
                >
                  {{ getApplicationFieldLabel(field) }}
                </el-tag>
                <span v-if="row.applicationFields.length > 2" class="more-tag">
                  +{{ row.applicationFields.length - 2 }}
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="主要联系人" width="180">
            <template #default="{ row }">
              <div class="contact-info">
                <div class="contact-name">
                  {{ row.contact.name }}
                </div>
                <div class="contact-position">
                  {{ row.contact.position }}
                </div>
                <div class="contact-phone">
                  {{ row.contact.phone }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="160" align="center">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column label="最后联系" width="160" align="center">
            <template #default="{ row }">
              <span v-if="row.lastContactDate">
                {{ formatDate(row.lastContactDate) }}
              </span>
              <span v-else class="no-contact">暂无联系</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button size="small" @click.stop="viewCustomer(row)">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button size="small" @click.stop="editCustomer(row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button size="small" @click.stop="showDuplicateDialog(row)">
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
                <el-button
size="small" type="danger"
@click.stop="deleteCustomer(row)"
>
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-show="tableView === 'card'" class="card-view">
        <el-row :gutter="20">
          <el-col
            v-for="customer in customerList"
            :key="customer.id"
            :span="8"
            style="margin-bottom: 20px"
          >
            <CustomerCard
              :customer="customer"
              @view="viewCustomer"
              @edit="editCustomer"
              @delete="deleteCustomer"
              @favorite="toggleFavorite"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange"
        />
      </div>
    </el-card>

    <!-- 批量操作浮动菜单 -->
    <div v-show="selectedCustomers.length > 0" class="batch-actions">
      <div class="batch-info">
已选择 {{ selectedCustomers.length }} 个客户
</div>
      <div class="batch-buttons">
        <el-button
size="small" @click="batchExport">批量导出</el-button>
        <el-button
size="small" type="warning" @click="batchUpdateStatus">批量更新状态</el-button>
        <el-button
size="small" type="danger" @click="batchDelete">批量删除</el-button>
        <el-button
size="small" @click="clearSelection">取消选择</el-button>
      </div>
    </div>

    <!-- 新增/编辑客户对话框 -->
    <CustomerDialog
      v-model:visible="showCreateDialog"
      :customer="currentCustomer"
      :mode="dialogMode"
      @save="handleSaveCustomer"
    />

    <!-- 客户详情对话框 -->
    <CustomerDetailDialog
v-model:visible="showDetailDialog" :customer="currentCustomer"
/>

    <!-- 复制客户对话框 -->
    <CustomerDuplicateDialog
      v-model:visible="showDuplicateDialogFlag"
      :customer="currentCustomer"
      @save="handleSaveCustomer"
    />

    <!-- 导入对话框 -->
    <CustomerImportDialog
v-model:visible="showImportDialog" @success="handleImportSuccess"
/>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import type {
    Customer,
    CustomerQueryParams,
    CreateCustomerData,
    UpdateCustomerData,
    CustomerType,
    CustomerLevel,
    CustomerStatus,
    CustomerScale,
    CreditLevel,
    ApplicationField
  } from '@/types/customer'
  import {
    mockCustomers,
    CUSTOMER_TYPE_OPTIONS,
    CUSTOMER_LEVEL_OPTIONS,
    CUSTOMER_STATUS_OPTIONS,
    CUSTOMER_SCALE_OPTIONS,
    CREDIT_LEVEL_OPTIONS,
    APPLICATION_FIELD_OPTIONS,
    generateNewCustomerCode
  } from '@/utils/mockData/customerMaster'
  import CustomerCard from '@/components/business/CustomerCard.vue'
  import CustomerDialog from '@/components/business/CustomerDialog.vue'
  import CustomerDetailDialog from '@/components/business/CustomerDetailDialog.vue'
  import CustomerDuplicateDialog from '@/components/business/CustomerDuplicateDialog.vue'
  import CustomerImportDialog from '@/components/business/CustomerImportDialog.vue'

  // 页面状态
  const loading = ref(false)
  const showAdvancedSearch = ref(false)
  const showOnlyFavorites = ref(false)
  const tableView = ref<'table' | 'card'>('table')

  // 搜索表单
  const searchForm = reactive<CustomerQueryParams>({
    keyword: '',
    type: [],
    level: [],
    status: [],
    scale: [],
    creditLevel: [],
    applicationFields: [],
    createdDateRange: null,
    page: 1,
    pageSize: 20
  })

  // 客户列表数据
  const customerList = ref<Customer[]>([])
  const selectedCustomers = ref<Customer[]>([])

  // 分页数据
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 对话框状态
  const showCreateDialog = ref(false)
  const showDetailDialog = ref(false)
  const showDuplicateDialogFlag = ref(false)
  const showImportDialog = ref(false)
  const currentCustomer = ref<Customer | null>(null)
  const dialogMode = ref<'create' | 'edit'>('create')

  // 客户统计数据
  const customerStats = computed(() => {
    const allCustomers = mockCustomers
    return {
      total: allCustomers.length,
      active: allCustomers.filter(c => c.status === 'active').length,
      strategic: allCustomers.filter(c => c.level === 'strategic').length,
      thisMonth: allCustomers.filter(c => {
        const created = new Date(c.createdAt)
        const now = new Date()
        return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear()
      }).length
    }
  })

  // 获取客户颜色
  const getCustomerColor = (type: CustomerType): string => {
    const colors = {
      fabless: '#409EFF',
      idm: '#67C23A',
      foundry: '#E6A23C',
      distributor: '#909399',
      broker: '#F56C6C',
      ems: '#9580FF'
    }
    return colors[type]
  }

  // 获取客户类型标签类型
  const getCustomerTypeTagType = (type: CustomerType): string => {
    const types = {
      fabless: 'primary',
      idm: 'success',
      foundry: 'warning',
      distributor: 'info',
      broker: 'danger',
      ems: ''
    }
    return types[type] || ''
  }

  // 获取客户类型标签文本
  const getCustomerTypeLabel = (type: CustomerType): string => {
    const option = CUSTOMER_TYPE_OPTIONS.find(opt => opt.value === type)
    return option?.label || type
  }

  // 获取客户等级颜色
  const getCustomerLevelColor = (level: CustomerLevel): string => {
    const option = CUSTOMER_LEVEL_OPTIONS.find(opt => opt.value === level)
    return option?.color || '#909399'
  }

  // 获取客户等级标签文本
  const getCustomerLevelLabel = (level: CustomerLevel): string => {
    const option = CUSTOMER_LEVEL_OPTIONS.find(opt => opt.value === level)
    return option?.label || level
  }

  // 获取状态标签类型
  const getStatusTagType = (status: CustomerStatus): string => {
    const types = {
      active: 'success',
      inactive: 'info',
      pending: 'warning',
      suspended: 'danger',
      archived: 'info'
    }
    return types[status] || ''
  }

  // 获取状态标签文本
  const getStatusLabel = (status: CustomerStatus): string => {
    const option = CUSTOMER_STATUS_OPTIONS.find(opt => opt.value === status)
    return option?.label || status
  }

  // 获取规模标签文本
  const getScaleLabel = (scale: CustomerScale): string => {
    const option = CUSTOMER_SCALE_OPTIONS.find(opt => opt.value === scale)
    return option?.label || scale
  }

  // 获取信用等级颜色
  const getCreditLevelColor = (level: CreditLevel): string => {
    const option = CREDIT_LEVEL_OPTIONS.find(opt => opt.value === level)
    return option?.color || '#909399'
  }

  // 获取应用领域标签文本
  const getApplicationFieldLabel = (field: ApplicationField): string => {
    const option = APPLICATION_FIELD_OPTIONS.find(opt => opt.value === field)
    return option?.label || field
  }

  // 格式化日期
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  // 搜索客户
  const searchCustomers = async () => {
    loading.value = true
    try {
      // 模拟搜索
      await new Promise(resolve => setTimeout(resolve, 300))

      let filteredCustomers = mockCustomers

      // 关键词搜索
      if (searchForm.keyword) {
        const keyword = searchForm.keyword.toLowerCase()
        filteredCustomers = filteredCustomers.filter(
          customer =>
            customer.name.toLowerCase().includes(keyword) ||
            customer.englishName?.toLowerCase().includes(keyword) ||
            customer.shortName?.toLowerCase().includes(keyword) ||
            customer.code.toLowerCase().includes(keyword)
        )
      }

      // 类型筛选
      if (searchForm.type && searchForm.type.length > 0) {
        filteredCustomers = filteredCustomers.filter(customer =>
          searchForm.type!.includes(customer.type)
        )
      }

      // 等级筛选
      if (searchForm.level && searchForm.level.length > 0) {
        filteredCustomers = filteredCustomers.filter(customer =>
          searchForm.level!.includes(customer.level)
        )
      }

      // 状态筛选
      if (searchForm.status && searchForm.status.length > 0) {
        filteredCustomers = filteredCustomers.filter(customer =>
          searchForm.status!.includes(customer.status)
        )
      }

      // 规模筛选
      if (searchForm.scale && searchForm.scale.length > 0) {
        filteredCustomers = filteredCustomers.filter(customer =>
          searchForm.scale!.includes(customer.scale)
        )
      }

      // 信用等级筛选
      if (searchForm.creditLevel && searchForm.creditLevel.length > 0) {
        filteredCustomers = filteredCustomers.filter(customer =>
          searchForm.creditLevel!.includes(customer.creditLevel)
        )
      }

      // 应用领域筛选
      if (searchForm.applicationFields && searchForm.applicationFields.length > 0) {
        filteredCustomers = filteredCustomers.filter(customer =>
          customer.applicationFields.some(field => searchForm.applicationFields!.includes(field))
        )
      }

      // 创建日期筛选
      if (searchForm.createdDateRange) {
        const [startDate, endDate] = searchForm.createdDateRange
        filteredCustomers = filteredCustomers.filter(customer => {
          const createdDate = new Date(customer.createdAt)
          const start = new Date(startDate)
          const end = new Date(endDate)
          end.setHours(23, 59, 59)
          return createdDate >= start && createdDate <= end
        })
      }

      // 收藏筛选
      if (showOnlyFavorites.value) {
        filteredCustomers = filteredCustomers.filter(customer => (customer as any).isFavorite)
      }

      // 分页
      pagination.total = filteredCustomers.length
      const startIndex = (pagination.page - 1) * pagination.pageSize
      const endIndex = startIndex + pagination.pageSize
      customerList.value = filteredCustomers.slice(startIndex, endIndex)
    } catch (error) {
      console.error('搜索客户失败:', error)
      ElMessage.error('搜索客户失败，请重试')
    } finally {
      loading.value = false
    }
  }

  // 重置搜索
  const resetSearch = () => {
    Object.assign(searchForm, {
      keyword: '',
      type: [],
      level: [],
      status: [],
      scale: [],
      creditLevel: [],
      applicationFields: [],
      createdDateRange: null
    })
    pagination.page = 1
    searchCustomers()
  }

  // 处理表格选择变化
  const handleSelectionChange = (selection: Customer[]) => {
    selectedCustomers.value = selection
  }

  // 处理行点击
  const handleRowClick = (row: Customer) => {
    viewCustomer(row)
  }

  // 收藏/取消收藏
  const toggleFavorite = (customer: Customer) => {
    const customerData = customer as any
    customerData.isFavorite = !customerData.isFavorite
    ElMessage.success(customerData.isFavorite ? '已添加到收藏' : '已从收藏中移除')
  }

  // 查看客户详情
  const viewCustomer = (customer: Customer) => {
    currentCustomer.value = customer
    showDetailDialog.value = true
  }

  // 编辑客户
  const editCustomer = (customer: Customer) => {
    currentCustomer.value = customer
    dialogMode.value = 'edit'
    showCreateDialog.value = true
  }

  // 复制客户
  const showDuplicateDialog = (customer: Customer) => {
    currentCustomer.value = customer
    showDuplicateDialogFlag.value = true
  }

  // 删除客户
  const deleteCustomer = async (customer: Customer) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除客户 "${customer.name}" 吗？此操作不可恢复。`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 模拟删除操作
      const index = customerList.value.findIndex(c => c.id === customer.id)
      if (index > -1) {
        customerList.value.splice(index, 1)
        pagination.total--
        ElMessage.success('删除成功')
      }
    } catch (error) {
      // 用户取消删除
    }
  }

  // 新增客户
  const createCustomer = () => {
    currentCustomer.value = null
    dialogMode.value = 'create'
    showCreateDialog.value = true
  }

  // 保存客户
  const handleSaveCustomer = async (customerData: CreateCustomerData | UpdateCustomerData) => {
    try {
      if (dialogMode.value === 'create') {
        // 模拟创建
        const newCustomer: Customer = {
          ...(customerData as CreateCustomerData),
          id: `cust_${Date.now()}`,
          code: customerData.code || generateNewCustomerCode(customerData.type),
          metrics: {
            totalOrders: 0,
            totalRevenue: 0,
            averageOrderValue: 0,
            onTimeDeliveryRate: 0,
            qualityScore: 0,
            satisfactionScore: 0
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'current-user'
        }

        customerList.value.unshift(newCustomer)
        pagination.total++
        ElMessage.success('客户创建成功')
      } else {
        // 模拟更新
        const index = customerList.value.findIndex(c => c.id === currentCustomer.value?.id)
        if (index > -1) {
          customerList.value[index] = {
            ...customerList.value[index],
            ...customerData,
            updatedAt: new Date().toISOString()
          }
          ElMessage.success('客户更新成功')
        }
      }

      showCreateDialog.value = false
      showDuplicateDialogFlag.value = false
    } catch (error) {
      console.error('保存客户失败:', error)
      ElMessage.error('保存失败，请重试')
    }
  }

  // 导出客户数据
  const exportCustomers = () => {
    ElMessage.info('导出功能开发中...')
  }

  // 批量导出
  const batchExport = () => {
    ElMessage.info(`正在导出 ${selectedCustomers.value.length} 个客户数据...`)
  }

  // 批量更新状态
  const batchUpdateStatus = () => {
    ElMessage.info('批量更新状态功能开发中...')
  }

  // 批量删除
  const batchDelete = async () => {
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedCustomers.value.length} 个客户吗？此操作不可恢复。`,
        '批量删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 模拟批量删除
      const deleteIds = selectedCustomers.value.map(c => c.id)
      customerList.value = customerList.value.filter(c => !deleteIds.includes(c.id))
      pagination.total -= selectedCustomers.value.length
      selectedCustomers.value = []

      ElMessage.success('批量删除成功')
    } catch (error) {
      // 用户取消
    }
  }

  // 清除选择
  const clearSelection = () => {
    selectedCustomers.value = []
  }

  // 处理导入成功
  const handleImportSuccess = (count: number) => {
    ElMessage.success(`成功导入 ${count} 个客户`)
    searchCustomers() // 刷新列表
  }

  // 分页处理
  const handlePageSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.page = 1
    searchCustomers()
  }

  const handleCurrentPageChange = (page: number) => {
    pagination.page = page
    searchCustomers()
  }

  // 监听收藏筛选变化
  watch(showOnlyFavorites, () => {
    searchCustomers()
  })

  // 页面初始化
  onMounted(() => {
    searchCustomers()
  })
</script>

<style lang="scss" scoped>
  .customer-master-data {
    min-height: 100vh;
    padding: 20px;
    background: var(--color-bg-light);

    .page-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 20px;

      .page-title {
        h2 {
          margin: 0 0 8px;
          font-size: 24px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .page-description {
          margin: 0;
          font-size: 14px;
          color: var(--color-text-secondary);
        }
      }

      .page-actions {
        display: flex;
        gap: 12px;
      }
    }

    .search-card {
      margin-bottom: 20px;

      .search-form {
        .advanced-search {
          margin-top: 20px;
        }
      }
    }

    .stats-cards {
      margin-bottom: 20px;

      .stats-card {
        .stats-item {
          display: flex;
          align-items: center;

          .stats-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            margin-right: 16px;
            border-radius: 50%;

            &.primary {
              color: var(--color-primary);
              background: var(--color-primary-light);
            }

            &.success {
              color: var(--color-success);
              background: var(--color-success-light);
            }

            &.warning {
              color: var(--color-warning);
              background: var(--color-warning-light);
            }

            &.info {
              color: var(--color-info);
              background: var(--color-info-light);
            }
          }

          .stats-content {
            .stats-value {
              font-size: 24px;
              font-weight: 600;
              line-height: 1;
              color: var(--color-text-primary);
            }

            .stats-label {
              margin-top: 4px;
              font-size: 14px;
              color: var(--color-text-secondary);
            }
          }
        }
      }
    }

    .table-card {
      .table-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .table-title {
          font-size: 16px;
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .table-actions {
          display: flex;
          gap: 16px;
          align-items: center;
        }
      }

      .customer-info {
        display: flex;
        align-items: center;

        .customer-avatar {
          margin-right: 12px;
        }

        .customer-details {
          .customer-name {
            margin-bottom: 2px;
            font-weight: 500;
            color: var(--color-text-primary);
          }

          .customer-english {
            margin-bottom: 2px;
            font-size: 12px;
            color: var(--color-text-secondary);
          }

          .customer-short {
            font-size: 12px;
            color: var(--color-text-regular);
          }
        }
      }

      .type-level-tags {
        display: flex;
        flex-direction: column;
        gap: 4px;
        align-items: center;
      }

      .application-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .more-tag {
          font-size: 12px;
          color: var(--color-text-secondary);
        }
      }

      .contact-info {
        .contact-name {
          margin-bottom: 2px;
          font-weight: 500;
          color: var(--color-text-primary);
        }

        .contact-position {
          margin-bottom: 2px;
          font-size: 12px;
          color: var(--color-text-regular);
        }

        .contact-phone {
          font-size: 12px;
          color: var(--color-text-secondary);
        }
      }

      .no-contact {
        font-style: italic;
        color: var(--color-text-placeholder);
      }

      .card-view {
        min-height: 600px;
      }

      .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }
    }

    .batch-actions {
      position: fixed;
      right: 50%;
      bottom: 20px;
      z-index: 1000;
      display: flex;
      gap: 20px;
      align-items: center;
      padding: 12px 20px;
      background: var(--color-white);
      border: 1px solid var(--color-border-light);
      border-radius: 24px;
      box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
      transform: translateX(50%);

      .batch-info {
        font-weight: 500;
        color: var(--color-text-primary);
      }

      .batch-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }

  // 响应式设计
  @media (width <= 1200px) {
    .customer-master-data {
      padding: 16px;

      .page-header {
        flex-direction: column;
        gap: 16px;
      }

      .stats-cards .el-col {
        margin-bottom: 16px;
      }
    }
  }
</style>
