# 项目开发规则 (PROJECT RULES)

## 🚨 前端开发服务器管理规则 (MANDATORY)

### 规则1: 服务器进程检查优先级 (Server Process Check Priority)
**在执行任何前端相关操作前，必须按以下顺序检查：**

1. **检查现有进程**：
```powershell
# Windows环境
Get-Process | Where-Object {$_.ProcessName -like "*node*" -or $_.ProcessName -like "*npm*"}

# 或使用跨平台命令
ps aux | grep node
lsof -i :3000
```

2. **测试开发服务器连通性**：
```powershell
# 测试默认端口3000
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000

# 如果3000不通，测试其他常用端口
curl -s -o /dev/null -w "%{http_code}" http://localhost:5173
curl -s -o /dev/null -w "%{http_code}" http://localhost:4173
```

3. **确认开发服务器状态**：
   - ✅ 如果返回200或其他2xx状态码 → **复用现有服务器**
   - ❌ 如果返回000或连接失败 → **启动新的开发服务器**

### 规则2: 开发服务器启动规范 (Development Server Standards)
**只有在以下情况下才启动新的开发服务器：**

1. **确认没有现有服务器运行**
2. **使用统一的启动命令**：`npm run dev`
3. **记录服务器地址**：启动后明确告知用户访问地址
4. **避免重复启动**：同一个session中不重复启动

### 规则3: 项目验证工作流 (Project Validation Workflow)
**每次代码修改后的验证顺序：**

1. **类型检查**: `npm run type-check`
2. **代码规范**: `npm run lint`
3. **样式检查**: `npm run lint:style`
4. **热重载验证**: 浏览器中查看实时效果
5. **功能测试**: 确认新功能正常工作

### 规则4: 端口管理策略 (Port Management Strategy)
**端口使用优先级：**
- **首选**: 3000 (Vite默认开发端口)
- **次选**: 5173 (Vite备用端口)
- **备选**: 4173 (Vite预览端口)

**端口冲突处理：**
```powershell
# 查找占用端口的进程
netstat -ano | findstr :3000

# 如果需要，终止冲突进程（谨慎操作）
taskkill /PID <进程ID> /F
```

## 🛠 Claude Code执行检查清单

### 开始任何前端工作前，必须执行：
- [ ] 检查现有Node.js进程：`ps aux | grep node`
- [ ] 测试开发服务器：`curl -s -o /dev/null -w "%{http_code}" http://localhost:3000`
- [ ] 如果服务器已运行，确认访问地址并告知用户
- [ ] 如果服务器未运行，启动：`npm run dev`

### 代码修改后，必须执行：
- [ ] 类型检查：`npm run type-check`
- [ ] 代码检查：`npm run lint`
- [ ] 确认热重载生效
- [ ] 验证新功能在浏览器中正常显示

### Session结束前，建议执行：
- [ ] 构建测试：`npm run build`
- [ ] 确认没有构建错误
- [ ] 记录当前开发服务器状态

## ⚡ 快速检查命令模板

```powershell
# 一键检查开发环境状态
echo "=== 检查Node.js进程 ===" && ps aux | grep node && echo "=== 检查端口3000 ===" && curl -s -o /dev/null -w "状态码: %{http_code}\n" http://localhost:3000 && echo "=== 检查完成 ==="
```

## 📋 问题预防措施

1. **会话开始时**：始终先检查现有进程
2. **代码修改时**：依赖热重载，避免重启服务器
3. **功能验证时**：使用现有服务器地址
4. **会话切换时**：记录服务器状态供下次会话参考

---

**此规则的目的**：
- 避免多个开发服务器同时运行
- 提高开发效率和资源利用率
- 确保前端效果验证的一致性
- 建立标准化的开发工作流程