// 深色主题专用样式
// 主要包含Element Plus等组件库的深色主题定制

// Element Plus 深色主题变量覆盖
$--color-primary: #3b82f6;
$--color-primary-light-1: #60a5fa;
$--color-primary-light-2: #93c5fd;
$--color-primary-light-3: #bfdbfe;
$--color-primary-light-4: #dbeafe;
$--color-primary-light-5: #eff6ff;
$--color-primary-light-6: #f0f9ff;
$--color-primary-light-7: #f8fafc;
$--color-primary-light-8: #f8fafc;
$--color-primary-light-9: #fff;
$--color-primary-dark-1: #2563eb;
$--color-primary-dark-2: #1d4ed8;
$--color-success: #34d399;
$--color-warning: #fbbf24;
$--color-danger: #f87171;
$--color-error: #f87171;
$--color-info: #94a3b8;

// 中性色
$--color-white: #f8fafc;
$--color-black: #0f172a;
$--color-text-primary: #f8fafc;
$--color-text-regular: #cbd5e1;
$--color-text-secondary: #94a3b8;
$--color-text-placeholder: #64748b;
$--color-text-disabled: #475569;

// 背景色
$--background-color-base: #1e293b;
$--background-color-page: #0f172a;
$--background-color-overlay: rgb(15 23 42 / 90%);

// 边框色
$--border-color-base: #475569;
$--border-color-light: #334155;
$--border-color-lighter: #1e293b;
$--border-color-extra-light: #0f172a;
$--border-color-dark: #64748b;
$--border-color-darker: #94a3b8;

// 填充色
$--fill-color-blank: #0f172a;
$--fill-color: #1e293b;
$--fill-color-light: #334155;
$--fill-color-lighter: #475569;
$--fill-color-extra-light: #64748b;
$--fill-color-dark: #0c1420;
$--fill-color-darker: #020617;

// 字体保持一致
$--font-family: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, 'Helvetica Neue', arial, 'Noto Sans', sans-serif;
$--font-size-extra-large: 20px;
$--font-size-large: 18px;
$--font-size-medium: 16px;
$--font-size-base: 14px;
$--font-size-small: 13px;
$--font-size-extra-small: 12px;

// 圆角保持一致
$--border-radius-base: 6px;
$--border-radius-small: 4px;
$--border-radius-round: 20px;
$--border-radius-circle: 100%;

// 深色阴影
$--box-shadow-base: 0 1px 3px 0 rgb(0 0 0 / 30%), 0 1px 2px 0 rgb(0 0 0 / 15%);
$--box-shadow-light: 0 1px 2px 0 rgb(0 0 0 / 20%);
$--box-shadow-lighter: 0 1px 1px 0 rgb(0 0 0 / 15%);
$--box-shadow-dark: 0 4px 6px -1px rgb(0 0 0 / 30%), 0 2px 4px -1px rgb(0 0 0 / 15%);

// 布局专用变量保持一致
$--header-height: 60px;
$--sidebar-width: 240px;
$--sidebar-collapsed-width: 64px;
$--footer-height: 40px;
$--breadcrumb-height: 48px;

// 极简设计专用变量
$--minimal-spacing: 16px;
$--minimal-border-width: 1px;
$--minimal-transition: 0.2s ease;

// 深色主题专用 mixin
@mixin dark-card {
  background: $--fill-color-blank;
  border: 1px solid $--border-color-light;
  border-radius: $--border-radius-base;
  box-shadow: $--box-shadow-light;
}

@mixin dark-hover {
  transition: all $--minimal-transition;

  &:hover {
    background: $--fill-color;
    border-color: $--border-color-base;
  }
}

@mixin dark-focus {
  outline: 2px solid rgb(59 130 246 / 40%);
  outline-offset: 2px;
}

// 深色主题特有的发光效果
@mixin dark-glow($color: $--color-primary) {
  box-shadow: 0 0 0 1px rgba($color, 0.3), 
              0 4px 6px -1px rgb(0 0 0 / 30%);
}