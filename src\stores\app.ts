/**
 * IC封测CIM系统 - 应用全局状态管理
 * Global Application Store for IC Packaging & Testing CIM System
 */

import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import { ElMessage, ElNotification, ElLoading } from 'element-plus'
import type { ApiResponse } from '@/api/config'

/**
 * 全局loading状态接口
 */
export interface LoadingState {
  global: boolean
  [key: string]: boolean
}

/**
 * 面包屑导航接口
 */
export interface BreadcrumbItem {
  title: string
  path?: string
  icon?: string
  disabled?: boolean
}

/**
 * 标签页接口
 */
export interface TabItem {
  key: string
  title: string
  path: string
  icon?: string
  closable?: boolean
  cached?: boolean
  params?: Record<string, any>
  query?: Record<string, any>
}

/**
 * 通知消息接口
 */
export interface NotificationItem {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: Date
  read: boolean
  actions?: Array<{
    label: string
    handler: () => void
    type?: 'primary' | 'default' | 'danger'
  }>
}

/**
 * 系统事件接口
 */
export interface SystemEvent {
  id: string
  type: 'system' | 'user' | 'equipment' | 'quality' | 'production'
  level: 'info' | 'warning' | 'error' | 'critical'
  title: string
  message: string
  source?: string
  timestamp: Date
  acknowledged: boolean
  data?: any
}

/**
 * 快捷操作接口
 */
export interface QuickAction {
  id: string
  title: string
  icon: string
  path?: string
  handler?: () => void
  permission?: string
  badge?: string | number
  disabled?: boolean
}

/**
 * 应用状态接口
 */
export interface AppState {
  // 界面状态
  sidebarCollapsed: boolean
  headerFixed: boolean
  sidebarFixed: boolean
  showBreadcrumb: boolean
  showTabs: boolean
  showFooter: boolean

  // 当前页面信息
  currentRoute: string
  currentTitle: string

  // 设备状态
  deviceType: 'desktop' | 'tablet' | 'mobile'
  screenSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  isOnline: boolean

  // 性能监控
  performanceData: {
    fps: number
    memory: number
    loadTime: number
    apiLatency: number
  }
}

/**
 * 应用全局状态Store
 */
export const useAppStore = defineStore('app', () => {
  // 基础状态
  const appState = ref<AppState>({
    sidebarCollapsed: false,
    headerFixed: true,
    sidebarFixed: true,
    showBreadcrumb: true,
    showTabs: true,
    showFooter: true,
    currentRoute: '/',
    currentTitle: '首页',
    deviceType: 'desktop',
    screenSize: 'lg',
    isOnline: navigator.onLine,
    performanceData: {
      fps: 60,
      memory: 0,
      loadTime: 0,
      apiLatency: 0
    }
  })

  // Loading状态
  const loadingState = ref<LoadingState>({
    global: false
  })

  // 面包屑导航
  const breadcrumbs = ref<BreadcrumbItem[]>([])

  // 标签页管理
  const tabs = ref<TabItem[]>([
    {
      key: 'home',
      title: '首页',
      path: '/',
      icon: 'House',
      closable: false,
      cached: true
    }
  ])
  const activeTab = ref('home')

  // 通知管理
  const notifications = ref<NotificationItem[]>([])
  const systemEvents = ref<SystemEvent[]>([])

  // 快捷操作
  const quickActions = ref<QuickAction[]>([
    {
      id: 'new-order',
      title: '新建订单',
      icon: 'Plus',
      path: '/orders/create',
      permission: 'order:create'
    },
    {
      id: 'quality-check',
      title: '质量检查',
      icon: 'Monitor',
      path: '/quality/inspection',
      permission: 'quality:read'
    },
    {
      id: 'equipment-status',
      title: '设备状态',
      icon: 'Setting',
      path: '/equipment/status',
      permission: 'equipment:read'
    },
    {
      id: 'reports',
      title: '报表中心',
      icon: 'DocumentCopy',
      path: '/reports',
      permission: 'report:read'
    }
  ])

  // 计算属性
  const unreadNotificationCount = computed(() => notifications.value.filter(n => !n.read).length)

  const criticalEventCount = computed(
    () => systemEvents.value.filter(e => e.level === 'critical' && !e.acknowledged).length
  )

  const isMobile = computed(() => appState.value.deviceType === 'mobile')

  const isTablet = computed(() => appState.value.deviceType === 'tablet')

  const isDesktop = computed(() => appState.value.deviceType === 'desktop')

  const currentTabItem = computed(() => tabs.value.find(tab => tab.key === activeTab.value))

  /**
   * 设置全局Loading状态
   */
  const setGlobalLoading = (loading: boolean, text?: string): void => {
    loadingState.value.global = loading

    if (loading && text) {
      ElLoading.service({
        lock: true,
        text,
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }
  }

  /**
   * 设置特定模块Loading状态
   */
  const setModuleLoading = (module: string, loading: boolean): void => {
    loadingState.value[module] = loading
  }

  /**
   * 获取模块Loading状态
   */
  const isModuleLoading = (module: string): boolean => {
    return loadingState.value[module] || false
  }

  /**
   * 更新设备信息
   */
  const updateDeviceInfo = (): void => {
    const width = window.innerWidth

    // 更新设备类型
    if (width < 768) {
      appState.value.deviceType = 'mobile'
      appState.value.screenSize = 'xs'
    } else if (width < 1024) {
      appState.value.deviceType = 'tablet'
      appState.value.screenSize = 'sm'
    } else if (width < 1200) {
      appState.value.deviceType = 'desktop'
      appState.value.screenSize = 'md'
    } else if (width < 1920) {
      appState.value.deviceType = 'desktop'
      appState.value.screenSize = 'lg'
    } else {
      appState.value.deviceType = 'desktop'
      appState.value.screenSize = 'xl'
    }

    // 移动端自动收起侧边栏
    if (appState.value.deviceType === 'mobile') {
      appState.value.sidebarCollapsed = true
    }
  }

  /**
   * 切换侧边栏
   */
  const toggleSidebar = (): void => {
    appState.value.sidebarCollapsed = !appState.value.sidebarCollapsed
    saveAppState()
  }

  /**
   * 设置面包屑导航
   */
  const setBreadcrumbs = (items: BreadcrumbItem[]): void => {
    breadcrumbs.value = items
  }

  /**
   * 添加面包屑项
   */
  const addBreadcrumb = (item: BreadcrumbItem): void => {
    const exists = breadcrumbs.value.find(b => b.path === item.path)
    if (!exists) {
      breadcrumbs.value.push(item)
    }
  }

  /**
   * 添加标签页
   */
  const addTab = (tab: Omit<TabItem, 'cached'>): void => {
    const exists = tabs.value.find(t => t.key === tab.key)

    if (!exists) {
      tabs.value.push({
        ...tab,
        cached: true
      })
    } else {
      // 更新现有标签页信息
      Object.assign(exists, tab)
    }

    activeTab.value = tab.key
    saveTabsState()
  }

  /**
   * 关闭标签页
   */
  const closeTab = (key: string): void => {
    const index = tabs.value.findIndex(t => t.key === key)
    if (index === -1) return

    const tab = tabs.value[index]
    if (!tab.closable) return

    // 如果关闭的是当前标签页，切换到相邻标签页
    if (activeTab.value === key) {
      const nextIndex = index > 0 ? index - 1 : index + 1
      if (tabs.value[nextIndex]) {
        activeTab.value = tabs.value[nextIndex].key
      }
    }

    tabs.value.splice(index, 1)
    saveTabsState()
  }

  /**
   * 关闭其他标签页
   */
  const closeOtherTabs = (key: string): void => {
    tabs.value = tabs.value.filter(t => t.key === key || !t.closable)
    activeTab.value = key
    saveTabsState()
  }

  /**
   * 关闭所有标签页
   */
  const closeAllTabs = (): void => {
    tabs.value = tabs.value.filter(t => !t.closable)
    if (tabs.value.length > 0) {
      activeTab.value = tabs.value[0].key
    }
    saveTabsState()
  }

  /**
   * 切换标签页
   */
  const switchTab = (key: string): void => {
    const tab = tabs.value.find(t => t.key === key)
    if (tab) {
      activeTab.value = key

      // 发送路由变化事件
      window.dispatchEvent(
        new CustomEvent('tab:switch', {
          detail: { tab }
        })
      )
    }
  }

  /**
   * 添加通知
   */
  const addNotification = (
    notification: Omit<NotificationItem, 'id' | 'timestamp' | 'read'>
  ): void => {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const newNotification: NotificationItem = {
      id,
      timestamp: new Date(),
      read: false,
      ...notification
    }

    notifications.value.unshift(newNotification)

    // 显示Element Plus通知
    ElNotification({
      type: notification.type,
      title: notification.title,
      message: notification.message,
      duration: notification.type === 'error' ? 0 : 4500,
      showClose: true
    })

    // 限制通知数量
    if (notifications.value.length > 100) {
      notifications.value.splice(100)
    }

    saveNotificationsState()
  }

  /**
   * 标记通知为已读
   */
  const markNotificationAsRead = (id: string): void => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
      saveNotificationsState()
    }
  }

  /**
   * 标记所有通知为已读
   */
  const markAllNotificationsAsRead = (): void => {
    notifications.value.forEach(n => (n.read = true))
    saveNotificationsState()
  }

  /**
   * 清除通知
   */
  const clearNotification = (id: string): void => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index !== -1) {
      notifications.value.splice(index, 1)
      saveNotificationsState()
    }
  }

  /**
   * 清除所有通知
   */
  const clearAllNotifications = (): void => {
    notifications.value = []
    saveNotificationsState()
  }

  /**
   * 添加系统事件
   */
  const addSystemEvent = (event: Omit<SystemEvent, 'id' | 'timestamp' | 'acknowledged'>): void => {
    const id = `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const newEvent: SystemEvent = {
      id,
      timestamp: new Date(),
      acknowledged: false,
      ...event
    }

    systemEvents.value.unshift(newEvent)

    // 关键事件自动创建通知
    if (event.level === 'critical' || event.level === 'error') {
      addNotification({
        type: event.level === 'critical' ? 'error' : 'warning',
        title: `系统事件: ${event.title}`,
        message: event.message
      })
    }

    // 限制事件数量
    if (systemEvents.value.length > 500) {
      systemEvents.value.splice(500)
    }

    saveEventsState()
  }

  /**
   * 确认系统事件
   */
  const acknowledgeEvent = (id: string): void => {
    const event = systemEvents.value.find(e => e.id === id)
    if (event) {
      event.acknowledged = true
      saveEventsState()
    }
  }

  /**
   * 更新性能数据
   */
  const updatePerformanceData = (data: Partial<AppState['performanceData']>): void => {
    appState.value.performanceData = {
      ...appState.value.performanceData,
      ...data
    }
  }

  /**
   * 显示成功消息
   */
  const showSuccess = (message: string, title?: string): void => {
    if (title) {
      ElNotification.success({ title, message })
    } else {
      ElMessage.success(message)
    }
  }

  /**
   * 显示错误消息
   */
  const showError = (message: string, title?: string): void => {
    if (title) {
      ElNotification.error({ title, message })
    } else {
      ElMessage.error(message)
    }
  }

  /**
   * 显示警告消息
   */
  const showWarning = (message: string, title?: string): void => {
    if (title) {
      ElNotification.warning({ title, message })
    } else {
      ElMessage.warning(message)
    }
  }

  /**
   * 显示信息消息
   */
  const showInfo = (message: string, title?: string): void => {
    if (title) {
      ElNotification.info({ title, message })
    } else {
      ElMessage.info(message)
    }
  }

  /**
   * 保存应用状态到本地存储
   */
  const saveAppState = (): void => {
    try {
      localStorage.setItem('cim_app_state', JSON.stringify(appState.value))
    } catch (error) {
      console.error('[App] Failed to save app state:', error)
    }
  }

  /**
   * 保存标签页状态
   */
  const saveTabsState = (): void => {
    try {
      localStorage.setItem(
        'cim_tabs_state',
        JSON.stringify({
          tabs: tabs.value,
          activeTab: activeTab.value
        })
      )
    } catch (error) {
      console.error('[App] Failed to save tabs state:', error)
    }
  }

  /**
   * 保存通知状态
   */
  const saveNotificationsState = (): void => {
    try {
      // 只保存最近50条通知
      const recentNotifications = notifications.value.slice(0, 50)
      localStorage.setItem('cim_notifications_state', JSON.stringify(recentNotifications))
    } catch (error) {
      console.error('[App] Failed to save notifications state:', error)
    }
  }

  /**
   * 保存事件状态
   */
  const saveEventsState = (): void => {
    try {
      // 只保存最近100条事件
      const recentEvents = systemEvents.value.slice(0, 100)
      localStorage.setItem('cim_events_state', JSON.stringify(recentEvents))
    } catch (error) {
      console.error('[App] Failed to save events state:', error)
    }
  }

  /**
   * 从本地存储恢复状态
   */
  const restoreFromStorage = (): void => {
    try {
      // 恢复应用状态
      const savedAppState = localStorage.getItem('cim_app_state')
      if (savedAppState) {
        const parsedState = JSON.parse(savedAppState)
        appState.value = { ...appState.value, ...parsedState }
      }

      // 恢复标签页状态
      const savedTabsState = localStorage.getItem('cim_tabs_state')
      if (savedTabsState) {
        const parsedTabsState = JSON.parse(savedTabsState)
        tabs.value = parsedTabsState.tabs || tabs.value
        activeTab.value = parsedTabsState.activeTab || activeTab.value
      }

      // 恢复通知状态
      const savedNotifications = localStorage.getItem('cim_notifications_state')
      if (savedNotifications) {
        notifications.value = JSON.parse(savedNotifications).map((n: any) => ({
          ...n,
          timestamp: new Date(n.timestamp)
        }))
      }

      // 恢复事件状态
      const savedEvents = localStorage.getItem('cim_events_state')
      if (savedEvents) {
        systemEvents.value = JSON.parse(savedEvents).map((e: any) => ({
          ...e,
          timestamp: new Date(e.timestamp)
        }))
      }
    } catch (error) {
      console.error('[App] Failed to restore from storage:', error)
    }
  }

  /**
   * 重置应用状态
   */
  const resetAppState = (): void => {
    // 重置基本状态
    appState.value = {
      sidebarCollapsed: false,
      headerFixed: true,
      sidebarFixed: true,
      showBreadcrumb: true,
      showTabs: true,
      showFooter: true,
      currentRoute: '/',
      currentTitle: '首页',
      deviceType: 'desktop',
      screenSize: 'lg',
      isOnline: navigator.onLine,
      performanceData: {
        fps: 60,
        memory: 0,
        loadTime: 0,
        apiLatency: 0
      }
    }

    // 重置标签页
    tabs.value = [
      {
        key: 'home',
        title: '首页',
        path: '/',
        icon: 'House',
        closable: false,
        cached: true
      }
    ]
    activeTab.value = 'home'

    // 清空通知和事件
    notifications.value = []
    systemEvents.value = []
    breadcrumbs.value = []

    // 清理本地存储
    localStorage.removeItem('cim_app_state')
    localStorage.removeItem('cim_tabs_state')
    localStorage.removeItem('cim_notifications_state')
    localStorage.removeItem('cim_events_state')
  }

  // 监听在线状态变化
  const handleOnlineStatusChange = (): void => {
    appState.value.isOnline = navigator.onLine

    if (navigator.onLine) {
      showSuccess('网络连接已恢复')
    } else {
      showWarning('网络连接已断开')
    }
  }

  // 初始化
  if (typeof window !== 'undefined') {
    // 恢复状态
    restoreFromStorage()

    // 更新设备信息
    updateDeviceInfo()

    // 监听窗口大小变化
    window.addEventListener('resize', updateDeviceInfo)

    // 监听在线状态变化
    window.addEventListener('online', handleOnlineStatusChange)
    window.addEventListener('offline', handleOnlineStatusChange)
  }

  return {
    // 状态
    appState,
    loadingState,
    breadcrumbs,
    tabs,
    activeTab,
    notifications,
    systemEvents,
    quickActions,

    // 计算属性
    unreadNotificationCount,
    criticalEventCount,
    isMobile,
    isTablet,
    isDesktop,
    currentTabItem,

    // 方法
    setGlobalLoading,
    setModuleLoading,
    isModuleLoading,
    updateDeviceInfo,
    toggleSidebar,
    setBreadcrumbs,
    addBreadcrumb,
    addTab,
    closeTab,
    closeOtherTabs,
    closeAllTabs,
    switchTab,
    addNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    clearNotification,
    clearAllNotifications,
    addSystemEvent,
    acknowledgeEvent,
    updatePerformanceData,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    resetAppState,
    restoreFromStorage
  }
})
