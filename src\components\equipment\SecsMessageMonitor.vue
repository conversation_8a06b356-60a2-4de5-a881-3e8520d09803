<template>
  <div class="secs-message-monitor">
    <!-- 控制面板 -->
    <div class="control-panel">
      <el-row :gutter="16" align="middle">
        <el-col :span="6">
          <el-select
            v-model="selectedEquipmentId"
            placeholder="选择设备"
            @change="handleEquipmentChange"
          >
            <el-option
v-for="eq in equipmentList" :key="eq.id"
:label="eq.name" :value="eq.id"
/>
          </el-select>
        </el-col>

        <el-col :span="4">
          <el-switch
v-model="autoRefresh" active-text="自动刷新"
@change="toggleAutoRefresh"
/>
        </el-col>

        <el-col :span="6">
          <el-button-group>
            <el-button
              :type="viewMode === 'list' ? 'primary' : 'default'"
              @click="viewMode = 'list'"
            >
              <el-icon><List /></el-icon>
              列表
            </el-button>
            <el-button
              :type="viewMode === 'timeline' ? 'primary' : 'default'"
              @click="viewMode = 'timeline'"
            >
              <el-icon><Clock /></el-icon>
              时间线
            </el-button>
          </el-button-group>
        </el-col>

        <el-col :span="4">
          <el-button type="primary" @click="showSendDialog = true">
            <el-icon><Position /></el-icon>
            发送消息
          </el-button>
        </el-col>

        <el-col :span="4">
          <el-button @click="refresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 统计信息 -->
    <div class="message-stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="总消息数" :value="messageStats.total">
            <template #suffix>
              <el-icon><ChatLineRound /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="已发送" :value="messageStats.sent">
            <template #suffix>
              <el-icon class="sent-icon">
                <Upload />
              </el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="已接收" :value="messageStats.received">
            <template #suffix>
              <el-icon class="received-icon">
                <Download />
              </el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="错误数" :value="messageStats.error">
            <template #suffix>
              <el-icon class="error-icon">
                <Warning />
              </el-icon>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <el-row :gutter="16">
        <el-col :span="4">
          <el-input
            v-model="streamFilter"
            placeholder="Stream"
            type="number"
            size="small"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="functionFilter"
            placeholder="Function"
            type="number"
            size="small"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-select
v-model="directionFilter" placeholder="方向"
size="small" clearable
>
            <el-option label="Host→Equipment" value="H→E" />
            <el-option label="Equipment→Host" value="E→H" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
v-model="statusFilter" placeholder="状态"
size="small" clearable
>
            <el-option label="已发送" value="SENT" />
            <el-option label="已接收" value="RECEIVED" />
            <el-option label="超时" value="TIMEOUT" />
            <el-option label="错误" value="ERROR" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="small"
            @change="handleTimeRangeChange"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 消息列表 -->
    <div
v-if="viewMode === 'list'" class="message-list"
>
      <el-table
:data="paginatedMessages" stripe
:height="400" @row-click="handleRowClick"
>
        <el-table-column prop="timestamp" label="时间" width="160">
          <template #default="{ row }">
            {{ formatTimestamp(row.timestamp) }}
          </template>
        </el-table-column>

        <el-table-column label="S/F" width="80">
          <template #default="{ row }">
            <span class="sf-code">S{{ row.stream }}F{{ row.function }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="direction" label="方向" width="100">
          <template #default="{ row }">
            <el-tag
:type="row.direction === 'H→E' ? 'primary' : 'success'" size="small"
>
              {{ row.direction }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
:type="getStatusType(row.status)" size="small"
>
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" show-overflow-tooltip />

        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="text"
size="small" @click="handleViewDetail(row)"
>
详情
</el-button>
            <el-button
              v-if="row.status === 'TIMEOUT' || row.status === 'ERROR'"
              type="text"
              size="small"
              @click="handleRetry(row)"
            >
              重试
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100]"
          :total="filteredMessages.length"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>

    <!-- 时间线视图 -->
    <div
v-if="viewMode === 'timeline'" class="message-timeline"
>
      <el-timeline>
        <el-timeline-item
          v-for="message in filteredMessages.slice(0, 50)"
          :key="message.id"
          :timestamp="formatTimestamp(message.timestamp)"
          :type="getTimelineType(message.status)"
          :icon="getTimelineIcon(message.direction)"
        >
          <el-card class="timeline-card">
            <div class="message-header">
              <span class="sf-code">S{{ message.stream }}F{{ message.function }}</span>
              <el-tag
:type="message.direction === 'H→E' ? 'primary' : 'success'" size="small"
>
                {{ message.direction }}
              </el-tag>
              <el-tag
:type="getStatusType(message.status)" size="small"
>
                {{ getStatusText(message.status) }}
              </el-tag>
            </div>
            <div class="message-description">
              {{ message.description }}
            </div>
            <div class="message-actions">
              <el-button
type="text" size="small"
@click="handleViewDetail(message)"
>
                查看详情
              </el-button>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 发送消息弹窗 -->
    <el-dialog
v-model="showSendDialog" title="发送SECS消息"
width="50%"
>
      <el-form :model="sendForm" :rules="sendRules" ref="sendFormRef" label-width="100px">
        <el-form-item label="设备" prop="equipmentId">
          <el-select v-model="sendForm.equipmentId" placeholder="选择设备">
            <el-option
v-for="eq in equipmentList" :key="eq.id"
:label="eq.name" :value="eq.id"
/>
          </el-select>
        </el-form-item>

        <el-form-item label="Stream" prop="stream">
          <el-input-number
v-model="sendForm.stream" :min="1"
:max="127" placeholder="1-127"
/>
        </el-form-item>

        <el-form-item label="Function" prop="function">
          <el-input-number
v-model="sendForm.function" :min="1"
:max="255" placeholder="1-255"
/>
        </el-form-item>

        <el-form-item label="数据" prop="data">
          <el-input
            v-model="sendForm.data"
            type="textarea"
            :rows="4"
            placeholder="JSON格式的数据，如: {}"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input v-model="sendForm.description" placeholder="消息描述（可选）" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showSendDialog = false">取消</el-button>
        <el-button type="primary"
:loading="sending" @click="handleSendMessage"
>
发送
</el-button>
      </template>
    </el-dialog>

    <!-- 消息详情弹窗 -->
    <el-dialog
v-model="showDetailDialog" title="消息详情"
width="60%"
>
      <div v-if="selectedMessage" class="message-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="消息ID">
            {{ selectedMessage.id }}
          </el-descriptions-item>
          <el-descriptions-item label="Stream/Function">
            S{{ selectedMessage.stream }}F{{ selectedMessage.function }}
          </el-descriptions-item>
          <el-descriptions-item label="方向">
            <el-tag :type="selectedMessage.direction === 'H→E' ? 'primary' : 'success'">
              {{ selectedMessage.direction }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedMessage.status)">
              {{ getStatusText(selectedMessage.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="时间戳">
            {{ formatTimestamp(selectedMessage.timestamp) }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" span="2">
            {{ selectedMessage.description }}
          </el-descriptions-item>
        </el-descriptions>

        <div
v-if="selectedMessage.data" class="message-data"
>
          <h4>消息数据</h4>
          <el-input
            :model-value="JSON.stringify(selectedMessage.data, null, 2)"
            type="textarea"
            :rows="10"
            readonly
            class="data-viewer"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch, onMounted, onUnmounted } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    List,
    Clock,
    Position,
    Refresh,
    ChatLineRound,
    Upload,
    Download,
    Warning
  } from '@element-plus/icons-vue'
  import type { Equipment, SecsMessage } from '@/types/equipment'

  interface Props {
    messages: SecsMessage[]
    equipmentList: Equipment[]
  }

  interface Emits {
    (
      e: 'send',
      data: {
        equipmentId: string
        stream: number
        function: number
        data?: any
        description?: string
      }
    ): void
    (e: 'retry', message: SecsMessage): void
    (e: 'refresh', equipmentId?: string): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 控制状态
  const selectedEquipmentId = ref('')
  const autoRefresh = ref(false)
  const viewMode = ref<'list' | 'timeline'>('list')
  const refreshTimer = ref<NodeJS.Timeout>()

  // 筛选器
  const streamFilter = ref<number>()
  const functionFilter = ref<number>()
  const directionFilter = ref('')
  const statusFilter = ref('')
  const timeRange = ref<[Date, Date] | null>(null)

  // 分页
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 弹窗状态
  const showSendDialog = ref(false)
  const showDetailDialog = ref(false)
  const selectedMessage = ref<SecsMessage | null>(null)
  const sending = ref(false)

  // 发送表单
  const sendForm = ref({
    equipmentId: '',
    stream: 1,
    function: 1,
    data: '{}',
    description: ''
  })

  const sendFormRef = ref()
  const sendRules = {
    equipmentId: [{ required: true, message: '请选择设备', trigger: 'change' }],
    stream: [{ required: true, message: '请输入Stream', trigger: 'blur' }],
    function: [{ required: true, message: '请输入Function', trigger: 'blur' }]
  }

  // 筛选后的消息
  const filteredMessages = computed(() => {
    let result = props.messages

    if (selectedEquipmentId.value) {
      // 这里需要根据设备ID筛选，实际应该在消息数据中包含设备ID
      // result = result.filter(msg => msg.equipmentId === selectedEquipmentId.value)
    }

    if (streamFilter.value !== undefined) {
      result = result.filter(msg => msg.stream === streamFilter.value)
    }

    if (functionFilter.value !== undefined) {
      result = result.filter(msg => msg.function === functionFilter.value)
    }

    if (directionFilter.value) {
      result = result.filter(msg => msg.direction === directionFilter.value)
    }

    if (statusFilter.value) {
      result = result.filter(msg => msg.status === statusFilter.value)
    }

    if (timeRange.value) {
      const [start, end] = timeRange.value
      result = result.filter(msg => {
        const msgTime = new Date(msg.timestamp)
        return msgTime >= start && msgTime <= end
      })
    }

    return result.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  })

  // 分页后的消息
  const paginatedMessages = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return filteredMessages.value.slice(start, end)
  })

  // 统计数据
  const messageStats = computed(() => ({
    total: filteredMessages.value.length,
    sent: filteredMessages.value.filter(msg => msg.status === 'SENT').length,
    received: filteredMessages.value.filter(msg => msg.status === 'RECEIVED').length,
    error: filteredMessages.value.filter(msg => msg.status === 'ERROR' || msg.status === 'TIMEOUT')
      .length
  }))

  // 工具方法
  const getStatusText = (status: string): string => {
    const textMap: Record<string, string> = {
      SENT: '已发送',
      RECEIVED: '已接收',
      TIMEOUT: '超时',
      ERROR: '错误'
    }
    return textMap[status] || status
  }

  const getStatusType = (status: string): string => {
    const typeMap: Record<string, string> = {
      SENT: 'info',
      RECEIVED: 'success',
      TIMEOUT: 'warning',
      ERROR: 'danger'
    }
    return typeMap[status] || 'info'
  }

  const getTimelineType = (status: string): string => {
    const typeMap: Record<string, string> = {
      SENT: 'primary',
      RECEIVED: 'success',
      TIMEOUT: 'warning',
      ERROR: 'danger'
    }
    return typeMap[status] || 'primary'
  }

  const getTimelineIcon = (direction: string) => {
    return direction === 'H→E' ? Upload : Download
  }

  const formatTimestamp = (timestamp: string): string => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 事件处理
  const handleEquipmentChange = (equipmentId: string) => {
    refresh()
  }

  const toggleAutoRefresh = () => {
    if (autoRefresh.value) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }

  const startAutoRefresh = () => {
    stopAutoRefresh()
    refreshTimer.value = setInterval(() => {
      refresh()
    }, 5000)
  }

  const stopAutoRefresh = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = undefined
    }
  }

  const refresh = () => {
    emit('refresh', selectedEquipmentId.value)
  }

  const handleRowClick = (row: SecsMessage) => {
    handleViewDetail(row)
  }

  const handleViewDetail = (message: SecsMessage) => {
    selectedMessage.value = message
    showDetailDialog.value = true
  }

  const handleRetry = async (message: SecsMessage) => {
    try {
      await ElMessageBox.confirm('确定要重试发送此消息吗？', '确认重试', {
        confirmButtonText: '重试',
        cancelButtonText: '取消',
        type: 'info'
      })
      emit('retry', message)
      ElMessage.success('消息重试发送中...')
    } catch (error) {
      // 用户取消
    }
  }

  const handleSendMessage = async () => {
    try {
      await sendFormRef.value?.validate()

      // 验证JSON格式
      let data
      try {
        data = JSON.parse(sendForm.value.data)
      } catch (error) {
        ElMessage.error('数据格式错误，请输入有效的JSON')
        return
      }

      sending.value = true

      emit('send', {
        equipmentId: sendForm.value.equipmentId,
        stream: sendForm.value.stream,
        function: sendForm.value.function,
        data,
        description: sendForm.value.description
      })

      showSendDialog.value = false
      ElMessage.success('消息发送成功')

      // 重置表单
      sendForm.value = {
        equipmentId: '',
        stream: 1,
        function: 1,
        data: '{}',
        description: ''
      }
    } catch (error) {
      ElMessage.error('消息发送失败')
    } finally {
      sending.value = false
    }
  }

  const handleTimeRangeChange = (range: [Date, Date] | null) => {
    timeRange.value = range
    currentPage.value = 1
  }

  // 生命周期
  onMounted(() => {
    if (props.equipmentList.length > 0) {
      selectedEquipmentId.value = props.equipmentList[0].id
    }
    refresh()
  })

  onUnmounted(() => {
    stopAutoRefresh()
  })

  // 监听筛选条件变化
  watch([streamFilter, functionFilter, directionFilter, statusFilter], () => {
    currentPage.value = 1
  })
</script>

<style lang="scss" scoped>
  .secs-message-monitor {
    .control-panel {
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      background-color: var(--color-bg-soft);
      border-radius: var(--radius-base);
    }

    .message-stats {
      margin-bottom: var(--spacing-4);

      .sent-icon {
        color: var(--color-primary);
      }

      .received-icon {
        color: var(--color-success);
      }

      .error-icon {
        color: var(--color-danger);
      }
    }

    .filters {
      padding: var(--spacing-3);
      margin-bottom: var(--spacing-4);
      background-color: var(--color-bg-soft);
      border-radius: var(--radius-base);
    }

    .message-list {
      .sf-code {
        font-family: var(--font-mono);
        font-weight: 600;
        color: var(--color-primary);
      }

      .pagination {
        display: flex;
        justify-content: center;
        margin-top: var(--spacing-4);
      }
    }

    .message-timeline {
      max-height: 600px;
      overflow-y: auto;

      .timeline-card {
        margin-bottom: var(--spacing-2);

        .message-header {
          display: flex;
          gap: var(--spacing-2);
          align-items: center;
          margin-bottom: var(--spacing-2);

          .sf-code {
            font-family: var(--font-mono);
            font-weight: 600;
            color: var(--color-primary);
          }
        }

        .message-description {
          margin-bottom: var(--spacing-2);
          font-size: 0.875rem;
          color: var(--color-text-secondary);
        }

        .message-actions {
          display: flex;
          justify-content: flex-end;
        }
      }
    }

    .message-detail {
      .message-data {
        margin-top: var(--spacing-4);

        h4 {
          margin-bottom: var(--spacing-3);
          font-size: 1rem;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .data-viewer {
          font-family: var(--font-mono);

          :deep(.el-textarea__inner) {
            font-family: var(--font-mono);
            font-size: 0.875rem;
            background-color: var(--color-bg-soft);
          }
        }
      }
    }
  }
</style>
