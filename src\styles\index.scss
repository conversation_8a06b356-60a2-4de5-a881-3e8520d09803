// IC封测CIM系统 - 样式统一入口
// 严格按照极简主义设计系统构建

// 1. 基础样式导入
@use './base/reset';
@use './base/typography';
@use './base/layout.scss' as base-layout;  
@use './base/utilities';

// 2. 主题系统导入
@use './themes/variables';
@use './themes/light';
@use './themes/dark';

// 3. 组件样式导入
@use './components/button';
@use './components/form';
@use './components/table';
@use './components/card';
@use './components/modal';
@use './components/layout.scss' as component-layout;

// 4. 页面样式导入
@use './pages/dashboard';
@use './pages/orders';
@use './pages/production';

// 5. IC封测专业样式
@use './professional/wafer-map';
@use './professional/equipment';
@use './professional/quality';

// 6. 监控中心大屏样式
@use './monitoring/index';