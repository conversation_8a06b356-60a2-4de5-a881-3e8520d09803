<template>
  <el-dialog
    :model-value="visible"
    :title="`客户详情 - ${customer?.name}`"
    width="1000px"
    :close-on-click-modal="false"
    class="customer-detail-dialog"
    @update:model-value="$emit('update:visible', $event)"
  >
    <div v-if="customer" class="customer-detail">
      <!-- 客户基本信息卡片 -->
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card shadow="never" class="info-card">
            <template #header>
              <div class="card-header">
                <div class="customer-header">
                  <el-avatar
                    :size="60"
                    :src="customer.logo"
                    :style="{ backgroundColor: getCustomerColor(customer.type) }"
                  >
                    {{ customer.shortName?.charAt(0) || customer.name.charAt(0) }}
                  </el-avatar>
                  <div class="customer-info">
                    <h3 class="customer-name">
                      {{ customer.name }}
                    </h3>
                    <p class="customer-english">
                      {{ customer.englishName }}
                    </p>
                    <div class="customer-tags">
                      <el-tag
size="small" :type="getCustomerTypeTagType(customer.type)"
>
                        {{ getCustomerTypeLabel(customer.type) }}
                      </el-tag>
                      <el-tag
                        size="small"
                        :color="getCustomerLevelColor(customer.level)"
                        style="margin-left: 8px; color: white"
                      >
                        {{ getCustomerLevelLabel(customer.level) }}
                      </el-tag>
                      <el-tag
                        size="small"
                        :type="getStatusTagType(customer.status)"
                        style="margin-left: 8px"
                      >
                        {{ getStatusLabel(customer.status) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
                <div class="header-actions">
                  <el-button type="primary" @click="$emit('edit', customer)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                </div>
              </div>
            </template>

            <el-descriptions :column="2" border>
              <el-descriptions-item label="客户编码">
                {{ customer.code }}
              </el-descriptions-item>
              <el-descriptions-item label="简称">
                {{ customer.shortName || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="客户规模">
                <el-tag size="small" type="info">
                  {{ getScaleLabel(customer.scale) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="信用等级">
                <el-tag
                  size="small"
                  :color="getCreditLevelColor(customer.creditLevel)"
                  style="color: white"
                >
                  {{ customer.creditLevel }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="官方网站">
                <el-link
                  v-if="customer.website"
                  :href="customer.website"
                  target="_blank"
                  type="primary"
                >
                  {{ customer.website }}
                </el-link>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="成立年份">
                {{ customer.foundedYear || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDateTime(customer.createdAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="最后更新">
                {{ formatDateTime(customer.updatedAt) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>

        <!-- 业务指标统计 -->
        <el-col :span="8">
          <el-card shadow="never" class="metrics-card">
            <template #header>
              <span class="card-title">
                <el-icon><TrendCharts /></el-icon>
                业务指标
              </span>
            </template>

            <div v-if="customer.metrics" class="metrics-grid">
              <div class="metric-item">
                <div class="metric-value primary">
                  {{ customer.metrics.totalOrders }}
                </div>
                <div class="metric-label">总订单数</div>
              </div>
              <div class="metric-item">
                <div class="metric-value success">
                  {{ formatRevenue(customer.metrics.totalRevenue) }}
                </div>
                <div class="metric-label">总营收</div>
              </div>
              <div class="metric-item">
                <div class="metric-value warning">
                  {{ formatCurrency(customer.metrics.averageOrderValue) }}
                </div>
                <div class="metric-label">平均订单价值</div>
              </div>
              <div class="metric-item">
                <div class="metric-value info">
                  {{ customer.metrics.onTimeDeliveryRate.toFixed(1) }}%
                </div>
                <div class="metric-label">准时交付率</div>
              </div>
              <div class="metric-item">
                <div class="metric-value success">
                  {{ customer.metrics.qualityScore.toFixed(1) }}
                </div>
                <div class="metric-label">质量评分</div>
              </div>
              <div class="metric-item">
                <div class="metric-value primary">
                  {{ customer.metrics.satisfactionScore.toFixed(1) }}
                </div>
                <div class="metric-label">满意度评分</div>
              </div>
            </div>
            <div v-else class="no-metrics">
              <el-empty description="暂无业务数据" :image-size="80" />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细信息标签页 -->
      <el-card shadow="never" class="detail-tabs-card">
        <el-tabs v-model="activeTab" class="detail-tabs">
          <!-- 技术信息 -->
          <el-tab-pane label="技术信息" name="technical">
            <div class="tab-content">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-section">
                    <h4 class="section-title">
                      <el-icon><Cpu /></el-icon>
                      应用领域
                    </h4>
                    <div class="field-tags">
                      <el-tag
                        v-for="field in customer.applicationFields"
                        :key="field"
                        size="small"
                        type="success"
                        style="margin: 2px 4px 2px 0"
                      >
                        {{ getApplicationFieldLabel(field) }}
                      </el-tag>
                    </div>
                  </div>
                </el-col>

                <el-col :span="12">
                  <div class="info-section">
                    <h4 class="section-title">
                      <el-icon><Setting /></el-icon>
                      工艺节点
                    </h4>
                    <div class="field-tags">
                      <el-tag
                        v-for="node in customer.processNodes"
                        :key="node"
                        size="small"
                        type="warning"
                        style="margin: 2px 4px 2px 0"
                      >
                        {{ node }}
                      </el-tag>
                    </div>
                  </div>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-section">
                    <h4 class="section-title">
                      <el-icon><Box /></el-icon>
                      封装偏好
                    </h4>
                    <div class="field-tags">
                      <el-tag
                        v-for="pkg in customer.packagePreferences"
                        :key="pkg"
                        size="small"
                        type="info"
                        style="margin: 2px 4px 2px 0"
                      >
                        {{ pkg }}
                      </el-tag>
                    </div>
                  </div>
                </el-col>

                <el-col :span="12">
                  <div class="info-section">
                    <h4 class="section-title">
                      <el-icon><Medal /></el-icon>
                      质量标准
                    </h4>
                    <div class="field-tags">
                      <el-tag
                        v-for="standard in customer.qualityStandard"
                        :key="standard"
                        size="small"
                        type="danger"
                        style="margin: 2px 4px 2px 0"
                      >
                        {{ standard }}
                      </el-tag>
                    </div>
                  </div>
                </el-col>
              </el-row>

              <div class="info-section">
                <h4 class="section-title">
                  <el-icon><DocumentChecked /></el-icon>
                  合规要求
                </h4>
                <div class="field-tags">
                  <el-tag
                    v-for="requirement in customer.complianceRequirements"
                    :key="requirement"
                    size="small"
                    style="margin: 2px 4px 2px 0"
                  >
                    {{ requirement }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 联系信息 -->
          <el-tab-pane label="联系信息" name="contact">
            <div class="tab-content">
              <div class="contact-card">
                <div class="contact-header">
                  <el-avatar :size="50">
                    {{ getPrimaryContactName(customer).charAt(0) }}
                  </el-avatar>
                  <div class="contact-info">
                    <h4>{{ getPrimaryContactName(customer) }}</h4>
                    <p>{{ getPrimaryContact(customer)?.englishName || '-' }}</p>
                    <div class="contact-tags">
                      <el-tag
size="small" type="primary"
>
                        {{ getPrimaryContactPosition(customer) }}
                      </el-tag>
                      <el-tag
size="small" type="info"
>
                        {{ getPrimaryContact(customer)?.department || '-' }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <el-descriptions :column="2" border>
                  <el-descriptions-item label="邮箱">
                    <el-link
:href="`mailto:${getPrimaryContactEmail(customer)}`" type="primary"
>
                      {{ getPrimaryContactEmail(customer) || '-' }}
                    </el-link>
                  </el-descriptions-item>
                  <el-descriptions-item label="电话">
                    <el-link
:href="`tel:${getPrimaryContactPhone(customer)}`" type="primary"
>
                      {{ getPrimaryContactPhone(customer) || '-' }}
                    </el-link>
                  </el-descriptions-item>
                  <el-descriptions-item label="手机">
                    {{ getPrimaryContact(customer)?.mobile || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="微信">
                    {{ getPrimaryContact(customer)?.wechat || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="角色">
                    <el-tag
size="small" type="success"
>
                      {{ getRoleLabel(getPrimaryContact(customer)?.role) }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="重要程度">
                    <el-rate
                      :model-value="getPrimaryContact(customer)?.importance || 0"
                      disabled
                      :max="5"
                      show-score
                      text-color="#ff9900"
                    />
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-tab-pane>

          <!-- 地址信息 -->
          <el-tab-pane label="地址信息" name="address">
            <div class="tab-content">
              <el-descriptions :column="2" border v-if="customer.address">
                <el-descriptions-item label="国家">
                  {{ customer.address.country }}
                </el-descriptions-item>
                <el-descriptions-item label="省份">
                  {{ customer.address.province }}
                </el-descriptions-item>
                <el-descriptions-item label="城市">
                  {{ customer.address.city }}
                </el-descriptions-item>
                <el-descriptions-item label="区县">
                  {{ customer.address.district || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="邮政编码">
                  {{ customer.address.postalCode || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="详细地址" :span="2">
                  {{ customer.address.street }}
                </el-descriptions-item>
              </el-descriptions>
              <el-empty v-else description="暂无地址信息" />
            </div>
          </el-tab-pane>

          <!-- 财务信息 -->
          <el-tab-pane label="财务信息" name="financial">
            <div class="tab-content">
              <el-descriptions :column="2" border v-if="customer.financialInfo">
                <el-descriptions-item label="信用额度">
                  <span class="financial-value">
                    {{ formatCurrency(customer.financialInfo.creditLimit) }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="付款条件">
                  <el-tag size="small" type="warning">
                    {{ customer.financialInfo.paymentTerms }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="结算货币">
                  <el-tag size="small" type="info">
                    {{ customer.financialInfo.currency }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="税号">
                  {{ customer.financialInfo.taxId || '-' }}
                </el-descriptions-item>
                <el-descriptions-item
                  v-if="customer.financialInfo.bankInfo"
                  label="银行信息"
                  :span="2"
                >
                  <div class="bank-info">
                    <p>
                      <strong>银行名称:</strong>
                      {{ customer.financialInfo.bankInfo.bankName }}
                    </p>
                    <p>
                      <strong>账号:</strong>
                      {{ customer.financialInfo.bankInfo.accountNumber }}
                    </p>
                    <p v-if="customer.financialInfo.bankInfo.swiftCode">
                      <strong>SWIFT:</strong>
                      {{ customer.financialInfo.bankInfo.swiftCode }}
                    </p>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
              <el-empty v-else description="暂无财务信息" />
            </div>
          </el-tab-pane>

          <!-- 业务信息 -->
          <el-tab-pane label="业务信息" name="business">
            <div class="tab-content">
              <el-descriptions :column="2" border v-if="customer.businessInfo">
                <el-descriptions-item label="年营收">
                  <span class="financial-value">
                    {{ formatRevenue(customer.businessInfo.annualRevenue || 0) }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="员工数量">
                  {{ (customer.businessInfo.employeeCount || 0).toLocaleString() }} 人
                </el-descriptions-item>
                <el-descriptions-item label="市场地位" :span="2">
                  {{ customer.businessInfo.marketPosition || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="主要产品" :span="2">
                  <div class="product-tags">
                    <el-tag
                      v-for="product in customer.businessInfo.mainProducts || []"
                      :key="product"
                      size="small"
                      type="success"
                      style="margin: 2px 4px 2px 0"
                    >
                      {{ product }}
                    </el-tag>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="主要竞争对手" :span="2">
                  <div class="competitor-tags">
                    <el-tag
                      v-for="competitor in customer.businessInfo.competitorInfo || []"
                      :key="competitor"
                      size="small"
                      type="warning"
                      style="margin: 2px 4px 2px 0"
                    >
                      {{ competitor }}
                    </el-tag>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
              <el-empty v-else description="暂无业务信息" />
            </div>
          </el-tab-pane>

          <!-- 质量认证 -->
          <el-tab-pane label="质量认证" name="certifications">
            <div class="tab-content">
              <div v-if="customer.qualityCertifications?.length" class="certification-list">
                <div
                  v-for="cert in customer.qualityCertifications"
                  :key="cert"
                  class="certification-item"
                >
                  <el-icon class="cert-icon">
                    <Medal />
                  </el-icon>
                  <span class="cert-name">{{ cert }}</span>
                  <el-tag
size="small" type="success">已认证</el-tag>
                </div>
              </div>
              <el-empty v-else description="暂无质量认证信息" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
        <el-button type="primary" @click="customer && $emit('edit', customer)">
          <el-icon><Edit /></el-icon>
          编辑客户
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import type {
    Customer,
    CustomerType,
    CustomerLevel,
    CustomerStatus,
    ContactRole
  } from '@/types/customer'
  import {
    CUSTOMER_TYPE_OPTIONS,
    CUSTOMER_LEVEL_OPTIONS,
    CUSTOMER_STATUS_OPTIONS,
    CUSTOMER_SCALE_OPTIONS,
    CREDIT_LEVEL_OPTIONS,
    APPLICATION_FIELD_OPTIONS
  } from '@/utils/mockData/customerMaster'

  interface Props {
    visible: boolean
    customer?: Customer | null
  }

  interface Emits {
    (e: 'update:visible', visible: boolean): void
    (e: 'edit', customer: Customer): void
  }

  defineProps<Props>()
  defineEmits<Emits>()

  const activeTab = ref('technical')

  // 获取客户颜色
  const getCustomerColor = (type: CustomerType): string => {
    const colors = {
      fabless: '#409EFF',
      idm: '#67C23A',
      foundry: '#E6A23C',
      distributor: '#909399',
      broker: '#F56C6C',
      ems: '#9580FF'
    }
    return colors[type]
  }

  // 获取客户类型标签类型
  const getCustomerTypeTagType = (type: CustomerType): string => {
    const types = {
      fabless: 'primary',
      idm: 'success',
      foundry: 'warning',
      distributor: 'info',
      broker: 'danger',
      ems: ''
    }
    return types[type] || ''
  }

  // 获取客户类型标签文本
  const getCustomerTypeLabel = (type: CustomerType): string => {
    const option = CUSTOMER_TYPE_OPTIONS.find(opt => opt.value === type)
    return option?.label || type
  }

  // 获取客户等级颜色
  const getCustomerLevelColor = (level: CustomerLevel): string => {
    const option = CUSTOMER_LEVEL_OPTIONS.find(opt => opt.value === level)
    return option?.color || '#909399'
  }

  // 获取客户等级标签文本
  const getCustomerLevelLabel = (level: CustomerLevel): string => {
    const option = CUSTOMER_LEVEL_OPTIONS.find(opt => opt.value === level)
    return option?.label || level
  }

  // 获取状态标签类型
  const getStatusTagType = (status: CustomerStatus): string => {
    const types = {
      active: 'success',
      inactive: 'info',
      pending: 'warning',
      suspended: 'danger',
      archived: 'info'
    }
    return types[status] || ''
  }

  // 获取状态标签文本
  const getStatusLabel = (status: CustomerStatus): string => {
    const option = CUSTOMER_STATUS_OPTIONS.find(opt => opt.value === status)
    return option?.label || status
  }

  // 获取规模标签文本
  const getScaleLabel = (scale: string): string => {
    const option = CUSTOMER_SCALE_OPTIONS.find(opt => opt.value === scale)
    return option?.label || scale
  }

  // 获取信用等级颜色
  const getCreditLevelColor = (level: string): string => {
    const option = CREDIT_LEVEL_OPTIONS.find(opt => opt.value === level)
    return option?.color || '#909399'
  }

  // 获取应用领域标签文本
  const getApplicationFieldLabel = (field: string): string => {
    const option = APPLICATION_FIELD_OPTIONS.find(opt => opt.value === field)
    return option?.label || field
  }

  // 获取角色标签文本
  const getRoleLabel = (role?: ContactRole): string => {
    if (!role) return '-'
    const roleLabels = {
      FAE: '现场应用工程师',
      DesignManager: '设计经理',
      TestEngineer: '测试工程师',
      QualityEngineer: '质量工程师',
      PurchaseManager: '采购经理',
      SupplyChainDirector: '供应链总监',
      BusinessManager: '商务经理',
      LegalManager: '法务经理',
      CTO: '首席技术官',
      VP: '副总裁',
      ProjectDirector: '项目总监',
      TechnicalDirector: '技术总监',
      PackageEngineer: '封装工程师',
      ReliabilityEngineer: '可靠性工程师',
      PMManager: '项目经理',
      CustomerService: '客服代表'
    }
    return roleLabels[role] || role
  }

  // 格式化日期时间
  const formatDateTime = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 格式化营收
  const formatRevenue = (revenue: number): string => {
    if (revenue >= 100000000) {
      return (revenue / 100000000).toFixed(1) + '亿'
    } else if (revenue >= 10000) {
      return (revenue / 10000).toFixed(1) + '万'
    } else {
      return revenue.toString()
    }
  }

  // 格式化货币
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0
    }).format(amount)
  }

  // 获取主要联系人信息的辅助函数
  const getPrimaryContact = (customer: Customer) => {
    return (
      customer.contact ||
      customer.contacts?.find(contact => contact.isPrimary) ||
      customer.contacts?.[0]
    )
  }

  const getPrimaryContactName = (customer: Customer): string => {
    return getPrimaryContact(customer)?.name || '未设置联系人'
  }

  const getPrimaryContactPosition = (customer: Customer): string => {
    return getPrimaryContact(customer)?.position || ''
  }

  const getPrimaryContactPhone = (customer: Customer): string => {
    const contact = getPrimaryContact(customer)
    return contact?.phone || contact?.mobile || ''
  }

  const getPrimaryContactEmail = (customer: Customer): string => {
    return getPrimaryContact(customer)?.email || ''
  }
</script>

<style lang="scss" scoped>
  .customer-detail-dialog {
    .customer-detail {
      .info-card {
        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .customer-header {
            display: flex;
            gap: 16px;
            align-items: center;

            .customer-info {
              .customer-name {
                margin: 0 0 4px;
                font-size: 20px;
                font-weight: 600;
                color: var(--color-text-primary);
              }

              .customer-english {
                margin: 0 0 8px;
                font-size: 14px;
                color: var(--color-text-regular);
              }

              .customer-tags {
                display: flex;
                align-items: center;
              }
            }
          }
        }
      }

      .metrics-card {
        .card-title {
          display: flex;
          gap: 8px;
          align-items: center;
          font-weight: 500;
        }

        .metrics-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;

          .metric-item {
            padding: 12px;
            text-align: center;
            background: var(--color-bg-light);
            border-radius: 6px;

            .metric-value {
              margin-bottom: 4px;
              font-size: 18px;
              font-weight: 600;
              line-height: 1;

              &.primary {
                color: var(--color-primary);
              }

              &.success {
                color: var(--color-success);
              }

              &.warning {
                color: var(--color-warning);
              }

              &.info {
                color: var(--color-info);
              }
            }

            .metric-label {
              font-size: 12px;
              color: var(--color-text-secondary);
            }
          }
        }

        .no-metrics {
          padding: 20px 0;
        }
      }

      .detail-tabs-card {
        margin-top: 20px;

        .detail-tabs {
          .tab-content {
            padding: 20px 0;

            .info-section {
              margin-bottom: 24px;

              .section-title {
                display: flex;
                gap: 8px;
                align-items: center;
                margin: 0 0 12px;
                font-size: 14px;
                font-weight: 500;
                color: var(--color-text-primary);
              }

              .field-tags {
                display: flex;
                flex-wrap: wrap;
              }
            }

            .contact-card {
              .contact-header {
                display: flex;
                gap: 16px;
                align-items: center;
                padding: 16px;
                margin-bottom: 20px;
                background: var(--color-bg-light);
                border-radius: 8px;

                .contact-info {
                  h4 {
                    margin: 0 0 4px;
                    color: var(--color-text-primary);
                  }

                  p {
                    margin: 0 0 8px;
                    font-size: 14px;
                    color: var(--color-text-regular);
                  }

                  .contact-tags {
                    display: flex;
                    gap: 8px;
                  }
                }
              }
            }

            .financial-value {
              font-weight: 600;
              color: var(--color-primary);
            }

            .bank-info {
              p {
                margin: 4px 0;
                font-size: 14px;
              }
            }

            .product-tags,
            .competitor-tags {
              display: flex;
              flex-wrap: wrap;
            }

            .certification-list {
              .certification-item {
                display: flex;
                gap: 12px;
                align-items: center;
                padding: 12px;
                margin-bottom: 8px;
                background: var(--color-bg-light);
                border-radius: 6px;

                .cert-icon {
                  color: var(--color-warning);
                }

                .cert-name {
                  flex: 1;
                  font-weight: 500;
                  color: var(--color-text-primary);
                }
              }
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }

  // 响应式调整
  @media (width <= 1200px) {
    .customer-detail-dialog {
      .customer-detail {
        .metrics-card {
          .metrics-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }

  @media (width <= 768px) {
    .customer-detail-dialog {
      width: 95% !important;

      .customer-detail {
        .info-card {
          .card-header {
            .customer-header {
              flex-direction: column;
              gap: 12px;
              text-align: center;
            }
          }
        }
      }
    }
  }
</style>
