// IC封测CIM系统 - 极简主题变量系统
// 基于设计规范的完整CSS Variables实现

:root {
  // ===== 基础系统变量 =====
  
  // 间距系统 - 基于4px网格
  --spacing-0: 0;
  --spacing-1: 4px;    // 0.25rem
  --spacing-2: 8px;    // 0.5rem  
  --spacing-3: 12px;   // 0.75rem
  --spacing-4: 16px;   // 1rem (基础单位)
  --spacing-5: 20px;   // 1.25rem
  --spacing-6: 24px;   // 1.5rem
  --spacing-8: 32px;   // 2rem
  --spacing-10: 40px;  // 2.5rem
  --spacing-12: 48px;  // 3rem
  --spacing-16: 64px;  // 4rem
  --spacing-20: 80px;  // 5rem
  --spacing-24: 96px;  // 6rem

  // 字体系统
  --font-family-base: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, 'Helvetica Neue', arial, 'Noto Sans', sans-serif;
  --font-family-mono: 'SF Mono', monaco, inconsolata, 'Roboto Mono', 'Source Code Pro', menlo, consolas, 'Courier New', monospace;
  --font-size-xs: 12px;
  --font-size-sm: 14px;   // 标准文字 (默认)
  --font-size-base: 16px; // 正文内容
  --font-size-lg: 18px;   // 小标题
  --font-size-xl: 20px;   // 标题
  --font-size-2xl: 24px;  // 大标题
  --font-size-3xl: 32px;  // 页面标题
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-loose: 1.75;

  // 圆角系统
  --radius-none: 0;
  --radius-sm: 4px;
  --radius-base: 6px;   // 标准元素 (默认)
  --radius-md: 8px;     // 卡片等
  --radius-lg: 12px;    // 大容器
  --radius-full: 9999px;

  // 过渡动效
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;

  // 层级系统
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  // 组件尺寸
  --header-height: 64px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  --footer-height: 48px;

  // 组件专用间距
  --button-padding-x: 16px;
  --button-padding-y: 8px;
  --button-gap: 8px;
  --form-item-margin: 16px;
  --form-label-margin: 4px;
  --card-padding: 20px;
  --card-margin: 16px;
  --table-cell-padding: 12px;
  --table-header-height: 48px;
  --table-row-height: 40px;
}

// ===== IC封测专业色彩 (两个主题通用) =====
:root {
  --color-wafer: #e0f2fe;          // 晶圆色
  --color-wafer-dark: #1e3a8a;     // 深色模式晶圆色
  --color-die-pass: #dcfce7;       // 良品die
  --color-die-pass-dark: #166534;  // 深色模式良品
  --color-die-fail: #fee2e2;       // 不良die  
  --color-die-fail-dark: #991b1b;  // 深色模式不良
  --color-cp-test: #f0f9ff;        // CP测试
  --color-assembly: #f3e8ff;       // Assembly
  --color-ft-test: #ecfdf5;        // FT测试
  
  // 工艺阶段色彩
  --color-stage-cp: #3b82f6;       // CP阶段
  --color-stage-assembly: #8b5cf6; // 封装阶段
  --color-stage-ft: #10b981;       // FT阶段
  --color-stage-delivery: #f59e0b; // 交付阶段
}

// ===== 响应式断点 =====
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}