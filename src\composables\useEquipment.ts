/**
 * 设备管理组合式函数
 * Equipment Management Composables
 */

import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useEquipmentStore } from '@/stores/equipment'
// Element Plus组件通过unplugin-auto-import自动导入
import type {
  Equipment,
  EquipmentStatusDetail,
  EquipmentAlarm,
  EquipmentEvent,
  Recipe,
  EquipmentConstant,
  MaintenanceTask,
  SecsMessage,
  EquipmentQueryParams,
  AlarmQueryParams,
  EventQueryParams,
  MaintenanceQueryParams
} from '@/types/equipment'
import { EquipmentStatus, EquipmentType } from '@/types/equipment'

// WebSocket连接管理
let ws: WebSocket | null = null
const wsReconnectTimeout = ref<NodeJS.Timeout>()
const wsReconnectDelay = 5000 // 5秒重连延迟

/**
 * 设备列表管理
 */
export function useEquipmentList() {
  const equipmentStore = useEquipmentStore()

  const searchKeyword = ref('')
  const statusFilter = ref<EquipmentStatus | ''>('')
  const typeFilter = ref<EquipmentType | ''>('')
  const locationFilter = ref('')

  const queryParams = computed<EquipmentQueryParams>(() => ({
    keyword: searchKeyword.value || undefined,
    status: statusFilter.value || undefined,
    type: typeFilter.value || undefined,
    location: locationFilter.value || undefined,
    page: equipmentStore.pagination.current,
    limit: equipmentStore.pagination.pageSize
  }))

  const filteredEquipment = computed(() => {
    let result = equipmentStore.equipment

    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      result = result.filter(
        eq =>
          eq.name.toLowerCase().includes(keyword) ||
          eq.code.toLowerCase().includes(keyword) ||
          eq.model.toLowerCase().includes(keyword)
      )
    }

    if (statusFilter.value) {
      result = result.filter(eq => eq.status === statusFilter.value)
    }

    if (typeFilter.value) {
      result = result.filter(eq => eq.type === typeFilter.value)
    }

    if (locationFilter.value) {
      result = result.filter(eq => eq.location.includes(locationFilter.value))
    }

    return result
  })

  const refresh = () => {
    equipmentStore.fetchEquipmentList(queryParams.value)
  }

  const resetFilters = () => {
    searchKeyword.value = ''
    statusFilter.value = ''
    typeFilter.value = ''
    locationFilter.value = ''
  }

  // 监听查询参数变化，自动刷新
  watch(
    queryParams,
    newParams => {
      equipmentStore.fetchEquipmentList(newParams)
    },
    { deep: true }
  )

  onMounted(() => {
    if (equipmentStore.equipment.length === 0) {
      refresh()
    }
  })

  return {
    equipment: equipmentStore.equipment,
    filteredEquipment,
    loading: equipmentStore.loading,
    error: equipmentStore.error,
    pagination: equipmentStore.pagination,
    searchKeyword,
    statusFilter,
    typeFilter,
    locationFilter,
    equipmentCount: equipmentStore.equipmentCount,
    refresh,
    resetFilters
  }
}

/**
 * 设备状态监控
 */
export function useEquipmentStatus() {
  const equipmentStore = useEquipmentStore()
  const autoRefresh = ref(true)
  const refreshInterval = ref(5000) // 5秒刷新间隔
  const refreshTimer = ref<NodeJS.Timeout>()

  const startAutoRefresh = () => {
    if (autoRefresh.value && !refreshTimer.value) {
      refreshTimer.value = setInterval(() => {
        equipmentStore.fetchEquipmentStatus()
        equipmentStore.fetchStatistics()
      }, refreshInterval.value)
    }
  }

  const stopAutoRefresh = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = undefined
    }
  }

  const toggleAutoRefresh = () => {
    autoRefresh.value = !autoRefresh.value
    if (autoRefresh.value) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }

  const refreshStatus = () => {
    equipmentStore.fetchEquipmentStatus()
    equipmentStore.fetchStatistics()
  }

  const getStatusColor = (status: EquipmentStatus): string => {
    const colorMap: Record<EquipmentStatus, string> = {
      [EquipmentStatus.RUN]: '#67C23A',
      [EquipmentStatus.IDLE]: '#E6A23C',
      [EquipmentStatus.DOWN]: '#F56C6C',
      [EquipmentStatus.PM]: '#909399',
      [EquipmentStatus.SETUP]: '#409EFF',
      [EquipmentStatus.ALARM]: '#F56C6C'
    }
    return colorMap[status] || '#909399'
  }

  const getStatusText = (status: EquipmentStatus): string => {
    const textMap: Record<EquipmentStatus, string> = {
      [EquipmentStatus.RUN]: '运行',
      [EquipmentStatus.IDLE]: '空闲',
      [EquipmentStatus.DOWN]: '故障',
      [EquipmentStatus.PM]: '保养',
      [EquipmentStatus.SETUP]: '调机',
      [EquipmentStatus.ALARM]: '告警'
    }
    return textMap[status] || '未知'
  }

  onMounted(() => {
    refreshStatus()
    if (autoRefresh.value) {
      startAutoRefresh()
    }
  })

  onUnmounted(() => {
    stopAutoRefresh()
  })

  watch(autoRefresh, newValue => {
    if (newValue) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  })

  watch(refreshInterval, () => {
    if (autoRefresh.value) {
      stopAutoRefresh()
      startAutoRefresh()
    }
  })

  return {
    equipmentStatus: equipmentStore.equipmentStatus,
    statistics: equipmentStore.statistics,
    autoRefresh,
    refreshInterval,
    refreshStatus,
    toggleAutoRefresh,
    getStatusColor,
    getStatusText
  }
}

/**
 * 设备告警管理
 */
export function useEquipmentAlarms() {
  const equipmentStore = useEquipmentStore()

  const severityFilter = ref('')
  const equipmentFilter = ref('')
  const categoryFilter = ref('')
  const activeOnly = ref(true)

  const queryParams = computed<AlarmQueryParams>(() => ({
    severity: severityFilter.value || undefined,
    equipmentId: equipmentFilter.value || undefined,
    category: categoryFilter.value || undefined,
    isActive: activeOnly.value
  }))

  const filteredAlarms = computed(() => {
    let result = equipmentStore.alarms

    if (severityFilter.value) {
      result = result.filter(alarm => alarm.severity === severityFilter.value)
    }

    if (equipmentFilter.value) {
      result = result.filter(alarm => alarm.equipmentId === equipmentFilter.value)
    }

    if (categoryFilter.value) {
      result = result.filter(alarm => alarm.category === categoryFilter.value)
    }

    if (activeOnly.value) {
      result = result.filter(alarm => alarm.isActive)
    }

    return result
  })

  const getSeverityColor = (severity: string): string => {
    const colorMap: Record<string, string> = {
      CRITICAL: '#F56C6C',
      MAJOR: '#E6A23C',
      MINOR: '#409EFF',
      WARNING: '#909399'
    }
    return colorMap[severity] || '#909399'
  }

  const getSeverityText = (severity: string): string => {
    const textMap: Record<string, string> = {
      CRITICAL: '严重',
      MAJOR: '重要',
      MINOR: '次要',
      WARNING: '警告'
    }
    return textMap[severity] || severity
  }

  const handleAcknowledgeAlarm = async (alarmId: string) => {
    try {
      await equipmentStore.acknowledgeAlarm(alarmId, 'current_user') // TODO: 获取当前用户
      ElMessage.success('告警确认成功')
    } catch (error) {
      ElMessage.error('告警确认失败')
    }
  }

  const handleClearAlarm = async (alarmId: string) => {
    try {
      await ElMessageBox.confirm('确定要清除此告警吗？', '确认清除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await equipmentStore.clearAlarm(alarmId, 'current_user') // TODO: 获取当前用户
      ElMessage.success('告警清除成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('告警清除失败')
      }
    }
  }

  const refresh = () => {
    equipmentStore.fetchAlarms(queryParams.value)
  }

  onMounted(() => {
    refresh()
  })

  watch(
    queryParams,
    () => {
      refresh()
    },
    { deep: true }
  )

  return {
    alarms: filteredAlarms,
    activeAlarms: equipmentStore.activeAlarms,
    criticalAlarms: equipmentStore.criticalAlarms,
    majorAlarms: equipmentStore.majorAlarms,
    severityFilter,
    equipmentFilter,
    categoryFilter,
    activeOnly,
    getSeverityColor,
    getSeverityText,
    handleAcknowledgeAlarm,
    handleClearAlarm,
    refresh
  }
}

/**
 * 设备控制功能
 */
export function useEquipmentControl() {
  const equipmentStore = useEquipmentStore()

  const startEquipment = async (equipmentId: string) => {
    try {
      await ElMessageBox.confirm('确定要启动此设备吗？', '确认启动', {
        confirmButtonText: '启动',
        cancelButtonText: '取消',
        type: 'info'
      })
      await equipmentStore.startEquipment(equipmentId, 'current_user') // TODO: 获取当前用户
      ElMessage.success('设备启动成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('设备启动失败')
      }
    }
  }

  const stopEquipment = async (equipmentId: string) => {
    try {
      await ElMessageBox.confirm('确定要停止此设备吗？', '确认停止', {
        confirmButtonText: '停止',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await equipmentStore.stopEquipment(equipmentId, 'current_user') // TODO: 获取当前用户
      ElMessage.success('设备停止成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('设备停止失败')
      }
    }
  }

  const canControl = (equipment: Equipment): boolean => {
    // TODO: 根据用户权限和设备状态判断是否可以控制
    return equipment.isConnected && equipment.status !== EquipmentStatus.DOWN
  }

  const isControllable = (status: EquipmentStatus): boolean => {
    return [EquipmentStatus.IDLE, EquipmentStatus.RUN].includes(status)
  }

  return {
    startEquipment,
    stopEquipment,
    canControl,
    isControllable
  }
}

/**
 * Recipe管理
 */
export function useRecipeManagement() {
  const equipmentStore = useEquipmentStore()

  const selectedEquipmentId = ref('')
  const showCreateDialog = ref(false)
  const editingRecipe = ref<Recipe | null>(null)

  const recipesByEquipment = computed(() => {
    if (!selectedEquipmentId.value) return equipmentStore.recipes
    return equipmentStore.recipes.filter(recipe => recipe.equipmentId === selectedEquipmentId.value)
  })

  const activeRecipe = computed(() => {
    return recipesByEquipment.value.find(recipe => recipe.isActive)
  })

  const handleCreateRecipe = () => {
    editingRecipe.value = null
    showCreateDialog.value = true
  }

  const handleEditRecipe = (recipe: Recipe) => {
    editingRecipe.value = recipe
    showCreateDialog.value = true
  }

  const handleActivateRecipe = async (recipeId: string, equipmentId: string) => {
    try {
      await ElMessageBox.confirm('确定要激活此Recipe吗？', '确认激活', {
        confirmButtonText: '激活',
        cancelButtonText: '取消',
        type: 'info'
      })
      await equipmentStore.activateRecipe(recipeId, equipmentId)
      ElMessage.success('Recipe激活成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('Recipe激活失败')
      }
    }
  }

  const refresh = (equipmentId?: string) => {
    equipmentStore.fetchRecipes(equipmentId)
  }

  onMounted(() => {
    refresh()
  })

  return {
    recipes: recipesByEquipment,
    activeRecipe,
    selectedEquipmentId,
    showCreateDialog,
    editingRecipe,
    handleCreateRecipe,
    handleEditRecipe,
    handleActivateRecipe,
    refresh
  }
}

/**
 * 维护管理
 */
export function useMaintenanceManagement() {
  const equipmentStore = useEquipmentStore()

  const statusFilter = ref('')
  const priorityFilter = ref('')
  const equipmentFilter = ref('')
  const overdueOnly = ref(false)

  const queryParams = computed<MaintenanceQueryParams>(() => ({
    status: statusFilter.value || undefined,
    priority: priorityFilter.value || undefined,
    equipmentId: equipmentFilter.value || undefined,
    overdue: overdueOnly.value || undefined
  }))

  const filteredTasks = computed(() => {
    let result = equipmentStore.maintenanceTasks

    if (statusFilter.value) {
      result = result.filter(task => task.status === statusFilter.value)
    }

    if (priorityFilter.value) {
      result = result.filter(task => task.priority === priorityFilter.value)
    }

    if (equipmentFilter.value) {
      result = result.filter(task => task.equipmentId === equipmentFilter.value)
    }

    if (overdueOnly.value) {
      const now = new Date()
      result = result.filter(
        task => task.status === 'SCHEDULED' && new Date(task.scheduledDate) < now
      )
    }

    return result
  })

  const getPriorityColor = (priority: string): string => {
    const colorMap: Record<string, string> = {
      CRITICAL: '#F56C6C',
      HIGH: '#E6A23C',
      MEDIUM: '#409EFF',
      LOW: '#67C23A'
    }
    return colorMap[priority] || '#909399'
  }

  const getPriorityText = (priority: string): string => {
    const textMap: Record<string, string> = {
      CRITICAL: '紧急',
      HIGH: '高',
      MEDIUM: '中',
      LOW: '低'
    }
    return textMap[priority] || priority
  }

  const getStatusText = (status: string): string => {
    const textMap: Record<string, string> = {
      SCHEDULED: '已计划',
      IN_PROGRESS: '进行中',
      COMPLETED: '已完成',
      CANCELLED: '已取消',
      OVERDUE: '逾期'
    }
    return textMap[status] || status
  }

  const handleStartTask = async (taskId: string) => {
    try {
      await equipmentStore.startMaintenanceTask(taskId, 'current_user') // TODO: 获取当前用户
      ElMessage.success('维护任务开始')
    } catch (error) {
      ElMessage.error('开始维护任务失败')
    }
  }

  const handleCompleteTask = async (taskId: string, data: { notes?: string; usedParts: any[] }) => {
    try {
      await equipmentStore.completeMaintenanceTask(taskId, {
        operator: 'current_user', // TODO: 获取当前用户
        ...data
      })
      ElMessage.success('维护任务完成')
    } catch (error) {
      ElMessage.error('完成维护任务失败')
    }
  }

  const refresh = () => {
    equipmentStore.fetchMaintenanceTasks(queryParams.value)
    equipmentStore.fetchMaintenanceStatistics()
  }

  onMounted(() => {
    refresh()
  })

  watch(
    queryParams,
    () => {
      refresh()
    },
    { deep: true }
  )

  return {
    tasks: filteredTasks,
    overdueTasks: equipmentStore.overdueTasks,
    statistics: equipmentStore.maintenanceStatistics,
    statusFilter,
    priorityFilter,
    equipmentFilter,
    overdueOnly,
    getPriorityColor,
    getPriorityText,
    getStatusText,
    handleStartTask,
    handleCompleteTask,
    refresh
  }
}

/**
 * WebSocket实时数据
 */
export function useEquipmentWebSocket() {
  const equipmentStore = useEquipmentStore()
  const connected = ref(false)
  const reconnecting = ref(false)

  const connect = () => {
    if (ws?.readyState === WebSocket.OPEN) return

    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/equipment`

    try {
      ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        connected.value = true
        reconnecting.value = false
        console.log('Equipment WebSocket connected')

        // 清除重连定时器
        if (wsReconnectTimeout.value) {
          clearTimeout(wsReconnectTimeout.value)
          wsReconnectTimeout.value = undefined
        }
      }

      ws.onmessage = event => {
        try {
          const data = JSON.parse(event.data)
          equipmentStore.handleRealTimeUpdate(data)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      ws.onclose = event => {
        connected.value = false
        console.log('Equipment WebSocket disconnected:', event.code, event.reason)

        // 自动重连
        if (!reconnecting.value) {
          reconnect()
        }
      }

      ws.onerror = error => {
        console.error('Equipment WebSocket error:', error)
        connected.value = false
      }
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      connected.value = false
      reconnect()
    }
  }

  const disconnect = () => {
    if (ws) {
      ws.close()
      ws = null
    }
    connected.value = false
    reconnecting.value = false

    if (wsReconnectTimeout.value) {
      clearTimeout(wsReconnectTimeout.value)
      wsReconnectTimeout.value = undefined
    }
  }

  const reconnect = () => {
    if (reconnecting.value) return

    reconnecting.value = true
    wsReconnectTimeout.value = setTimeout(() => {
      console.log('Attempting to reconnect Equipment WebSocket...')
      connect()
    }, wsReconnectDelay)
  }

  onMounted(() => {
    connect()
  })

  onUnmounted(() => {
    disconnect()
  })

  return {
    connected: readonly(connected),
    reconnecting: readonly(reconnecting),
    connect,
    disconnect,
    reconnect
  }
}
