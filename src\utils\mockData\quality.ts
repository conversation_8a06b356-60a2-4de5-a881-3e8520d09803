// IC封装测试质量管理模块Mock数据
import type {
  SPCData,
  SPCPoint,
  QualityTraceability,
  TraceabilityNode,
  QualityEvent,
  ComplianceDocument,
  ComplianceAudit,
  QualityInspection,
  QualityAnalytics,
  QualityKPI,
  ControlLimits,
  SPCStatistics,
  ViolationRule
} from '@/types/quality'

// SPC违规规则定义(Nelson Rules)
export const violationRules: ViolationRule[] = [
  {
    id: 'NELSON_1',
    name: '超出3σ控制限',
    description: '任何一个点超出上控制限或下控制限',
    type: 'NELSON',
    severity: 'HIGH',
    isActive: true
  },
  {
    id: 'NELSON_2',
    name: '连续9点在中心线同一侧',
    description: '连续9个或更多点在中心线的同一侧',
    type: 'NELSON',
    severity: 'MEDIUM',
    isActive: true
  },
  {
    id: 'NELSON_3',
    name: '连续6点持续上升或下降',
    description: '连续6个点持续增长或持续下降',
    type: 'NELSON',
    severity: 'MEDIUM',
    isActive: true
  },
  {
    id: 'NELSON_4',
    name: '连续14点交替上下',
    description: '连续14个点在中心线上下交替变化',
    type: 'NELSON',
    severity: 'LOW',
    isActive: true
  },
  {
    id: 'NELSON_5',
    name: '连续2/3点在2σ外',
    description: '连续3个点中有2个点在2σ控制限外(同一侧)',
    type: 'NELSON',
    severity: 'MEDIUM',
    isActive: true
  },
  {
    id: 'NELSON_6',
    name: '连续4/5点在1σ外',
    description: '连续5个点中有4个点在1σ控制限外(同一侧)',
    type: 'NELSON',
    severity: 'MEDIUM',
    isActive: true
  },
  {
    id: 'NELSON_7',
    name: '连续15点在1σ内',
    description: '连续15个点都在中心线±1σ范围内',
    type: 'NELSON',
    severity: 'LOW',
    isActive: true
  },
  {
    id: 'NELSON_8',
    name: '连续8点在1σ外',
    description: '连续8个点都在中心线±1σ范围外',
    type: 'NELSON',
    severity: 'MEDIUM',
    isActive: true
  }
]

// 生成SPC数据点
function generateSPCPoint(index: number, baseValue: number, variation: number): SPCPoint {
  const values = Array.from({ length: 5 }, () => baseValue + (Math.random() - 0.5) * variation)
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length
  const range = Math.max(...values) - Math.min(...values)
  const variance =
    values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1)
  const standardDeviation = Math.sqrt(variance)

  // 模拟一些异常点
  let result: 'NORMAL' | 'WARNING' | 'OUT_OF_CONTROL' = 'NORMAL'
  const violatedRules: string[] = []

  if (Math.random() < 0.05) {
    result = 'OUT_OF_CONTROL'
    violatedRules.push('NELSON_1')
  } else if (Math.random() < 0.1) {
    result = 'WARNING'
    violatedRules.push('NELSON_2')
  }

  return {
    id: `spc_point_${index}`,
    timestamp: new Date(Date.now() - (100 - index) * 60 * 60 * 1000),
    sampleNumber: index + 1,
    values,
    mean,
    range,
    standardDeviation,
    result,
    violatedRules
  }
}

// 计算控制限
function calculateControlLimits(data: SPCPoint[]): ControlLimits {
  const means = data.map(point => point.mean)
  const ranges = data.map(point => point.range)

  const xbarMean = means.reduce((sum, val) => sum + val, 0) / means.length
  const rMean = ranges.reduce((sum, val) => sum + val, 0) / ranges.length

  // X-bar图控制限 (子组大小n=5的A2系数)
  const A2 = 0.577
  const D3 = 0.0 // R图下控制限系数(n=5)
  const D4 = 2.115 // R图上控制限系数(n=5)

  return {
    xbar: {
      ucl: xbarMean + A2 * rMean,
      cl: xbarMean,
      lcl: xbarMean - A2 * rMean
    },
    r: {
      ucl: D4 * rMean,
      cl: rMean,
      lcl: D3 * rMean
    },
    sigma: {
      ucl: 3.0,
      cl: 0,
      lcl: -3.0
    }
  }
}

// 计算SPC统计量
function calculateSPCStatistics(
  data: SPCPoint[],
  usl: number,
  lsl: number,
  target: number
): SPCStatistics {
  const allValues = data.flatMap(point => point.values)
  const mean = allValues.reduce((sum, val) => sum + val, 0) / allValues.length
  const variance =
    allValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (allValues.length - 1)
  const standardDeviation = Math.sqrt(variance)

  // 过程能力计算
  const cp = (usl - lsl) / (6 * standardDeviation)
  const cpk = Math.min(
    (usl - mean) / (3 * standardDeviation),
    (mean - lsl) / (3 * standardDeviation)
  )
  const pp = cp // 简化处理，实际应使用过程性能标准差
  const ppk = cpk // 简化处理
  const ca = Math.abs(mean - target) / ((usl - lsl) / 2)

  // 良率计算
  const passCount = allValues.filter(val => val >= lsl && val <= usl).length
  const yieldValue = passCount / allValues.length

  return {
    cpk,
    ppk,
    cp,
    pp,
    ca,
    mean,
    standardDeviation,
    yield: yieldValue
  }
}

// IC封装关键工艺SPC数据
export const spcDataList: SPCData[] = [
  // 晶圆探针测试关键参数
  {
    id: 'spc_vth',
    processName: 'CP测试',
    parameter: '阈值电压(Vth)',
    sampleData: Array.from({ length: 50 }, (_, i) => generateSPCPoint(i, 0.75, 0.1)),
    controlLimits: {} as ControlLimits,
    statistics: {} as SPCStatistics,
    violationRules,
    lastUpdate: new Date()
  },
  {
    id: 'spc_leakage',
    processName: 'CP测试',
    parameter: '漏电流(nA)',
    sampleData: Array.from({ length: 50 }, (_, i) => generateSPCPoint(i, 5.2, 1.0)),
    controlLimits: {} as ControlLimits,
    statistics: {} as SPCStatistics,
    violationRules,
    lastUpdate: new Date()
  },
  // 封装工艺关键参数
  {
    id: 'spc_die_attach',
    processName: '贴片工艺',
    parameter: '贴片厚度(μm)',
    sampleData: Array.from({ length: 50 }, (_, i) => generateSPCPoint(i, 25.0, 2.0)),
    controlLimits: {} as ControlLimits,
    statistics: {} as SPCStatistics,
    violationRules,
    lastUpdate: new Date()
  },
  {
    id: 'spc_wire_bond',
    processName: '线键合工艺',
    parameter: '键合拉力(gf)',
    sampleData: Array.from({ length: 50 }, (_, i) => generateSPCPoint(i, 12.5, 1.5)),
    controlLimits: {} as ControlLimits,
    statistics: {} as SPCStatistics,
    violationRules,
    lastUpdate: new Date()
  },
  {
    id: 'spc_molding',
    processName: '塑封工艺',
    parameter: '塑封厚度(μm)',
    sampleData: Array.from({ length: 50 }, (_, i) => generateSPCPoint(i, 850, 30)),
    controlLimits: {} as ControlLimits,
    statistics: {} as SPCStatistics,
    violationRules,
    lastUpdate: new Date()
  },
  // 最终测试关键参数
  {
    id: 'spc_ft_current',
    processName: 'FT测试',
    parameter: '工作电流(mA)',
    sampleData: Array.from({ length: 50 }, (_, i) => generateSPCPoint(i, 125, 8)),
    controlLimits: {} as ControlLimits,
    statistics: {} as SPCStatistics,
    violationRules,
    lastUpdate: new Date()
  }
]

// 为每个SPC数据计算控制限和统计量
spcDataList.forEach(spc => {
  spc.controlLimits = calculateControlLimits(spc.sampleData)

  // 根据参数设置规格限
  let usl: number, lsl: number, target: number
  switch (spc.parameter) {
    case '阈值电压(Vth)':
      usl = 0.85
      lsl = 0.65
      target = 0.75
      break
    case '漏电流(nA)':
      usl = 10.0
      lsl = 0
      target = 5.0
      break
    case '贴片厚度(μm)':
      usl = 30
      lsl = 20
      target = 25
      break
    case '键合拉力(gf)':
      usl = 18
      lsl = 8
      target = 12.5
      break
    case '塑封厚度(μm)':
      usl = 900
      lsl = 800
      target = 850
      break
    case '工作电流(mA)':
      usl = 140
      lsl = 110
      target = 125
      break
    default:
      usl = 100
      lsl = 0
      target = 50
  }

  spc.statistics = calculateSPCStatistics(spc.sampleData, usl, lsl, target)
})

// 质量追溯链数据
export const qualityTraceabilityData: QualityTraceability[] = [
  {
    traceId: 'TRACE_20241225001',
    lotNumber: 'LOT24120001',
    waferId: 'W24120001-15',
    packageId: 'PKG24120001-001',
    serialNumber: 'SN241200010001',
    traceabilityChain: [
      {
        id: 'node_wafer_fab',
        processStep: '晶圆制造',
        timestamp: new Date('2024-12-01T08:00:00'),
        location: '供应商FAB',
        operator: 'EXT_SUPPLIER',
        equipment: 'EXTERNAL',
        parameters: {
          process: '0.18um CMOS',
          thickness: '725μm',
          resistivity: '1-10Ω·cm'
        },
        qualityData: [
          {
            id: 'qd_wafer_001',
            timestamp: new Date('2024-12-01T08:00:00'),
            value: 725,
            specification: {
              usl: 750,
              lsl: 700,
              target: 725,
              ucl: 740,
              lcl: 710,
              unit: 'μm',
              parameter: '晶圆厚度'
            },
            result: 'PASS',
            location: '供应商FAB',
            operator: 'EXT_SUPPLIER'
          }
        ],
        nextNodes: ['node_cp_test'],
        previousNodes: []
      },
      {
        id: 'node_cp_test',
        processStep: 'CP测试',
        timestamp: new Date('2024-12-10T10:30:00'),
        location: 'CP测试区',
        operator: 'OP001_张工',
        equipment: 'TESTER_CP001',
        parameters: {
          probeCard: 'PC_QFP64_V2.1',
          testProgram: 'TP_MCU_V1.5',
          temperature: '25°C'
        },
        qualityData: [
          {
            id: 'qd_cp_001',
            timestamp: new Date('2024-12-10T10:30:00'),
            value: 0.745,
            specification: {
              usl: 0.85,
              lsl: 0.65,
              target: 0.75,
              ucl: 0.8,
              lcl: 0.7,
              unit: 'V',
              parameter: '阈值电压'
            },
            result: 'PASS',
            location: 'CP测试区',
            operator: 'OP001_张工'
          }
        ],
        nextNodes: ['node_die_attach'],
        previousNodes: ['node_wafer_fab']
      },
      {
        id: 'node_die_attach',
        processStep: '贴片工艺',
        timestamp: new Date('2024-12-12T14:15:00'),
        location: '封装生产线1',
        operator: 'OP003_李师傅',
        equipment: 'BONDER_ASM_001',
        parameters: {
          epoxy: 'EP_Silver_A1',
          temperature: '150°C',
          pressure: '2kg'
        },
        qualityData: [
          {
            id: 'qd_attach_001',
            timestamp: new Date('2024-12-12T14:15:00'),
            value: 24.8,
            specification: {
              usl: 30,
              lsl: 20,
              target: 25,
              ucl: 28,
              lcl: 22,
              unit: 'μm',
              parameter: '贴片厚度'
            },
            result: 'PASS',
            location: '封装生产线1',
            operator: 'OP003_李师傅'
          }
        ],
        nextNodes: ['node_wire_bond'],
        previousNodes: ['node_cp_test']
      },
      {
        id: 'node_wire_bond',
        processStep: '线键合',
        timestamp: new Date('2024-12-13T09:45:00'),
        location: '封装生产线1',
        operator: 'OP005_王师傅',
        equipment: 'WIREBONDER_KULICKE_001',
        parameters: {
          wireType: 'Gold_25μm',
          bondForce: '12gf',
          ultrasonic: '60mW'
        },
        qualityData: [
          {
            id: 'qd_bond_001',
            timestamp: new Date('2024-12-13T09:45:00'),
            value: 12.3,
            specification: {
              usl: 18,
              lsl: 8,
              target: 12.5,
              ucl: 15,
              lcl: 10,
              unit: 'gf',
              parameter: '键合拉力'
            },
            result: 'PASS',
            location: '封装生产线1',
            operator: 'OP005_王师傅'
          }
        ],
        nextNodes: ['node_molding'],
        previousNodes: ['node_die_attach']
      },
      {
        id: 'node_molding',
        processStep: '塑封工艺',
        timestamp: new Date('2024-12-14T16:20:00'),
        location: '封装生产线2',
        operator: 'OP007_赵师傅',
        equipment: 'MOLD_APIC_001',
        parameters: {
          moldCompound: 'EMC_G750',
          temperature: '175°C',
          pressure: '70kg/cm²',
          cureTime: '120s'
        },
        qualityData: [
          {
            id: 'qd_mold_001',
            timestamp: new Date('2024-12-14T16:20:00'),
            value: 847,
            specification: {
              usl: 900,
              lsl: 800,
              target: 850,
              ucl: 880,
              lcl: 820,
              unit: 'μm',
              parameter: '塑封厚度'
            },
            result: 'PASS',
            location: '封装生产线2',
            operator: 'OP007_赵师傅'
          }
        ],
        nextNodes: ['node_ft_test'],
        previousNodes: ['node_wire_bond']
      },
      {
        id: 'node_ft_test',
        processStep: 'FT测试',
        timestamp: new Date('2024-12-15T11:10:00'),
        location: 'FT测试区',
        operator: 'OP002_陈工',
        equipment: 'TESTER_FT002',
        parameters: {
          testSocket: 'Socket_QFP64',
          testProgram: 'TP_MCU_FINAL_V1.8',
          temperature: '85°C'
        },
        qualityData: [
          {
            id: 'qd_ft_001',
            timestamp: new Date('2024-12-15T11:10:00'),
            value: 126.5,
            specification: {
              usl: 140,
              lsl: 110,
              target: 125,
              ucl: 135,
              lcl: 115,
              unit: 'mA',
              parameter: '工作电流'
            },
            result: 'PASS',
            location: 'FT测试区',
            operator: 'OP002_陈工'
          }
        ],
        nextNodes: ['node_final_inspect'],
        previousNodes: ['node_molding']
      },
      {
        id: 'node_final_inspect',
        processStep: '最终检验',
        timestamp: new Date('2024-12-16T13:30:00'),
        location: 'QC检验区',
        operator: 'QC001_刘检验员',
        equipment: 'OPTICAL_INSPECT_001',
        parameters: {
          inspectionStandard: 'IPC_A_610_Class3',
          magnification: '50X'
        },
        qualityData: [
          {
            id: 'qd_inspect_001',
            timestamp: new Date('2024-12-16T13:30:00'),
            value: 1,
            specification: {
              usl: 1,
              lsl: 1,
              target: 1,
              ucl: 1,
              lcl: 1,
              unit: '',
              parameter: '外观检验'
            },
            result: 'PASS',
            location: 'QC检验区',
            operator: 'QC001_刘检验员'
          }
        ],
        nextNodes: [],
        previousNodes: ['node_ft_test']
      }
    ],
    qualityHistory: [
      {
        id: 'event_001',
        eventType: 'INSPECTION',
        timestamp: new Date('2024-12-10T10:30:00'),
        description: 'CP测试完成，所有参数正常',
        severity: 'LOW',
        responsible: 'OP001_张工',
        status: 'CLOSED'
      },
      {
        id: 'event_002',
        eventType: 'INSPECTION',
        timestamp: new Date('2024-12-15T11:10:00'),
        description: 'FT测试完成，电性能参数符合要求',
        severity: 'LOW',
        responsible: 'OP002_陈工',
        status: 'CLOSED'
      },
      {
        id: 'event_003',
        eventType: 'RELEASE',
        timestamp: new Date('2024-12-16T13:30:00'),
        description: '最终检验通过，产品放行',
        severity: 'LOW',
        responsible: 'QC001_刘检验员',
        status: 'CLOSED'
      }
    ],
    currentStatus: {
      overall: 'PASS',
      iqc: {
        status: 'PASS',
        timestamp: new Date('2024-12-01T08:00:00'),
        inspector: 'IQC001_晶圆检验员',
        notes: '晶圆来料检验合格',
        defects: []
      },
      ipqc: {
        status: 'PASS',
        timestamp: new Date('2024-12-14T16:20:00'),
        inspector: 'IPQC001_过程检验员',
        notes: '封装过程检验合格',
        defects: []
      },
      fqc: {
        status: 'PASS',
        timestamp: new Date('2024-12-16T13:30:00'),
        inspector: 'QC001_刘检验员',
        notes: '成品检验合格',
        defects: []
      },
      reliability: {
        status: 'PENDING',
        inspector: 'REL001_可靠性工程师',
        notes: '等待可靠性测试',
        defects: []
      }
    },
    customerInfo: {
      customerId: 'CUST001',
      customerName: '某汽车电子公司',
      contactInfo: '<EMAIL>',
      qualityRequirements: [
        {
          parameter: 'Cpk',
          specification: {
            usl: 999,
            lsl: 1.33,
            target: 1.67,
            ucl: 2.0,
            lcl: 1.33,
            unit: '',
            parameter: 'Cpk'
          },
          testMethod: 'SPC统计',
          frequency: '每批次',
          reportingRequirement: '月度质量报告'
        }
      ]
    }
  }
]

// IATF16949合规文档数据
export const complianceDocuments: ComplianceDocument[] = [
  {
    id: 'doc_control_plan_001',
    documentType: 'CONTROL_PLAN',
    title: 'IC封装工艺控制计划',
    version: 'V2.3',
    status: 'APPROVED',
    effectiveDate: new Date('2024-01-15'),
    nextReviewDate: new Date('2024-07-15'),
    owner: '工艺工程师_张工',
    approver: '质量经理_王经理',
    content: `
1. 产品描述：QFP64封装MCU芯片
2. 关键工艺控制要求：
   - CP测试：阈值电压0.65V-0.85V，Cpk≥1.33
   - 贴片工艺：厚度20-30μm，100%检查
   - 线键合：拉力8-18gf，抽样检查10%
   - 塑封工艺：厚度800-900μm，SPC监控
   - FT测试：工作电流110-140mA，Cpk≥1.33
3. 检验频率及方法详见各工序SOP
    `,
    attachments: [
      {
        id: 'att_001',
        filename: '封装工艺流程图.pdf',
        fileSize: 2048576,
        fileType: 'application/pdf',
        uploadDate: new Date('2024-01-15'),
        uploadedBy: '张工',
        url: '/documents/process_flow.pdf'
      }
    ],
    changeHistory: [
      {
        id: 'change_001',
        version: 'V2.3',
        changeDate: new Date('2024-01-15'),
        changedBy: '张工',
        changeReason: '增加新客户要求',
        changeDescription: '更新Cpk要求从1.0提升至1.33',
        approvedBy: '王经理'
      }
    ]
  },
  {
    id: 'doc_fmea_001',
    documentType: 'FMEA',
    title: '封装工艺DFMEA分析',
    version: 'V1.5',
    status: 'APPROVED',
    effectiveDate: new Date('2024-02-01'),
    nextReviewDate: new Date('2024-08-01'),
    owner: '质量工程师_李工',
    approver: '质量经理_王经理',
    content: `
FMEA分析结果：
1. 贴片偏移：RPN=144，增加视觉检查
2. 线键合脱落：RPN=120，提升键合参数控制
3. 塑封空洞：RPN=108，优化固化工艺
    `,
    attachments: [],
    changeHistory: []
  }
]

// 审核数据
export const complianceAudits: ComplianceAudit[] = [
  {
    id: 'audit_internal_2024001',
    auditType: 'INTERNAL',
    auditor: '内审员_陈工',
    auditDate: new Date('2024-03-15'),
    scope: ['7.5.1 文档控制', '8.3.2 设计开发策划', '8.5.1 生产服务控制'],
    findings: [
      {
        id: 'finding_001',
        clause: '*******',
        nonConformityType: 'MINOR',
        description: '部分作业指导书未及时更新版本号',
        evidence: '发现3份SOP文档版本不一致',
        rootCause: '文档更新流程执行不到位',
        riskLevel: 'LOW',
        dueDate: new Date('2024-04-15')
      }
    ],
    correctionActions: [
      {
        id: 'action_001',
        findingId: 'finding_001',
        description: '建立文档版本控制检查清单，月度自查',
        responsible: '文档控制员',
        dueDate: new Date('2024-04-15'),
        status: 'COMPLETED',
        effectiveness: 'EFFECTIVE',
        verificationDate: new Date('2024-04-10'),
        verifiedBy: '内审员_陈工'
      }
    ],
    status: 'COMPLETED',
    nextAuditDate: new Date('2024-09-15')
  }
]

// 质量检验数据
export const qualityInspections: QualityInspection[] = [
  {
    id: 'inspect_iqc_001',
    inspectionType: 'IQC',
    lotNumber: 'LOT24120001',
    materialCode: 'WAFER_8INCH_CMOS18',
    sampleSize: 10,
    inspectionPlan: {
      id: 'plan_iqc_wafer',
      planName: '晶圆来料检验计划',
      applicableProducts: ['WAFER_8INCH_CMOS18'],
      inspectionItems: [
        {
          id: 'item_thickness',
          itemName: '晶圆厚度',
          testMethod: '千分尺测量',
          specification: {
            usl: 750,
            lsl: 700,
            target: 725,
            ucl: 740,
            lcl: 710,
            unit: 'μm',
            parameter: '厚度'
          },
          criticalLevel: 'MAJOR',
          inspectionSequence: 1,
          equipmentRequired: ['千分尺001']
        }
      ],
      samplingPlan: {
        planType: 'SINGLE',
        aql: 1.0,
        sampleSize: 10,
        acceptanceNumber: 0,
        rejectionNumber: 1,
        inspectionLevel: 'II'
      },
      acceptanceCriteria: {
        overallAql: 1.0,
        criticalAql: 0.1,
        majorAql: 1.0,
        minorAql: 2.5
      }
    },
    results: [
      {
        id: 'result_001',
        inspectionItemId: 'item_thickness',
        measuredValue: 723.5,
        result: 'PASS',
        notes: '厚度均匀性良好',
        equipment: '千分尺001',
        measurementDate: new Date('2024-12-01T08:00:00')
      }
    ],
    inspector: 'IQC001_晶圆检验员',
    inspectionDate: new Date('2024-12-01T08:00:00'),
    status: 'COMPLETED',
    conclusion: 'ACCEPT'
  }
]

// 质量KPI数据
export const qualityKPIs: QualityKPI[] = [
  {
    kpiName: '整体良率',
    currentValue: 98.5,
    target: 99.0,
    unit: '%',
    trend: 'UP',
    trendPercentage: 1.2,
    status: 'GOOD',
    timeRange: '本月'
  },
  {
    kpiName: 'CP良率',
    currentValue: 95.2,
    target: 96.0,
    unit: '%',
    trend: 'STABLE',
    trendPercentage: 0.1,
    status: 'WARNING',
    timeRange: '本月'
  },
  {
    kpiName: 'FT良率',
    currentValue: 99.1,
    target: 99.5,
    unit: '%',
    trend: 'UP',
    trendPercentage: 0.8,
    status: 'GOOD',
    timeRange: '本月'
  },
  {
    kpiName: '平均Cpk',
    currentValue: 1.45,
    target: 1.33,
    unit: '',
    trend: 'UP',
    trendPercentage: 5.2,
    status: 'EXCELLENT',
    timeRange: '本月'
  },
  {
    kpiName: '客户投诉PPM',
    currentValue: 12.5,
    target: 10.0,
    unit: 'PPM',
    trend: 'DOWN',
    trendPercentage: -8.5,
    status: 'WARNING',
    timeRange: '本月'
  },
  {
    kpiName: '质量成本率',
    currentValue: 2.3,
    target: 2.0,
    unit: '%',
    trend: 'UP',
    trendPercentage: 2.8,
    status: 'WARNING',
    timeRange: '本月'
  }
]

// 质量分析数据
export const qualityAnalyticsData: QualityAnalytics = {
  period: {
    start: new Date('2024-12-01'),
    end: new Date('2024-12-31')
  },
  yieldAnalysis: {
    overallYield: 98.5,
    yieldByProcess: {
      CP测试: 95.2,
      贴片工艺: 99.8,
      线键合: 99.5,
      塑封工艺: 99.2,
      FT测试: 99.1
    },
    yieldTrend: [
      { date: '2024-12-01', value: 97.8 },
      { date: '2024-12-08', value: 98.2 },
      { date: '2024-12-15', value: 98.5 },
      { date: '2024-12-22', value: 98.7 },
      { date: '2024-12-29', value: 98.5 }
    ],
    firstPassYield: 96.8,
    finalYield: 98.5,
    reworkRate: 1.7,
    scrapRate: 1.5
  },
  defectAnalysis: {
    totalDefects: 2450,
    defectRate: 1.5,
    defectsByType: {
      电性能异常: 35,
      外观缺陷: 28,
      封装缺陷: 22,
      测试异常: 15
    },
    defectsByLocation: {
      CP测试: 35,
      线键合: 28,
      塑封工艺: 22,
      FT测试: 15
    },
    paretoChart: [
      { category: '电性能异常', value: 35, percentage: 35, cumulativePercentage: 35 },
      { category: '外观缺陷', value: 28, percentage: 28, cumulativePercentage: 63 },
      { category: '封装缺陷', value: 22, percentage: 22, cumulativePercentage: 85 },
      { category: '测试异常', value: 15, percentage: 15, cumulativePercentage: 100 }
    ],
    topDefects: [
      { type: '阈值电压偏移', count: 850, rate: 0.52, trend: 'DECREASING', impact: 'HIGH' },
      { type: '线键合强度不足', count: 620, rate: 0.38, trend: 'STABLE', impact: 'MEDIUM' },
      { type: '塑封空洞', count: 480, rate: 0.29, trend: 'INCREASING', impact: 'MEDIUM' }
    ]
  },
  customerComplaint: {
    totalComplaints: 8,
    complaintRate: 12.5,
    responseTime: {
      average: 4.2,
      target: 4.0
    },
    resolutionTime: {
      average: 12.5,
      target: 10.0
    },
    complaintsByType: {
      功能异常: 3,
      可靠性问题: 2,
      外观缺陷: 2,
      包装问题: 1
    },
    customerSatisfaction: 8.5
  },
  qualityCost: {
    totalQualityCost: 1250000,
    costByCategory: {
      prevention: 180000,
      appraisal: 320000,
      internalFailure: 580000,
      externalFailure: 170000
    },
    costTrend: [
      { date: '2024-09', value: 1180000 },
      { date: '2024-10', value: 1220000 },
      { date: '2024-11', value: 1280000 },
      { date: '2024-12', value: 1250000 }
    ],
    costAsPercentageOfSales: 2.3
  },
  processCapability: {
    processes: [
      {
        processName: 'CP测试',
        parameter: '阈值电压',
        cp: 1.42,
        cpk: 1.35,
        pp: 1.38,
        ppk: 1.32,
        sigma: 4.05,
        yield: 95.2,
        status: 'GOOD'
      },
      {
        processName: '线键合',
        parameter: '键合拉力',
        cp: 1.55,
        cpk: 1.48,
        pp: 1.52,
        ppk: 1.45,
        sigma: 4.44,
        yield: 99.5,
        status: 'EXCELLENT'
      }
    ],
    overallCapability: 1.45,
    capabilityTrend: [
      { date: '2024-09', value: 1.38 },
      { date: '2024-10', value: 1.42 },
      { date: '2024-11', value: 1.44 },
      { date: '2024-12', value: 1.45 }
    ]
  }
}
