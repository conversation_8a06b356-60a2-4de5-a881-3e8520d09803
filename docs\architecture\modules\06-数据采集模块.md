# IC封测数据采集模块设计

## 1. 模块概述

### 1.1 模块定位
IC封测数据采集模块专为半导体封装测试行业设计，提供符合SEMI标准的SECS/GEM设备通信、高精度测试数据采集、实时数据处理和智能数据分发服务，确保封测生产数据的完整性、准确性和可追溯性。

### 1.2 IC封测行业复用价值
- **SECS/GEM标准协议**：完整支持SEMI E4/E5/E30/E37等半导体行业标准
- **高频精密数据采集**：支持ATE/Prober等设备的毫秒级数据采集
- **智能数据处理**：封测特有的数据质量控制和异常检测
- **STDF数据格式支持**：标准测试数据格式的完整支持

### 1.3 IC封测数据采集场景覆盖
```
IC封测数据采集应用场景
├── 半导体测试设备数据采集
│   ├── ATE设备数据(SECS/GEM)
│   ├── Prober探针台数据(HSMS-SS)
│   ├── Handler分选机数据(SEMI E37)
│   └── 封装设备数据(SEMI E40)
├── 封测工艺过程数据
│   ├── CP电测参数采集(STDF格式)
│   ├── Die Attach工艺参数
│   ├── Wire Bond键合数据
│   └── FT成品测试数据
├── 环境与设施监控
│   ├── Cleanroom环境参数
│   ├── 气体系统状态监控
│   ├── 纯水系统参数
│   └── 设备Utility监控
├── 质量与可靠性数据
│   ├── Wafer Map数据采集
│   ├── SPC统计数据采集
│   ├── Yield良率数据
│   └── 可靠性测试数据
└── 封测专业系统集成
    ├── ERP系统数据交换
    ├── 客户系统数据推送
    ├── 供应商系统集成
    └── 第三方Lab数据
```

## 2. IC封测专业技术架构

### 2.1 架构设计
```
IC封测数据采集模块架构
├── SECS/GEM通信引擎        # SEMI标准协议处理引擎
├── 封测协议适配器层        # ATE/Prober/Handler协议适配
├── 高速数据缓冲系统        # 测试数据高速缓冲和批处理
├── 封测数据处理引擎        # STDF/Wafer Map等专业数据处理
├── 半导体数据质量控制      # 封测特有的数据质量标准
├── 实时数据分发中心        # 测试结果实时分发
├── Equipment监控告警       # 设备状态和通信监控
└── Recipe配置管理中心      # 设备Recipe和参数管理
```

### 2.2 IC封测专业数据模型

#### 2.2.1 封测设备数据源配置管理
```sql
-- IC封测设备数据源定义表
CREATE TABLE ic_equipment_data_sources (
    source_id VARCHAR(30) PRIMARY KEY,
    equipment_id VARCHAR(30),               -- 关联设备ID
    equipment_name VARCHAR(100),            -- 设备名称
    equipment_type ENUM('ATE','Prober','Handler','DieBonder','WireBonder','Molder','Tester'), -- 设备类型
    secs_gem_config JSON,                   -- SECS/GEM协议配置
    connection_config JSON,                 -- 连接配置(HSMS-SS等)
    semi_standard_version VARCHAR(20),      -- 支持的SEMI标准版本
    equipment_constant_ec JSON,             -- Equipment Constants
    status_variables_sv JSON,              -- Status Variables
    is_gem_enabled BOOLEAN DEFAULT TRUE,   -- 是否启用GEM
    is_active BOOLEAN DEFAULT TRUE,
    fab_location VARCHAR(50),              -- FAB位置
    bay_number VARCHAR(20),                -- Bay编号
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_equipment_type_active (equipment_type, is_active),
    INDEX idx_fab_location (fab_location, bay_number)
);

-- 封测数据点定义表（扩展半导体特殊需求）
CREATE TABLE ic_test_data_points (
    point_id VARCHAR(30) PRIMARY KEY,
    source_id VARCHAR(30),                  -- 数据源ID
    parameter_name VARCHAR(100),            -- 测试参数名称
    parameter_code VARCHAR(100),            -- 参数编码
    secs_item_id VARCHAR(50),              -- SECS消息Item ID
    test_number INT,                       -- Test Number (for STDF)
    data_type ENUM('boolean','uint16','int16','uint32','int32','float','double','string','binary'), -- 数据类型
    unit_of_measure VARCHAR(20),           -- 测量单位(V/A/Hz/°C等)
    scale_factor DECIMAL(15,6) DEFAULT 1.0, -- 精密缩放因子
    offset_value DECIMAL(15,6) DEFAULT 0.0, -- 偏移量
    specification_min DECIMAL(15,6),       -- 规格下限
    specification_max DECIMAL(15,6),       -- 规格上限
    control_min DECIMAL(15,6),            -- 控制下限
    control_max DECIMAL(15,6),            -- 控制上限
    collect_frequency ENUM('CONTINUOUS','ON_EVENT','ON_REQUEST'), -- 采集频率类型
    collect_interval_ms INT DEFAULT 100,   -- 采集间隔(毫秒级)
    data_quality_grade ENUM('CRITICAL','IMPORTANT','NORMAL'), -- 数据质量等级
    stdf_compatible BOOLEAN DEFAULT TRUE,   -- 是否STDF兼容
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP,
    
    INDEX idx_source_active (source_id, is_active),
    INDEX idx_parameter_code (parameter_code),
    INDEX idx_test_number (test_number),
    INDEX idx_quality_grade (data_quality_grade)
);

-- 采集器实例表
CREATE TABLE data_collectors (
    collector_id VARCHAR(30) PRIMARY KEY,
    collector_name VARCHAR(100),            -- 采集器名称
    collector_type VARCHAR(50),             -- 采集器类型
    source_id VARCHAR(30),                  -- 关联数据源
    host_address VARCHAR(100),              -- 部署主机
    process_id VARCHAR(50),                 -- 进程ID
    status ENUM('running','stopped','error','maintenance'), -- 状态
    last_heartbeat TIMESTAMP,              -- 最后心跳时间
    config_version VARCHAR(20),             -- 配置版本
    performance_stats JSON,                 -- 性能统计
    error_message TEXT,                     -- 错误信息
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_status_heartbeat (status, last_heartbeat)
);
```

#### 2.2.2 实时数据存储
```sql
-- 实时数据表
CREATE TABLE realtime_data (
    data_id VARCHAR(30) PRIMARY KEY,
    point_id VARCHAR(30),                   -- 数据点ID
    collector_id VARCHAR(30),               -- 采集器ID
    raw_value TEXT,                         -- 原始值
    processed_value DECIMAL(15,4),          -- 处理后的值
    quality_code INT,                       -- 数据质量码
    timestamp BIGINT,                       -- 时间戳(毫秒)
    collect_time TIMESTAMP(3),              -- 采集时间
    process_time TIMESTAMP(3),              -- 处理时间
    
    INDEX idx_point_time (point_id, collect_time),
    INDEX idx_timestamp (timestamp),
    INDEX idx_quality (quality_code, collect_time)
) PARTITION BY RANGE (UNIX_TIMESTAMP(collect_time)) (
    PARTITION p_current VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 DAY))),
    PARTITION p_day1 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 2 DAY))),
    PARTITION p_day2 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 3 DAY))),
    PARTITION p_day3 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 4 DAY))),
    PARTITION p_day4 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 5 DAY))),
    PARTITION p_day5 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 6 DAY))),
    PARTITION p_day6 VALUES LESS THAN (UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 7 DAY))),
    PARTITION p_old VALUES LESS THAN MAXVALUE
);

-- 采集状态统计表
CREATE TABLE collector_statistics (
    stat_id VARCHAR(30) PRIMARY KEY,
    collector_id VARCHAR(30),               -- 采集器ID
    stat_time TIMESTAMP,                    -- 统计时间
    total_points INT,                       -- 总数据点数
    success_count INT,                      -- 成功采集数
    error_count INT,                        -- 错误采集数
    avg_response_time INT,                  -- 平均响应时间(ms)
    max_response_time INT,                  -- 最大响应时间(ms)
    data_throughput DECIMAL(10,2),          -- 数据吞吐量(点/秒)
    memory_usage DECIMAL(8,2),              -- 内存使用率(%)
    cpu_usage DECIMAL(8,2),                 -- CPU使用率(%)
    
    INDEX idx_collector_time (collector_id, stat_time)
);
```

## 3. IC封测专业协议适配器层

### 3.1 封测设备协议适配器抽象接口
```java
public interface ProtocolAdapter {
    
    /**
     * 协议类型
     */
    String getProtocolType();
    
    /**
     * 初始化连接
     */
    void initialize(DataSourceConfig config);
    
    /**
     * 连接到数据源
     */
    boolean connect();
    
    /**
     * 断开连接
     */
    void disconnect();
    
    /**
     * 检查连接状态
     */
    boolean isConnected();
    
    /**
     * 读取单个数据点
     */
    DataValue readPoint(DataPoint point);
    
    /**
     * 批量读取数据点
     */
    Map<String, DataValue> readPoints(List<DataPoint> points);
    
    /**
     * 写入数据点
     */
    boolean writePoint(DataPoint point, Object value);
    
    /**
     * 订阅数据变化
     */
    void subscribe(List<DataPoint> points, DataChangeListener listener);
    
    /**
     * 取消订阅
     */
    void unsubscribe(List<DataPoint> points);
    
    /**
     * 获取适配器状态
     */
    AdapterStatus getStatus();
}

@Component
public class ProtocolAdapterManager {
    
    private Map<String, ProtocolAdapter> adapters = new ConcurrentHashMap<>();
    
    @Autowired
    private List<ProtocolAdapter> adapterList;
    
    @PostConstruct
    public void initAdapters() {
        for (ProtocolAdapter adapter : adapterList) {
            adapters.put(adapter.getProtocolType(), adapter);
        }
    }
    
    public ProtocolAdapter getAdapter(String protocolType) {
        return adapters.get(protocolType);
    }
    
    public void registerAdapter(ProtocolAdapter adapter) {
        adapters.put(adapter.getProtocolType(), adapter);
    }
}
```

### 3.2 SECS/GEM协议适配器（核心）
```java
@Component
public class SecsGemProtocolAdapter implements ProtocolAdapter {
    
    private SecsGemClient secsClient;
    private String equipmentId;
    private boolean connected = false;
    private boolean gemOnline = false;
    
    @Override
    public String getProtocolType() {
        return "secs_gem";
    }
    
    @Override
    public void initialize(DataSourceConfig config) {
        JSONObject protocolConfig = config.getProtocolConfig();
        
        // SECS/GEM连接配置
        SecsGemConfig secsConfig = SecsGemConfig.builder()
            .deviceId(protocolConfig.getIntValue("deviceId"))
            .isHost(protocolConfig.getBooleanValue("isHost"))
            .socketAddress(new InetSocketAddress(
                protocolConfig.getString("ipAddress"),
                protocolConfig.getIntValue("port")
            ))
            .sessionTimeout(protocolConfig.getIntValue("sessionTimeout"))
            .selectTimeout(protocolConfig.getIntValue("selectTimeout"))
            .linkTest(protocolConfig.getBooleanValue("linkTest"))
            .build();
            
        this.equipmentId = protocolConfig.getString("equipmentId");
        this.secsClient = new SecsGemClient(secsConfig);
        
        // 注册SECS消息处理器
        registerSecsMessageHandlers();
    }
    
    @Override
    public boolean connect() {
        try {
            // 建立HSMS连接
            secsClient.open();
            
            // 等待连接建立
            boolean connected = secsClient.waitUntilCommunicateState(
                HsmsCommunicateState.SELECTED, 30, TimeUnit.SECONDS);
                
            if (connected) {
                this.connected = true;
                
                // 发送S1F13 Establish Communication Request
                establishCommunication();
                
                // 发送S1F17 Request Online
                requestOnline();
                
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("SECS/GEM连接失败", e);
            this.connected = false;
            return false;
        }
    }
    
    @Override
    public void disconnect() {
        if (secsClient != null) {
            try {
                // 发送S1F15 Request Offline
                if (gemOnline) {
                    requestOffline();
                }
                
                secsClient.close();
                connected = false;
                gemOnline = false;
                
            } catch (Exception e) {
                log.error("SECS/GEM断开连接失败", e);
            }
        }
    }
    
    @Override
    public DataValue readPoint(DataPoint point) {
        if (!connected || !gemOnline) {
            return DataValue.error("SECS/GEM未在线");
        }
        
        try {
            // 根据数据点类型选择不同的SECS消息
            String itemId = point.getPointAddress(); // SECS Item ID，如 "SVID_001"
            
            if (itemId.startsWith("SVID_")) {
                // Status Variable - 发送S1F3 Selected Status Variable Data Request
                return readStatusVariable(itemId, point);
            } else if (itemId.startsWith("ECID_")) {
                // Equipment Constant - 发送S2F13 Equipment Constant Request
                return readEquipmentConstant(itemId, point);
            } else if (itemId.startsWith("CEID_")) {
                // Collection Event - 订阅事件数据
                return subscribeCollectionEvent(itemId, point);
            } else {
                return DataValue.error("不支持的SECS Item类型: " + itemId);
            }
            
        } catch (Exception e) {
            log.error("SECS/GEM读取失败: {}", point.getPointCode(), e);
            return DataValue.error("读取失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, DataValue> readPoints(List<DataPoint> points) {
        if (!connected || !gemOnline) {
            return points.stream().collect(Collectors.toMap(
                DataPoint::getPointCode,
                p -> DataValue.error("SECS/GEM未在线")
            ));
        }
        
        // 按数据类型分组批量读取
        Map<String, List<DataPoint>> groupedPoints = points.stream()
            .collect(Collectors.groupingBy(p -> {
                String itemId = p.getPointAddress();
                if (itemId.startsWith("SVID_")) return "SV";
                if (itemId.startsWith("ECID_")) return "EC";
                if (itemId.startsWith("CEID_")) return "CE";
                return "OTHER";
            }));
        
        Map<String, DataValue> results = new HashMap<>();
        
        // 批量读取Status Variables
        if (groupedPoints.containsKey("SV")) {
            readStatusVariablesBatch(groupedPoints.get("SV"), results);
        }
        
        // 批量读取Equipment Constants
        if (groupedPoints.containsKey("EC")) {
            readEquipmentConstantsBatch(groupedPoints.get("EC"), results);
        }
        
        // 处理Collection Events
        if (groupedPoints.containsKey("CE")) {
            subscribeCollectionEventsBatch(groupedPoints.get("CE"), results);
        }
        
        return results;
    }
    
    private void establishCommunication() throws Exception {
        // S1F13 Establish Communication Request
        SecsMessage s1f13 = SecsMessage.sml(
            "s1f13", 
            SmlDataItem.list(
                SmlDataItem.ascii(equipmentId),        // MDLN - Model
                SmlDataItem.ascii("1.0.0")            // SOFTREV - Software Revision
            )
        );
        
        Optional<SecsMessage> reply = secsClient.send(s1f13, 10, TimeUnit.SECONDS);
        
        if (reply.isPresent()) {
            SecsMessage s1f14 = reply.get();
            // 处理S1F14响应
            SmlDataItem commack = s1f14.sml().getItem(0);
            SmlDataItem mdln = s1f14.sml().getItem(1);
            SmlDataItem softrev = s1f14.sml().getItem(2);
            
            log.info("SECS通信建立成功: COMMACK={}, MDLN={}, SOFTREV={}", 
                commack.toString(), mdln.toString(), softrev.toString());
        } else {
            throw new RuntimeException("S1F13 Establish Communication超时");
        }
    }
    
    private void requestOnline() throws Exception {
        // S1F17 Request Online
        SecsMessage s1f17 = SecsMessage.sml("s1f17");
        
        Optional<SecsMessage> reply = secsClient.send(s1f17, 10, TimeUnit.SECONDS);
        
        if (reply.isPresent()) {
            SecsMessage s1f18 = reply.get();
            SmlDataItem onlack = s1f18.sml().getItem(0);
            
            if ("0".equals(onlack.toString())) {
                gemOnline = true;
                log.info("GEM Online成功");
            } else {
                throw new RuntimeException("GEM Online失败, ONLACK=" + onlack.toString());
            }
        } else {
            throw new RuntimeException("S1F17 Request Online超时");
        }
    }
    
    private DataValue readStatusVariable(String svid, DataPoint point) throws Exception {
        // S1F3 Selected Status Variable Data Request
        SecsMessage s1f3 = SecsMessage.sml(
            "s1f3",
            SmlDataItem.list(
                SmlDataItem.uint32(Integer.parseInt(svid.substring(5))) // 提取SVID数字
            )
        );
        
        Optional<SecsMessage> reply = secsClient.send(s1f3, 5, TimeUnit.SECONDS);
        
        if (reply.isPresent()) {
            SecsMessage s1f4 = reply.get();
            SmlDataItem value = s1f4.sml().getItem(0);
            
            Object convertedValue = convertSecsValue(value, point.getDataType());
            return DataValue.success(convertedValue, System.currentTimeMillis());
        } else {
            return DataValue.error("S1F3请求超时");
        }
    }
    
    private void readStatusVariablesBatch(List<DataPoint> points, Map<String, DataValue> results) {
        try {
            // 构建SVID列表
            List<SmlDataItem> svidList = points.stream()
                .map(p -> {
                    String svid = p.getPointAddress();
                    return SmlDataItem.uint32(Integer.parseInt(svid.substring(5)));
                })
                .collect(Collectors.toList());
            
            // S1F3 Selected Status Variable Data Request
            SecsMessage s1f3 = SecsMessage.sml(
                "s1f3",
                SmlDataItem.list(svidList)
            );
            
            Optional<SecsMessage> reply = secsClient.send(s1f3, 10, TimeUnit.SECONDS);
            
            if (reply.isPresent()) {
                SecsMessage s1f4 = reply.get();
                SmlDataItem valueList = s1f4.sml();
                
                for (int i = 0; i < points.size() && i < valueList.size(); i++) {
                    DataPoint point = points.get(i);
                    SmlDataItem value = valueList.getItem(i);
                    
                    try {
                        Object convertedValue = convertSecsValue(value, point.getDataType());
                        results.put(point.getPointCode(), 
                            DataValue.success(convertedValue, System.currentTimeMillis()));
                    } catch (Exception e) {
                        results.put(point.getPointCode(), 
                            DataValue.error("值转换失败: " + e.getMessage()));
                    }
                }
            } else {
                // 批量读取失败，标记所有点为错误
                for (DataPoint point : points) {
                    results.put(point.getPointCode(), DataValue.error("S1F3请求超时"));
                }
            }
            
        } catch (Exception e) {
            log.error("批量读取Status Variables失败", e);
            for (DataPoint point : points) {
                results.put(point.getPointCode(), DataValue.error("批量读取失败"));
            }
        }
    }
    
    private Object convertSecsValue(SmlDataItem value, DataType targetType) throws Exception {
        if (value.isEmpty()) return null;
        
        switch (targetType) {
            case BOOLEAN:
                return value.booleans()[0];
            case INT16:
                return (short) value.int32s()[0];
            case INT32:
                return value.int32s()[0];
            case FLOAT:
                return value.float32s()[0];
            case DOUBLE:
                return value.float64s()[0];
            case STRING:
                return value.ascii();
            default:
                return value.toString();
        }
    }
    
    private void registerSecsMessageHandlers() {
        // 注册Collection Event处理器
        secsClient.addSecsMessageReceiveListener(new SecsMessageReceiveListener() {
            @Override
            public void received(SecsMessage message) {
                if (message.getStream() == 6 && message.getFunction() == 11) {
                    // S6F11 Event Report
                    handleCollectionEvent(message);
                }
            }
        });
    }
    
    private void handleCollectionEvent(SecsMessage s6f11) {
        try {
            // 解析S6F11 Event Report
            SmlDataItem root = s6f11.sml();
            SmlDataItem dataid = root.getItem(0);    // DATAID
            SmlDataItem ceid = root.getItem(1);      // CEID
            SmlDataItem rptid = root.getItem(2);     // RPTID
            SmlDataItem rptData = root.getItem(3);   // Report Data
            
            // 处理事件数据
            processCollectionEventData(ceid.toString(), rptData);
            
            // 发送S6F12 Event Report Acknowledge
            SecsMessage s6f12 = SecsMessage.sml("s6f12", SmlDataItem.binary((byte) 0));
            secsClient.send(s6f12);
            
        } catch (Exception e) {
            log.error("处理Collection Event失败", e);
        }
    }
    
    private void processCollectionEventData(String ceid, SmlDataItem rptData) {
        // 根据CEID处理不同类型的事件数据
        switch (ceid) {
            case "1001": // Lot Start Event
                handleLotStartEvent(rptData);
                break;
            case "1002": // Lot End Event
                handleLotEndEvent(rptData);
                break;
            case "2001": // Recipe Change Event
                handleRecipeChangeEvent(rptData);
                break;
            case "3001": // Alarm Event
                handleAlarmEvent(rptData);
                break;
            default:
                log.debug("未处理的Collection Event: {}", ceid);
        }
    }
    
    @Override
    public boolean writePoint(DataPoint point, Object value) {
        if (!connected || !gemOnline) {
            return false;
        }
        
        try {
            String itemId = point.getPointAddress();
            
            if (itemId.startsWith("ECID_")) {
                // Equipment Constant - 发送S2F15 New Equipment Constant Send
                return writeEquipmentConstant(itemId, value, point.getDataType());
            } else if (itemId.startsWith("PPID_")) {
                // Process Program - 发送S7F1 Process Program Load Inquire
                return writeProcessProgram(itemId, value);
            } else {
                log.warn("不支持写入的SECS Item类型: {}", itemId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("SECS/GEM写入失败: {}", point.getPointCode(), e);
            return false;
        }
    }
}

### 3.3 Modbus协议适配器（传统协议支持）
```java
@Component  
public class ModbusProtocolAdapter implements ProtocolAdapter {
    
    private ModbusMaster master;
    private ModbusFactory factory;
    private TcpParameters tcpParameters;
    private boolean connected = false;
    
    @Override
    public String getProtocolType() {
        return "modbus";
    }
    
    @Override
    public void initialize(DataSourceConfig config) {
        JSONObject protocolConfig = config.getProtocolConfig();
        
        // 初始化Modbus连接参数
        factory = new ModbusFactory();
        tcpParameters = new TcpParameters();
        tcpParameters.setHost(protocolConfig.getString("host"));
        tcpParameters.setPort(protocolConfig.getIntValue("port"));
        tcpParameters.setEncapsulated(protocolConfig.getBooleanValue("encapsulated"));
        
        master = factory.createTcpMaster(tcpParameters, false);
    }
    
    @Override
    public boolean connect() {
        try {
            master.setTimeout(5000); // 5秒超时
            master.setRetries(3);
            master.init();
            connected = true;
            return true;
        } catch (ModbusInitException e) {
            log.error("Modbus连接失败", e);
            connected = false;
            return false;
        }
    }
    
    @Override
    public void disconnect() {
        if (master != null) {
            master.destroy();
            connected = false;
        }
    }
    
    @Override
    public DataValue readPoint(DataPoint point) {
        if (!connected) {
            return DataValue.error("连接未建立");
        }
        
        try {
            ModbusRequest request = createReadRequest(point);
            ModbusResponse response = master.send(request);
            
            if (response.isException()) {
                return DataValue.error("读取异常: " + response.getExceptionMessage());
            }
            
            Object value = parseResponse(response, point.getDataType());
            return DataValue.success(value, System.currentTimeMillis());
            
        } catch (ModbusTransportException e) {
            log.error("Modbus读取失败: {}", point.getPointCode(), e);
            return DataValue.error("读取失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, DataValue> readPoints(List<DataPoint> points) {
        Map<String, DataValue> results = new HashMap<>();
        
        // 按寄存器类型和地址范围分组批量读取
        Map<String, List<DataPoint>> groupedPoints = groupPointsByRegion(points);
        
        for (Map.Entry<String, List<DataPoint>> entry : groupedPoints.entrySet()) {
            List<DataPoint> regionPoints = entry.getValue();
            readPointsBatch(regionPoints, results);
        }
        
        return results;
    }
    
    private void readPointsBatch(List<DataPoint> points, Map<String, DataValue> results) {
        if (points.isEmpty()) return;
        
        // 计算读取范围
        DataPoint firstPoint = points.get(0);
        String address = firstPoint.getPointAddress(); // 格式: "40001:10" (起始地址:长度)
        String[] parts = address.split(":");
        int startAddress = Integer.parseInt(parts[0]) - 40001; // 转换为0基址
        int quantity = Integer.parseInt(parts[1]);
        
        try {
            ReadHoldingRegistersRequest request = new ReadHoldingRegistersRequest(1, startAddress, quantity);
            ReadHoldingRegistersResponse response = (ReadHoldingRegistersResponse) master.send(request);
            
            if (!response.isException()) {
                short[] registers = response.getShortData();
                
                // 解析每个数据点的值
                for (DataPoint point : points) {
                    try {
                        Object value = extractValueFromRegisters(registers, point, startAddress);
                        results.put(point.getPointCode(), DataValue.success(value, System.currentTimeMillis()));
                    } catch (Exception e) {
                        results.put(point.getPointCode(), DataValue.error("解析失败: " + e.getMessage()));
                    }
                }
            } else {
                // 批量读取失败，回退到单个读取
                for (DataPoint point : points) {
                    results.put(point.getPointCode(), readPoint(point));
                }
            }
            
        } catch (ModbusTransportException e) {
            log.error("Modbus批量读取失败", e);
            // 批量读取失败，回退到单个读取
            for (DataPoint point : points) {
                results.put(point.getPointCode(), readPoint(point));
            }
        }
    }
    
    private ModbusRequest createReadRequest(DataPoint point) {
        String address = point.getPointAddress(); // 格式示例: "40001" 或 "10001"
        int addr = Integer.parseInt(address);
        
        if (addr >= 40001 && addr <= 49999) {
            // 保持寄存器
            return new ReadHoldingRegistersRequest(1, addr - 40001, 1);
        } else if (addr >= 30001 && addr <= 39999) {
            // 输入寄存器
            return new ReadInputRegistersRequest(1, addr - 30001, 1);
        } else if (addr >= 10001 && addr <= 19999) {
            // 输入状态
            return new ReadDiscreteInputsRequest(1, addr - 10001, 1);
        } else if (addr >= 1 && addr <= 9999) {
            // 线圈状态
            return new ReadCoilsRequest(1, addr - 1, 1);
        } else {
            throw new IllegalArgumentException("无效的Modbus地址: " + address);
        }
    }
}
```

### 3.3 OPC UA协议适配器
```java
@Component
public class OPCUAProtocolAdapter implements ProtocolAdapter {
    
    private OpcUaClient client;
    private String endpointUrl;
    private boolean connected = false;
    
    @Override
    public String getProtocolType() {
        return "opcua";
    }
    
    @Override
    public void initialize(DataSourceConfig config) {
        JSONObject protocolConfig = config.getProtocolConfig();
        this.endpointUrl = protocolConfig.getString("endpointUrl");
    }
    
    @Override
    public boolean connect() {
        try {
            EndpointDescription endpoint = UaTcpStackClient.getEndpoints(endpointUrl)
                .get()
                .stream()
                .filter(e -> e.getSecurityPolicyUri().equals(SecurityPolicy.None.getUri()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("没有找到合适的endpoint"));
            
            OpcUaClientConfig config = OpcUaClientConfig.builder()
                .setApplicationName(LocalizedText.englishText("CIM Data Collector"))
                .setApplicationUri("urn:cim:datacollector")
                .setEndpoint(endpoint)
                .setIdentityProvider(new AnonymousProvider())
                .setRequestTimeout(UInteger.valueOf(5000))
                .build();
            
            client = OpcUaClient.create(config);
            client.connect().get();
            connected = true;
            return true;
            
        } catch (Exception e) {
            log.error("OPC UA连接失败", e);
            connected = false;
            return false;
        }
    }
    
    @Override
    public void disconnect() {
        if (client != null) {
            try {
                client.disconnect().get();
            } catch (Exception e) {
                log.error("OPC UA断开连接失败", e);
            }
            connected = false;
        }
    }
    
    @Override
    public DataValue readPoint(DataPoint point) {
        if (!connected) {
            return DataValue.error("OPC UA连接未建立");
        }
        
        try {
            NodeId nodeId = NodeId.parse(point.getPointAddress());
            ReadValueId readValueId = new ReadValueId(nodeId, AttributeId.Value.uid(), null, null);
            
            ReadRequest request = new ReadRequest(null, null, TimestampsToReturn.Both,
                new ReadValueId[]{readValueId});
            
            ReadResponse response = client.read(request).get();
            
            org.eclipse.milo.opcua.stack.core.types.builtin.DataValue dataValue = response.getResults()[0];
            
            if (dataValue.getStatusCode().isGood()) {
                Object value = convertOpcValue(dataValue.getValue(), point.getDataType());
                long timestamp = dataValue.getSourceTime() != null ? 
                    dataValue.getSourceTime().getJavaTime() : System.currentTimeMillis();
                return DataValue.success(value, timestamp);
            } else {
                return DataValue.error("OPC UA读取错误: " + dataValue.getStatusCode().toString());
            }
            
        } catch (Exception e) {
            log.error("OPC UA读取失败: {}", point.getPointCode(), e);
            return DataValue.error("读取失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, DataValue> readPoints(List<DataPoint> points) {
        if (!connected) {
            return points.stream().collect(Collectors.toMap(
                DataPoint::getPointCode,
                p -> DataValue.error("OPC UA连接未建立")
            ));
        }
        
        try {
            ReadValueId[] readValueIds = points.stream()
                .map(point -> new ReadValueId(
                    NodeId.parse(point.getPointAddress()),
                    AttributeId.Value.uid(),
                    null,
                    null
                ))
                .toArray(ReadValueId[]::new);
            
            ReadRequest request = new ReadRequest(null, null, TimestampsToReturn.Both, readValueIds);
            ReadResponse response = client.read(request).get();
            
            Map<String, DataValue> results = new HashMap<>();
            org.eclipse.milo.opcua.stack.core.types.builtin.DataValue[] dataValues = response.getResults();
            
            for (int i = 0; i < points.size(); i++) {
                DataPoint point = points.get(i);
                org.eclipse.milo.opcua.stack.core.types.builtin.DataValue dataValue = dataValues[i];
                
                if (dataValue.getStatusCode().isGood()) {
                    Object value = convertOpcValue(dataValue.getValue(), point.getDataType());
                    long timestamp = dataValue.getSourceTime() != null ? 
                        dataValue.getSourceTime().getJavaTime() : System.currentTimeMillis();
                    results.put(point.getPointCode(), DataValue.success(value, timestamp));
                } else {
                    results.put(point.getPointCode(), 
                        DataValue.error("读取错误: " + dataValue.getStatusCode().toString()));
                }
            }
            
            return results;
            
        } catch (Exception e) {
            log.error("OPC UA批量读取失败", e);
            return points.stream().collect(Collectors.toMap(
                DataPoint::getPointCode,
                p -> DataValue.error("读取失败: " + e.getMessage())
            ));
        }
    }
    
    @Override
    public void subscribe(List<DataPoint> points, DataChangeListener listener) {
        if (!connected) return;
        
        try {
            // 创建订阅
            UaSubscription subscription = client.getSubscriptionManager()
                .createSubscription(1000.0) // 1秒发布间隔
                .get();
            
            // 创建监控项
            List<MonitoredItemCreateRequest> requests = points.stream()
                .map(point -> {
                    ReadValueId readValueId = new ReadValueId(
                        NodeId.parse(point.getPointAddress()),
                        AttributeId.Value.uid(),
                        null,
                        null
                    );
                    
                    MonitoringParameters parameters = new MonitoringParameters(
                        UInteger.valueOf(subscription.nextClientHandle()),
                        1000.0, // 采样间隔
                        null,
                        UInteger.valueOf(10), // 队列大小
                        true
                    );
                    
                    return new MonitoredItemCreateRequest(
                        readValueId,
                        MonitoringMode.Reporting,
                        parameters
                    );
                })
                .collect(Collectors.toList());
            
            UaMonitoredItem.ValueConsumer consumer = (item, value) -> {
                // 找到对应的数据点
                DataPoint point = findPointByNodeId(points, item.getReadValueId().getNodeId());
                if (point != null) {
                    Object convertedValue = convertOpcValue(value.getValue(), point.getDataType());
                    long timestamp = value.getSourceTime() != null ? 
                        value.getSourceTime().getJavaTime() : System.currentTimeMillis();
                    
                    DataValue dataValue = value.getStatusCode().isGood() ?
                        DataValue.success(convertedValue, timestamp) :
                        DataValue.error("状态错误: " + value.getStatusCode().toString());
                    
                    listener.onDataChange(point.getPointCode(), dataValue);
                }
            };
            
            List<UaMonitoredItem> monitoredItems = subscription
                .createMonitoredItems(TimestampsToReturn.Both, requests, consumer)
                .get();
                
        } catch (Exception e) {
            log.error("OPC UA订阅失败", e);
        }
    }
    
    private Object convertOpcValue(Variant variant, DataType targetType) {
        if (variant.isNull()) return null;
        
        Object value = variant.getValue();
        
        switch (targetType) {
            case BOOLEAN:
                return Boolean.valueOf(value.toString());
            case INT16:
                return ((Number) value).shortValue();
            case INT32:
                return ((Number) value).intValue();
            case FLOAT:
                return ((Number) value).floatValue();
            case DOUBLE:
                return ((Number) value).doubleValue();
            case STRING:
                return value.toString();
            default:
                return value;
        }
    }
}
```

## 4. 数据采集引擎

### 4.1 数据采集调度器
```java
@Service
public class DataCollectionEngine {
    
    @Autowired
    private ProtocolAdapterManager adapterManager;
    
    @Autowired
    private DataPointRepository dataPointRepository;
    
    @Autowired
    private DataProcessingService dataProcessingService;
    
    @Autowired
    private DataBufferService dataBufferService;
    
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(10);
    private final Map<String, ScheduledFuture<?>> collectionTasks = new ConcurrentHashMap<>();
    
    @EventListener
    public void onDataSourceActivated(DataSourceActivatedEvent event) {
        startCollection(event.getSourceId());
    }
    
    @EventListener  
    public void onDataSourceDeactivated(DataSourceDeactivatedEvent event) {
        stopCollection(event.getSourceId());
    }
    
    public void startCollection(String sourceId) {
        DataSource dataSource = dataSourceRepository.findById(sourceId)
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在"));
        
        if (!dataSource.isActive()) {
            return;
        }
        
        List<DataPoint> points = dataPointRepository.findBySourceIdAndIsActiveTrue(sourceId);
        if (points.isEmpty()) {
            return;
        }
        
        // 按采集间隔分组
        Map<Integer, List<DataPoint>> groupedPoints = points.stream()
            .collect(Collectors.groupingBy(DataPoint::getCollectInterval));
        
        for (Map.Entry<Integer, List<DataPoint>> entry : groupedPoints.entrySet()) {
            int interval = entry.getKey();
            List<DataPoint> groupPoints = entry.getValue();
            
            String taskKey = sourceId + "_" + interval;
            
            ScheduledFuture<?> task = scheduler.scheduleAtFixedRate(
                new DataCollectionTask(dataSource, groupPoints),
                0,
                interval,
                TimeUnit.MILLISECONDS
            );
            
            collectionTasks.put(taskKey, task);
        }
        
        log.info("数据源采集已启动: {}, 数据点数量: {}", sourceId, points.size());
    }
    
    public void stopCollection(String sourceId) {
        collectionTasks.entrySet().removeIf(entry -> {
            if (entry.getKey().startsWith(sourceId + "_")) {
                entry.getValue().cancel(true);
                return true;
            }
            return false;
        });
        
        log.info("数据源采集已停止: {}", sourceId);
    }
    
    private class DataCollectionTask implements Runnable {
        private final DataSource dataSource;
        private final List<DataPoint> points;
        private final ProtocolAdapter adapter;
        
        public DataCollectionTask(DataSource dataSource, List<DataPoint> points) {
            this.dataSource = dataSource;
            this.points = points;
            this.adapter = adapterManager.getAdapter(dataSource.getSourceType().name().toLowerCase());
            
            if (this.adapter == null) {
                throw new IllegalStateException("找不到协议适配器: " + dataSource.getSourceType());
            }
            
            // 初始化适配器
            this.adapter.initialize(new DataSourceConfig(dataSource));
            this.adapter.connect();
        }
        
        @Override
        public void run() {
            if (!adapter.isConnected()) {
                if (!adapter.connect()) {
                    log.warn("数据源连接失败，跳过本次采集: {}", dataSource.getSourceName());
                    return;
                }
            }
            
            try {
                long startTime = System.currentTimeMillis();
                
                // 批量读取数据点
                Map<String, DataValue> results = adapter.readPoints(points);
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                
                // 处理采集到的数据
                List<RealTimeData> dataList = new ArrayList<>();
                for (Map.Entry<String, DataValue> entry : results.entrySet()) {
                    String pointCode = entry.getKey();
                    DataValue value = entry.getValue();
                    
                    DataPoint point = findPointByCode(pointCode);
                    if (point != null) {
                        RealTimeData data = processDataValue(point, value, startTime);
                        if (data != null) {
                            dataList.add(data);
                        }
                    }
                }
                
                // 批量写入缓冲区
                if (!dataList.isEmpty()) {
                    dataBufferService.bufferData(dataList);
                }
                
                // 记录采集统计
                recordCollectionStatistics(dataSource.getSourceId(), results.size(), 
                    (int) results.values().stream().filter(DataValue::isSuccess).count(),
                    (int) duration);
                
            } catch (Exception e) {
                log.error("数据采集异常: {}", dataSource.getSourceName(), e);
                recordCollectionError(dataSource.getSourceId(), e.getMessage());
            }
        }
        
        private RealTimeData processDataValue(DataPoint point, DataValue value, long collectTime) {
            if (!value.isSuccess()) {
                log.debug("数据点采集失败: {} - {}", point.getPointCode(), value.getErrorMessage());
                return null;
            }
            
            try {
                // 数据预处理
                ProcessedValue processed = dataProcessingService.processValue(point, value);
                
                RealTimeData data = new RealTimeData();
                data.setDataId(IdGenerator.generateId());
                data.setPointId(point.getPointId());
                data.setCollectorId(dataSource.getSourceId());
                data.setRawValue(String.valueOf(value.getValue()));
                data.setProcessedValue(processed.getValue());
                data.setQualityCode(processed.getQualityCode());
                data.setTimestamp(value.getTimestamp());
                data.setCollectTime(LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(collectTime), 
                    ZoneId.systemDefault()
                ));
                data.setProcessTime(LocalDateTime.now());
                
                return data;
                
            } catch (Exception e) {
                log.error("数据处理失败: {} - {}", point.getPointCode(), e.getMessage());
                return null;
            }
        }
        
        private DataPoint findPointByCode(String pointCode) {
            return points.stream()
                .filter(p -> p.getPointCode().equals(pointCode))
                .findFirst()
                .orElse(null);
        }
    }
}
```

### 4.2 数据质量控制
```java
@Service
public class DataProcessingService {
    
    @Autowired
    private DataQualityRuleRepository qualityRuleRepository;
    
    public ProcessedValue processValue(DataPoint point, DataValue rawValue) {
        ProcessedValue result = new ProcessedValue();
        result.setOriginalValue(rawValue.getValue());
        result.setTimestamp(rawValue.getTimestamp());
        
        try {
            // 1. 数据类型转换
            Object convertedValue = convertDataType(rawValue.getValue(), point.getDataType());
            
            // 2. 缩放和偏移处理
            Double scaledValue = applyScaleAndOffset(convertedValue, point.getScaleFactor(), point.getOffsetValue());
            
            // 3. 范围检查
            QualityCode qualityCode = checkValueRange(scaledValue, point.getRangeMin(), point.getRangeMax());
            
            // 4. 应用质量规则
            qualityCode = applyQualityRules(point, scaledValue, qualityCode);
            
            // 5. 数据平滑处理（可选）
            scaledValue = applySmoothingFilter(point, scaledValue);
            
            result.setValue(scaledValue);
            result.setQualityCode(qualityCode.getCode());
            result.setQualityDescription(qualityCode.getDescription());
            
        } catch (Exception e) {
            log.error("数据处理失败: {}", point.getPointCode(), e);
            result.setValue(null);
            result.setQualityCode(QualityCode.BAD_PROCESSING_ERROR.getCode());
            result.setQualityDescription("处理异常: " + e.getMessage());
        }
        
        return result;
    }
    
    private Object convertDataType(Object value, DataType targetType) {
        if (value == null) return null;
        
        try {
            switch (targetType) {
                case BOOLEAN:
                    return convertToBoolean(value);
                case INT16:
                    return ((Number) value).shortValue();
                case INT32:
                    return ((Number) value).intValue();
                case FLOAT:
                    return ((Number) value).floatValue();
                case DOUBLE:
                    return ((Number) value).doubleValue();
                case STRING:
                    return value.toString();
                default:
                    return value;
            }
        } catch (Exception e) {
            throw new DataProcessingException("数据类型转换失败: " + value + " -> " + targetType, e);
        }
    }
    
    private Double applyScaleAndOffset(Object value, BigDecimal scaleFactor, BigDecimal offsetValue) {
        if (!(value instanceof Number)) return null;
        
        double numValue = ((Number) value).doubleValue();
        double scale = scaleFactor != null ? scaleFactor.doubleValue() : 1.0;
        double offset = offsetValue != null ? offsetValue.doubleValue() : 0.0;
        
        return numValue * scale + offset;
    }
    
    private QualityCode checkValueRange(Double value, BigDecimal rangeMin, BigDecimal rangeMax) {
        if (value == null) return QualityCode.BAD_NO_DATA;
        
        if (rangeMin != null && value < rangeMin.doubleValue()) {
            return QualityCode.BAD_OUT_OF_RANGE;
        }
        
        if (rangeMax != null && value > rangeMax.doubleValue()) {
            return QualityCode.BAD_OUT_OF_RANGE;
        }
        
        return QualityCode.GOOD;
    }
    
    private QualityCode applyQualityRules(DataPoint point, Double value, QualityCode currentQuality) {
        List<DataQualityRule> rules = qualityRuleRepository.findByPointIdAndIsActiveTrue(point.getPointId());
        
        for (DataQualityRule rule : rules) {
            QualityCode ruleResult = evaluateQualityRule(rule, value, currentQuality);
            if (ruleResult.isWorseThan(currentQuality)) {
                currentQuality = ruleResult;
            }
        }
        
        return currentQuality;
    }
    
    private Double applySmoothingFilter(DataPoint point, Double currentValue) {
        // 获取历史数据进行平滑处理
        List<RealTimeData> history = getRecentHistory(point.getPointId(), 5);
        
        if (history.size() < 2) {
            return currentValue; // 数据不足，不进行平滑
        }
        
        // 简单的移动平均滤波
        double sum = currentValue;
        for (RealTimeData data : history) {
            if (data.getProcessedValue() != null) {
                sum += data.getProcessedValue().doubleValue();
            }
        }
        
        return sum / (history.size() + 1);
    }
    
    // 质量代码枚举
    public enum QualityCode {
        GOOD(192, "良好"),
        GOOD_CLAMPED(208, "良好但被钳位"),
        UNCERTAIN(64, "不确定"),
        UNCERTAIN_LAST_USABLE_VALUE(68, "不确定-使用上次值"),
        BAD(0, "坏值"),
        BAD_NO_DATA(16, "坏值-无数据"),
        BAD_OUT_OF_RANGE(20, "坏值-超出范围"),
        BAD_PROCESSING_ERROR(24, "坏值-处理错误");
        
        private final int code;
        private final String description;
        
        QualityCode(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public boolean isWorseThan(QualityCode other) {
            return this.code < other.code;
        }
        
        // getters...
    }
}
```

## 5. 数据缓冲服务

### 5.1 高性能数据缓冲
```java
@Service
public class DataBufferService {
    
    private static final int BUFFER_SIZE = 10000;
    private static final long FLUSH_INTERVAL_MS = 5000; // 5秒刷新一次
    
    private final DisruptorBufferManager<RealTimeData> bufferManager;
    private final BatchDataPersistService persistService;
    
    @Autowired
    private RealTimeDataRepository realTimeDataRepository;
    
    @Autowired
    private TimeSeriesDataService timeSeriesDataService;
    
    public DataBufferService(BatchDataPersistService persistService) {
        this.persistService = persistService;
        this.bufferManager = new DisruptorBufferManager<>(
            BUFFER_SIZE,
            this::handleBufferedData,
            FLUSH_INTERVAL_MS
        );
    }
    
    public void bufferData(List<RealTimeData> dataList) {
        for (RealTimeData data : dataList) {
            bufferManager.publish(data);
        }
    }
    
    public void bufferSingleData(RealTimeData data) {
        bufferManager.publish(data);
    }
    
    private void handleBufferedData(List<RealTimeData> dataList) {
        try {
            // 批量持久化到关系数据库
            persistService.batchInsertRealTimeData(dataList);
            
            // 同时写入时序数据库
            timeSeriesDataService.batchInsertTimeSeriesData(dataList);
            
            // 触发实时数据事件
            publishRealTimeDataEvent(dataList);
            
        } catch (Exception e) {
            log.error("批量数据持久化失败", e);
            // 可以考虑将失败的数据写入死信队列
            handlePersistFailure(dataList, e);
        }
    }
    
    private void publishRealTimeDataEvent(List<RealTimeData> dataList) {
        // 通过事件总线发布实时数据更新事件
        for (RealTimeData data : dataList) {
            RealTimeDataEvent event = new RealTimeDataEvent(
                data.getPointId(),
                data.getProcessedValue(),
                data.getQualityCode(),
                data.getTimestamp()
            );
            
            ApplicationEventPublisher.publishEvent(event);
        }
    }
    
    @PreDestroy
    public void shutdown() {
        bufferManager.shutdown();
    }
}

@Component
public class BatchDataPersistService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    public void batchInsertRealTimeData(List<RealTimeData> dataList) {
        if (dataList.isEmpty()) return;
        
        String sql = "INSERT INTO realtime_data (data_id, point_id, collector_id, raw_value, " +
                    "processed_value, quality_code, timestamp, collect_time, process_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        List<Object[]> batchArgs = dataList.stream()
            .map(data -> new Object[]{
                data.getDataId(),
                data.getPointId(),
                data.getCollectorId(),
                data.getRawValue(),
                data.getProcessedValue(),
                data.getQualityCode(),
                data.getTimestamp(),
                Timestamp.valueOf(data.getCollectTime()),
                Timestamp.valueOf(data.getProcessTime())
            })
            .collect(Collectors.toList());
        
        jdbcTemplate.batchUpdate(sql, batchArgs);
    }
}

// Disruptor高性能缓冲区管理器
public class DisruptorBufferManager<T> {
    
    private final Disruptor<DataEvent<T>> disruptor;
    private final RingBuffer<DataEvent<T>> ringBuffer;
    private final ScheduledExecutorService flushScheduler;
    private final ConcurrentLinkedQueue<T> pendingData;
    
    public DisruptorBufferManager(int bufferSize, Consumer<List<T>> dataHandler, long flushIntervalMs) {
        this.pendingData = new ConcurrentLinkedQueue<>();
        
        EventFactory<DataEvent<T>> eventFactory = DataEvent::new;
        EventHandler<DataEvent<T>> eventHandler = (event, sequence, endOfBatch) -> {
            pendingData.offer(event.getData());
            event.clear();
        };
        
        this.disruptor = new Disruptor<>(
            eventFactory,
            bufferSize,
            Executors.newCachedThreadPool(),
            ProducerType.MULTI,
            new BlockingWaitStrategy()
        );
        
        disruptor.handleEventsWith(eventHandler);
        disruptor.start();
        
        this.ringBuffer = disruptor.getRingBuffer();
        
        // 定时刷新调度器
        this.flushScheduler = Executors.newSingleThreadScheduledExecutor();
        this.flushScheduler.scheduleAtFixedRate(
            () -> flushPendingData(dataHandler),
            flushIntervalMs,
            flushIntervalMs,
            TimeUnit.MILLISECONDS
        );
    }
    
    public void publish(T data) {
        long sequence = ringBuffer.next();
        try {
            DataEvent<T> event = ringBuffer.get(sequence);
            event.setData(data);
        } finally {
            ringBuffer.publish(sequence);
        }
    }
    
    private void flushPendingData(Consumer<List<T>> dataHandler) {
        if (pendingData.isEmpty()) return;
        
        List<T> dataList = new ArrayList<>();
        T data;
        while ((data = pendingData.poll()) != null) {
            dataList.add(data);
        }
        
        if (!dataList.isEmpty()) {
            dataHandler.accept(dataList);
        }
    }
    
    public void shutdown() {
        flushScheduler.shutdown();
        disruptor.shutdown();
    }
    
    private static class DataEvent<T> {
        private T data;
        
        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
        public void clear() { this.data = null; }
    }
}
```

## 6. 实时数据分发

### 6.1 WebSocket实时推送
```java
@Component
public class RealTimeDataPushService {
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    private final Map<String, Set<String>> pointSubscriptions = new ConcurrentHashMap<>();
    
    @EventListener
    @Async
    public void onRealTimeDataUpdate(RealTimeDataEvent event) {
        // 查找订阅该数据点的用户
        Set<String> subscribers = pointSubscriptions.get(event.getPointId());
        if (subscribers != null && !subscribers.isEmpty()) {
            
            RealTimeDataMessage message = RealTimeDataMessage.builder()
                .pointId(event.getPointId())
                .value(event.getValue())
                .qualityCode(event.getQualityCode())
                .timestamp(event.getTimestamp())
                .build();
            
            // 推送给所有订阅者
            for (String userId : subscribers) {
                messagingTemplate.convertAndSendToUser(
                    userId,
                    "/queue/realtime-data/" + event.getPointId(),
                    message
                );
            }
        }
    }
    
    public void subscribeToPoint(String userId, String pointId) {
        pointSubscriptions.computeIfAbsent(pointId, k -> ConcurrentHashMap.newKeySet())
                         .add(userId);
    }
    
    public void unsubscribeFromPoint(String userId, String pointId) {
        Set<String> subscribers = pointSubscriptions.get(pointId);
        if (subscribers != null) {
            subscribers.remove(userId);
            if (subscribers.isEmpty()) {
                pointSubscriptions.remove(pointId);
            }
        }
    }
}

@RestController
@RequestMapping("/api/realtime")
public class RealTimeDataController {
    
    @Autowired
    private RealTimeDataPushService pushService;
    
    @Autowired
    private RealTimeDataRepository realTimeDataRepository;
    
    /**
     * 订阅实时数据
     */
    @PostMapping("/subscribe")
    public ResponseEntity<Void> subscribe(@RequestBody SubscriptionRequest request) {
        String userId = getCurrentUserId();
        
        for (String pointId : request.getPointIds()) {
            pushService.subscribeToPoint(userId, pointId);
        }
        
        return ResponseEntity.ok().build();
    }
    
    /**
     * 取消订阅
     */
    @PostMapping("/unsubscribe")
    public ResponseEntity<Void> unsubscribe(@RequestBody SubscriptionRequest request) {
        String userId = getCurrentUserId();
        
        for (String pointId : request.getPointIds()) {
            pushService.unsubscribeFromPoint(userId, pointId);
        }
        
        return ResponseEntity.ok().build();
    }
    
    /**
     * 获取当前实时数据
     */
    @GetMapping("/current")
    public ResponseEntity<List<RealTimeDataDTO>> getCurrentData(
            @RequestParam List<String> pointIds) {
        
        List<RealTimeData> currentData = realTimeDataRepository.findCurrentValuesByPointIds(pointIds);
        
        List<RealTimeDataDTO> result = currentData.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取历史趋势数据
     */
    @GetMapping("/trend")
    public ResponseEntity<Map<String, List<TrendDataPoint>>> getTrendData(
            @RequestParam List<String> pointIds,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "1000") int maxPoints) {
        
        Map<String, List<TrendDataPoint>> trendData = new HashMap<>();
        
        for (String pointId : pointIds) {
            List<RealTimeData> history = realTimeDataRepository.findTrendData(
                pointId, startTime, endTime, maxPoints);
                
            List<TrendDataPoint> trendPoints = history.stream()
                .map(data -> new TrendDataPoint(
                    data.getTimestamp(),
                    data.getProcessedValue(),
                    data.getQualityCode()
                ))
                .collect(Collectors.toList());
                
            trendData.put(pointId, trendPoints);
        }
        
        return ResponseEntity.ok(trendData);
    }
}
```

---

*数据采集模块为CIM系统提供了高性能、可扩展、高可靠的工业数据采集解决方案，支持多种工业协议和实时数据处理能力*