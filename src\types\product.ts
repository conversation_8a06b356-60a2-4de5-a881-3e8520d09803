// ================================
// IC产品主数据管理系统类型定义
// ================================

import { BaseEntity, PaginationQuery, PaginationResponse } from './common'
import { Customer } from './customer'

// ================================
// 产品基础类型枚举
// ================================

// IC产品分类
export enum ICProductCategory {
  CPU = 'CPU',
  GPU = 'GPU',
  MCU = 'MCU',
  SENSOR = 'SENSOR',
  POWER = 'POWER',
  RF = 'RF',
  ANALOG = 'ANALOG',
  MEMORY = 'MEMORY',
  INTERFACE = 'INTERFACE',
  OTHER = 'OTHER'
}

// 封装类型
export enum PackageType {
  // 引脚插入式
  DIP = 'DIP',
  SIP = 'SIP',

  // 表面贴装式
  QFP = 'QFP',
  TQFP = 'TQFP',
  LQFP = 'LQFP',
  QFN = 'QFN',
  DFN = 'DFN',
  SOP = 'SOP',
  SOIC = 'SOIC',
  TSOP = 'TSOP',
  SSOP = 'SSOP',

  // 球栅阵列
  BGA = 'BGA',
  PBGA = 'PBGA',
  TBGA = 'TBGA',
  FBGA = 'FBGA',

  // 芯片级封装
  CSP = 'CSP',
  WLCSP = 'WLCSP',
  FCBGA = 'FCBGA',

  // 特殊封装
  COB = 'COB',
  COG = 'COG',
  TO = 'TO',
  OTHER = 'OTHER'
}

// 工艺节点
export enum ProcessNode {
  N_3NM = '3nm',
  N_5NM = '5nm',
  N_7NM = '7nm',
  N_10NM = '10nm',
  N_14NM = '14nm',
  N_16NM = '16nm',
  N_22NM = '22nm',
  N_28NM = '28nm',
  N_40NM = '40nm',
  N_45NM = '45nm',
  N_65NM = '65nm',
  N_90NM = '90nm',
  N_130NM = '130nm',
  N_180NM = '180nm',
  N_350NM = '350nm',
  OTHER = 'OTHER'
}

// 应用领域
export enum ApplicationField {
  AUTOMOTIVE = 'AUTOMOTIVE',
  CONSUMER = 'CONSUMER',
  INDUSTRIAL = 'INDUSTRIAL',
  COMMUNICATION = 'COMMUNICATION',
  MEDICAL = 'MEDICAL',
  AEROSPACE = 'AEROSPACE',
  IOT = 'IOT',
  SERVER = 'SERVER',
  OTHER = 'OTHER'
}

// 产品状态
export enum ProductStatus {
  DEVELOPMENT = 'DEVELOPMENT',
  QUALIFICATION = 'QUALIFICATION',
  MASS_PRODUCTION = 'MASS_PRODUCTION',
  EOL = 'EOL',
  OBSOLETE = 'OBSOLETE'
}

// 基板类型
export enum SubstrateType {
  FR4 = 'FR4',
  CERAMIC = 'CERAMIC',
  GLASS = 'GLASS',
  FLEX = 'FLEX',
  RIGID_FLEX = 'RIGID_FLEX',
  OTHER = 'OTHER'
}

// 金线规格
export enum WireBondType {
  AU_15UM = 'AU_15UM',
  AU_20UM = 'AU_20UM',
  AU_25UM = 'AU_25UM',
  CU_18UM = 'CU_18UM',
  CU_23UM = 'CU_23UM',
  AL_25UM = 'AL_25UM',
  OTHER = 'OTHER'
}

// ================================
// 产品规格定义接口
// ================================

// 技术规格
export interface TechnicalSpecification {
  processNode: ProcessNode
  dieSizeX: number // Die尺寸X (mm)
  dieSizeY: number // Die尺寸Y (mm)
  pinCount: number // 引脚数
  packageSizeX: number // 封装尺寸X (mm)
  packageSizeY: number // 封装尺寸Y (mm)
  packageHeight: number // 封装高度 (mm)
  operatingTempMin: number // 工作温度下限 (°C)
  operatingTempMax: number // 工作温度上限 (°C)
  supplyVoltageMin: number // 供电电压下限 (V)
  supplyVoltageMax: number // 供电电压上限 (V)
  powerConsumption: number // 功耗 (W)
  frequency: number // 频率 (MHz)
}

// 封装信息
export interface PackageInfo {
  packageType: PackageType
  substrateType: SubstrateType
  wireBondType: WireBondType
  leadFrame: string // 引线框架
  moldingCompound: string // 封装料
  platingType: string // 电镀类型
  markingMethod: string // 标识方式
}

// CP测试参数
export interface CPTestParameters {
  testTemperature: number[] // 测试温度点 (°C)
  testVoltage: number[] // 测试电压 (V)
  testCurrent: number[] // 测试电流 (A)
  testItems: string[] // 测试项目
  yieldTarget: number // 良率目标 (%)
  testTime: number // 单片测试时间 (s)
}

// FT测试参数
export interface FTTestParameters {
  functionalTests: string[] // 功能测试项目
  electricalTests: string[] // 电气测试项目
  thermalTests: string[] // 热测试项目
  reliabilityTests: string[] // 可靠性测试
  burnInHours: number // 老化时间 (h)
  burnInTemp: number // 老化温度 (°C)
  finalYieldTarget: number // 最终良率目标 (%)
}

// 成本参数
export interface CostParameters {
  // 材料成本
  dieCost: number // Die成本 (元)
  substrateCost: number // 基板成本 (元)
  wireCost: number // 金线成本 (元)
  moldingCost: number // 封装料成本 (元)

  // 工艺成本
  cpTestCost: number // CP测试成本 (元)
  assemblyCost: number // 封装成本 (元)
  ftTestCost: number // FT测试成本 (元)

  // 其他成本
  packagingCost: number // 包装成本 (元)
  shippingCost: number // 运输成本 (元)
  overheadRate: number // 管理费用率 (%)
  profitMargin: number // 利润率 (%)
}

// 质量要求
export interface QualityRequirements {
  defectRate: number // 缺陷率要求 (ppm)
  reliabilityLevel: string // 可靠性等级
  certificationStandards: string[] // 认证标准
  qualityGrade: string // 质量等级
  inspectionLevel: string // 检查水平
}

// ================================
// 产品主数据主接口
// ================================

export interface Product extends BaseEntity {
  // 基本信息
  productCode: string // 产品代码
  productName: string // 产品名称
  customerCode?: string // 客户代码
  customer?: Customer // 客户信息
  category: ICProductCategory // 产品分类
  applicationField: ApplicationField // 应用领域
  status: ProductStatus // 产品状态

  // 描述信息
  description: string // 产品描述
  features: string[] // 产品特性
  applications: string[] // 主要应用

  // 技术规格
  technicalSpec: TechnicalSpecification

  // 封装信息
  packageInfo: PackageInfo

  // 测试参数
  cpTestParams: CPTestParameters
  ftTestParams: FTTestParameters

  // 质量要求
  qualityReq: QualityRequirements

  // 成本信息
  costParams: CostParameters

  // 版本控制
  version: string // 版本号
  revisionHistory: ProductRevision[] // 修订历史

  // 附件
  attachments?: ProductAttachment[] // 相关文档

  // 管理信息
  engineerName: string // 负责工程师
  businessName: string // 负责商务
  approvalStatus: string // 审批状态
  effectiveDate?: Date // 生效日期
  expiryDate?: Date // 失效日期
}

// 产品修订历史
export interface ProductRevision {
  id: string
  version: string // 版本号
  changeDescription: string // 变更描述
  changedBy: string // 变更人
  changedAt: Date // 变更时间
  approvedBy?: string // 审批人
  approvedAt?: Date // 审批时间
  reason: string // 变更原因
}

// 产品附件
export interface ProductAttachment {
  id: string
  fileName: string // 文件名
  fileType: string // 文件类型
  fileSize: number // 文件大小
  filePath: string // 文件路径
  uploadBy: string // 上传人
  uploadAt: Date // 上传时间
  description: string // 文件描述
}

// ================================
// API相关接口
// ================================

// 产品查询参数
export interface ProductQueryParams extends PaginationQuery {
  productCode?: string
  productName?: string
  customerCode?: string
  category?: ICProductCategory
  packageType?: PackageType
  processNode?: ProcessNode
  applicationField?: ApplicationField
  status?: ProductStatus
  engineerName?: string
  businessName?: string
  startDate?: string
  endDate?: string
}

// 产品列表响应
export interface ProductListResponse extends PaginationResponse {
  data: Product[]
  summary: {
    totalProducts: number
    activeProducts: number
    developmentProducts: number
    eolProducts: number
  }
}

// 产品创建/更新数据
export interface CreateProductData {
  productCode: string
  productName: string
  customerCode?: string
  category: ICProductCategory
  applicationField: ApplicationField
  description: string
  features: string[]
  applications: string[]
  technicalSpec: TechnicalSpecification
  packageInfo: PackageInfo
  cpTestParams: CPTestParameters
  ftTestParams: FTTestParameters
  qualityReq: QualityRequirements
  costParams: CostParameters
  engineerName: string
  businessName: string
  effectiveDate?: Date
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: string
  version: string
  changeDescription: string
  reason: string
}

// 产品统计数据
export interface ProductStatistics {
  categoryStats: {
    category: ICProductCategory
    count: number
    percentage: number
  }[]
  packageStats: {
    packageType: PackageType
    count: number
    percentage: number
  }[]
  statusStats: {
    status: ProductStatus
    count: number
    percentage: number
  }[]
  processNodeStats: {
    processNode: ProcessNode
    count: number
    percentage: number
  }[]
}

// 产品导入/导出
export interface ProductImportResult {
  totalRows: number
  successRows: number
  failedRows: number
  errors: {
    row: number
    field: string
    message: string
  }[]
}

export interface ProductExportParams {
  productCodes?: string[]
  category?: ICProductCategory
  status?: ProductStatus
  includeSpecs?: boolean
  includeCosts?: boolean
  format: 'excel' | 'csv'
}
