/**
 * 监控数据管理 Composable
 * 提供实时数据获取、WebSocket连接、自动刷新等功能
 */

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
// Element Plus组件通过unplugin-auto-import自动导入
import { monitoringApi } from '@/api/monitoring'
import type {
  ProductionKPI,
  EquipmentStatus,
  QualityKPI,
  SystemStatusSummary,
  RealtimeMessage,
  MonitoringConfig,
  TimeRange,
  RefreshInterval,
  DisplayMode,
  MonitoringPanel,
  ChartSeries
} from '@/types/monitoring'

/**
 * 监控数据管理Hook
 */
export function useMonitoring() {
  // 状态管理
  const loading = ref(false)
  const connected = ref(false)
  const error = ref<string>('')

  // 数据状态
  const productionKPI = ref<ProductionKPI>()
  const equipmentStatus = ref<EquipmentStatus[]>([])
  const qualityKPI = ref<QualityKPI>()
  const systemStatus = ref<SystemStatusSummary>()
  const realtimeMessages = ref<RealtimeMessage[]>([])
  const productionTrend = ref<ChartSeries[]>([])

  // 配置状态
  const config = reactive<MonitoringConfig>({
    currentPanel: 'production',
    displayMode: 'normal',
    refreshInterval: 10,
    soundAlertEnabled: true,
    timeRange: '24h'
  })

  // WebSocket连接
  let ws: WebSocket | null = null
  let heartbeatTimer: number | null = null
  let reconnectTimer: number | null = null
  let autoRefreshTimer: number | null = null

  // 计算属性
  const isLoading = computed(() => loading.value)
  const isConnected = computed(() => connected.value)
  const hasError = computed(() => !!error.value)

  // 活跃告警数量
  const activeAlarmsCount = computed(() => {
    const status = systemStatus.value
    if (!status) return 0
    return status.activeAlarms.critical + status.activeAlarms.warning + status.activeAlarms.info
  })

  // 设备状态统计
  const equipmentSummary = computed(() => {
    const equipment = equipmentStatus.value
    if (!equipment.length)
      return { total: 0, running: 0, idle: 0, maintenance: 0, alarm: 0, offline: 0 }

    return {
      total: equipment.length,
      running: equipment.filter(e => e.status === 'running').length,
      idle: equipment.filter(e => e.status === 'idle').length,
      maintenance: equipment.filter(e => e.status === 'maintenance').length,
      alarm: equipment.filter(e => e.status === 'alarm').length,
      offline: equipment.filter(e => e.status === 'offline').length
    }
  })

  // 未读消息数量
  const unreadMessagesCount = computed(() => {
    return realtimeMessages.value.filter(msg => !msg.acknowledged).length
  })

  /**
   * 初始化监控数据
   */
  const initializeMonitoring = async () => {
    loading.value = true
    error.value = ''

    try {
      await loadAllData()
      setupWebSocket()
      startAutoRefresh()
      connected.value = true
    } catch (err) {
      error.value = err instanceof Error ? err.message : '初始化失败'
      ElMessage.error('监控系统初始化失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 加载所有数据
   */
  const loadAllData = async () => {
    const [prodKPI, equipStatus, qualKPI, sysStatus, messages, prodTrend] = await Promise.all([
      monitoringApi.getProductionKPI(),
      monitoringApi.getEquipmentStatus(),
      monitoringApi.getQualityKPI(),
      monitoringApi.getSystemStatusSummary(),
      monitoringApi.getRealtimeMessages(),
      monitoringApi.getProductionTrend(config.timeRange)
    ])

    productionKPI.value = prodKPI
    equipmentStatus.value = equipStatus
    qualityKPI.value = qualKPI
    systemStatus.value = sysStatus
    realtimeMessages.value = messages
    productionTrend.value = prodTrend
  }

  /**
   * 刷新生产数据
   */
  const refreshProductionData = async () => {
    try {
      const [kpi, trend] = await Promise.all([
        monitoringApi.getProductionKPI(),
        monitoringApi.getProductionTrend(config.timeRange)
      ])
      productionKPI.value = kpi
      productionTrend.value = trend
    } catch (err) {
      console.error('刷新生产数据失败:', err)
    }
  }

  /**
   * 刷新设备数据
   */
  const refreshEquipmentData = async () => {
    try {
      const status = await monitoringApi.getEquipmentStatus()
      equipmentStatus.value = status
    } catch (err) {
      console.error('刷新设备数据失败:', err)
    }
  }

  /**
   * 刷新质量数据
   */
  const refreshQualityData = async () => {
    try {
      const kpi = await monitoringApi.getQualityKPI()
      qualityKPI.value = kpi
    } catch (err) {
      console.error('刷新质量数据失败:', err)
    }
  }

  /**
   * 刷新系统状态
   */
  const refreshSystemStatus = async () => {
    try {
      const status = await monitoringApi.getSystemStatusSummary()
      systemStatus.value = status
    } catch (err) {
      console.error('刷新系统状态失败:', err)
    }
  }

  /**
   * 刷新实时消息
   */
  const refreshMessages = async () => {
    try {
      const messages = await monitoringApi.getRealtimeMessages()
      realtimeMessages.value = messages
    } catch (err) {
      console.error('刷新消息失败:', err)
    }
  }

  /**
   * 全量刷新数据
   */
  const refreshAllData = async () => {
    loading.value = true
    try {
      await loadAllData()
      ElMessage.success('数据刷新成功')
    } catch (err) {
      ElMessage.error('数据刷新失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置WebSocket连接
   */
  const setupWebSocket = () => {
    if (typeof window === 'undefined') return

    // 开发环境下跳过WebSocket连接
    if (import.meta.env.DEV && !import.meta.env.VITE_ENABLE_WEBSOCKET) {
      console.log('[开发模式] 跳过WebSocket连接，使用模拟数据')
      return
    }

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}/api/ws/monitoring`

    try {
      ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        connected.value = true
        startHeartbeat()
        ElMessage.success('实时数据连接已建立')
      }

      ws.onmessage = event => {
        try {
          const data = JSON.parse(event.data)
          handleWebSocketMessage(data)
        } catch (err) {
          console.error('WebSocket消息解析失败:', err)
        }
      }

      ws.onclose = () => {
        connected.value = false
        stopHeartbeat()
        // 开发环境不尝试重连
        if (!import.meta.env.DEV) {
          scheduleReconnect()
        }
      }

      ws.onerror = err => {
        // 开发环境下静默处理WebSocket错误
        if (import.meta.env.DEV) {
          console.warn('[开发模式] WebSocket连接失败，这是正常的')
        } else {
          console.error('WebSocket连接错误:', err)
        }
        connected.value = false
      }
    } catch (err) {
      if (import.meta.env.DEV) {
        console.warn('[开发模式] WebSocket初始化失败，使用模拟数据:', err.message)
      } else {
        console.error('WebSocket初始化失败:', err)
      }
    }
  }

  /**
   * 处理WebSocket消息
   */
  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'production_update':
        productionKPI.value = data.payload
        break
      case 'equipment_update':
        updateEquipmentStatus(data.payload)
        break
      case 'quality_update':
        qualityKPI.value = data.payload
        break
      case 'system_status':
        systemStatus.value = data.payload
        break
      case 'realtime_message':
        addRealtimeMessage(data.payload)
        break
      case 'alarm':
        handleAlarmMessage(data.payload)
        break
      default:
        console.warn('未知的WebSocket消息类型:', data.type)
    }
  }

  /**
   * 更新设备状态
   */
  const updateEquipmentStatus = (updatedEquipment: EquipmentStatus) => {
    const index = equipmentStatus.value.findIndex(
      eq => eq.equipmentId === updatedEquipment.equipmentId
    )
    if (index >= 0) {
      equipmentStatus.value[index] = updatedEquipment
    } else {
      equipmentStatus.value.push(updatedEquipment)
    }
  }

  /**
   * 添加实时消息
   */
  const addRealtimeMessage = (message: RealtimeMessage) => {
    realtimeMessages.value.unshift(message)
    // 保持最多100条消息
    if (realtimeMessages.value.length > 100) {
      realtimeMessages.value = realtimeMessages.value.slice(0, 100)
    }

    // 高优先级消息提示
    if (message.priority === 'high') {
      ElMessage({
        type: message.type === 'error' ? 'error' : 'warning',
        message: message.message,
        duration: 5000
      })

      // 声音提醒
      if (config.soundAlertEnabled) {
        playAlertSound()
      }
    }
  }

  /**
   * 处理告警消息
   */
  const handleAlarmMessage = (alarm: any) => {
    ElMessage({
      type: alarm.level === 'critical' ? 'error' : 'warning',
      title: '设备告警',
      message: `${alarm.equipmentName}: ${alarm.message}`,
      duration: 0, // 不自动关闭
      showClose: true
    })

    if (config.soundAlertEnabled) {
      playAlertSound()
    }
  }

  /**
   * 播放提示音
   */
  const playAlertSound = () => {
    try {
      const audio = new Audio('/sounds/alert.mp3')
      audio.volume = 0.3
      audio.play().catch(() => {
        // 忽略播放失败
      })
    } catch (err) {
      // 忽略音频播放错误
    }
  }

  /**
   * 启动心跳检测
   */
  const startHeartbeat = () => {
    heartbeatTimer = window.setInterval(() => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000) // 30秒心跳
  }

  /**
   * 停止心跳检测
   */
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  /**
   * 计划重连
   */
  const scheduleReconnect = () => {
    if (reconnectTimer) return

    reconnectTimer = window.setTimeout(() => {
      reconnectTimer = null
      setupWebSocket()
    }, 5000) // 5秒后重连
  }

  /**
   * 启动自动刷新
   */
  const startAutoRefresh = () => {
    stopAutoRefresh()

    autoRefreshTimer = window.setInterval(async () => {
      // 如果WebSocket未连接，则使用轮询方式更新数据
      if (!connected.value) {
        await refreshAllData()
      }
    }, config.refreshInterval * 1000)
  }

  /**
   * 停止自动刷新
   */
  const stopAutoRefresh = () => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer)
      autoRefreshTimer = null
    }
  }

  /**
   * 切换面板
   */
  const switchPanel = (panel: MonitoringPanel) => {
    config.currentPanel = panel
  }

  /**
   * 设置显示模式
   */
  const setDisplayMode = (mode: DisplayMode) => {
    config.displayMode = mode
  }

  /**
   * 设置刷新间隔
   */
  const setRefreshInterval = (interval: RefreshInterval) => {
    config.refreshInterval = interval
    startAutoRefresh()
  }

  /**
   * 设置时间范围
   */
  const setTimeRange = (range: TimeRange) => {
    config.timeRange = range
    refreshProductionData() // 重新加载趋势数据
  }

  /**
   * 确认消息
   */
  const acknowledgeMessage = (messageId: string) => {
    const message = realtimeMessages.value.find(msg => msg.id === messageId)
    if (message) {
      message.acknowledged = true
    }
  }

  /**
   * 清空所有消息
   */
  const clearAllMessages = () => {
    realtimeMessages.value = []
  }

  /**
   * 导出数据
   */
  const exportData = (type: 'production' | 'equipment' | 'quality' | 'all') => {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    let data: any = {}
    const filename = `monitoring_${type}_${timestamp}.json`

    switch (type) {
      case 'production':
        data = { productionKPI: productionKPI.value, productionTrend: productionTrend.value }
        break
      case 'equipment':
        data = { equipmentStatus: equipmentStatus.value }
        break
      case 'quality':
        data = { qualityKPI: qualityKPI.value }
        break
      case 'all':
        data = {
          productionKPI: productionKPI.value,
          productionTrend: productionTrend.value,
          equipmentStatus: equipmentStatus.value,
          qualityKPI: qualityKPI.value,
          systemStatus: systemStatus.value,
          messages: realtimeMessages.value
        }
        break
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  // 生命周期管理
  onMounted(() => {
    initializeMonitoring()
  })

  onUnmounted(() => {
    if (ws) {
      ws.close()
    }
    stopHeartbeat()
    stopAutoRefresh()
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
    }
  })

  return {
    // 状态
    loading: isLoading,
    connected: isConnected,
    error: computed(() => error.value),

    // 数据
    productionKPI: computed(() => productionKPI.value),
    equipmentStatus: computed(() => equipmentStatus.value),
    qualityKPI: computed(() => qualityKPI.value),
    systemStatus: computed(() => systemStatus.value),
    realtimeMessages: computed(() => realtimeMessages.value),
    productionTrend: computed(() => productionTrend.value),

    // 计算属性
    activeAlarmsCount,
    equipmentSummary,
    unreadMessagesCount,

    // 配置
    config,

    // 方法
    refreshAllData,
    refreshProductionData,
    refreshEquipmentData,
    refreshQualityData,
    refreshSystemStatus,
    refreshMessages,
    switchPanel,
    setDisplayMode,
    setRefreshInterval,
    setTimeRange,
    acknowledgeMessage,
    clearAllMessages,
    exportData
  }
}
