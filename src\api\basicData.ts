/**
 * IC封测CIM系统 - 基础数据管理API
 * Basic Data Management API Services
 */

import type {
  Product,
  Equipment,
  ProcessParameter,
  QualityStandard,
  Supplier,
  PackageTypeInfo,
  BasicDataQueryParams,
  BasicDataListResponse,
  BasicDataStats
} from '@/types/basicData'

import {
  mockProducts,
  mockEquipment,
  mockProcessParameters,
  mockQualityStandards,
  mockSuppliers,
  packageTypeInfos,
  mockBasicDataStats,
  generateMoreProducts
} from '@/utils/mockData/basicData'

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// ===== 产品管理API =====

/**
 * 获取产品列表
 */
export async function getProducts(
  params: BasicDataQueryParams = {}
): Promise<BasicDataListResponse<Product>> {
  await delay(300)

  let filteredProducts = [...mockProducts]

  // 关键词搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredProducts = filteredProducts.filter(
      product =>
        product.productCode.toLowerCase().includes(keyword) ||
        product.productName.toLowerCase().includes(keyword)
    )
  }

  // 分类筛选
  if (params.category) {
    filteredProducts = filteredProducts.filter(product => product.category === params.category)
  }

  // 状态筛选
  if (params.status) {
    filteredProducts = filteredProducts.filter(product => product.status === params.status)
  }

  // 排序
  if (params.sortBy) {
    filteredProducts.sort((a, b) => {
      let aValue: any = a[params.sortBy as keyof Product]
      let bValue: any = b[params.sortBy as keyof Product]

      if (params.sortBy === 'createdAt' || params.sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      if (aValue < bValue) return params.sortOrder === 'desc' ? 1 : -1
      if (aValue > bValue) return params.sortOrder === 'desc' ? -1 : 1
      return 0
    })
  }

  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 20
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex)

  return {
    data: paginatedProducts,
    total: filteredProducts.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredProducts.length / pageSize)
  }
}

/**
 * 根据ID获取产品详情
 */
export async function getProductById(id: string): Promise<Product | null> {
  await delay(200)
  const product = mockProducts.find(p => p.id === id)
  return product || null
}

/**
 * 创建产品
 */
export async function createProduct(
  data: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>
): Promise<Product> {
  await delay(500)

  const newProduct: Product = {
    ...data,
    id: `product_${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  mockProducts.push(newProduct)
  return newProduct
}

/**
 * 更新产品
 */
export async function updateProduct(id: string, data: Partial<Product>): Promise<Product> {
  await delay(400)

  const productIndex = mockProducts.findIndex(p => p.id === id)
  if (productIndex === -1) {
    throw new Error('产品不存在')
  }

  const updatedProduct: Product = {
    ...mockProducts[productIndex],
    ...data,
    updatedAt: new Date().toISOString()
  }

  mockProducts[productIndex] = updatedProduct
  return updatedProduct
}

/**
 * 删除产品
 */
export async function deleteProduct(id: string): Promise<boolean> {
  await delay(300)

  const productIndex = mockProducts.findIndex(p => p.id === id)
  if (productIndex === -1) {
    return false
  }

  mockProducts.splice(productIndex, 1)
  return true
}

// ===== 设备管理API =====

/**
 * 获取设备列表
 */
export async function getEquipment(
  params: BasicDataQueryParams = {}
): Promise<BasicDataListResponse<Equipment>> {
  await delay(300)

  let filteredEquipment = [...mockEquipment]

  // 关键词搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredEquipment = filteredEquipment.filter(
      equipment =>
        equipment.equipmentCode.toLowerCase().includes(keyword) ||
        equipment.equipmentName.toLowerCase().includes(keyword) ||
        equipment.model.toLowerCase().includes(keyword)
    )
  }

  // 类型筛选
  if (params.category) {
    filteredEquipment = filteredEquipment.filter(equipment => equipment.type === params.category)
  }

  // 状态筛选
  if (params.status) {
    filteredEquipment = filteredEquipment.filter(equipment => equipment.status === params.status)
  }

  // 排序
  if (params.sortBy) {
    filteredEquipment.sort((a, b) => {
      let aValue: any = a[params.sortBy as keyof Equipment]
      let bValue: any = b[params.sortBy as keyof Equipment]

      if (params.sortBy === 'createdAt' || params.sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      if (aValue < bValue) return params.sortOrder === 'desc' ? 1 : -1
      if (aValue > bValue) return params.sortOrder === 'desc' ? -1 : 1
      return 0
    })
  }

  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 20
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedEquipment = filteredEquipment.slice(startIndex, endIndex)

  return {
    data: paginatedEquipment,
    total: filteredEquipment.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredEquipment.length / pageSize)
  }
}

/**
 * 根据ID获取设备详情
 */
export async function getEquipmentById(id: string): Promise<Equipment | null> {
  await delay(200)
  const equipment = mockEquipment.find(e => e.id === id)
  return equipment || null
}

/**
 * 创建设备
 */
export async function createEquipment(
  data: Omit<Equipment, 'id' | 'createdAt' | 'updatedAt'>
): Promise<Equipment> {
  await delay(500)

  const newEquipment: Equipment = {
    ...data,
    id: `equip_${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  mockEquipment.push(newEquipment)
  return newEquipment
}

/**
 * 更新设备
 */
export async function updateEquipment(id: string, data: Partial<Equipment>): Promise<Equipment> {
  await delay(400)

  const equipmentIndex = mockEquipment.findIndex(e => e.id === id)
  if (equipmentIndex === -1) {
    throw new Error('设备不存在')
  }

  const updatedEquipment: Equipment = {
    ...mockEquipment[equipmentIndex],
    ...data,
    updatedAt: new Date().toISOString()
  }

  mockEquipment[equipmentIndex] = updatedEquipment
  return updatedEquipment
}

// ===== 工艺参数管理API =====

/**
 * 获取工艺参数列表
 */
export async function getProcessParameters(
  params: BasicDataQueryParams = {}
): Promise<BasicDataListResponse<ProcessParameter>> {
  await delay(300)

  let filteredParameters = [...mockProcessParameters]

  // 关键词搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredParameters = filteredParameters.filter(
      param =>
        param.parameterName.toLowerCase().includes(keyword) ||
        param.parameterCode.toLowerCase().includes(keyword) ||
        param.description.toLowerCase().includes(keyword)
    )
  }

  // 工艺类型筛选
  if (params.category) {
    filteredParameters = filteredParameters.filter(param => param.processType === params.category)
  }

  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 20
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedParameters = filteredParameters.slice(startIndex, endIndex)

  return {
    data: paginatedParameters,
    total: filteredParameters.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredParameters.length / pageSize)
  }
}

/**
 * 根据ID获取工艺参数详情
 */
export async function getProcessParameterById(id: string): Promise<ProcessParameter | null> {
  await delay(200)
  const parameter = mockProcessParameters.find(p => p.id === id)
  return parameter || null
}

// ===== 质量标准管理API =====

/**
 * 获取质量标准列表
 */
export async function getQualityStandards(
  params: BasicDataQueryParams = {}
): Promise<BasicDataListResponse<QualityStandard>> {
  await delay(300)

  let filteredStandards = [...mockQualityStandards]

  // 关键词搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredStandards = filteredStandards.filter(
      standard =>
        standard.standardCode.toLowerCase().includes(keyword) ||
        standard.standardName.toLowerCase().includes(keyword)
    )
  }

  // 标准类型筛选
  if (params.category) {
    filteredStandards = filteredStandards.filter(standard => standard.type === params.category)
  }

  // 状态筛选
  if (params.status) {
    filteredStandards = filteredStandards.filter(standard => standard.status === params.status)
  }

  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 20
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedStandards = filteredStandards.slice(startIndex, endIndex)

  return {
    data: paginatedStandards,
    total: filteredStandards.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredStandards.length / pageSize)
  }
}

// ===== 供应商管理API =====

/**
 * 获取供应商列表
 */
export async function getSuppliers(
  params: BasicDataQueryParams = {}
): Promise<BasicDataListResponse<Supplier>> {
  await delay(300)

  let filteredSuppliers = [...mockSuppliers]

  // 关键词搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredSuppliers = filteredSuppliers.filter(
      supplier =>
        supplier.supplierCode.toLowerCase().includes(keyword) ||
        supplier.supplierName.toLowerCase().includes(keyword) ||
        (supplier.supplierNameEn && supplier.supplierNameEn.toLowerCase().includes(keyword))
    )
  }

  // 供应商类型筛选
  if (params.category) {
    filteredSuppliers = filteredSuppliers.filter(supplier => supplier.type === params.category)
  }

  // 状态筛选
  if (params.status) {
    filteredSuppliers = filteredSuppliers.filter(supplier => supplier.status === params.status)
  }

  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 20
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedSuppliers = filteredSuppliers.slice(startIndex, endIndex)

  return {
    data: paginatedSuppliers,
    total: filteredSuppliers.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredSuppliers.length / pageSize)
  }
}

// ===== 封装类型信息API =====

/**
 * 获取封装类型信息列表
 */
export async function getPackageTypeInfos(): Promise<PackageTypeInfo[]> {
  await delay(200)
  return packageTypeInfos
}

/**
 * 根据封装类型获取详细信息
 */
export async function getPackageTypeInfo(packageType: string): Promise<PackageTypeInfo | null> {
  await delay(150)
  const info = packageTypeInfos.find(info => info.type === packageType)
  return info || null
}

// ===== 基础数据统计API =====

/**
 * 获取基础数据统计信息
 */
export async function getBasicDataStats(): Promise<BasicDataStats> {
  await delay(200)
  return mockBasicDataStats
}

// ===== 导出功能 =====

/**
 * 导出产品数据
 */
export async function exportProducts(params: BasicDataQueryParams = {}): Promise<Blob> {
  await delay(1000)

  const { data } = await getProducts(params)

  const headers = [
    '产品代码',
    '产品名称',
    '产品分类',
    '封装类型',
    '工作电压',
    '工作温度',
    '引脚数',
    '芯片尺寸',
    '状态',
    '创建时间'
  ]

  const csvContent = [
    headers.join(','),
    ...data.map(product =>
      [
        product.productCode,
        product.productName,
        product.category,
        product.packageTypes.join(';'),
        product.specifications.workingVoltage,
        product.specifications.workingTemperature,
        product.specifications.pinCount,
        product.specifications.dieSize,
        product.status,
        product.createdAt.split('T')[0]
      ].join(',')
    )
  ].join('\n')

  return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
}

/**
 * 导出设备数据
 */
export async function exportEquipment(params: BasicDataQueryParams = {}): Promise<Blob> {
  await delay(1000)

  const { data } = await getEquipment(params)

  const headers = [
    '设备编号',
    '设备名称',
    '设备类型',
    '型号',
    '制造商',
    '位置',
    '状态',
    '产能(UPH)',
    '运行小时数',
    '安装日期'
  ]

  const csvContent = [
    headers.join(','),
    ...data.map(equipment =>
      [
        equipment.equipmentCode,
        equipment.equipmentName,
        equipment.type,
        equipment.model,
        equipment.manufacturer,
        `${equipment.location.building}-${equipment.location.room}`,
        equipment.status,
        equipment.specifications.capacity,
        equipment.operationalInfo.operatingHours,
        equipment.operationalInfo.installDate.split('T')[0]
      ].join(',')
    )
  ].join('\n')

  return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
}

// ===== 搜索建议API =====

/**
 * 搜索产品建议
 */
export async function searchProductSuggestions(
  keyword: string
): Promise<Array<{ id: string; code: string; name: string }>> {
  await delay(150)

  if (!keyword || keyword.length < 2) {
    return []
  }

  const suggestions = mockProducts
    .filter(
      product =>
        product.productCode.toLowerCase().includes(keyword.toLowerCase()) ||
        product.productName.toLowerCase().includes(keyword.toLowerCase())
    )
    .slice(0, 10)
    .map(product => ({
      id: product.id,
      code: product.productCode,
      name: product.productName
    }))

  return suggestions
}

/**
 * 搜索设备建议
 */
export async function searchEquipmentSuggestions(
  keyword: string
): Promise<Array<{ id: string; code: string; name: string }>> {
  await delay(150)

  if (!keyword || keyword.length < 2) {
    return []
  }

  const suggestions = mockEquipment
    .filter(
      equipment =>
        equipment.equipmentCode.toLowerCase().includes(keyword.toLowerCase()) ||
        equipment.equipmentName.toLowerCase().includes(keyword.toLowerCase()) ||
        equipment.model.toLowerCase().includes(keyword.toLowerCase())
    )
    .slice(0, 10)
    .map(equipment => ({
      id: equipment.id,
      code: equipment.equipmentCode,
      name: equipment.equipmentName
    }))

  return suggestions
}
