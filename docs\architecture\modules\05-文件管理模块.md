# 文件管理模块设计

## 1. 模块概述

### 1.1 模块定位
文件管理模块为CIM系统提供统一的文件存储、管理、预览和权限控制服务，支持各业务模块的文件上传、下载、版本控制和协作需求，确保文件安全性和可追溯性。

### 1.2 复用价值
- **统一存储**：所有文件通过统一接口管理，支持多种存储后端
- **版本控制**：自动文件版本管理和历史追踪
- **权限控制**：细粒度文件访问权限控制
- **批量操作**：支持文件批量上传、下载和处理

### 1.3 应用场景覆盖
```
文件管理模块应用场景
├── 技术文档管理
│   ├── 工艺文件管理
│   ├── 设备手册管理
│   ├── 作业指导书管理
│   └── 技术规范文档
├── 质量记录管理
│   ├── 检验报告文件
│   ├── 证书文件管理
│   ├── 不合格品记录
│   └── 审核记录文件
├── 生产资料管理
│   ├── 产品图纸文件
│   ├── BOM清单文件
│   ├── 生产计划文件
│   └── 工单附件管理
├── 系统配置文件
│   ├── 系统配置文件
│   ├── 报表模板文件
│   ├── 流程定义文件
│   └── 数据导入模板
└── 多媒体资源
    ├── 产品照片管理
    ├── 视频培训资料
    ├── 音频文件管理
    └── 截图文件管理
```

## 2. 技术架构

### 2.1 架构设计
```
文件管理模块架构
├── 文件上传网关          # 文件上传接入层
├── 文件存储引擎          # 多种存储后端适配
├── 元数据管理中心        # 文件元信息管理
├── 版本控制系统          # 文件版本管理
├── 权限控制中心          # 文件访问权限
├── 预览转换服务          # 文件格式转换预览
├── 缓存加速层            # 文件访问缓存
└── 搜索索引引擎          # 文件内容搜索
```

### 2.2 核心数据模型

#### 2.2.1 文件基础信息
```sql
-- 文件信息表
CREATE TABLE file_info (
    file_id VARCHAR(30) PRIMARY KEY,
    file_name VARCHAR(500),                 -- 文件名
    original_name VARCHAR(500),             -- 原始文件名
    file_path VARCHAR(1000),                -- 存储路径
    file_size BIGINT,                       -- 文件大小(字节)
    mime_type VARCHAR(100),                 -- MIME类型
    file_extension VARCHAR(20),             -- 文件扩展名
    md5_hash VARCHAR(32),                   -- MD5校验值
    sha256_hash VARCHAR(64),                -- SHA256校验值
    storage_type ENUM('local','oss','s3','minio'), -- 存储类型
    bucket_name VARCHAR(100),               -- 存储桶名称
    object_key VARCHAR(500),                -- 对象键
    business_type VARCHAR(50),              -- 业务类型
    business_id VARCHAR(100),               -- 业务关联ID
    upload_user_id VARCHAR(20),             -- 上传用户
    upload_time TIMESTAMP,                  -- 上传时间
    status ENUM('uploading','active','deleted','archived'), -- 状态
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_business (business_type, business_id),
    INDEX idx_upload_user (upload_user_id, upload_time),
    INDEX idx_hash (md5_hash),
    INDEX idx_mime_type (mime_type)
);

-- 文件版本表
CREATE TABLE file_versions (
    version_id VARCHAR(30) PRIMARY KEY,
    file_id VARCHAR(30),                    -- 文件ID
    version_number INT,                     -- 版本号
    file_path VARCHAR(1000),                -- 版本文件路径
    file_size BIGINT,                       -- 版本文件大小
    md5_hash VARCHAR(32),                   -- 版本文件MD5
    change_description TEXT,                -- 变更描述
    created_by VARCHAR(20),                 -- 创建人
    created_at TIMESTAMP,                   -- 创建时间
    is_current BOOLEAN DEFAULT FALSE,       -- 是否为当前版本
    
    INDEX idx_file_version (file_id, version_number),
    INDEX idx_current (file_id, is_current)
);

-- 文件权限表
CREATE TABLE file_permissions (
    permission_id VARCHAR(30) PRIMARY KEY,
    file_id VARCHAR(30),                    -- 文件ID
    permission_type ENUM('user','role','department'), -- 权限类型
    target_id VARCHAR(20),                  -- 目标ID
    permissions VARCHAR(20),                -- 权限位(read,write,delete,share)
    granted_by VARCHAR(20),                 -- 授权人
    granted_at TIMESTAMP,                   -- 授权时间
    expires_at TIMESTAMP,                   -- 过期时间
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_file_target (file_id, permission_type, target_id)
);
```

#### 2.2.2 文件分类和标签
```sql
-- 文件分类表
CREATE TABLE file_categories (
    category_id VARCHAR(30) PRIMARY KEY,
    parent_category_id VARCHAR(30),         -- 父分类ID
    category_name VARCHAR(100),             -- 分类名称
    category_path VARCHAR(500),             -- 分类路径
    sort_order INT,                         -- 排序
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP
);

-- 文件标签表
CREATE TABLE file_tags (
    tag_id VARCHAR(30) PRIMARY KEY,
    tag_name VARCHAR(50) UNIQUE,            -- 标签名称
    tag_color VARCHAR(10),                  -- 标签颜色
    usage_count INT DEFAULT 0,              -- 使用次数
    created_at TIMESTAMP
);

-- 文件标签关联表
CREATE TABLE file_tag_relations (
    relation_id VARCHAR(30) PRIMARY KEY,
    file_id VARCHAR(30),                    -- 文件ID
    tag_id VARCHAR(30),                     -- 标签ID
    tagged_by VARCHAR(20),                  -- 标记人
    tagged_at TIMESTAMP,                    -- 标记时间
    
    UNIQUE KEY uk_file_tag (file_id, tag_id)
);

-- 文件操作日志表
CREATE TABLE file_operation_logs (
    log_id VARCHAR(30) PRIMARY KEY,
    file_id VARCHAR(30),                    -- 文件ID
    operation_type ENUM('upload','download','view','edit','delete','share','move'), -- 操作类型
    operation_user VARCHAR(20),             -- 操作用户
    operation_time TIMESTAMP,               -- 操作时间
    ip_address VARCHAR(50),                 -- IP地址
    user_agent TEXT,                        -- 用户代理
    operation_details JSON,                 -- 操作详情
    
    INDEX idx_file_time (file_id, operation_time),
    INDEX idx_user_time (operation_user, operation_time)
);
```

## 3. 文件存储引擎

### 3.1 存储适配器抽象
```java
public interface FileStorageAdapter {
    
    /**
     * 上传文件
     */
    FileUploadResult upload(FileUploadRequest request);
    
    /**
     * 下载文件
     */
    InputStream download(String objectKey, String bucketName);
    
    /**
     * 删除文件
     */
    boolean delete(String objectKey, String bucketName);
    
    /**
     * 获取文件信息
     */
    FileStorageInfo getFileInfo(String objectKey, String bucketName);
    
    /**
     * 生成预签名URL
     */
    String generatePresignedUrl(String objectKey, String bucketName, Duration expiration);
    
    /**
     * 检查文件是否存在
     */
    boolean exists(String objectKey, String bucketName);
    
    /**
     * 列举文件
     */
    List<FileStorageInfo> listFiles(String bucketName, String prefix);
}

@Service
public class FileStorageService {
    
    private Map<StorageType, FileStorageAdapter> adapters = new HashMap<>();
    
    @Autowired
    private LocalStorageAdapter localAdapter;
    
    @Autowired
    private OSSStorageAdapter ossAdapter;
    
    @Autowired
    private S3StorageAdapter s3Adapter;
    
    @Autowired
    private MinioStorageAdapter minioAdapter;
    
    @PostConstruct
    public void initAdapters() {
        adapters.put(StorageType.LOCAL, localAdapter);
        adapters.put(StorageType.OSS, ossAdapter);
        adapters.put(StorageType.S3, s3Adapter);
        adapters.put(StorageType.MINIO, minioAdapter);
    }
    
    public FileUploadResult uploadFile(FileUploadRequest request) {
        StorageType storageType = determineStorageType(request);
        FileStorageAdapter adapter = adapters.get(storageType);
        
        if (adapter == null) {
            throw new UnsupportedOperationException("不支持的存储类型: " + storageType);
        }
        
        return adapter.upload(request);
    }
    
    private StorageType determineStorageType(FileUploadRequest request) {
        // 根据文件类型、大小、业务类型等决定存储类型
        if (request.getFileSize() > 100 * 1024 * 1024) { // 大于100MB
            return StorageType.OSS; // 使用云存储
        } else if (isSecureFile(request.getBusinessType())) {
            return StorageType.LOCAL; // 敏感文件本地存储
        } else {
            return StorageType.MINIO; // 默认使用MinIO
        }
    }
}
```

### 3.2 本地存储适配器
```java
@Component
public class LocalStorageAdapter implements FileStorageAdapter {
    
    @Value("${file.storage.local.base-path}")
    private String basePath;
    
    @Override
    public FileUploadResult upload(FileUploadRequest request) {
        try {
            // 生成文件路径
            String relativePath = generateFilePath(request);
            Path fullPath = Paths.get(basePath, relativePath);
            
            // 确保目录存在
            Files.createDirectories(fullPath.getParent());
            
            // 保存文件
            try (InputStream inputStream = request.getInputStream()) {
                Files.copy(inputStream, fullPath, StandardCopyOption.REPLACE_EXISTING);
            }
            
            return FileUploadResult.builder()
                .objectKey(relativePath)
                .bucketName("local")
                .fileSize(Files.size(fullPath))
                .md5Hash(calculateMD5(fullPath))
                .build();
                
        } catch (IOException e) {
            throw new FileStorageException("本地文件上传失败", e);
        }
    }
    
    @Override
    public InputStream download(String objectKey, String bucketName) {
        try {
            Path filePath = Paths.get(basePath, objectKey);
            if (!Files.exists(filePath)) {
                throw new FileNotFoundException("文件不存在: " + objectKey);
            }
            return Files.newInputStream(filePath);
        } catch (IOException e) {
            throw new FileStorageException("文件下载失败", e);
        }
    }
    
    private String generateFilePath(FileUploadRequest request) {
        LocalDate now = LocalDate.now();
        String datePath = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String fileName = UUID.randomUUID().toString() + "." + request.getFileExtension();
        
        return Paths.get(request.getBusinessType(), datePath, fileName).toString();
    }
    
    private String calculateMD5(Path filePath) throws IOException {
        try (InputStream is = Files.newInputStream(filePath)) {
            return DigestUtils.md5DigestAsHex(is);
        }
    }
}
```

### 3.3 云存储适配器
```java
@Component
public class OSSStorageAdapter implements FileStorageAdapter {
    
    @Autowired
    private OSS ossClient;
    
    @Value("${file.storage.oss.bucket-name}")
    private String defaultBucketName;
    
    @Override
    public FileUploadResult upload(FileUploadRequest request) {
        String objectKey = generateObjectKey(request);
        String bucketName = request.getBucketName() != null ? 
            request.getBucketName() : defaultBucketName;
        
        try {
            // 创建上传元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(request.getFileSize());
            metadata.setContentType(request.getMimeType());
            metadata.addUserMetadata("originalName", request.getOriginalFileName());
            metadata.addUserMetadata("businessType", request.getBusinessType());
            metadata.addUserMetadata("uploadUser", request.getUploadUserId());
            
            // 上传文件
            PutObjectResult result = ossClient.putObject(
                bucketName, 
                objectKey, 
                request.getInputStream(), 
                metadata
            );
            
            return FileUploadResult.builder()
                .objectKey(objectKey)
                .bucketName(bucketName)
                .fileSize(request.getFileSize())
                .md5Hash(result.getETag())
                .build();
                
        } catch (Exception e) {
            throw new FileStorageException("OSS文件上传失败", e);
        }
    }
    
    @Override
    public String generatePresignedUrl(String objectKey, String bucketName, Duration expiration) {
        try {
            Date expireDate = new Date(System.currentTimeMillis() + expiration.toMillis());
            URL url = ossClient.generatePresignedUrl(bucketName, objectKey, expireDate);
            return url.toString();
        } catch (Exception e) {
            throw new FileStorageException("生成预签名URL失败", e);
        }
    }
    
    @Override
    public List<FileStorageInfo> listFiles(String bucketName, String prefix) {
        try {
            ListObjectsRequest request = new ListObjectsRequest(bucketName)
                .withPrefix(prefix)
                .withMaxKeys(1000);
            
            ObjectListing listing = ossClient.listObjects(request);
            
            return listing.getObjectSummaries().stream()
                .map(this::convertToFileStorageInfo)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            throw new FileStorageException("列举文件失败", e);
        }
    }
    
    private FileStorageInfo convertToFileStorageInfo(OSSObjectSummary summary) {
        return FileStorageInfo.builder()
            .objectKey(summary.getKey())
            .bucketName(summary.getBucketName())
            .fileSize(summary.getSize())
            .lastModified(summary.getLastModified())
            .build();
    }
}
```

## 4. 文件上传服务

### 4.1 分片上传支持
```java
@RestController
@RequestMapping("/api/files")
public class FileUploadController {
    
    @Autowired
    private FileUploadService fileUploadService;
    
    /**
     * 初始化分片上传
     */
    @PostMapping("/multipart/init")
    public ResponseEntity<MultipartUploadResult> initMultipartUpload(
            @RequestBody MultipartUploadRequest request) {
        
        MultipartUploadResult result = fileUploadService.initMultipartUpload(request);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 上传分片
     */
    @PostMapping("/multipart/{uploadId}/parts/{partNumber}")
    public ResponseEntity<PartUploadResult> uploadPart(
            @PathVariable String uploadId,
            @PathVariable Integer partNumber,
            @RequestParam("file") MultipartFile file) {
        
        PartUploadResult result = fileUploadService.uploadPart(uploadId, partNumber, file);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 完成分片上传
     */
    @PostMapping("/multipart/{uploadId}/complete")
    public ResponseEntity<FileInfo> completeMultipartUpload(
            @PathVariable String uploadId,
            @RequestBody CompleteMultipartRequest request) {
        
        FileInfo fileInfo = fileUploadService.completeMultipartUpload(uploadId, request);
        return ResponseEntity.ok(fileInfo);
    }
    
    /**
     * 简单文件上传
     */
    @PostMapping("/upload")
    public ResponseEntity<FileInfo> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("businessType") String businessType,
            @RequestParam(value = "businessId", required = false) String businessId) {
        
        FileUploadRequest request = FileUploadRequest.builder()
            .multipartFile(file)
            .businessType(businessType)
            .businessId(businessId)
            .uploadUserId(getCurrentUserId())
            .build();
        
        FileInfo fileInfo = fileUploadService.uploadFile(request);
        return ResponseEntity.ok(fileInfo);
    }
}

@Service
public class FileUploadService {
    
    @Autowired
    private FileStorageService storageService;
    
    @Autowired
    private FileInfoRepository fileInfoRepository;
    
    // 分片上传状态缓存
    private final Cache<String, MultipartUploadSession> uploadSessions = 
        Caffeine.newBuilder()
            .expireAfterWrite(2, TimeUnit.HOURS)
            .maximumSize(10000)
            .build();
    
    public MultipartUploadResult initMultipartUpload(MultipartUploadRequest request) {
        String uploadId = UUID.randomUUID().toString();
        
        MultipartUploadSession session = new MultipartUploadSession();
        session.setUploadId(uploadId);
        session.setFileName(request.getFileName());
        session.setFileSize(request.getFileSize());
        session.setBusinessType(request.getBusinessType());
        session.setBusinessId(request.getBusinessId());
        session.setUploadUserId(request.getUploadUserId());
        session.setTotalParts(calculateTotalParts(request.getFileSize()));
        session.setCreatedAt(LocalDateTime.now());
        
        uploadSessions.put(uploadId, session);
        
        return MultipartUploadResult.builder()
            .uploadId(uploadId)
            .totalParts(session.getTotalParts())
            .partSize(PART_SIZE)
            .build();
    }
    
    public PartUploadResult uploadPart(String uploadId, Integer partNumber, MultipartFile file) {
        MultipartUploadSession session = uploadSessions.getIfPresent(uploadId);
        if (session == null) {
            throw new IllegalArgumentException("上传会话不存在或已过期");
        }
        
        try {
            // 生成分片存储路径
            String partKey = generatePartKey(uploadId, partNumber);
            
            // 上传分片
            FileUploadRequest partRequest = FileUploadRequest.builder()
                .inputStream(file.getInputStream())
                .fileSize(file.getSize())
                .originalFileName(file.getOriginalFilename())
                .mimeType(file.getContentType())
                .businessType(session.getBusinessType())
                .uploadUserId(session.getUploadUserId())
                .objectKey(partKey)
                .build();
            
            FileUploadResult result = storageService.uploadFile(partRequest);
            
            // 记录分片信息
            PartInfo partInfo = new PartInfo();
            partInfo.setPartNumber(partNumber);
            partInfo.setObjectKey(result.getObjectKey());
            partInfo.setSize(file.getSize());
            partInfo.setETag(result.getMd5Hash());
            partInfo.setUploadedAt(LocalDateTime.now());
            
            session.addPart(partInfo);
            
            return PartUploadResult.builder()
                .partNumber(partNumber)
                .eTag(result.getMd5Hash())
                .size(file.getSize())
                .build();
                
        } catch (IOException e) {
            throw new FileUploadException("分片上传失败", e);
        }
    }
    
    public FileInfo completeMultipartUpload(String uploadId, CompleteMultipartRequest request) {
        MultipartUploadSession session = uploadSessions.getIfPresent(uploadId);
        if (session == null) {
            throw new IllegalArgumentException("上传会话不存在或已过期");
        }
        
        // 验证所有分片都已上传
        if (session.getParts().size() != session.getTotalParts()) {
            throw new IllegalStateException("分片上传未完成");
        }
        
        try {
            // 合并分片
            String finalObjectKey = mergeParts(session);
            
            // 创建文件记录
            FileInfo fileInfo = createFileInfo(session, finalObjectKey);
            fileInfo = fileInfoRepository.save(fileInfo);
            
            // 清理分片文件
            cleanupParts(session);
            
            // 移除上传会话
            uploadSessions.invalidate(uploadId);
            
            return fileInfo;
            
        } catch (Exception e) {
            throw new FileUploadException("完成分片上传失败", e);
        }
    }
    
    private String mergeParts(MultipartUploadSession session) {
        String finalObjectKey = generateFinalObjectKey(session);
        
        // 根据存储类型选择合并策略
        StorageType storageType = determineStorageType(session);
        
        if (storageType == StorageType.LOCAL) {
            mergePartsLocally(session, finalObjectKey);
        } else {
            mergePartsInCloud(session, finalObjectKey);
        }
        
        return finalObjectKey;
    }
    
    private void mergePartsLocally(MultipartUploadSession session, String finalObjectKey) {
        try {
            Path finalPath = Paths.get(basePath, finalObjectKey);
            Files.createDirectories(finalPath.getParent());
            
            try (FileOutputStream fos = new FileOutputStream(finalPath.toFile());
                 FileChannel finalChannel = fos.getChannel()) {
                
                for (PartInfo part : session.getParts()) {
                    Path partPath = Paths.get(basePath, part.getObjectKey());
                    try (FileInputStream fis = new FileInputStream(partPath.toFile());
                         FileChannel partChannel = fis.getChannel()) {
                        partChannel.transferTo(0, partChannel.size(), finalChannel);
                    }
                }
            }
        } catch (IOException e) {
            throw new FileStorageException("本地分片合并失败", e);
        }
    }
}
```

## 5. 文件预览服务

### 5.1 文件预览转换
```java
@Service
public class FilePreviewService {
    
    @Autowired
    private List<FilePreviewConverter> converters;
    
    private final Cache<String, PreviewResult> previewCache = 
        Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();
    
    public PreviewResult getPreview(String fileId, PreviewOptions options) {
        String cacheKey = generateCacheKey(fileId, options);
        
        PreviewResult cached = previewCache.getIfPresent(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        FileInfo fileInfo = fileInfoRepository.findById(fileId)
            .orElseThrow(() -> new FileNotFoundException("文件不存在"));
        
        FilePreviewConverter converter = findConverter(fileInfo.getMimeType());
        if (converter == null) {
            throw new UnsupportedOperationException("不支持该文件类型的预览: " + fileInfo.getMimeType());
        }
        
        try (InputStream inputStream = storageService.downloadFile(fileInfo.getObjectKey(), fileInfo.getBucketName())) {
            PreviewResult result = converter.convert(inputStream, fileInfo, options);
            previewCache.put(cacheKey, result);
            return result;
        } catch (IOException e) {
            throw new FilePreviewException("文件预览转换失败", e);
        }
    }
    
    private FilePreviewConverter findConverter(String mimeType) {
        return converters.stream()
            .filter(converter -> converter.supports(mimeType))
            .findFirst()
            .orElse(null);
    }
}

public interface FilePreviewConverter {
    boolean supports(String mimeType);
    PreviewResult convert(InputStream inputStream, FileInfo fileInfo, PreviewOptions options);
}

@Component
public class PDFPreviewConverter implements FilePreviewConverter {
    
    @Override
    public boolean supports(String mimeType) {
        return "application/pdf".equals(mimeType);
    }
    
    @Override
    public PreviewResult convert(InputStream inputStream, FileInfo fileInfo, PreviewOptions options) {
        try {
            PDDocument document = PDDocument.load(inputStream);
            List<String> imageUrls = new ArrayList<>();
            
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            int pageCount = document.getNumberOfPages();
            
            // 转换指定页面或全部页面
            int startPage = options.getStartPage() != null ? options.getStartPage() : 0;
            int endPage = options.getEndPage() != null ? 
                Math.min(options.getEndPage(), pageCount - 1) : pageCount - 1;
            
            for (int page = startPage; page <= endPage; page++) {
                BufferedImage bim = pdfRenderer.renderImageWithDPI(page, 150, ImageType.RGB);
                String imageUrl = savePreviewImage(bim, fileInfo.getFileId(), page);
                imageUrls.add(imageUrl);
            }
            
            document.close();
            
            return PreviewResult.builder()
                .type("image")
                .pageCount(pageCount)
                .imageUrls(imageUrls)
                .build();
                
        } catch (IOException e) {
            throw new FilePreviewException("PDF预览转换失败", e);
        }
    }
    
    private String savePreviewImage(BufferedImage image, String fileId, int pageNumber) {
        try {
            String fileName = String.format("preview_%s_%d.png", fileId, pageNumber);
            String previewPath = Paths.get("previews", fileName).toString();
            Path fullPath = Paths.get(previewBasePath, previewPath);
            
            Files.createDirectories(fullPath.getParent());
            ImageIO.write(image, "PNG", fullPath.toFile());
            
            return "/api/files/preview/" + previewPath;
        } catch (IOException e) {
            throw new FilePreviewException("保存预览图片失败", e);
        }
    }
}

@Component
public class OfficePreviewConverter implements FilePreviewConverter {
    
    @Override
    public boolean supports(String mimeType) {
        return mimeType.startsWith("application/vnd.openxmlformats") ||
               mimeType.startsWith("application/msword") ||
               mimeType.startsWith("application/vnd.ms-excel") ||
               mimeType.startsWith("application/vnd.ms-powerpoint");
    }
    
    @Override
    public PreviewResult convert(InputStream inputStream, FileInfo fileInfo, PreviewOptions options) {
        try {
            // 使用Apache POI或LibreOffice进行转换
            String htmlContent = convertToHtml(inputStream, fileInfo.getMimeType());
            
            return PreviewResult.builder()
                .type("html")
                .content(htmlContent)
                .build();
                
        } catch (Exception e) {
            throw new FilePreviewException("Office文档预览转换失败", e);
        }
    }
    
    private String convertToHtml(InputStream inputStream, String mimeType) {
        // 具体实现根据文档类型选择对应的转换器
        // 这里简化处理，实际项目中可能需要调用LibreOffice命令行工具
        return "<html><body><p>Office文档预览功能开发中...</p></body></html>";
    }
}
```

## 6. 文件版本控制

### 6.1 版本管理服务
```java
@Service
public class FileVersionService {
    
    @Autowired
    private FileVersionRepository versionRepository;
    
    @Autowired
    private FileInfoRepository fileInfoRepository;
    
    @Autowired
    private FileStorageService storageService;
    
    public FileVersion createVersion(String fileId, MultipartFile newFile, String changeDescription, String userId) {
        FileInfo fileInfo = fileInfoRepository.findById(fileId)
            .orElseThrow(() -> new FileNotFoundException("文件不存在"));
        
        // 获取当前最大版本号
        int nextVersion = getNextVersionNumber(fileId);
        
        try {
            // 上传新版本文件
            FileUploadRequest uploadRequest = FileUploadRequest.builder()
                .multipartFile(newFile)
                .businessType(fileInfo.getBusinessType())
                .businessId(fileInfo.getBusinessId())
                .uploadUserId(userId)
                .build();
            
            FileUploadResult uploadResult = storageService.uploadFile(uploadRequest);
            
            // 创建版本记录
            FileVersion version = new FileVersion();
            version.setVersionId(IdGenerator.generateId());
            version.setFileId(fileId);
            version.setVersionNumber(nextVersion);
            version.setFilePath(uploadResult.getObjectKey());
            version.setFileSize(uploadResult.getFileSize());
            version.setMd5Hash(uploadResult.getMd5Hash());
            version.setChangeDescription(changeDescription);
            version.setCreatedBy(userId);
            version.setCreatedAt(LocalDateTime.now());
            
            // 更新当前版本标记
            updateCurrentVersion(fileId, version);
            
            // 更新文件主记录
            updateFileMainVersion(fileInfo, uploadResult);
            
            return versionRepository.save(version);
            
        } catch (Exception e) {
            throw new FileVersionException("创建文件版本失败", e);
        }
    }
    
    public void revertToVersion(String fileId, int versionNumber, String userId) {
        FileVersion targetVersion = versionRepository
            .findByFileIdAndVersionNumber(fileId, versionNumber)
            .orElseThrow(() -> new FileVersionException("目标版本不存在"));
        
        FileInfo fileInfo = fileInfoRepository.findById(fileId)
            .orElseThrow(() -> new FileNotFoundException("文件不存在"));
        
        // 复制目标版本文件作为新版本
        try (InputStream versionStream = storageService.downloadFile(
                targetVersion.getFilePath(), fileInfo.getBucketName())) {
            
            int nextVersion = getNextVersionNumber(fileId);
            
            FileUploadRequest uploadRequest = FileUploadRequest.builder()
                .inputStream(versionStream)
                .fileSize(targetVersion.getFileSize())
                .originalFileName(fileInfo.getOriginalName())
                .mimeType(fileInfo.getMimeType())
                .businessType(fileInfo.getBusinessType())
                .businessId(fileInfo.getBusinessId())
                .uploadUserId(userId)
                .build();
            
            FileUploadResult uploadResult = storageService.uploadFile(uploadRequest);
            
            // 创建回滚版本记录
            FileVersion revertVersion = new FileVersion();
            revertVersion.setVersionId(IdGenerator.generateId());
            revertVersion.setFileId(fileId);
            revertVersion.setVersionNumber(nextVersion);
            revertVersion.setFilePath(uploadResult.getObjectKey());
            revertVersion.setFileSize(uploadResult.getFileSize());
            revertVersion.setMd5Hash(uploadResult.getMd5Hash());
            revertVersion.setChangeDescription("回滚到版本 " + versionNumber);
            revertVersion.setCreatedBy(userId);
            revertVersion.setCreatedAt(LocalDateTime.now());
            
            updateCurrentVersion(fileId, revertVersion);
            updateFileMainVersion(fileInfo, uploadResult);
            
            versionRepository.save(revertVersion);
            
        } catch (IOException e) {
            throw new FileVersionException("文件版本回滚失败", e);
        }
    }
    
    public List<FileVersion> getVersionHistory(String fileId) {
        return versionRepository.findByFileIdOrderByVersionNumberDesc(fileId);
    }
    
    public FileVersionCompareResult compareVersions(String fileId, int version1, int version2) {
        FileVersion v1 = versionRepository.findByFileIdAndVersionNumber(fileId, version1)
            .orElseThrow(() -> new FileVersionException("版本1不存在"));
        FileVersion v2 = versionRepository.findByFileIdAndVersionNumber(fileId, version2)
            .orElseThrow(() -> new FileVersionException("版本2不存在"));
        
        FileVersionCompareResult result = new FileVersionCompareResult();
        result.setVersion1(v1);
        result.setVersion2(v2);
        result.setSizeDiff(v2.getFileSize() - v1.getFileSize());
        result.setHashChanged(!v1.getMd5Hash().equals(v2.getMd5Hash()));
        result.setTimeDiff(Duration.between(v1.getCreatedAt(), v2.getCreatedAt()));
        
        // 如果是文本文件，进行内容对比
        FileInfo fileInfo = fileInfoRepository.findById(fileId)
            .orElseThrow(() -> new FileNotFoundException("文件不存在"));
        
        if (isTextFile(fileInfo.getMimeType())) {
            result.setContentDiff(compareTextContent(v1, v2, fileInfo));
        }
        
        return result;
    }
    
    private void updateCurrentVersion(String fileId, FileVersion newCurrentVersion) {
        // 清除之前的当前版本标记
        versionRepository.clearCurrentVersion(fileId);
        
        // 设置新的当前版本
        newCurrentVersion.setIsCurrent(true);
    }
    
    private int getNextVersionNumber(String fileId) {
        return versionRepository.findMaxVersionNumber(fileId)
            .orElse(0) + 1;
    }
}
```

## 7. 文件搜索服务

### 7.1 文件内容索引
```java
@Service
public class FileSearchService {
    
    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;
    
    @Autowired
    private List<FileContentExtractor> extractors;
    
    public void indexFile(FileInfo fileInfo) {
        try {
            // 提取文件内容
            String content = extractFileContent(fileInfo);
            
            // 创建搜索文档
            FileSearchDocument document = new FileSearchDocument();
            document.setFileId(fileInfo.getFileId());
            document.setFileName(fileInfo.getFileName());
            document.setOriginalName(fileInfo.getOriginalName());
            document.setMimeType(fileInfo.getMimeType());
            document.setFileSize(fileInfo.getFileSize());
            document.setBusinessType(fileInfo.getBusinessType());
            document.setBusinessId(fileInfo.getBusinessId());
            document.setUploadUserId(fileInfo.getUploadUserId());
            document.setUploadTime(fileInfo.getUploadTime());
            document.setContent(content);
            document.setContentLength(content.length());
            
            // 提取文件标签
            List<String> tags = extractFileTags(fileInfo);
            document.setTags(tags);
            
            // 索引到Elasticsearch
            elasticsearchTemplate.save(document);
            
        } catch (Exception e) {
            log.error("文件索引失败: {}", fileInfo.getFileId(), e);
        }
    }
    
    public FileSearchResult searchFiles(FileSearchQuery query) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool();
        
        // 全文检索
        if (StringUtils.hasText(query.getKeyword())) {
            boolQuery.should(QueryBuilders.multiMatch()
                .query(query.getKeyword())
                .fields("fileName^3", "originalName^2", "content^1")
                .fuzziness(Fuzziness.AUTO)
            );
        }
        
        // 文件类型过滤
        if (query.getMimeTypes() != null && !query.getMimeTypes().isEmpty()) {
            boolQuery.filter(QueryBuilders.terms()
                .field("mimeType")
                .terms(query.getMimeTypes())
            );
        }
        
        // 业务类型过滤
        if (StringUtils.hasText(query.getBusinessType())) {
            boolQuery.filter(QueryBuilders.term()
                .field("businessType")
                .value(query.getBusinessType())
            );
        }
        
        // 文件大小范围过滤
        if (query.getMinSize() != null || query.getMaxSize() != null) {
            RangeQuery.Builder rangeQuery = QueryBuilders.range().field("fileSize");
            if (query.getMinSize() != null) {
                rangeQuery.gte(query.getMinSize());
            }
            if (query.getMaxSize() != null) {
                rangeQuery.lte(query.getMaxSize());
            }
            boolQuery.filter(rangeQuery.build());
        }
        
        // 时间范围过滤
        if (query.getStartTime() != null || query.getEndTime() != null) {
            RangeQuery.Builder rangeQuery = QueryBuilders.range().field("uploadTime");
            if (query.getStartTime() != null) {
                rangeQuery.gte(query.getStartTime().toString());
            }
            if (query.getEndTime() != null) {
                rangeQuery.lte(query.getEndTime().toString());
            }
            boolQuery.filter(rangeQuery.build());
        }
        
        // 构建搜索请求
        SearchRequest searchRequest = SearchRequest.of(s -> s
            .index("file_index")
            .query(boolQuery.build())
            .sort(SortOptions.of(sort -> sort
                .field(FieldSort.of(f -> f
                    .field("uploadTime")
                    .order(SortOrder.Desc)
                ))
            ))
            .from(query.getFrom())
            .size(query.getSize())
            .highlight(Highlight.of(h -> h
                .fields("fileName", HighlightField.of(hf -> hf))
                .fields("content", HighlightField.of(hf -> hf
                    .fragmentSize(200)
                    .numberOfFragments(3)
                ))
            ))
        );
        
        try {
            SearchResponse<FileSearchDocument> response = elasticsearchTemplate
                .getElasticsearchClient()
                .search(searchRequest, FileSearchDocument.class);
            
            return convertToSearchResult(response, query);
            
        } catch (IOException e) {
            throw new FileSearchException("文件搜索失败", e);
        }
    }
    
    private String extractFileContent(FileInfo fileInfo) {
        FileContentExtractor extractor = findExtractor(fileInfo.getMimeType());
        if (extractor == null) {
            return ""; // 不支持的文件类型返回空内容
        }
        
        try (InputStream inputStream = storageService.downloadFile(
                fileInfo.getObjectKey(), fileInfo.getBucketName())) {
            return extractor.extractContent(inputStream, fileInfo);
        } catch (IOException e) {
            log.warn("文件内容提取失败: {}", fileInfo.getFileId(), e);
            return "";
        }
    }
    
    private FileContentExtractor findExtractor(String mimeType) {
        return extractors.stream()
            .filter(extractor -> extractor.supports(mimeType))
            .findFirst()
            .orElse(null);
    }
}

public interface FileContentExtractor {
    boolean supports(String mimeType);
    String extractContent(InputStream inputStream, FileInfo fileInfo);
}

@Component
public class PDFContentExtractor implements FileContentExtractor {
    
    @Override
    public boolean supports(String mimeType) {
        return "application/pdf".equals(mimeType);
    }
    
    @Override
    public String extractContent(InputStream inputStream, FileInfo fileInfo) {
        try {
            PDDocument document = PDDocument.load(inputStream);
            PDFTextStripper stripper = new PDFTextStripper();
            
            // 限制提取的文本长度，避免索引过大
            stripper.setEndPage(Math.min(document.getNumberOfPages(), 50)); // 最多提取前50页
            
            String content = stripper.getText(document);
            document.close();
            
            // 限制内容长度
            if (content.length() > 50000) {
                content = content.substring(0, 50000) + "...";
            }
            
            return content;
            
        } catch (IOException e) {
            throw new FileContentExtractionException("PDF内容提取失败", e);
        }
    }
}

@Component
public class TextContentExtractor implements FileContentExtractor {
    
    @Override
    public boolean supports(String mimeType) {
        return mimeType.startsWith("text/") ||
               "application/json".equals(mimeType) ||
               "application/xml".equals(mimeType);
    }
    
    @Override
    public String extractContent(InputStream inputStream, FileInfo fileInfo) {
        try {
            String content = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            
            // 限制内容长度
            if (content.length() > 50000) {
                content = content.substring(0, 50000) + "...";
            }
            
            return content;
            
        } catch (IOException e) {
            throw new FileContentExtractionException("文本内容提取失败", e);
        }
    }
}
```

---

*文件管理模块为CIM系统提供了完整、安全、高效的文件管理解决方案，支持大文件上传、版本控制、全文搜索和多格式预览*