// IC封测CIM系统 - 布局组件样式
// 专业级布局组件的样式定义

// ===== 头部布局组件 =====
.app-header-layout {
  z-index: var(--z-sticky);
  height: var(--header-height);
  background-color: var(--color-nav-bg);
  border-bottom: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-sm);
}

// ===== 侧边栏布局组件 =====
.app-sidebar-layout {
  width: var(--sidebar-width);
  background-color: var(--color-sidebar-bg);
  border-right: 1px solid var(--color-sidebar-border);
  transition: width var(--transition-normal);

  &.collapsed {
    width: var(--sidebar-collapsed-width);
  }
}

// ===== 面包屑组件 =====
.app-breadcrumb {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);

  &__item {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    
    &:not(:last-child)::after {
      margin-left: var(--spacing-2);
      color: var(--color-text-tertiary);
      content: '/';
    }
    
    a {
      color: var(--color-text-secondary);
      text-decoration: none;
      transition: color var(--transition-fast);
      
      &:hover {
        color: var(--color-primary);
      }
    }
    
    &.active {
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
  }
}

// ===== 页面标题组件 =====
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  border-bottom: 1px solid var(--color-border-light);

  &__left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  &__title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--color-text-primary);
  }

  &__subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  &__actions {
    display: flex;
    gap: var(--spacing-3);
    align-items: center;
  }
}

// ===== 内容容器 =====
.content-container {
  overflow: hidden;
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-lg);

  &__header {
    padding: var(--spacing-4) var(--spacing-5);
    background-color: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-border-light);
    
    &-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
  }

  &__body {
    padding: var(--spacing-5);
  }

  &__footer {
    padding: var(--spacing-4) var(--spacing-5);
    background-color: var(--color-bg-secondary);
    border-top: 1px solid var(--color-border-light);
  }
}

// ===== 栅格系统 =====
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(var(--spacing-3) * -1);
}

.col {
  flex: 1;
  min-width: 0;
  padding: 0 var(--spacing-3);
}

@for $i from 1 through 12 {
  .col-#{$i} {
    flex: 0 0 calc(#{$i} / 12 * 100%);
    max-width: calc(#{$i} / 12 * 100%);
    padding: 0 var(--spacing-3);
  }
}

// ===== 响应式栅格 =====
@media (width >= 768px) {
  @for $i from 1 through 12 {
    .col-md-#{$i} {
      flex: 0 0 calc(#{$i} / 12 * 100%);
      max-width: calc(#{$i} / 12 * 100%);
    }
  }
}

@media (width >= 1024px) {
  @for $i from 1 through 12 {
    .col-lg-#{$i} {
      flex: 0 0 calc(#{$i} / 12 * 100%);
      max-width: calc(#{$i} / 12 * 100%);
    }
  }
}