// IC封测CIM系统 - 晶圆图专业组件样式

.wafer-map {
  padding: var(--spacing-5);
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);
    
    &-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
    
    &-info {
      display: flex;
      gap: var(--spacing-4);
      align-items: center;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }
  }
  
  &__canvas {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
    background-color: var(--color-wafer);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);
  }
  
  &__legend {
    display: flex;
    gap: var(--spacing-6);
    align-items: center;
    justify-content: center;
    padding-top: var(--spacing-4);
    margin-top: var(--spacing-4);
    border-top: 1px solid var(--color-border-light);
  }
  
  &__legend-item {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    
    &-dot {
      width: 12px;
      height: 12px;
      border-radius: var(--radius-full);
      
      &--pass {
        background-color: var(--color-die-pass);
      }
      
      &--fail {
        background-color: var(--color-die-fail);
      }
      
      &--untested {
        background-color: var(--color-text-tertiary);
      }
    }
  }
}

.die {
  position: absolute;
  width: 8px;
  height: 8px;
  cursor: pointer;
  border-radius: 1px;
  transition: all var(--transition-fast);
  
  &:hover {
    z-index: 1;
    transform: scale(1.2);
  }
  
  &--pass {
    background-color: var(--color-success);
  }
  
  &--fail {
    background-color: var(--color-error);
  }
  
  &--untested {
    background-color: var(--color-text-tertiary);
  }
}