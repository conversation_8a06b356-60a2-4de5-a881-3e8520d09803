<template>
  <div class="product-master-data">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title">
        <h2>产品主数据管理</h2>
        <p>IC封装测试服务产品规格库维护</p>
      </div>
      <div class="page-actions">
        <el-button type="success"
:icon="Download" @click="handleExport"
>
导出产品
</el-button>
        <el-button type="warning"
:icon="Upload" @click="handleImport"
>
批量导入
</el-button>
        <el-button type="primary"
:icon="Plus" @click="handleAdd"
>
新增产品
</el-button>
      </div>
    </div>

    <!-- 搜索筛选区 -->
    <el-card class="search-card" shadow="never">
      <el-form
ref="searchFormRef" :model="searchForm"
:inline="true" label-width="auto"
>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="产品代码">
              <el-input
                v-model="searchForm.productCode"
                placeholder="请输入产品代码"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品名称">
              <el-input
                v-model="searchForm.productName"
                placeholder="请输入产品名称"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品分类">
              <el-select
v-model="searchForm.category" placeholder="请选择产品分类"
clearable
>
                <el-option
                  v-for="option in productCategoryOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="封装类型">
              <el-select
v-model="searchForm.packageType" placeholder="请选择封装类型"
clearable
>
                <el-option
                  v-for="option in packageTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="工艺节点">
              <el-select
v-model="searchForm.processNode" placeholder="请选择工艺节点"
clearable
>
                <el-option
                  v-for="option in processNodeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="应用领域">
              <el-select
                v-model="searchForm.applicationField"
                placeholder="请选择应用领域"
                clearable
              >
                <el-option
                  v-for="option in applicationFieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品状态">
              <el-select
v-model="searchForm.status" placeholder="请选择产品状态"
clearable
>
                <el-option
                  v-for="option in productStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="负责工程师">
              <el-input
                v-model="searchForm.engineerName"
                placeholder="请输入工程师姓名"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right">
            <el-button @click="handleReset">重置</el-button>
            <el-button
type="primary" @click="handleSearch">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon size="24">
                  <Cpu />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ summary.totalProducts }}
                </div>
                <div class="stat-label">产品总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon size="24">
                  <Check />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ summary.activeProducts }}
                </div>
                <div class="stat-label">量产产品</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon developing">
                <el-icon size="24">
                  <Tools />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ summary.developmentProducts }}
                </div>
                <div class="stat-label">开发中产品</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon eol">
                <el-icon size="24">
                  <Warning />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">
                  {{ summary.eolProducts }}
                </div>
                <div class="stat-label">停产产品</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 产品列表表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>产品列表</span>
          <div class="header-actions">
            <el-button size="small"
:icon="Refresh" @click="handleRefresh"
>
刷新
</el-button>
            <el-button
size="small" :icon="Setting"
@click="showColumnSettings = true"
>
              列设置
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="productList"
        stripe
        size="default"
        height="600"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="productCode"
          label="产品代码"
          width="120"
          fixed="left"
          show-overflow-tooltip
        />
        <el-table-column
prop="productName" label="产品名称"
width="200" show-overflow-tooltip
/>
        <el-table-column
prop="category" label="产品分类"
width="120"
>
          <template #default="{ row }">
            <el-tag size="small" type="info">
              {{ getCategoryLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="customer.customerName"
          label="客户名称"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
prop="packageInfo.packageType" label="封装类型"
width="100"
>
          <template #default="{ row }">
            <el-tag size="small" type="success">
              {{ row.packageInfo.packageType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="technicalSpec.processNode"
          label="工艺节点"
          width="80"
          align="center"
        />
        <el-table-column
prop="technicalSpec.pinCount" label="引脚数"
width="80" align="right"
/>
        <el-table-column
prop="applicationField" label="应用领域"
width="100"
>
          <template #default="{ row }">
            <el-tag size="small" type="warning">
              {{ getApplicationFieldLabel(row.applicationField) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
prop="status" label="产品状态"
width="100" align="center"
>
          <template #default="{ row }">
            <el-tag
size="small" :type="getStatusType(row.status)"
>
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
prop="engineerName" label="负责工程师"
width="100" show-overflow-tooltip
/>
        <el-table-column
prop="version" label="版本"
width="80" align="center"
/>
        <el-table-column
prop="updatedAt" label="更新时间"
width="100" align="center"
>
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column
label="操作" width="200"
fixed="right" align="center"
>
          <template #default="{ row }">
            <el-button size="small"
type="primary" link @click="handleView(row)"
>
查看
</el-button>
            <el-button size="small"
type="primary" link @click="handleEdit(row)"
>
编辑
</el-button>
            <el-button size="small"
type="primary" link @click="handleCopy(row)"
>
复制
</el-button>
            <el-button size="small"
type="danger" link @click="handleDelete(row)"
>
删除
</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 产品详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="90%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <ProductForm
        v-if="dialogVisible"
        ref="productFormRef"
        :product="currentProduct"
        :readonly="dialogMode === 'view'"
        @save="handleSave"
        @cancel="handleCancel"
      />
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importVisible"
      title="批量导入产品"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="import-section">
        <el-alert
          title="导入说明"
          type="info"
          show-icon
          :closable="false"
          style="margin-bottom: 20px"
        >
          <template #default>
            <p>1. 请下载导入模板，按照模板格式填写产品信息</p>
            <p>2. 支持Excel (.xlsx) 和 CSV (.csv) 格式</p>
            <p>3. 单次最多导入1000条记录</p>
          </template>
        </el-alert>

        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :limit="1"
          :on-change="handleFileChange"
          :before-remove="handleBeforeRemove"
          accept=".xlsx,.csv"
          drag
        >
          <el-icon class="el-icon--upload">
            <Upload />
          </el-icon>
          <div class="el-upload__text">
            将文件拖拽到此处，或
            <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
只能上传 .xlsx 或 .csv 文件，且不超过10MB
</div>
          </template>
        </el-upload>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDownloadTemplate">下载模板</el-button>
          <el-button @click="importVisible = false">取消</el-button>
          <el-button
type="primary" :loading="importLoading"
@click="handleImportConfirm"
>
            开始导入
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 列设置对话框 -->
    <el-dialog
v-model="showColumnSettings" title="列显示设置"
width="400px"
>
      <div class="column-settings">
        <el-checkbox-group v-model="visibleColumns">
          <el-checkbox
            v-for="column in allColumns"
            :key="column.key"
            :label="column.key"
            :disabled="column.required"
          >
            {{ column.label }}
          </el-checkbox>
        </el-checkbox-group>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showColumnSettings = false">取消</el-button>
          <el-button
type="primary" @click="handleColumnSettingsConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  // Element Plus组件通过unplugin-auto-import自动导入
  import {
    Plus,
    Download,
    Upload,
    Refresh,
    Setting,
    Search,
    Delete,
    Edit,
    View,
    Copy,
    Cpu,
    Check,
    Tools,
    Warning
  } from '@element-plus/icons-vue'

  import type {
    Product,
    ProductQueryParams,
    ICProductCategory,
    PackageType,
    ProcessNode,
    ApplicationField,
    ProductStatus
  } from '@/types/product'

  import {
    mockProducts,
    mockProductStatistics,
    productCategoryOptions,
    packageTypeOptions,
    processNodeOptions,
    applicationFieldOptions,
    productStatusOptions
  } from '@/utils/mockData/products'

  import ProductForm from './components/ProductForm.vue'

  // ================================
  // 响应式数据定义
  // ================================

  // 搜索表单
  const searchForm = reactive<ProductQueryParams>({
    productCode: '',
    productName: '',
    category: undefined,
    packageType: undefined,
    processNode: undefined,
    applicationField: undefined,
    status: undefined,
    engineerName: '',
    page: 1,
    size: 20
  })

  // 列表数据
  const loading = ref(false)
  const productList = ref<Product[]>([])
  const selectedProducts = ref<Product[]>([])

  // 分页数据
  const pagination = reactive({
    page: 1,
    size: 20,
    total: 0
  })

  // 统计数据
  const summary = ref({
    totalProducts: 0,
    activeProducts: 0,
    developmentProducts: 0,
    eolProducts: 0
  })

  // 对话框状态
  const dialogVisible = ref(false)
  const dialogTitle = ref('')
  const dialogMode = ref<'add' | 'edit' | 'view'>('add')
  const currentProduct = ref<Product | null>(null)

  // 导入功能
  const importVisible = ref(false)
  const importLoading = ref(false)

  // 列设置
  const showColumnSettings = ref(false)
  const visibleColumns = ref<string[]>([
    'productCode',
    'productName',
    'category',
    'customer',
    'packageType',
    'processNode',
    'applicationField',
    'status'
  ])

  const allColumns = [
    { key: 'productCode', label: '产品代码', required: true },
    { key: 'productName', label: '产品名称', required: true },
    { key: 'category', label: '产品分类', required: false },
    { key: 'customer', label: '客户名称', required: false },
    { key: 'packageType', label: '封装类型', required: false },
    { key: 'processNode', label: '工艺节点', required: false },
    { key: 'pinCount', label: '引脚数', required: false },
    { key: 'applicationField', label: '应用领域', required: false },
    { key: 'status', label: '产品状态', required: false },
    { key: 'engineerName', label: '负责工程师', required: false },
    { key: 'version', label: '版本', required: false },
    { key: 'updatedAt', label: '更新时间', required: false }
  ]

  // 引用
  const searchFormRef = ref()
  const productFormRef = ref()
  const uploadRef = ref()

  // ================================
  // 计算属性
  // ================================

  const filteredProducts = computed(() => {
    let filtered = [...mockProducts]

    // 应用搜索条件
    if (searchForm.productCode) {
      filtered = filtered.filter(p =>
        p.productCode.toLowerCase().includes(searchForm.productCode!.toLowerCase())
      )
    }
    if (searchForm.productName) {
      filtered = filtered.filter(p =>
        p.productName.toLowerCase().includes(searchForm.productName!.toLowerCase())
      )
    }
    if (searchForm.category) {
      filtered = filtered.filter(p => p.category === searchForm.category)
    }
    if (searchForm.packageType) {
      filtered = filtered.filter(p => p.packageInfo.packageType === searchForm.packageType)
    }
    if (searchForm.processNode) {
      filtered = filtered.filter(p => p.technicalSpec.processNode === searchForm.processNode)
    }
    if (searchForm.applicationField) {
      filtered = filtered.filter(p => p.applicationField === searchForm.applicationField)
    }
    if (searchForm.status) {
      filtered = filtered.filter(p => p.status === searchForm.status)
    }
    if (searchForm.engineerName) {
      filtered = filtered.filter(p =>
        p.engineerName.toLowerCase().includes(searchForm.engineerName!.toLowerCase())
      )
    }

    return filtered
  })

  // ================================
  // 方法定义
  // ================================

  // 加载产品列表
  const loadProducts = async () => {
    loading.value = true
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 300))

      const filtered = filteredProducts.value
      const start = (pagination.page - 1) * pagination.size
      const end = start + pagination.size

      productList.value = filtered.slice(start, end)
      pagination.total = filtered.length

      // 更新统计数据
      updateSummary()
    } catch (error) {
      ElMessage.error('加载产品列表失败')
    } finally {
      loading.value = false
    }
  }

  // 更新统计数据
  const updateSummary = () => {
    const stats = mockProductStatistics.statusStats.reduce(
      (acc, stat) => {
        if (stat.status === ProductStatus.MASS_PRODUCTION) {
          acc.activeProducts = stat.count
        } else if (
          stat.status === ProductStatus.DEVELOPMENT ||
          stat.status === ProductStatus.QUALIFICATION
        ) {
          acc.developmentProducts += stat.count
        } else if (stat.status === ProductStatus.EOL || stat.status === ProductStatus.OBSOLETE) {
          acc.eolProducts += stat.count
        }
        return acc
      },
      {
        totalProducts: mockProducts.length,
        activeProducts: 0,
        developmentProducts: 0,
        eolProducts: 0
      }
    )

    summary.value = stats
  }

  // 获取分类标签
  const getCategoryLabel = (category: ICProductCategory): string => {
    return productCategoryOptions.find(opt => opt.value === category)?.label || category
  }

  // 获取应用领域标签
  const getApplicationFieldLabel = (field: ApplicationField): string => {
    return applicationFieldOptions.find(opt => opt.value === field)?.label || field
  }

  // 获取状态标签和类型
  const getStatusLabel = (status: ProductStatus): string => {
    return productStatusOptions.find(opt => opt.value === status)?.label || status
  }

  const getStatusType = (status: ProductStatus): string => {
    return productStatusOptions.find(opt => opt.value === status)?.color || 'info'
  }

  // 格式化日期
  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleDateString('zh-CN')
  }

  // ================================
  // 事件处理
  // ================================

  // 搜索
  const handleSearch = () => {
    pagination.page = 1
    loadProducts()
  }

  // 重置搜索
  const handleReset = () => {
    searchFormRef.value?.resetFields()
    Object.keys(searchForm).forEach(key => {
      if (key !== 'page' && key !== 'size') {
        ;(searchForm as any)[key] = key === 'page' ? 1 : key === 'size' ? 20 : undefined
      }
    })
    handleSearch()
  }

  // 刷新
  const handleRefresh = () => {
    loadProducts()
  }

  // 分页
  const handleSizeChange = (newSize: number) => {
    pagination.size = newSize
    pagination.page = 1
    loadProducts()
  }

  const handleCurrentChange = (newPage: number) => {
    pagination.page = newPage
    loadProducts()
  }

  // 选择变化
  const handleSelectionChange = (selection: Product[]) => {
    selectedProducts.value = selection
  }

  // 行点击
  const handleRowClick = (row: Product) => {
    // 可以实现行点击逻辑
  }

  // 新增产品
  const handleAdd = () => {
    dialogTitle.value = '新增产品'
    dialogMode.value = 'add'
    currentProduct.value = null
    dialogVisible.value = true
  }

  // 查看产品
  const handleView = (product: Product) => {
    dialogTitle.value = '查看产品详情'
    dialogMode.value = 'view'
    currentProduct.value = { ...product }
    dialogVisible.value = true
  }

  // 编辑产品
  const handleEdit = (product: Product) => {
    dialogTitle.value = '编辑产品'
    dialogMode.value = 'edit'
    currentProduct.value = { ...product }
    dialogVisible.value = true
  }

  // 复制产品
  const handleCopy = (product: Product) => {
    dialogTitle.value = '复制产品'
    dialogMode.value = 'add'
    const copyProduct = { ...product }
    copyProduct.id = ''
    copyProduct.productCode = `${product.productCode}_COPY`
    copyProduct.productName = `${product.productName}（复制）`
    currentProduct.value = copyProduct
    dialogVisible.value = true
  }

  // 删除产品
  const handleDelete = async (product: Product) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除产品 "${product.productName}" 吗？此操作不可恢复。`,
        '删除确认',
        {
          type: 'warning',
          confirmButtonText: '删除',
          cancelButtonText: '取消'
        }
      )

      // 模拟删除操作
      ElMessage.success('删除成功')
      loadProducts()
    } catch (error) {
      // 用户取消删除
    }
  }

  // 保存产品
  const handleSave = async (product: Product) => {
    try {
      // 模拟保存操作
      await new Promise(resolve => setTimeout(resolve, 1000))

      ElMessage.success(dialogMode.value === 'add' ? '新增成功' : '更新成功')
      dialogVisible.value = false
      loadProducts()
    } catch (error) {
      ElMessage.error('保存失败')
    }
  }

  // 取消编辑
  const handleCancel = () => {
    dialogVisible.value = false
  }

  // 导出产品
  const handleExport = () => {
    if (selectedProducts.value.length === 0) {
      ElMessage.warning('请先选择要导出的产品')
      return
    }

    // 模拟导出操作
    ElMessage.success(`正在导出 ${selectedProducts.value.length} 条产品数据`)
  }

  // 批量导入
  const handleImport = () => {
    importVisible.value = true
  }

  // 下载导入模板
  const handleDownloadTemplate = () => {
    // 模拟下载模板
    ElMessage.success('模板下载中...')
  }

  // 文件变化处理
  const handleFileChange = (file: any) => {
    // 文件选择处理
  }

  // 移除文件前确认
  const handleBeforeRemove = () => {
    return true
  }

  // 确认导入
  const handleImportConfirm = async () => {
    const files = uploadRef.value?.uploadFiles
    if (!files || files.length === 0) {
      ElMessage.warning('请选择要导入的文件')
      return
    }

    importLoading.value = true
    try {
      // 模拟导入处理
      await new Promise(resolve => setTimeout(resolve, 2000))

      ElMessage.success('导入成功')
      importVisible.value = false
      loadProducts()
    } catch (error) {
      ElMessage.error('导入失败')
    } finally {
      importLoading.value = false
    }
  }

  // 列设置确认
  const handleColumnSettingsConfirm = () => {
    // 应用列设置
    showColumnSettings.value = false
    ElMessage.success('列设置已更新')
  }

  // ================================
  // 生命周期
  // ================================

  onMounted(() => {
    loadProducts()
  })
</script>

<style lang="scss" scoped>
  .product-master-data {
    min-height: 100vh;
    padding: var(--spacing-4);
    background-color: var(--color-bg-secondary);

    // 页面头部样式
    .page-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      background: var(--color-bg-primary);
      border-radius: var(--radius-base);
      box-shadow: var(--shadow-light);

      .page-title {
        h2 {
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--color-text-primary);
        }

        p {
          margin: var(--spacing-1) 0 0 0;
          font-size: 14px;
          color: var(--color-text-secondary);
        }
      }

      .page-actions {
        display: flex;
        gap: var(--spacing-2);
      }
    }

    // 搜索卡片样式
    .search-card {
      margin-bottom: var(--spacing-4);
      border: none;

      :deep(.el-card__body) {
        padding: var(--spacing-4);
      }
    }

    // 统计卡片样式
    .statistics-section {
      margin-bottom: var(--spacing-4);

      .stat-card {
        border: none;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--shadow-base);
          transform: translateY(-2px);
        }

        .stat-content {
          display: flex;
          gap: var(--spacing-3);
          align-items: center;

          .stat-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            color: white;
            border-radius: 50%;

            &.total {
              background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
            }

            &.active {
              background: linear-gradient(135deg, var(--color-success), var(--color-success-light));
            }

            &.developing {
              background: linear-gradient(135deg, var(--color-warning), var(--color-warning-light));
            }

            &.eol {
              background: linear-gradient(135deg, var(--color-danger), var(--color-danger-light));
            }
          }

          .stat-info {
            .stat-value {
              font-size: 24px;
              font-weight: 600;
              line-height: 1;
              color: var(--color-text-primary);
            }

            .stat-label {
              margin-top: var(--spacing-1);
              font-size: 12px;
              color: var(--color-text-secondary);
            }
          }
        }
      }
    }

    // 表格卡片样式
    .table-card {
      border: none;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-actions {
          display: flex;
          gap: var(--spacing-2);
        }
      }

      :deep(.el-card__body) {
        padding: 0;
      }

      :deep(.el-table) {
        .el-table__header-wrapper {
          background-color: var(--color-bg-tertiary);
        }

        .el-table__row {
          &:hover {
            background-color: var(--color-bg-light);
          }
        }
      }

      .pagination-container {
        display: flex;
        justify-content: center;
        padding: var(--spacing-4);
        background-color: var(--color-bg-primary);
      }
    }

    // 导入对话框样式
    .import-section {
      :deep(.el-upload-dragger) {
        background-color: var(--color-bg-secondary);
        border: 2px dashed var(--color-border-light);
        border-radius: var(--radius-base);

        &:hover {
          border-color: var(--color-primary);
        }
      }
    }

    // 列设置样式
    .column-settings {
      :deep(.el-checkbox-group) {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);

        .el-checkbox {
          margin: 0;
        }
      }
    }

    // 响应式设计
    @media (width <= 1200px) {
      .statistics-section {
        .el-col {
          margin-bottom: var(--spacing-2);
        }
      }
    }

    @media (width <= 768px) {
      padding: var(--spacing-2);

      .page-header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;

        .page-actions {
          justify-content: center;
        }
      }

      .search-card {
        :deep(.el-form) {
          .el-col {
            span: 24 !important;
          }
        }
      }

      .statistics-section {
        .el-col {
          span: 12 !important;
        }
      }
    }
  }
</style>
