<template>
  <div class="api-demo">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>API集成和状态管理演示</h1>
      <p>展示统一API管理、Pinia状态管理、WebSocket实时数据、缓存管理和错误处理功能</p>
    </div>

    <!-- 功能模块卡片 -->
    <div class="demo-grid">
      <!-- API管理演示 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <Icon name="Link" />
            <span>API管理演示</span>
          </div>
        </template>
        
        <div class="demo-content">
          <div class="demo-stats">
            <div class="stat-item">
              <span class="stat-label">请求总数:</span>
              <span class="stat-value">{{ apiStats.totalRequests }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">成功率:</span>
              <span class="stat-value">{{ apiStats.successRate }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">平均延迟:</span>
              <span class="stat-value">{{ apiStats.averageLatency }}ms</span>
            </div>
          </div>
          
          <div class="demo-actions">
            <el-button @click="testApiRequest" :loading="apiLoading">
              发送API请求
            </el-button>
            <el-button @click="testApiError" type="warning">
              测试错误处理
            </el-button>
            <el-button @click="testRetryMechanism" type="info">
              测试重试机制
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 状态管理演示 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <Icon name="Database" />
            <span>状态管理演示</span>
          </div>
        </template>
        
        <div class="demo-content">
          <div class="store-status">
            <div class="status-item">
              <span class="status-label">用户登录:</span>
              <el-tag :type="authStore.isLoggedIn ? 'success' : 'danger'">
                {{ authStore.isLoggedIn ? '已登录' : '未登录' }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="status-label">实时连接:</span>
              <el-tag :type="realtimeStore.isConnected ? 'success' : 'warning'">
                {{ realtimeStore.isConnected ? '已连接' : '未连接' }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="status-label">系统状态:</span>
              <el-tag :type="configStore.isSystemOnline ? 'success' : 'danger'">
                {{ configStore.isSystemOnline ? '在线' : '离线' }}
              </el-tag>
            </div>
          </div>
          
          <div class="demo-actions">
            <el-button @click="testLogin" :loading="authStore.isLoggingIn">
              测试登录
            </el-button>
            <el-button @click="testLogout" :loading="authStore.isLoggingOut">
              测试登出
            </el-button>
            <el-button @click="testNotification" type="success">
              测试通知
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- WebSocket演示 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <Icon name="Connection" />
            <span>实时数据演示</span>
          </div>
        </template>
        
        <div class="demo-content">
          <div class="websocket-status">
            <div class="connection-info">
              <span class="info-label">连接状态:</span>
              <el-tag :type="getConnectionStatusType(realtimeStore.connectionStatus.status)">
                {{ getConnectionStatusText(realtimeStore.connectionStatus.status) }}
              </el-tag>
            </div>
            <div class="connection-info">
              <span class="info-label">延迟:</span>
              <span class="info-value">{{ realtimeStore.connectionStatus.latency }}ms</span>
            </div>
            <div class="connection-info">
              <span class="info-label">重连次数:</span>
              <span class="info-value">{{ realtimeStore.connectionStatus.reconnectAttempts }}</span>
            </div>
          </div>
          
          <div class="realtime-data">
            <div class="data-item">
              <span class="data-label">在线设备:</span>
              <span class="data-value">{{ realtimeStore.statistics.onlineEquipment }}</span>
            </div>
            <div class="data-item">
              <span class="data-label">活跃告警:</span>
              <span class="data-value text-danger">{{ realtimeStore.statistics.activeAlarms }}</span>
            </div>
            <div class="data-item">
              <span class="data-label">平均OEE:</span>
              <span class="data-value">{{ realtimeStore.statistics.averageOEE.toFixed(1) }}%</span>
            </div>
          </div>
          
          <div class="demo-actions">
            <el-button @click="connectWebSocket" :disabled="realtimeStore.isConnected">
              连接WebSocket
            </el-button>
            <el-button @click="disconnectWebSocket" :disabled="!realtimeStore.isConnected">
              断开连接
            </el-button>
            <el-button @click="sendTestMessage" :disabled="!realtimeStore.isConnected">
              发送测试消息
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 缓存管理演示 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <Icon name="DocumentCopy" />
            <span>缓存管理演示</span>
          </div>
        </template>
        
        <div class="demo-content">
          <div class="cache-stats">
            <div class="cache-item">
              <span class="cache-label">API缓存:</span>
              <span class="cache-value">{{ cacheStats.api.totalItems }} 项</span>
            </div>
            <div class="cache-item">
              <span class="cache-label">用户缓存:</span>
              <span class="cache-value">{{ cacheStats.user.totalItems }} 项</span>
            </div>
            <div class="cache-item">
              <span class="cache-label">命中率:</span>
              <span class="cache-value">{{ (cacheStats.api.hitRate * 100).toFixed(1) }}%</span>
            </div>
          </div>
          
          <div class="demo-actions">
            <el-button @click="testCacheSet">
              设置缓存
            </el-button>
            <el-button @click="testCacheGet">
              读取缓存
            </el-button>
            <el-button @click="clearAllCache" type="danger">
              清空缓存
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 错误处理演示 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <Icon name="Warning" />
            <span>错误处理演示</span>
          </div>
        </template>
        
        <div class="demo-content">
          <div class="error-stats">
            <div class="error-item">
              <span class="error-label">总错误数:</span>
              <span class="error-value">{{ errorStats.total }}</span>
            </div>
            <div class="error-item">
              <span class="error-label">已处理:</span>
              <span class="error-value text-success">{{ errorStats.handled }}</span>
            </div>
            <div class="error-item">
              <span class="error-label">未处理:</span>
              <span class="error-value text-danger">{{ errorStats.unhandled }}</span>
            </div>
          </div>
          
          <div class="demo-actions">
            <el-button @click="triggerInfoError" type="info">
              信息错误
            </el-button>
            <el-button @click="triggerWarningError" type="warning">
              警告错误
            </el-button>
            <el-button @click="triggerCriticalError" type="danger">
              严重错误
            </el-button>
            <el-button @click="exportErrorReport">
              导出错误报告
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 系统监控演示 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <Icon name="Monitor" />
            <span>系统监控演示</span>
          </div>
        </template>
        
        <div class="demo-content">
          <div class="system-metrics">
            <div class="metric-item">
              <span class="metric-label">内存使用:</span>
              <el-progress 
                :percentage="systemMetrics.memoryUsage" 
                :color="getProgressColor(systemMetrics.memoryUsage)"
              />
            </div>
            <div class="metric-item">
              <span class="metric-label">CPU使用:</span>
              <el-progress 
                :percentage="systemMetrics.cpuUsage" 
                :color="getProgressColor(systemMetrics.cpuUsage)"
              />
            </div>
            <div class="metric-item">
              <span class="metric-label">网络延迟:</span>
              <span class="metric-value">{{ systemMetrics.networkLatency }}ms</span>
            </div>
          </div>
          
          <div class="demo-actions">
            <el-button @click="refreshMetrics">
              刷新监控数据
            </el-button>
            <el-button @click="toggleAutoRefresh" :type="autoRefresh ? 'success' : 'info'">
              {{ autoRefresh ? '停止' : '开始' }}自动刷新
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 实时日志 -->
    <el-card class="log-panel">
      <template #header>
        <div class="card-header">
          <Icon name="Document" />
          <span>实时日志</span>
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </template>
      
      <div class="log-content" ref="logContainer">
        <div 
          v-for="(log, index) in logs" 
          :key="index" 
          :class="['log-item', `log-${log.level}`]"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="log-empty">
          暂无日志信息
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { Icon } from '@iconify/vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  useAuthStore, 
  useConfigStore, 
  useAppStore, 
  useRealtimeStore 
} from '@/stores'
import { 
  apiClient, 
  handleError, 
  handleApiError,
  globalErrorHandler,
  ErrorType,
  ErrorLevel
} from '@/api'
import { 
  apiCache, 
  userCache, 
  CacheFactory 
} from '@/utils/cache'

// Store实例
const authStore = useAuthStore()
const configStore = useConfigStore()
const appStore = useAppStore()
const realtimeStore = useRealtimeStore()

// 响应式数据
const apiLoading = ref(false)
const autoRefresh = ref(false)
let refreshTimer: number | null = null

// API统计数据
const apiStats = reactive({
  totalRequests: 156,
  successRate: 98.2,
  averageLatency: 245
})

// 缓存统计数据
const cacheStats = reactive({
  api: apiCache.getStats(),
  user: userCache.getStats()
})

// 错误统计数据
const errorStats = computed(() => globalErrorHandler.getErrorStats())

// 系统监控数据
const systemMetrics = reactive({
  memoryUsage: 45,
  cpuUsage: 32,
  networkLatency: 125
})

// 日志数据
const logs = ref<Array<{
  timestamp: Date
  level: 'info' | 'warning' | 'error' | 'success'
  message: string
}>>([])
const logContainer = ref<HTMLElement>()

// 计算属性
const getConnectionStatusType = (status: string) => {
  const typeMap = {
    'connected': 'success',
    'connecting': 'warning',
    'disconnected': 'danger',
    'reconnecting': 'warning',
    'error': 'danger'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

const getConnectionStatusText = (status: string) => {
  const textMap = {
    'connected': '已连接',
    'connecting': '连接中',
    'disconnected': '未连接',
    'reconnecting': '重连中',
    'error': '错误'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67C23A'
  if (percentage < 80) return '#E6A23C'
  return '#F56C6C'
}

// 日志方法
const addLog = (level: 'info' | 'warning' | 'error' | 'success', message: string) => {
  logs.value.unshift({
    timestamp: new Date(),
    level,
    message
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value.splice(100)
  }
  
  // 自动滚动到顶部
  setTimeout(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = 0
    }
  }, 10)
}

const clearLogs = () => {
  logs.value = []
  addLog('info', '日志已清空')
}

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString()
}

// API测试方法
const testApiRequest = async () => {
  apiLoading.value = true
  addLog('info', '发送API请求测试...')
  
  try {
    const response = await apiClient.get('/test/demo', {}, {
      showLoading: true,
      cache: true
    })
    
    apiStats.totalRequests++
    apiStats.averageLatency = Math.floor(Math.random() * 500) + 100
    
    addLog('success', `API请求成功: ${JSON.stringify(response)}`)
    ElMessage.success('API请求成功')
  } catch (error) {
    addLog('error', `API请求失败: ${error}`)
  } finally {
    apiLoading.value = false
  }
}

const testApiError = async () => {
  addLog('info', '测试API错误处理...')
  
  try {
    await apiClient.get('/test/error')
  } catch (error) {
    addLog('warning', `API错误已被捕获和处理: ${error}`)
  }
}

const testRetryMechanism = async () => {
  addLog('info', '测试API重试机制...')
  
  try {
    await apiClient.get('/test/retry', {}, {
      retry: true,
      retryCount: 3,
      retryDelay: 1000
    })
  } catch (error) {
    addLog('error', `重试机制测试完成: ${error}`)
  }
}

// 认证测试方法
const testLogin = async () => {
  addLog('info', '测试用户登录...')
  
  const success = await authStore.login({
    username: 'admin',
    password: '123456'
  })
  
  if (success) {
    addLog('success', `用户登录成功: ${authStore.currentUser?.realName}`)
  } else {
    addLog('error', '用户登录失败')
  }
}

const testLogout = async () => {
  addLog('info', '测试用户登出...')
  
  await authStore.logout(false)
  addLog('warning', '用户已登出')
}

const testNotification = () => {
  appStore.addNotification({
    type: 'success',
    title: '测试通知',
    message: '这是一个测试通知消息'
  })
  addLog('info', '发送测试通知')
}

// WebSocket测试方法
const connectWebSocket = async () => {
  addLog('info', '连接WebSocket...')
  
  try {
    await realtimeStore.connect()
    addLog('success', 'WebSocket连接成功')
  } catch (error) {
    addLog('error', `WebSocket连接失败: ${error}`)
  }
}

const disconnectWebSocket = () => {
  addLog('info', '断开WebSocket连接...')
  realtimeStore.disconnect()
  addLog('warning', 'WebSocket连接已断开')
}

const sendTestMessage = () => {
  const success = realtimeStore.sendMessage({
    type: 'test',
    data: { message: '这是一个测试消息' }
  })
  
  if (success) {
    addLog('info', '发送WebSocket测试消息')
  } else {
    addLog('error', 'WebSocket消息发送失败')
  }
}

// 缓存测试方法
const testCacheSet = () => {
  const testData = {
    id: Date.now(),
    message: '这是缓存测试数据',
    timestamp: new Date().toISOString()
  }
  
  apiCache.set(`test_${testData.id}`, testData)
  cacheStats.api = apiCache.getStats()
  
  addLog('success', `缓存数据已设置: test_${testData.id}`)
}

const testCacheGet = () => {
  const keys = apiCache.keys('test_*')
  if (keys.length > 0) {
    const data = apiCache.get(keys[0])
    addLog('success', `读取缓存数据: ${JSON.stringify(data)}`)
  } else {
    addLog('warning', '没有找到测试缓存数据')
  }
}

const clearAllCache = () => {
  apiCache.clear()
  userCache.clear()
  
  cacheStats.api = apiCache.getStats()
  cacheStats.user = userCache.getStats()
  
  addLog('warning', '所有缓存已清空')
  ElMessage.success('缓存清空成功')
}

// 错误处理测试方法
const triggerInfoError = () => {
  handleError('这是一个信息级别的错误', ErrorType.BUSINESS_ERROR, ErrorLevel.INFO)
  addLog('info', '触发信息级别错误')
}

const triggerWarningError = () => {
  handleError('这是一个警告级别的错误', ErrorType.VALIDATION_ERROR, ErrorLevel.WARNING)
  addLog('warning', '触发警告级别错误')
}

const triggerCriticalError = () => {
  handleError('这是一个严重级别的错误', ErrorType.EQUIPMENT_ERROR, ErrorLevel.CRITICAL, {
    showToUser: true,
    reportToServer: true
  })
  addLog('error', '触发严重级别错误')
}

const exportErrorReport = () => {
  const report = globalErrorHandler.exportErrorReport()
  
  // 创建下载链接
  const blob = new Blob([report], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  
  link.href = url
  link.download = `error-report-${new Date().toISOString().split('T')[0]}.json`
  link.click()
  
  URL.revokeObjectURL(url)
  addLog('info', '错误报告已导出')
  ElMessage.success('错误报告导出成功')
}

// 系统监控方法
const refreshMetrics = () => {
  systemMetrics.memoryUsage = Math.floor(Math.random() * 100)
  systemMetrics.cpuUsage = Math.floor(Math.random() * 100)
  systemMetrics.networkLatency = Math.floor(Math.random() * 500) + 50
  
  addLog('info', '系统监控数据已刷新')
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    refreshTimer = window.setInterval(() => {
      refreshMetrics()
    }, 5000)
    addLog('info', '自动刷新已启用 (5秒间隔)')
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    addLog('warning', '自动刷新已停用')
  }
}

// 生命周期
onMounted(() => {
  addLog('success', 'API集成和状态管理演示页面已加载')
  
  // 定期更新缓存统计
  setInterval(() => {
    cacheStats.api = apiCache.getStats()
    cacheStats.user = userCache.getStats()
  }, 2000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style lang="scss" scoped>
.api-demo {
  padding: 24px;
  min-height: calc(100vh - 100px);
  background: var(--color-bg-light);
}

.page-header {
  margin-bottom: 24px;
  text-align: center;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0 0 8px 0;
  }

  p {
    color: var(--color-text-secondary);
    font-size: 16px;
    margin: 0;
  }
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.demo-card {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--color-text-primary);
  }
}

.demo-content {
  .demo-stats,
  .store-status,
  .websocket-status,
  .cache-stats,
  .error-stats {
    margin-bottom: 16px;
    padding: 16px;
    background: var(--color-bg-soft);
    border-radius: var(--radius-base);

    .stat-item,
    .status-item,
    .connection-info,
    .cache-item,
    .error-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .stat-label,
      .status-label,
      .info-label,
      .cache-label,
      .error-label {
        color: var(--color-text-secondary);
        font-size: 14px;
      }

      .stat-value,
      .info-value,
      .cache-value,
      .error-value {
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }
  }

  .realtime-data {
    margin-bottom: 16px;
    padding: 16px;
    background: var(--color-bg-soft);
    border-radius: var(--radius-base);

    .data-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .data-label {
        color: var(--color-text-secondary);
        font-size: 14px;
      }

      .data-value {
        font-weight: 600;
        color: var(--color-text-primary);

        &.text-danger {
          color: var(--color-danger);
        }

        &.text-success {
          color: var(--color-success);
        }
      }
    }
  }

  .system-metrics {
    margin-bottom: 16px;
    padding: 16px;
    background: var(--color-bg-soft);
    border-radius: var(--radius-base);

    .metric-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .metric-label {
        display: block;
        color: var(--color-text-secondary);
        font-size: 14px;
        margin-bottom: 8px;
      }

      .metric-value {
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }
  }

  .demo-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .el-button {
      flex: none;
    }
  }
}

.log-panel {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    > div {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.log-content {
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;

  .log-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 4px 0;
    border-bottom: 1px solid var(--color-border-soft);

    &:last-child {
      border-bottom: none;
    }

    .log-time {
      color: var(--color-text-placeholder);
      white-space: nowrap;
      flex-shrink: 0;
      min-width: 80px;
    }

    .log-level {
      font-weight: 600;
      white-space: nowrap;
      flex-shrink: 0;
      min-width: 60px;
    }

    .log-message {
      word-break: break-word;
    }

    &.log-info .log-level {
      color: var(--color-primary);
    }

    &.log-success .log-level {
      color: var(--color-success);
    }

    &.log-warning .log-level {
      color: var(--color-warning);
    }

    &.log-error .log-level {
      color: var(--color-danger);
    }
  }

  .log-empty {
    text-align: center;
    color: var(--color-text-placeholder);
    padding: 40px;
    font-style: italic;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .api-demo {
    padding: 16px;
  }

  .demo-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .demo-content .demo-actions {
    .el-button {
      font-size: 12px;
      padding: 8px 12px;
    }
  }
}
</style>