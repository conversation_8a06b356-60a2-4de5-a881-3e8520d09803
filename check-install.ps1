Write-Host "D盘软件安装验证" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

Write-Host ""
Write-Host "环境变量检查:" -ForegroundColor Yellow
Write-Host "JAVA_HOME: $env:JAVA_HOME"
Write-Host "MAVEN_HOME: $env:MAVEN_HOME" 
Write-Host "REDIS_HOME: $env:REDIS_HOME"

Write-Host ""
Write-Host "命令检查:" -ForegroundColor Yellow

if (Get-Command java -ErrorAction SilentlyContinue) {
    Write-Host "Java: 可用" -ForegroundColor Green
} else {
    Write-Host "Java: 不可用" -ForegroundColor Red
}

if (Get-Command mvn -ErrorAction SilentlyContinue) {
    Write-Host "Maven: 可用" -ForegroundColor Green
} else {
    Write-Host "Maven: 不可用" -ForegroundColor Red
}

if (Get-Command redis-cli -ErrorAction SilentlyContinue) {
    Write-Host "Redis: 可用" -ForegroundColor Green
} else {
    Write-Host "Redis: 不可用" -ForegroundColor Red
}

Write-Host ""
Write-Host "文件检查:" -ForegroundColor Yellow

if (Test-Path "D:\Development\Java\jdk-17\bin\java.exe") {
    Write-Host "Java文件: 存在" -ForegroundColor Green
} else {
    Write-Host "Java文件: 不存在" -ForegroundColor Red
}

if (Test-Path "D:\Development\Java\maven\bin\mvn.cmd") {
    Write-Host "Maven文件: 存在" -ForegroundColor Green
} else {
    Write-Host "Maven文件: 不存在" -ForegroundColor Red
}

if (Test-Path "D:\Development\Redis\redis-cli.exe") {
    Write-Host "Redis文件: 存在" -ForegroundColor Green
} else {
    Write-Host "Redis文件: 不存在" -ForegroundColor Red
}

if (Test-Path "D:\Projects\JSCIM-System\docker-compose.yml") {
    Write-Host "Docker配置: 存在" -ForegroundColor Green
} else {
    Write-Host "Docker配置: 不存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "检查完成" -ForegroundColor Cyan