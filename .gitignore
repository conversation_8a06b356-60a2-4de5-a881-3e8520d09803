# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependencies
node_modules/
jspm_packages/
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Build output
dist/
dist-ssr/
build/
lib/

# Environment variables
.env.local
.env.*.local

# Editor directories and files
.vscode/settings.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Auto-generated files
src/types/auto-imports.d.ts
src/types/components.d.ts
.eslintrc-auto-import.json

# Test coverage
coverage/
*.lcov

# Cypress
cypress/videos/
cypress/screenshots/

# Temporary files
*.tmp
*.temp
temp/

# Cache directories
.cache/
.parcel-cache/

# Local env files
.env.local
.env.*.local

# Generated CSS from Sass
*.css.map