# 测试程序管理模块设计

## 1. 模块概述

### 1.1 模块定位
测试程序管理模块是IC封测CIM系统的核心测试技术模块，专门管理从测试程序开发到生产应用的完整测试程序生命周期。该模块涵盖CP（Circuit Probing）和FT（Final Test）测试程序的开发、调试、验证、发布、版本控制以及测试数据分析，是确保IC产品质量和测试效率的技术保障。

### 1.2 IC封测测试程序特点
- **测试复杂性高**：涉及DC、AC、功能、扫描等多种测试类型
- **平台多样性**：支持多种ATE平台（Teradyne、Advantest、Cohu等）
- **精度要求严格**：测试精度通常在μV、nA级别
- **速度要求高**：单颗IC测试时间通常在几十毫秒到几秒
- **关联性强**：与设计规格、工艺参数、质量标准紧密关联
- **版本管理复杂**：同一产品的不同客户、不同版本需要不同测试程序

### 1.3 核心业务价值
- **缩短测试开发周期**：通过模板复用和自动生成，减少40-60%的开发时间
- **提高测试质量**：通过标准化和验证流程，确保测试程序质量和一致性
- **降低测试成本**：通过优化测试时间和提高良率，降低测试成本15-25%
- **增强测试能力**：通过数据分析和机器学习，提升测试缺陷检出能力
- **知识资产管理**：建立企业级测试程序知识库，形成技术积累

### 1.4 应用场景覆盖
```
测试程序管理应用场景
├── CP测试程序管理
│   ├── Wafer级电性能测试程序
│   ├── Die级功能测试程序
│   ├── 参数化测试程序
│   └── Wafer Map生成程序
├── FT测试程序管理
│   ├── 成品电性能测试程序
│   ├── 功能测试程序
│   ├── 老化测试程序
│   └── 特殊测试程序
├── 测试程序开发
│   ├── 需求分析与规格制定
│   ├── 测试程序编码
│   ├── 测试向量生成
│   └── 测试限值设定
├── 测试程序验证
│   ├── 功能验证测试
│   ├── 关联性验证
│   ├── 重复性验证
│   └── 客户样品验证
├── 测试程序优化
│   ├── 测试时间优化
│   ├── 测试覆盖率优化
│   ├── 测试精度优化
│   └── 并行测试优化
├── 版本控制管理
│   ├── 程序版本发布
│   ├── 版本变更管理
│   ├── 回退管理
│   └── 版本对比分析
├── 测试数据分析
│   ├── 测试结果统计
│   ├── 良率分析
│   ├── 参数分布分析
│   └── 缺陷模式分析
├── ATE平台集成
│   ├── Teradyne平台集成
│   ├── Advantest平台集成
│   ├── Cohu平台集成
│   └── 自研平台集成
└── 测试标准化
    ├── 测试规范制定
    ├── 测试流程标准化
    ├── 测试文档模板
    └── 培训考核体系
```

## 2. IC封测测试专业架构设计

### 2.1 技术架构
```
测试程序管理模块架构
├── 测试程序开发环境      # 集成开发环境和代码编辑器
├── 多平台适配引擎        # 支持多种ATE平台的程序转换
├── 测试程序编译系统      # 程序编译、优化和验证
├── 版本控制系统          # 完整的程序版本管理
├── 测试程序验证平台      # 自动化测试程序验证
├── 测试数据分析引擎      # 实时测试数据分析和挖掘
├── 测试程序优化引擎      # AI驱动的程序性能优化
├── ATE设备集成接口       # 与各种ATE设备的深度集成
├── 测试知识库系统        # 测试经验和最佳实践管理
└── 质量统计分析中心      # 测试质量统计和改进分析
```

### 2.2 核心数据模型

#### 2.2.1 测试程序项目管理
```sql
-- 测试程序开发项目表
CREATE TABLE ic_test_program_projects (
    project_id VARCHAR(30) PRIMARY KEY,
    npi_project_id VARCHAR(30),              -- 关联NPI项目ID
    project_code VARCHAR(50) UNIQUE,         -- 测试程序项目编码
    project_name VARCHAR(200),               -- 项目名称
    product_code VARCHAR(100),               -- 产品编码
    package_type VARCHAR(50),                -- 封装类型
    test_phase ENUM('cp','ft','burn_in','reliability'), -- 测试阶段
    ate_platform VARCHAR(50),                -- ATE平台
    target_ate_model VARCHAR(100),           -- 目标ATE型号
    development_priority ENUM('low','normal','high','urgent'), -- 开发优先级
    complexity LEVEL ENUM('simple','medium','complex','very_complex'), -- 复杂度
    test_engineer VARCHAR(20),               -- 测试工程师
    project_manager VARCHAR(20),             -- 项目经理
    customer_requirements JSON,              -- 客户测试要求
    test_coverage_target DECIMAL(5,2) DEFAULT 100.0, -- 目标测试覆盖率%
    test_time_target INT,                    -- 目标测试时间(ms)
    dppm_target DECIMAL(8,2),                -- 目标DPPM
    yield_target DECIMAL(5,2),               -- 目标良率%
    development_budget DECIMAL(12,2),        -- 开发预算
    actual_cost DECIMAL(12,2),              -- 实际成本
    planned_start_date DATE,                 -- 计划开始日期
    planned_end_date DATE,                   -- 计划结束日期
    actual_start_date DATE,                  -- 实际开始日期
    actual_end_date DATE,                    -- 实际结束日期
    project_status ENUM('planning','developing','debugging','testing','validating','completed','cancelled'), -- 项目状态
    risk_assessment JSON,                    -- 风险评估
    technical_challenges TEXT,               -- 技术挑战
    success_criteria JSON,                   -- 成功标准
    lessons_learned TEXT,                    -- 经验教训
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_npi_project (npi_project_id),
    INDEX idx_engineer_status (test_engineer, project_status),
    INDEX idx_ate_platform (ate_platform),
    INDEX idx_test_phase (test_phase),
    INDEX idx_priority_dates (development_priority, planned_end_date)
);

-- 测试程序主表
CREATE TABLE ic_test_programs (
    program_id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30),                  -- 测试程序项目ID
    program_code VARCHAR(100) UNIQUE,        -- 程序编码
    program_name VARCHAR(200),               -- 程序名称
    program_version VARCHAR(20),             -- 程序版本
    program_type ENUM('cp_test','ft_test','burn_in','reliability_test','characterization'), -- 程序类型
    ate_platform VARCHAR(50),                -- ATE平台
    ate_model VARCHAR(100),                  -- ATE型号
    program_language VARCHAR(50),            -- 编程语言
    compiler_version VARCHAR(20),            -- 编译器版本
    test_head_count INT,                     -- 测试头数量
    parallel_sites INT DEFAULT 1,            -- 并行站点数
    test_time_ms INT,                        -- 测试时间(毫秒)
    test_parameter_count INT,                -- 测试参数数量
    vector_count BIGINT,                     -- 测试向量数量
    pattern_count INT,                       -- 测试模式数量
    memory_usage_mb DECIMAL(10,2),          -- 内存使用量(MB)
    program_size_kb DECIMAL(10,2),          -- 程序大小(KB)
    compilation_time_s INT,                  -- 编译时间(秒)
    test_coverage DECIMAL(5,2),             -- 测试覆盖率%
    fault_coverage DECIMAL(5,2),            -- 故障覆盖率%
    program_status ENUM('development','testing','validated','production','deprecated'), -- 程序状态
    validation_status ENUM('not_validated','in_validation','validated','failed_validation'), -- 验证状态
    release_date DATE,                       -- 发布日期
    expiry_date DATE,                        -- 失效日期
    program_description TEXT,                -- 程序描述
    change_log TEXT,                         -- 变更记录
    performance_metrics JSON,               -- 性能指标
    quality_metrics JSON,                   -- 质量指标
    optimization_history JSON,              -- 优化历史
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_by VARCHAR(20),
    updated_at TIMESTAMP,
    
    INDEX idx_project_program (project_id, program_code),
    INDEX idx_ate_platform_model (ate_platform, ate_model),
    INDEX idx_program_type (program_type),
    INDEX idx_status_release (program_status, release_date),
    INDEX idx_validation_status (validation_status)
);

-- 测试参数定义表
CREATE TABLE ic_test_parameters (
    parameter_id VARCHAR(30) PRIMARY KEY,
    program_id VARCHAR(30),                  -- 测试程序ID
    parameter_sequence INT,                  -- 参数序号
    parameter_name VARCHAR(100),             -- 参数名称
    parameter_code VARCHAR(50),              -- 参数编码
    test_number INT,                         -- 测试号
    parameter_category ENUM('dc','ac','functional','scan','characterization'), -- 参数分类
    test_type ENUM('voltage','current','frequency','time','digital','analog'), -- 测试类型
    pin_list TEXT,                           -- 引脚列表
    test_condition VARCHAR(500),             -- 测试条件
    measurement_unit VARCHAR(20),            -- 测量单位
    nominal_value DECIMAL(15,8),             -- 标准值
    lower_limit DECIMAL(15,8),               -- 下限
    upper_limit DECIMAL(15,8),               -- 上限
    resolution DECIMAL(15,10),               -- 分辨率
    accuracy DECIMAL(15,10),                 -- 精度
    measurement_range_min DECIMAL(15,8),     -- 测量范围最小值
    measurement_range_max DECIMAL(15,8),     -- 测量范围最大值
    settling_time_us DECIMAL(10,3),         -- 建立时间(微秒)
    measurement_time_us DECIMAL(10,3),      -- 测量时间(微秒)
    force_value DECIMAL(15,8),              -- 强制值
    load_value DECIMAL(15,8),               -- 负载值
    termination_value DECIMAL(15,8),        -- 终端值
    parameter_priority ENUM('critical','major','minor'), -- 参数重要性
    is_binning_parameter BOOLEAN DEFAULT FALSE, -- 是否分选参数
    is_datalog_parameter BOOLEAN DEFAULT TRUE, -- 是否记录数据
    is_spc_monitored BOOLEAN DEFAULT FALSE, -- 是否SPC监控
    test_method_description TEXT,           -- 测试方法描述
    calibration_requirements TEXT,          -- 校准要求
    correlation_requirements TEXT,          -- 关联要求
    repeatability_specification DECIMAL(8,4), -- 重复性规格
    reproducibility_specification DECIMAL(8,4), -- 再现性规格
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_program_sequence (program_id, parameter_sequence),
    INDEX idx_parameter_code (parameter_code),
    INDEX idx_test_number (test_number),
    INDEX idx_category_type (parameter_category, test_type),
    INDEX idx_priority_binning (parameter_priority, is_binning_parameter)
);
```

#### 2.2.2 测试程序版本控制
```sql
-- 测试程序版本控制表
CREATE TABLE ic_test_program_versions (
    version_id VARCHAR(30) PRIMARY KEY,
    program_id VARCHAR(30),                  -- 测试程序ID
    version_number VARCHAR(20),              -- 版本号
    parent_version_id VARCHAR(30),          -- 父版本ID
    version_type ENUM('major','minor','patch','hotfix'), -- 版本类型
    branch_name VARCHAR(50),                 -- 分支名称
    commit_hash VARCHAR(64),                 -- 提交哈希
    change_type ENUM('new_feature','enhancement','bug_fix','optimization','compliance'), -- 变更类型
    change_reason VARCHAR(500),              -- 变更原因
    change_description TEXT,                 -- 变更描述
    impact_analysis TEXT,                    -- 影响分析
    test_changes JSON,                       -- 测试变更详情
    parameter_changes JSON,                  -- 参数变更详情
    limit_changes JSON,                      -- 限值变更详情
    performance_changes JSON,               -- 性能变更详情
    compatibility_impact TEXT,              -- 兼容性影响
    validation_required BOOLEAN DEFAULT TRUE, -- 是否需要重新验证
    customer_approval_required BOOLEAN DEFAULT FALSE, -- 是否需要客户批准
    release_notes TEXT,                      -- 发布说明
    version_status ENUM('development','testing','approved','released','deprecated'), -- 版本状态
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    approved_by VARCHAR(20),
    approved_at TIMESTAMP,
    released_by VARCHAR(20),
    released_at TIMESTAMP,
    
    INDEX idx_program_version (program_id, version_number),
    INDEX idx_parent_version (parent_version_id),
    INDEX idx_version_status (version_status),
    INDEX idx_release_date (released_at)
);

-- 测试程序代码存储表
CREATE TABLE ic_test_program_source_code (
    source_id VARCHAR(30) PRIMARY KEY,
    version_id VARCHAR(30),                  -- 程序版本ID
    file_type ENUM('main_program','pattern_file','vector_file','timing_file','pin_map','config_file'), -- 文件类型
    file_name VARCHAR(200),                  -- 文件名
    file_path VARCHAR(500),                  -- 文件路径
    file_size_bytes BIGINT,                  -- 文件大小(字节)
    file_hash VARCHAR(64),                   -- 文件哈希值
    source_code LONGTEXT,                    -- 源代码内容
    binary_data LONGBLOB,                    -- 二进制数据
    compilation_order INT,                   -- 编译顺序
    dependencies JSON,                       -- 依赖文件列表
    include_files JSON,                      -- 包含文件列表
    encoding VARCHAR(20) DEFAULT 'UTF-8',   -- 文件编码
    line_count INT,                          -- 代码行数
    comment_ratio DECIMAL(5,2),             -- 注释比例
    complexity_score INT,                    -- 复杂度评分
    last_modified_by VARCHAR(20),
    last_modified_at TIMESTAMP,
    created_at TIMESTAMP,
    
    INDEX idx_version_file (version_id, file_type),
    INDEX idx_file_hash (file_hash),
    INDEX idx_compilation_order (version_id, compilation_order)
);
```

#### 2.2.3 测试程序验证管理
```sql
-- 测试程序验证项目表
CREATE TABLE ic_test_program_validations (
    validation_id VARCHAR(30) PRIMARY KEY,
    program_id VARCHAR(30),                  -- 测试程序ID
    version_id VARCHAR(30),                  -- 程序版本ID
    validation_code VARCHAR(50) UNIQUE,     -- 验证编码
    validation_name VARCHAR(200),           -- 验证名称
    validation_type ENUM('functional','correlation','repeatability','reproducibility','stress','characterization'), -- 验证类型
    validation_phase ENUM('alpha','beta','gamma','production'), -- 验证阶段
    validation_scope ENUM('full','partial','regression','specific'), -- 验证范围
    validation_method ENUM('golden_die','reference_unit','statistical','comparative'), -- 验证方法
    sample_size INT,                         -- 样品数量
    test_sites INT,                          -- 测试站点数
    test_runs_per_site INT,                  -- 每站点测试次数
    reference_program_id VARCHAR(30),       -- 参考程序ID
    reference_platform VARCHAR(50),         -- 参考平台
    validation_criteria JSON,               -- 验证准则
    pass_criteria JSON,                      -- 通过标准
    statistical_requirements JSON,          -- 统计要求
    validation_engineer VARCHAR(20),        -- 验证工程师
    planned_start_date DATE,                 -- 计划开始日期
    planned_end_date DATE,                   -- 计划结束日期
    actual_start_date DATE,                  -- 实际开始日期
    actual_end_date DATE,                    -- 实际结束日期
    validation_status ENUM('planning','running','analyzing','completed','failed','cancelled'), -- 验证状态
    validation_result ENUM('passed','failed','conditional_pass','inconclusive'), -- 验证结果
    confidence_level DECIMAL(5,2),          -- 置信度
    correlation_coefficient DECIMAL(8,6),   -- 关联系数
    repeatability_analysis JSON,            -- 重复性分析结果
    reproducibility_analysis JSON,          -- 再现性分析结果
    outlier_analysis JSON,                  -- 异常值分析
    statistical_summary JSON,               -- 统计摘要
    validation_report_path VARCHAR(500),    -- 验证报告路径
    issues_found JSON,                      -- 发现的问题
    recommendations TEXT,                    -- 建议
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_program_validation (program_id, validation_code),
    INDEX idx_version_validation (version_id),
    INDEX idx_validation_type_phase (validation_type, validation_phase),
    INDEX idx_engineer_status (validation_engineer, validation_status),
    INDEX idx_validation_dates (planned_start_date, planned_end_date)
);

-- 测试程序验证数据表
CREATE TABLE ic_test_validation_data (
    data_id VARCHAR(30) PRIMARY KEY,
    validation_id VARCHAR(30),               -- 验证项目ID
    parameter_id VARCHAR(30),                -- 测试参数ID
    sample_id VARCHAR(50),                   -- 样品ID
    site_number INT,                         -- 站点号
    run_number INT,                          -- 运行次数
    test_timestamp TIMESTAMP(3),             -- 测试时间戳
    measured_value DECIMAL(15,8),            -- 测量值
    reference_value DECIMAL(15,8),           -- 参考值
    difference DECIMAL(15,8),                -- 差值
    percent_difference DECIMAL(8,4),         -- 百分比差值
    pass_fail_result ENUM('pass','fail'),    -- 通过/失败结果
    measurement_quality ENUM('good','questionable','invalid'), -- 测量质量
    temperature DECIMAL(8,2),                -- 测试温度(°C)
    voltage DECIMAL(8,4),                    -- 测试电压(V)
    frequency DECIMAL(15,3),                 -- 测试频率(Hz)
    environmental_conditions JSON,           -- 环境条件
    equipment_used VARCHAR(100),             -- 使用设备
    operator_id VARCHAR(20),                 -- 操作员
    test_duration_ms INT,                    -- 测试持续时间(毫秒)
    repeatability_flag BOOLEAN DEFAULT FALSE, -- 重复性标志
    outlier_flag BOOLEAN DEFAULT FALSE,     -- 异常值标志
    exclusion_reason TEXT,                   -- 排除原因
    data_notes TEXT,                         -- 数据备注
    created_at TIMESTAMP,
    
    INDEX idx_validation_parameter (validation_id, parameter_id),
    INDEX idx_sample_site_run (sample_id, site_number, run_number),
    INDEX idx_test_timestamp (test_timestamp),
    INDEX idx_pass_fail (pass_fail_result),
    INDEX idx_outlier_flag (outlier_flag)
);
```

#### 2.2.4 测试数据分析管理
```sql
-- 测试数据分析项目表
CREATE TABLE ic_test_data_analysis (
    analysis_id VARCHAR(30) PRIMARY KEY,
    program_id VARCHAR(30),                  -- 测试程序ID
    analysis_code VARCHAR(50) UNIQUE,       -- 分析编码
    analysis_name VARCHAR(200),             -- 分析名称
    analysis_type ENUM('yield_analysis','parameter_distribution','correlation_analysis','cpk_analysis','outlier_detection'), -- 分析类型
    analysis_scope ENUM('single_lot','multi_lot','time_series','comparative'), -- 分析范围
    data_source ENUM('production','validation','characterization','debug'), -- 数据来源
    time_period_start TIMESTAMP,            -- 时间段开始
    time_period_end TIMESTAMP,              -- 时间段结束
    lot_numbers JSON,                        -- 批次号列表
    wafer_numbers JSON,                      -- 晶圆号列表
    test_conditions JSON,                    -- 测试条件筛选
    sample_size BIGINT,                      -- 样品数量
    parameter_list JSON,                     -- 参数列表
    statistical_methods JSON,               -- 统计方法
    analysis_parameters JSON,               -- 分析参数
    analysis_engineer VARCHAR(20),          -- 分析工程师
    analysis_purpose TEXT,                  -- 分析目的
    analysis_status ENUM('setup','running','completed','failed'), -- 分析状态
    start_time TIMESTAMP,                   -- 开始时间
    end_time TIMESTAMP,                     -- 结束时间
    processing_time_minutes INT,            -- 处理时间(分钟)
    results_summary JSON,                   -- 结果摘要
    key_findings TEXT,                      -- 关键发现
    recommendations TEXT,                   -- 建议
    report_generated BOOLEAN DEFAULT FALSE, -- 是否生成报告
    report_path VARCHAR(500),               -- 报告路径
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_program_analysis (program_id, analysis_code),
    INDEX idx_analysis_type (analysis_type),
    INDEX idx_time_period (time_period_start, time_period_end),
    INDEX idx_engineer_status (analysis_engineer, analysis_status),
    INDEX idx_analysis_dates (start_time, end_time)
);

-- 测试数据分析结果表
CREATE TABLE ic_test_analysis_results (
    result_id VARCHAR(30) PRIMARY KEY,
    analysis_id VARCHAR(30),                 -- 分析项目ID
    parameter_id VARCHAR(30),                -- 测试参数ID
    result_category ENUM('descriptive','distribution','correlation','trend','anomaly'), -- 结果类别
    result_type VARCHAR(100),                -- 结果类型
    result_name VARCHAR(200),                -- 结果名称
    numeric_value DECIMAL(15,8),             -- 数值结果
    text_value TEXT,                         -- 文本结果
    json_value JSON,                         -- JSON格式结果
    statistical_significance DECIMAL(8,6),   -- 统计显著性
    confidence_interval_lower DECIMAL(15,8), -- 置信区间下限
    confidence_interval_upper DECIMAL(15,8), -- 置信区间上限
    confidence_level DECIMAL(5,2),          -- 置信水平
    p_value DECIMAL(8,6),                   -- P值
    correlation_coefficient DECIMAL(8,6),   -- 关联系数
    r_squared DECIMAL(8,6),                 -- R平方值
    standard_error DECIMAL(15,8),           -- 标准误差
    degrees_of_freedom INT,                  -- 自由度
    sample_size BIGINT,                      -- 样本大小
    outlier_count INT,                       -- 异常值数量
    distribution_type VARCHAR(50),           -- 分布类型
    normality_test_result ENUM('normal','non_normal','inconclusive'), -- 正态性检验结果
    trend_direction ENUM('increasing','decreasing','stable','cyclical'), -- 趋势方向
    change_point_detected BOOLEAN DEFAULT FALSE, -- 是否检测到变化点
    anomaly_score DECIMAL(8,4),            -- 异常评分
    business_impact_level ENUM('low','medium','high','critical'), -- 业务影响级别
    actionable_insight TEXT,                -- 可行洞察
    visualization_data JSON,                -- 可视化数据
    result_quality ENUM('excellent','good','acceptable','poor'), -- 结果质量
    validation_status ENUM('validated','needs_validation','invalid'), -- 验证状态
    created_at TIMESTAMP,
    
    INDEX idx_analysis_parameter (analysis_id, parameter_id),
    INDEX idx_result_category_type (result_category, result_type),
    INDEX idx_significance (statistical_significance),
    INDEX idx_business_impact (business_impact_level),
    INDEX idx_validation_status (validation_status)
);
```

## 3. 测试程序管理引擎

### 3.1 测试程序开发引擎
```java
@Service
public class TestProgramDevelopmentService {
    
    @Autowired
    private TestProgramRepository programRepository;
    
    @Autowired
    private TestParameterRepository parameterRepository;
    
    @Autowired
    private VersionControlService versionControlService;
    
    /**
     * 创建测试程序开发项目
     */
    public ICTestProgramProject createProgramProject(ProgramDevelopmentRequest request) {
        // 1. 验证项目参数
        validateProjectRequest(request);
        
        // 2. 创建项目主记录
        ICTestProgramProject project = new ICTestProgramProject();
        project.setProjectId(IdGenerator.generateId());
        project.setNpiProjectId(request.getNpiProjectId());
        project.setProjectCode(generateProjectCode(request));
        project.setProjectName(request.getProjectName());
        project.setProductCode(request.getProductCode());
        project.setPackageType(request.getPackageType());
        project.setTestPhase(request.getTestPhase());
        project.setAtePlatform(request.getAtePlatform());
        project.setTargetAteModel(request.getTargetAteModel());
        project.setDevelopmentPriority(request.getDevelopmentPriority());
        project.setComplexityLevel(assessComplexity(request));
        project.setTestEngineer(request.getTestEngineer());
        project.setProjectManager(getCurrentUserId());
        project.setCustomerRequirements(request.getCustomerRequirements());
        project.setTestCoverageTarget(request.getTestCoverageTarget());
        project.setTestTimeTarget(request.getTestTimeTarget());
        project.setDppmTarget(request.getDppmTarget());
        project.setYieldTarget(request.getYieldTarget());
        project.setProjectStatus(ProjectStatus.PLANNING);
        project.setRiskAssessment(performRiskAssessment(request));
        
        project = testProgramProjectRepository.save(project);
        
        // 3. 创建开发计划
        ProjectDevelopmentPlan developmentPlan = createDevelopmentPlan(project, request);
        
        // 4. 生成测试参数模板
        List<TestParameterTemplate> parameterTemplates = generateParameterTemplates(
            request.getProductSpecification(), request.getTestPhase());
        
        // 5. 设置开发环境
        DevelopmentEnvironment devEnv = setupDevelopmentEnvironment(project);
        
        // 6. 初始化版本控制
        versionControlService.initializeRepository(project.getProjectId());
        
        // 7. 创建项目团队
        ProjectTeam team = createProjectTeam(project, request.getTeamMembers());
        
        return project;
    }
    
    /**
     * 基于模板生成测试程序
     */
    public ICTestProgram generateProgramFromTemplate(ProgramGenerationRequest request) {
        // 1. 选择适合的程序模板
        TestProgramTemplate template = selectOptimalTemplate(
            request.getProductCode(), request.getPackageType(), 
            request.getTestPhase(), request.getAtePlatform());
        
        if (template == null) {
            throw new IllegalStateException("找不到合适的程序模板");
        }
        
        // 2. 创建程序主记录
        ICTestProgram program = new ICTestProgram();
        program.setProgramId(IdGenerator.generateId());
        program.setProjectId(request.getProjectId());
        program.setProgramCode(generateProgramCode(request));
        program.setProgramName(request.getProgramName());
        program.setProgramVersion("1.0.0");
        program.setProgramType(request.getProgramType());
        program.setAtePlatform(request.getAtePlatform());
        program.setAteModel(request.getAteModel());
        program.setProgramLanguage(template.getProgramLanguage());
        program.setCompilerVersion(getLatestCompilerVersion(request.getAtePlatform()));
        program.setTestHeadCount(request.getTestHeadCount());
        program.setParallelSites(request.getParallelSites());
        program.setProgramStatus(ProgramStatus.DEVELOPMENT);
        program.setValidationStatus(ValidationStatus.NOT_VALIDATED);
        program.setProgramDescription(request.getDescription());
        
        program = programRepository.save(program);
        
        // 3. 根据产品规格生成测试参数
        List<ICTestParameter> parameters = generateTestParameters(
            program, request.getProductSpecification(), template);
        parameterRepository.saveAll(parameters);
        
        // 4. 生成程序代码框架
        ProgramCodeFramework codeFramework = generateCodeFramework(program, parameters, template);
        
        // 5. 创建初始版本
        ICTestProgramVersion initialVersion = versionControlService.createInitialVersion(
            program, codeFramework, "Initial program generation from template");
        
        // 6. 更新程序统计信息
        updateProgramStatistics(program, parameters, codeFramework);
        
        return programRepository.save(program);
    }
    
    /**
     * 测试参数智能生成
     */
    private List<ICTestParameter> generateTestParameters(ICTestProgram program, 
                                                        ProductSpecification productSpec,
                                                        TestProgramTemplate template) {
        List<ICTestParameter> parameters = new ArrayList<>();
        
        // 1. DC参数生成
        List<ICTestParameter> dcParameters = generateDCParameters(productSpec, template);
        parameters.addAll(dcParameters);
        
        // 2. AC参数生成
        List<ICTestParameter> acParameters = generateACParameters(productSpec, template);
        parameters.addAll(acParameters);
        
        // 3. 功能测试参数生成
        List<ICTestParameter> funcParameters = generateFunctionalParameters(productSpec, template);
        parameters.addAll(funcParameters);
        
        // 4. 扫描测试参数生成（如适用）
        if (productSpec.supportsScanTesting()) {
            List<ICTestParameter> scanParameters = generateScanParameters(productSpec, template);
            parameters.addAll(scanParameters);
        }
        
        // 5. 为每个参数设置基本属性
        for (int i = 0; i < parameters.size(); i++) {
            ICTestParameter param = parameters.get(i);
            param.setProgramId(program.getProgramId());
            param.setParameterSequence(i + 1);
            param.setTestNumber(i + 1);
            
            // 基于产品规格设置测试限值
            setParameterLimits(param, productSpec);
            
            // 设置测试条件
            setTestConditions(param, productSpec, template);
            
            // 优化测试时间
            optimizeParameterTiming(param, template);
        }
        
        return parameters;
    }
    
    /**
     * DC参数生成
     */
    private List<ICTestParameter> generateDCParameters(ProductSpecification productSpec,
                                                      TestProgramTemplate template) {
        List<ICTestParameter> dcParameters = new ArrayList<>();
        
        // 1. 输入泄漏电流测试
        for (Pin pin : productSpec.getInputPins()) {
            ICTestParameter ileakParam = new ICTestParameter();
            ileakParam.setParameterName("ILEAK_" + pin.getPinName());
            ileakParam.setParameterCode("ILEAK_" + pin.getPinNumber());
            ileakParam.setParameterCategory(ParameterCategory.DC);
            ileakParam.setTestType(TestType.CURRENT);
            ileakParam.setPinList(pin.getPinName());
            ileakParam.setMeasurementUnit("nA");
            ileakParam.setTestCondition(String.format("VIN=%sV, VOUT=Float", pin.getInputVoltage()));
            ileakParam.setNominalValue(BigDecimal.ZERO);
            ileakParam.setLowerLimit(BigDecimal.valueOf(-100));  // -100nA
            ileakParam.setUpperLimit(BigDecimal.valueOf(100));   // +100nA
            ileakParam.setResolution(BigDecimal.valueOf(0.1));   // 0.1nA
            ileakParam.setAccuracy(BigDecimal.valueOf(1.0));     // 1nA
            ileakParam.setMeasurementTimeUs(BigDecimal.valueOf(100)); // 100μs
            ileakParam.setParameterPriority(ParameterPriority.CRITICAL);
            ileakParam.setIsBinningParameter(true);
            dcParameters.add(ileakParam);
        }
        
        // 2. 输出电压测试
        for (Pin pin : productSpec.getOutputPins()) {
            ICTestParameter vohParam = new ICTestParameter();
            vohParam.setParameterName("VOH_" + pin.getPinName());
            vohParam.setParameterCode("VOH_" + pin.getPinNumber());
            vohParam.setParameterCategory(ParameterCategory.DC);
            vohParam.setTestType(TestType.VOLTAGE);
            vohParam.setPinList(pin.getPinName());
            vohParam.setMeasurementUnit("V");
            vohParam.setTestCondition(String.format("VDD=%sV, IOH=%smA", 
                productSpec.getSupplyVoltage(), pin.getOutputCurrent()));
            vohParam.setNominalValue(pin.getOutputHighVoltage());
            vohParam.setLowerLimit(pin.getVohMin());
            vohParam.setUpperLimit(pin.getVohMax());
            vohParam.setResolution(BigDecimal.valueOf(0.001));   // 1mV
            vohParam.setAccuracy(BigDecimal.valueOf(0.01));      // 10mV
            vohParam.setMeasurementTimeUs(BigDecimal.valueOf(50)); // 50μs
            vohParam.setParameterPriority(ParameterPriority.CRITICAL);
            vohParam.setIsBinningParameter(true);
            dcParameters.add(vohParam);
            
            // 对应的VOL测试
            ICTestParameter volParam = new ICTestParameter();
            volParam.setParameterName("VOL_" + pin.getPinName());
            volParam.setParameterCode("VOL_" + pin.getPinNumber());
            volParam.setParameterCategory(ParameterCategory.DC);
            volParam.setTestType(TestType.VOLTAGE);
            volParam.setPinList(pin.getPinName());
            volParam.setMeasurementUnit("V");
            volParam.setTestCondition(String.format("VDD=%sV, IOL=%smA", 
                productSpec.getSupplyVoltage(), pin.getOutputCurrent()));
            volParam.setNominalValue(pin.getOutputLowVoltage());
            volParam.setLowerLimit(pin.getVolMin());
            volParam.setUpperLimit(pin.getVolMax());
            volParam.setResolution(BigDecimal.valueOf(0.001));
            volParam.setAccuracy(BigDecimal.valueOf(0.01));
            volParam.setMeasurementTimeUs(BigDecimal.valueOf(50));
            volParam.setParameterPriority(ParameterPriority.CRITICAL);
            volParam.setIsBinningParameter(true);
            dcParameters.add(volParam);
        }
        
        // 3. 电源电流测试
        ICTestParameter iddParam = new ICTestParameter();
        iddParam.setParameterName("IDD_STATIC");
        iddParam.setParameterCode("IDD_STAT");
        iddParam.setParameterCategory(ParameterCategory.DC);
        iddParam.setTestType(TestType.CURRENT);
        iddParam.setPinList("VDD");
        iddParam.setMeasurementUnit("mA");
        iddParam.setTestCondition(String.format("VDD=%sV, All inputs static", 
            productSpec.getSupplyVoltage()));
        iddParam.setNominalValue(productSpec.getTypicalSupplyCurrent());
        iddParam.setLowerLimit(BigDecimal.ZERO);
        iddParam.setUpperLimit(productSpec.getMaxSupplyCurrent());
        iddParam.setResolution(BigDecimal.valueOf(0.001));  // 1μA
        iddParam.setAccuracy(BigDecimal.valueOf(0.01));     // 10μA
        iddParam.setMeasurementTimeUs(BigDecimal.valueOf(1000)); // 1ms
        iddParam.setParameterPriority(ParameterPriority.CRITICAL);
        iddParam.setIsBinningParameter(true);
        dcParameters.add(iddParam);
        
        return dcParameters;
    }
    
    /**
     * 程序代码框架生成
     */
    private ProgramCodeFramework generateCodeFramework(ICTestProgram program,
                                                      List<ICTestParameter> parameters,
                                                      TestProgramTemplate template) {
        ProgramCodeFramework framework = new ProgramCodeFramework();
        framework.setProgramId(program.getProgramId());
        framework.setAtePlatform(program.getAtePlatform());
        
        // 1. 生成主程序文件
        String mainProgram = generateMainProgramCode(program, parameters, template);
        framework.setMainProgramCode(mainProgram);
        
        // 2. 生成引脚映射文件
        String pinMapFile = generatePinMapFile(program, parameters, template);
        framework.setPinMapCode(pinMapFile);
        
        // 3. 生成时序文件
        String timingFile = generateTimingFile(program, parameters, template);
        framework.setTimingCode(timingFile);
        
        // 4. 生成测试向量文件
        String vectorFile = generateVectorFile(program, parameters, template);
        framework.setVectorCode(vectorFile);
        
        // 5. 生成配置文件
        String configFile = generateConfigFile(program, parameters, template);
        framework.setConfigCode(configFile);
        
        // 6. 生成测试流程
        String testFlow = generateTestFlow(program, parameters, template);
        framework.setTestFlowCode(testFlow);
        
        return framework;
    }
}
```

### 3.2 测试程序验证引擎
```java
@Service
public class TestProgramValidationService {
    
    @Autowired
    private ValidationRepository validationRepository;
    
    @Autowired
    private ValidationDataRepository validationDataRepository;
    
    @Autowired
    private StatisticalAnalysisService statisticalService;
    
    /**
     * 创建测试程序验证项目
     */
    public ICTestProgramValidation createValidationProject(ValidationRequest request) {
        // 1. 验证请求参数
        validateValidationRequest(request);
        
        // 2. 创建验证项目
        ICTestProgramValidation validation = new ICTestProgramValidation();
        validation.setValidationId(IdGenerator.generateId());
        validation.setProgramId(request.getProgramId());
        validation.setVersionId(request.getVersionId());
        validation.setValidationCode(generateValidationCode(request));
        validation.setValidationName(request.getValidationName());
        validation.setValidationType(request.getValidationType());
        validation.setValidationPhase(request.getValidationPhase());
        validation.setValidationScope(request.getValidationScope());
        validation.setValidationMethod(request.getValidationMethod());
        validation.setSampleSize(request.getSampleSize());
        validation.setTestSites(request.getTestSites());
        validation.setTestRunsPerSite(request.getTestRunsPerSite());
        validation.setReferenceProgramId(request.getReferenceProgramId());
        validation.setReferencePlatform(request.getReferencePlatform());
        validation.setValidationCriteria(request.getValidationCriteria());
        validation.setPassCriteria(request.getPassCriteria());
        validation.setStatisticalRequirements(request.getStatisticalRequirements());
        validation.setValidationEngineer(getCurrentUserId());
        validation.setValidationStatus(ValidationStatus.PLANNING);
        
        validation = validationRepository.save(validation);
        
        // 3. 创建验证计划
        ValidationPlan validationPlan = createValidationPlan(validation, request);
        
        // 4. 准备验证样品
        SamplePreparation samplePrep = prepareSamples(validation, request);
        
        // 5. 设置验证环境
        ValidationEnvironment validationEnv = setupValidationEnvironment(validation);
        
        return validation;
    }
    
    /**
     * 执行关联性验证
     */
    public CorrelationValidationResult performCorrelationValidation(String validationId) {
        ICTestProgramValidation validation = validationRepository.findById(validationId)
            .orElseThrow(() -> new EntityNotFoundException("验证项目不存在"));
        
        CorrelationValidationResult result = new CorrelationValidationResult();
        result.setValidationId(validationId);
        result.setAnalysisDate(LocalDateTime.now());
        
        // 1. 获取测试数据
        List<ICTestValidationData> testData = validationDataRepository
            .findByValidationIdOrderByParameterIdAscSampleIdAsc(validationId);
        
        if (testData.isEmpty()) {
            throw new IllegalStateException("没有找到验证数据");
        }
        
        // 2. 按参数分组数据
        Map<String, List<ICTestValidationData>> parameterDataMap = testData.stream()
            .collect(Collectors.groupingBy(ICTestValidationData::getParameterId));
        
        List<ParameterCorrelationResult> parameterResults = new ArrayList<>();
        
        for (Map.Entry<String, List<ICTestValidationData>> entry : parameterDataMap.entrySet()) {
            String parameterId = entry.getKey();
            List<ICTestValidationData> paramData = entry.getValue();
            
            ParameterCorrelationResult paramResult = analyzeParameterCorrelation(parameterId, paramData);
            parameterResults.add(paramResult);
        }
        
        result.setParameterResults(parameterResults);
        
        // 3. 计算总体关联性
        OverallCorrelationAnalysis overallAnalysis = calculateOverallCorrelation(parameterResults);
        result.setOverallAnalysis(overallAnalysis);
        
        // 4. 生成验证结论
        ValidationConclusion conclusion = generateValidationConclusion(result, validation.getPassCriteria());
        result.setConclusion(conclusion);
        
        return result;
    }
    
    /**
     * 参数关联性分析
     */
    private ParameterCorrelationResult analyzeParameterCorrelation(String parameterId,
                                                                  List<ICTestValidationData> data) {
        ParameterCorrelationResult result = new ParameterCorrelationResult();
        result.setParameterId(parameterId);
        
        // 1. 提取测量值和参考值
        List<Double> measuredValues = data.stream()
            .map(d -> d.getMeasuredValue().doubleValue())
            .collect(Collectors.toList());
        
        List<Double> referenceValues = data.stream()
            .map(d -> d.getReferenceValue().doubleValue())
            .collect(Collectors.toList());
        
        // 2. 描述性统计
        DescriptiveStatistics measuredStats = new DescriptiveStatistics(
            measuredValues.stream().mapToDouble(Double::doubleValue).toArray());
        DescriptiveStatistics referenceStats = new DescriptiveStatistics(
            referenceValues.stream().mapToDouble(Double::doubleValue).toArray());
        
        result.setMeasuredMean(measuredStats.getMean());
        result.setMeasuredStdDev(measuredStats.getStandardDeviation());
        result.setReferenceMean(referenceStats.getMean());
        result.setReferenceStdDev(referenceStats.getStandardDeviation());
        
        // 3. 计算关联系数
        PearsonsCorrelation correlation = new PearsonsCorrelation();
        double correlationCoeff = correlation.correlation(
            measuredValues.stream().mapToDouble(Double::doubleValue).toArray(),
            referenceValues.stream().mapToDouble(Double::doubleValue).toArray());
        result.setCorrelationCoefficient(correlationCoeff);
        
        // 4. 线性回归分析
        SimpleRegression regression = new SimpleRegression();
        for (int i = 0; i < measuredValues.size(); i++) {
            regression.addData(referenceValues.get(i), measuredValues.get(i));
        }
        
        result.setSlope(regression.getSlope());
        result.setIntercept(regression.getIntercept());
        result.setrSquared(regression.getRSquare());
        result.setMeanSquareError(regression.getMeanSquareError());
        
        // 5. 偏差分析
        List<Double> differences = new ArrayList<>();
        List<Double> percentDifferences = new ArrayList<>();
        
        for (int i = 0; i < measuredValues.size(); i++) {
            double measured = measuredValues.get(i);
            double reference = referenceValues.get(i);
            double diff = measured - reference;
            double percentDiff = (diff / reference) * 100;
            
            differences.add(diff);
            percentDifferences.add(percentDiff);
        }
        
        DescriptiveStatistics diffStats = new DescriptiveStatistics(
            differences.stream().mapToDouble(Double::doubleValue).toArray());
        DescriptiveStatistics percentDiffStats = new DescriptiveStatistics(
            percentDifferences.stream().mapToDouble(Double::doubleValue).toArray());
        
        result.setMeanDifference(diffStats.getMean());
        result.setStdDevDifference(diffStats.getStandardDeviation());
        result.setMeanPercentDifference(percentDiffStats.getMean());
        result.setStdDevPercentDifference(percentDiffStats.getStandardDeviation());
        
        // 6. 异常值检测
        OutlierDetectionResult outliers = detectOutliers(differences, 3.0); // 3-sigma rule
        result.setOutlierCount(outliers.getOutlierCount());
        result.setOutlierIndices(outliers.getOutlierIndices());
        
        // 7. 正态性检验
        NormalityTestResult normalityTest = performNormalityTest(differences);
        result.setNormalityTestResult(normalityTest);
        
        // 8. 评估关联性质量
        CorrelationQuality quality = assessCorrelationQuality(result);
        result.setCorrelationQuality(quality);
        
        return result;
    }
    
    /**
     * 重复性验证
     */
    public RepeatabilityValidationResult performRepeatabilityValidation(String validationId) {
        ICTestProgramValidation validation = validationRepository.findById(validationId)
            .orElseThrow(() -> new EntityNotFoundException("验证项目不存在"));
        
        RepeatabilityValidationResult result = new RepeatabilityValidationResult();
        result.setValidationId(validationId);
        result.setAnalysisDate(LocalDateTime.now());
        
        // 1. 获取重复性测试数据
        List<ICTestValidationData> testData = validationDataRepository
            .findByValidationIdAndRepeatabilityFlagTrue(validationId);
        
        if (testData.isEmpty()) {
            throw new IllegalStateException("没有找到重复性验证数据");
        }
        
        // 2. 按参数和样品分组数据
        Map<String, Map<String, List<ICTestValidationData>>> groupedData = testData.stream()
            .collect(Collectors.groupingBy(ICTestValidationData::getParameterId,
                Collectors.groupingBy(ICTestValidationData::getSampleId)));
        
        List<ParameterRepeatabilityResult> parameterResults = new ArrayList<>();
        
        for (Map.Entry<String, Map<String, List<ICTestValidationData>>> paramEntry : groupedData.entrySet()) {
            String parameterId = paramEntry.getKey();
            Map<String, List<ICTestValidationData>> sampleData = paramEntry.getValue();
            
            ParameterRepeatabilityResult paramResult = analyzeParameterRepeatability(parameterId, sampleData);
            parameterResults.add(paramResult);
        }
        
        result.setParameterResults(parameterResults);
        
        // 3. 计算总体重复性
        OverallRepeatabilityAnalysis overallAnalysis = calculateOverallRepeatability(parameterResults);
        result.setOverallAnalysis(overallAnalysis);
        
        // 4. Gage R&R分析
        GageRRAnalysis gageRR = performGageRRAnalysis(testData);
        result.setGageRRAnalysis(gageRR);
        
        return result;
    }
    
    /**
     * 参数重复性分析
     */
    private ParameterRepeatabilityResult analyzeParameterRepeatability(String parameterId,
                                                                      Map<String, List<ICTestValidationData>> sampleData) {
        ParameterRepeatabilityResult result = new ParameterRepeatabilityResult();
        result.setParameterId(parameterId);
        
        List<SampleRepeatabilityData> sampleResults = new ArrayList<>();
        List<Double> allMeasurements = new ArrayList<>();
        List<Double> sampleRanges = new ArrayList<>();
        List<Double> sampleStdDevs = new ArrayList<>();
        
        // 1. 分析每个样品的重复性
        for (Map.Entry<String, List<ICTestValidationData>> sampleEntry : sampleData.entrySet()) {
            String sampleId = sampleEntry.getKey();
            List<ICTestValidationData> measurements = sampleEntry.getValue();
            
            if (measurements.size() < 2) {
                continue; // 需要至少2次测量才能计算重复性
            }
            
            List<Double> values = measurements.stream()
                .map(d -> d.getMeasuredValue().doubleValue())
                .collect(Collectors.toList());
            
            allMeasurements.addAll(values);
            
            DescriptiveStatistics stats = new DescriptiveStatistics(
                values.stream().mapToDouble(Double::doubleValue).toArray());
            
            double range = stats.getMax() - stats.getMin();
            double stdDev = stats.getStandardDeviation();
            
            sampleRanges.add(range);
            sampleStdDevs.add(stdDev);
            
            SampleRepeatabilityData sampleResult = new SampleRepeatabilityData();
            sampleResult.setSampleId(sampleId);
            sampleResult.setMeasurementCount(values.size());
            sampleResult.setMean(stats.getMean());
            sampleResult.setStandardDeviation(stdDev);
            sampleResult.setRange(range);
            sampleResult.setCoefficientOfVariation((stdDev / stats.getMean()) * 100);
            
            sampleResults.add(sampleResult);
        }
        
        result.setSampleResults(sampleResults);
        
        // 2. 计算总体重复性统计
        DescriptiveStatistics rangeStats = new DescriptiveStatistics(
            sampleRanges.stream().mapToDouble(Double::doubleValue).toArray());
        DescriptiveStatistics stdDevStats = new DescriptiveStatistics(
            sampleStdDevs.stream().mapToDouble(Double::doubleValue).toArray());
        
        result.setAverageRange(rangeStats.getMean());
        result.setAverageStandardDeviation(stdDevStats.getMean());
        
        // 3. 重复性指标计算
        double repeatabilityStdDev = calculateRepeatabilityStandardDeviation(sampleStdDevs);
        double repeatability = 2.8 * repeatabilityStdDev; // 95%置信区间
        
        result.setRepeatabilityStandardDeviation(repeatabilityStdDev);
        result.setRepeatability(repeatability);
        
        // 4. 相对重复性
        DescriptiveStatistics allStats = new DescriptiveStatistics(
            allMeasurements.stream().mapToDouble(Double::doubleValue).toArray());
        double relativeRepeatability = (repeatability / allStats.getMean()) * 100;
        result.setRelativeRepeatability(relativeRepeatability);
        
        // 5. 评估重复性质量
        RepeatabilityQuality quality = assessRepeatabilityQuality(relativeRepeatability);
        result.setRepeatabilityQuality(quality);
        
        return result;
    }
}
```

### 3.3 测试数据分析引擎
```java
@Service
public class TestDataAnalysisService {
    
    @Autowired
    private TestDataAnalysisRepository analysisRepository;
    
    @Autowired
    private TestDataRepository testDataRepository;
    
    @Autowired
    private MachineLearningService mlService;
    
    /**
     * 良率分析
     */
    public YieldAnalysisResult performYieldAnalysis(YieldAnalysisRequest request) {
        // 1. 创建分析项目
        ICTestDataAnalysis analysis = createAnalysisProject(request, AnalysisType.YIELD_ANALYSIS);
        
        YieldAnalysisResult result = new YieldAnalysisResult();
        result.setAnalysisId(analysis.getAnalysisId());
        result.setAnalysisDate(LocalDateTime.now());
        
        try {
            // 2. 获取测试数据
            TestDataSet testDataSet = retrieveTestData(request);
            
            // 3. 基本良率统计
            BasicYieldStatistics basicStats = calculateBasicYieldStatistics(testDataSet);
            result.setBasicStatistics(basicStats);
            
            // 4. 分层良率分析
            if (request.getStratificationFactors() != null) {
                StratifiedYieldAnalysis stratifiedAnalysis = performStratifiedYieldAnalysis(
                    testDataSet, request.getStratificationFactors());
                result.setStratifiedAnalysis(stratifiedAnalysis);
            }
            
            // 5. 趋势分析
            YieldTrendAnalysis trendAnalysis = performYieldTrendAnalysis(testDataSet, request.getTimeGranularity());
            result.setTrendAnalysis(trendAnalysis);
            
            // 6. 帕累托分析
            ParetoAnalysis paretoAnalysis = performParetoAnalysis(testDataSet);
            result.setParetoAnalysis(paretoAnalysis);
            
            // 7. 根因分析
            if (basicStats.getOverallYield() < request.getYieldTarget()) {
                RootCauseAnalysis rootCauseAnalysis = performRootCauseAnalysis(testDataSet);
                result.setRootCauseAnalysis(rootCauseAnalysis);
            }
            
            // 8. 预测分析
            YieldForecastAnalysis forecastAnalysis = performYieldForecast(testDataSet);
            result.setForecastAnalysis(forecastAnalysis);
            
            analysis.setAnalysisStatus(AnalysisStatus.COMPLETED);
            analysis.setResultsSummary(generateYieldAnalysisSummary(result));
            
        } catch (Exception e) {
            analysis.setAnalysisStatus(AnalysisStatus.FAILED);
            analysis.setResultsSummary("分析失败: " + e.getMessage());
            throw new AnalysisExecutionException("良率分析执行失败", e);
        } finally {
            analysis.setEndTime(LocalDateTime.now());
            analysisRepository.save(analysis);
        }
        
        return result;
    }
    
    /**
     * 参数分布分析
     */
    public ParameterDistributionResult performParameterDistributionAnalysis(ParameterDistributionRequest request) {
        ICTestDataAnalysis analysis = createAnalysisProject(request, AnalysisType.PARAMETER_DISTRIBUTION);
        
        ParameterDistributionResult result = new ParameterDistributionResult();
        result.setAnalysisId(analysis.getAnalysisId());
        
        try {
            TestDataSet testDataSet = retrieveTestData(request);
            
            List<ParameterDistributionAnalysis> parameterAnalyses = new ArrayList<>();
            
            for (String parameterId : request.getParameterIds()) {
                ParameterDistributionAnalysis paramAnalysis = analyzeParameterDistribution(
                    testDataSet, parameterId);
                parameterAnalyses.add(paramAnalysis);
            }
            
            result.setParameterAnalyses(parameterAnalyses);
            
            // 多变量分析
            if (request.getParameterIds().size() > 1) {
                MultivariateAnalysis multivariateAnalysis = performMultivariateAnalysis(
                    testDataSet, request.getParameterIds());
                result.setMultivariateAnalysis(multivariateAnalysis);
            }
            
            analysis.setAnalysisStatus(AnalysisStatus.COMPLETED);
            
        } catch (Exception e) {
            analysis.setAnalysisStatus(AnalysisStatus.FAILED);
            throw new AnalysisExecutionException("参数分布分析执行失败", e);
        } finally {
            analysis.setEndTime(LocalDateTime.now());
            analysisRepository.save(analysis);
        }
        
        return result;
    }
    
    /**
     * 单参数分布分析
     */
    private ParameterDistributionAnalysis analyzeParameterDistribution(TestDataSet testDataSet, 
                                                                      String parameterId) {
        ParameterDistributionAnalysis analysis = new ParameterDistributionAnalysis();
        analysis.setParameterId(parameterId);
        
        // 1. 提取参数数据
        List<Double> parameterValues = testDataSet.getParameterValues(parameterId);
        
        if (parameterValues.isEmpty()) {
            throw new IllegalArgumentException("参数数据为空: " + parameterId);
        }
        
        // 2. 描述性统计
        DescriptiveStatistics stats = new DescriptiveStatistics(
            parameterValues.stream().mapToDouble(Double::doubleValue).toArray());
        
        analysis.setSampleSize(parameterValues.size());
        analysis.setMean(stats.getMean());
        analysis.setMedian(stats.getPercentile(50));
        analysis.setStandardDeviation(stats.getStandardDeviation());
        analysis.setVariance(stats.getVariance());
        analysis.setSkewness(stats.getSkewness());
        analysis.setKurtosis(stats.getKurtosis());
        analysis.setMinValue(stats.getMin());
        analysis.setMaxValue(stats.getMax());
        analysis.setRange(stats.getMax() - stats.getMin());
        
        // 3. 百分位数计算
        Map<String, Double> percentiles = new HashMap<>();
        percentiles.put("P1", stats.getPercentile(1));
        percentiles.put("P5", stats.getPercentile(5));
        percentiles.put("P10", stats.getPercentile(10));
        percentiles.put("P25", stats.getPercentile(25));
        percentiles.put("P75", stats.getPercentile(75));
        percentiles.put("P90", stats.getPercentile(90));
        percentiles.put("P95", stats.getPercentile(95));
        percentiles.put("P99", stats.getPercentile(99));
        analysis.setPercentiles(percentiles);
        
        // 4. 分布拟合
        DistributionFittingResult distributionFit = fitDistributions(parameterValues);
        analysis.setDistributionFit(distributionFit);
        
        // 5. 正态性检验
        NormalityTestResult normalityTest = performNormalityTest(parameterValues);
        analysis.setNormalityTest(normalityTest);
        
        // 6. 异常值检测
        OutlierDetectionResult outliers = detectOutliers(parameterValues, 3.0);
        analysis.setOutliers(outliers);
        
        // 7. 直方图数据
        HistogramData histogram = generateHistogram(parameterValues, 50);
        analysis.setHistogram(histogram);
        
        // 8. Cpk计算（如果有规格限）
        ParameterSpecification spec = getParameterSpecification(parameterId);
        if (spec != null) {
            ProcessCapabilityAnalysis capability = calculateProcessCapability(parameterValues, spec);
            analysis.setProcessCapability(capability);
        }
        
        return analysis;
    }
    
    /**
     * 智能缺陷模式识别
     */
    public DefectPatternRecognitionResult performDefectPatternRecognition(DefectPatternRequest request) {
        ICTestDataAnalysis analysis = createAnalysisProject(request, AnalysisType.DEFECT_PATTERN_RECOGNITION);
        
        DefectPatternRecognitionResult result = new DefectPatternRecognitionResult();
        result.setAnalysisId(analysis.getAnalysisId());
        
        try {
            // 1. 获取缺陷数据
            DefectDataSet defectDataSet = retrieveDefectData(request);
            
            // 2. 特征工程
            FeatureEngineering featureEngineering = performFeatureEngineering(defectDataSet);
            result.setFeatureEngineering(featureEngineering);
            
            // 3. 无监督学习-聚类分析
            ClusteringAnalysisResult clustering = performDefectClustering(featureEngineering.getFeatureMatrix());
            result.setClusteringAnalysis(clustering);
            
            // 4. 模式识别
            List<DefectPattern> identifiedPatterns = identifyDefectPatterns(clustering, defectDataSet);
            result.setIdentifiedPatterns(identifiedPatterns);
            
            // 5. 关联规则挖掘
            AssociationRuleResult associationRules = mineAssociationRules(defectDataSet);
            result.setAssociationRules(associationRules);
            
            // 6. 时序模式分析
            if (request.includeTemporalAnalysis()) {
                TemporalPatternAnalysis temporalAnalysis = analyzeTemporalPatterns(defectDataSet);
                result.setTemporalPatternAnalysis(temporalAnalysis);
            }
            
            // 7. 空间模式分析（晶圆地图）
            if (request.includeSpatialAnalysis()) {
                SpatialPatternAnalysis spatialAnalysis = analyzeSpatialPatterns(defectDataSet);
                result.setSpatialPatternAnalysis(spatialAnalysis);
            }
            
            // 8. 根因推断
            RootCauseInference rootCauseInference = performRootCauseInference(
                identifiedPatterns, defectDataSet);
            result.setRootCauseInference(rootCauseInference);
            
            // 9. 预防措施建议
            List<PreventiveActionRecommendation> recommendations = generatePreventiveRecommendations(
                identifiedPatterns, rootCauseInference);
            result.setPreventiveRecommendations(recommendations);
            
            analysis.setAnalysisStatus(AnalysisStatus.COMPLETED);
            
        } catch (Exception e) {
            analysis.setAnalysisStatus(AnalysisStatus.FAILED);
            throw new AnalysisExecutionException("缺陷模式识别分析执行失败", e);
        } finally {
            analysis.setEndTime(LocalDateTime.now());
            analysisRepository.save(analysis);
        }
        
        return result;
    }
    
    /**
     * 预测性质量分析
     */
    public PredictiveQualityAnalysisResult performPredictiveQualityAnalysis(PredictiveQualityRequest request) {
        ICTestDataAnalysis analysis = createAnalysisProject(request, AnalysisType.PREDICTIVE_QUALITY);
        
        PredictiveQualityAnalysisResult result = new PredictiveQualityAnalysisResult();
        result.setAnalysisId(analysis.getAnalysisId());
        
        try {
            // 1. 数据准备
            PredictiveDataSet dataSet = preparePredictiveDataSet(request);
            
            // 2. 特征选择
            FeatureSelectionResult featureSelection = performFeatureSelection(dataSet);
            result.setFeatureSelection(featureSelection);
            
            // 3. 模型训练和验证
            List<PredictiveModel> models = trainPredictiveModels(dataSet, featureSelection);
            result.setPredictiveModels(models);
            
            // 4. 模型性能评估
            ModelPerformanceEvaluation performance = evaluateModelPerformance(models, dataSet);
            result.setModelPerformance(performance);
            
            // 5. 最优模型选择
            PredictiveModel bestModel = selectBestModel(models, performance);
            result.setBestModel(bestModel);
            
            // 6. 质量预测
            QualityPredictionResult prediction = performQualityPrediction(bestModel, request.getPredictionData());
            result.setQualityPrediction(prediction);
            
            // 7. 特征重要性分析
            FeatureImportanceAnalysis featureImportance = analyzeFeatureImportance(bestModel, dataSet);
            result.setFeatureImportance(featureImportance);
            
            // 8. 预警阈值设置
            EarlyWarningThresholds earlyWarning = establishEarlyWarningThresholds(bestModel, dataSet);
            result.setEarlyWarningThresholds(earlyWarning);
            
            analysis.setAnalysisStatus(AnalysisStatus.COMPLETED);
            
        } catch (Exception e) {
            analysis.setAnalysisStatus(AnalysisStatus.FAILED);
            throw new AnalysisExecutionException("预测性质量分析执行失败", e);
        } finally {
            analysis.setEndTime(LocalDateTime.now());
            analysisRepository.save(analysis);
        }
        
        return result;
    }
}
```

---

*测试程序管理模块为IC封测CIM系统提供了完整的测试程序开发、验证、版本控制和数据分析功能，确保测试程序的质量、效率和持续改进*