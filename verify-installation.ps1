# IC封测CIM系统 - PowerShell验证脚本
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "       D盘软件安装验证检查" -ForegroundColor Cyan  
Write-Host "==========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "[1/5] 检查环境变量..." -ForegroundColor Yellow
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor White
Write-Host "MAVEN_HOME: $env:MAVEN_HOME" -ForegroundColor White
Write-Host "REDIS_HOME: $env:REDIS_HOME" -ForegroundColor White

Write-Host ""
Write-Host "[2/5] 检查Java安装..." -ForegroundColor Yellow
$javaCmd = Get-Command java -ErrorAction SilentlyContinue
if ($javaCmd) {
    $javaVersion = java -version 2>&1
    Write-Host "✓ Java安装成功" -ForegroundColor Green
    Write-Host "  路径: $($javaCmd.Source)" -ForegroundColor Gray
} else {
    Write-Host "✗ Java命令未找到" -ForegroundColor Red
}

Write-Host ""
Write-Host "[3/5] 检查Maven安装..." -ForegroundColor Yellow
$mavenCmd = Get-Command mvn -ErrorAction SilentlyContinue
if ($mavenCmd) {
    Write-Host "✓ Maven安装成功" -ForegroundColor Green
    Write-Host "  路径: $($mavenCmd.Source)" -ForegroundColor Gray
} else {
    Write-Host "✗ Maven命令未找到" -ForegroundColor Red
}

Write-Host ""
Write-Host "[4/5] 检查Redis安装..." -ForegroundColor Yellow
$redisCmd = Get-Command redis-cli -ErrorAction SilentlyContinue
if ($redisCmd) {
    Write-Host "✓ Redis安装成功" -ForegroundColor Green
    Write-Host "  路径: $($redisCmd.Source)" -ForegroundColor Gray
} else {
    Write-Host "✗ Redis命令未找到" -ForegroundColor Red
}

Write-Host ""
Write-Host "[5/5] 检查Docker..." -ForegroundColor Yellow
$dockerCmd = Get-Command docker -ErrorAction SilentlyContinue
if ($dockerCmd) {
    Write-Host "✓ Docker可用" -ForegroundColor Green
    Write-Host "  路径: $($dockerCmd.Source)" -ForegroundColor Gray
} else {
    Write-Host "⚠ Docker未安装或未启动" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "[文件] 检查安装文件..." -ForegroundColor Yellow

$javaPath = "D:\Development\Java\jdk-17\bin\java.exe"
if (Test-Path $javaPath) {
    Write-Host "✓ Java JDK 文件存在" -ForegroundColor Green
} else {
    Write-Host "✗ Java JDK 文件未找到: $javaPath" -ForegroundColor Red
}

$mavenPath = "D:\Development\Java\maven\bin\mvn.cmd"
if (Test-Path $mavenPath) {
    Write-Host "✓ Maven 文件存在" -ForegroundColor Green
} else {
    Write-Host "✗ Maven 文件未找到: $mavenPath" -ForegroundColor Red
}

$redisPath = "D:\Development\Redis\redis-cli.exe"
if (Test-Path $redisPath) {
    Write-Host "✓ Redis 文件存在" -ForegroundColor Green
} else {
    Write-Host "✗ Redis 文件未找到: $redisPath" -ForegroundColor Red
}

$dockerComposePath = "D:\Projects\JSCIM-System\docker-compose.yml"
if (Test-Path $dockerComposePath) {
    Write-Host "✓ Docker配置文件存在" -ForegroundColor Green
} else {
    Write-Host "✗ Docker配置文件未找到: $dockerComposePath" -ForegroundColor Red
}

Write-Host ""
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "           验证检查完成" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

Write-Host ""
if ($env:JAVA_HOME -and $env:MAVEN_HOME) {
    Write-Host "🎉 基础环境配置正常！" -ForegroundColor Green
} 
if (-not $env:JAVA_HOME -or -not $env:MAVEN_HOME) {
    Write-Host "⚠️  如果有✗标记，请：" -ForegroundColor Yellow
    Write-Host "1. 重启PowerShell终端" -ForegroundColor White
    Write-Host "2. 或注销重新登录Windows" -ForegroundColor White
    Write-Host "3. 再次运行此脚本" -ForegroundColor White
}

Write-Host ""
Write-Host "启动Docker服务命令：" -ForegroundColor Cyan
Write-Host "cd D:\Projects\JSCIM-System" -ForegroundColor White
Write-Host "docker-compose up -d" -ForegroundColor White

Write-Host ""
Read-Host "按Enter键继续..."