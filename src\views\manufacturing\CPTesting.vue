<template>
  <div class="cp-testing-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title">
        <h1>CP晶圆测试管理</h1>
        <p>Crystal Probe Testing Management</p>
      </div>
      <div class="page-actions">
        <CButton
:loading="loading" @click="refreshData"
>
          <el-icon><RefreshRight /></el-icon>
          刷新数据
        </CButton>
        <CButton type="primary" @click="openNewTestDialog">
          <el-icon><Plus /></el-icon>
          新建测试
        </CButton>
      </div>
    </div>

    <!-- 实时概览 -->
    <div class="overview-section">
      <div class="kpi-cards">
        <div class="kpi-card">
          <div class="kpi-value">
            {{ dashboardData.cpTesting.activeWafers }}
          </div>
          <div class="kpi-label">正在测试晶圆</div>
          <div class="kpi-trend up">+2.3%</div>
        </div>
        <div class="kpi-card">
          <div class="kpi-value">{{ dashboardData.cpTesting.avgYield.toFixed(1) }}%</div>
          <div class="kpi-label">平均良率</div>
          <div class="kpi-trend down">-0.8%</div>
        </div>
        <div class="kpi-card">
          <div class="kpi-value">
            {{ formatNumber(dashboardData.cpTesting.totalTestedDie) }}
          </div>
          <div class="kpi-label">今日测试Die数</div>
          <div class="kpi-trend up">+15.2%</div>
        </div>
        <div class="kpi-card">
          <div class="kpi-value">
            {{ dashboardData.cpTesting.equipmentUtilization.toFixed(1) }}%
          </div>
          <div class="kpi-label">设备稼动率</div>
          <div class="kpi-trend up">+3.5%</div>
        </div>
      </div>
    </div>

    <div class="main-content">
      <!-- 左侧：设备状态监控 -->
      <div class="equipment-section">
        <div class="section-header">
          <h2>测试设备状态</h2>
          <div class="status-filter">
            <el-radio-group v-model="equipmentFilter" size="small">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="running">运行中</el-radio-button>
              <el-radio-button label="alarm">报警</el-radio-button>
              <el-radio-button label="idle">空闲</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="equipment-grid">
          <ManufacturingEquipmentCard
            v-for="equipment in filteredEquipments"
            :key="equipment.id"
            :equipment="equipment"
            @view-details="viewEquipmentDetails"
            @control-equipment="controlEquipment"
          />
        </div>

        <!-- 探针卡管理 -->
        <div class="probe-card-section">
          <h3>探针卡管理</h3>
          <el-table :data="probeCards" stripe>
            <el-table-column prop="cardNumber" label="探针卡编号" width="120" />
            <el-table-column prop="type" label="类型" width="100" />
            <el-table-column label="使用情况" width="120">
              <template #default="{ row }">
                <el-progress
                  :percentage="(row.totalTouches / row.maxTouches) * 100"
                  :color="getProbeCardColor(row.totalTouches / row.maxTouches)"
                  :stroke-width="8"
                />
              </template>
            </el-table-column>
            <el-table-column prop="assignedTester" label="分配测试机" />
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="getProbeCardStatusType(row.status)">
                  {{ getProbeCardStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="下次保养" width="120">
              <template #default="{ row }">
                <span :class="{ 'pm-warning': isNearPM(row.nextPMTime) }">
                  {{ formatDate(row.nextPMTime) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <CButton size="small"
link @click="manageProbeCard(row)"
>
管理
</CButton>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 右侧：测试监控和结果 -->
      <div class="testing-section">
        <!-- 当前测试列表 -->
        <div class="current-tests">
          <div class="section-header">
            <h2>当前测试任务</h2>
            <el-select v-model="testStatusFilter" placeholder="状态筛选" size="small" clearable>
              <el-option label="运行中" value="running" />
              <el-option label="等待中" value="waiting" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
            </el-select>
          </div>

          <div class="test-cards">
            <div
              v-for="test in filteredTests"
              :key="test.id"
              class="test-card"
              :class="`test-card--${test.waferInfo.status}`"
              @click="selectTest(test)"
            >
              <div class="test-card__header">
                <div class="wafer-id">
                  {{ test.waferInfo.waferLot }}-{{ test.waferInfo.waferNumber }}
                </div>
                <el-tag :type="getTestStatusType(test.waferInfo.status)" size="small">
                  {{ getTestStatusText(test.waferInfo.status) }}
                </el-tag>
              </div>

              <div class="test-card__content">
                <div class="test-info">
                  <span>客户料号: {{ test.waferInfo.customerPN }}</span>
                  <span>产品代码: {{ test.waferInfo.productCode }}</span>
                  <span>测试机: {{ test.tester.name }}</span>
                </div>

                <div class="test-progress">
                  <div class="progress-bar">
                    <el-progress
                      :percentage="(test.waferInfo.testedDie / test.waferInfo.totalDie) * 100"
                      :stroke-width="6"
                      :show-text="false"
                    />
                  </div>
                  <div class="progress-text">
                    {{ test.waferInfo.testedDie }} / {{ test.waferInfo.totalDie }} ({{
                      ((test.waferInfo.testedDie / test.waferInfo.totalDie) * 100).toFixed(1)
                    }}%)
                  </div>
                </div>

                <div
v-if="test.waferInfo.testedDie > 0" class="test-yield"
>
                  良率:
                  <strong :class="getYieldClass(test.waferInfo.yield)">
                    {{ test.waferInfo.yield.toFixed(2) }}%
                  </strong>
                </div>
              </div>

              <div class="test-card__footer">
                <span class="test-duration">
                  {{ getTestDuration(test.startTime, test.endTime) }}
                </span>
                <span class="operator">{{ test.operator }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 测试详情面板 -->
        <div
v-if="selectedTest" class="test-details-panel"
>
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 晶圆图 -->
            <el-tab-pane label="晶圆图" name="wafer-map">
              <WaferMapViewer
                :wafer-map="selectedTest.waferMap"
                @die-selected="onDieSelected"
                @export-map="exportWaferMap"
              />
            </el-tab-pane>

            <!-- 电测参数 -->
            <el-tab-pane label="电测参数" name="parameters">
              <ProcessParameterTable
                :parameters="testParametersData"
                :loading="parameterLoading"
                @refresh="refreshParameters"
                @edit-parameter="editParameter"
              />
            </el-tab-pane>

            <!-- 测试结果 -->
            <el-tab-pane label="测试结果" name="results">
              <div class="test-results-content">
                <div class="results-summary">
                  <h3>测试结果汇总</h3>
                  <div class="summary-grid">
                    <div class="summary-item">
                      <div class="summary-label">测试时长</div>
                      <div class="summary-value">{{ selectedTest.duration }} 分钟</div>
                    </div>
                    <div class="summary-item">
                      <div class="summary-label">测试温度</div>
                      <div class="summary-value">{{ selectedTest.temperature }}°C</div>
                    </div>
                    <div class="summary-item">
                      <div class="summary-label">测试程序</div>
                      <div class="summary-value">
                        {{ selectedTest.testProgram }}
                      </div>
                    </div>
                    <div class="summary-item">
                      <div class="summary-label">探针卡</div>
                      <div class="summary-value">
                        {{ selectedTest.probeCard.cardNumber }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="electrical-results">
                  <h3>电测结果详情</h3>
                  <el-table :data="selectedTest.testResults" stripe>
                    <el-table-column prop="parameterName" label="参数名称" />
                    <el-table-column prop="measuredValue" label="测试值" width="100">
                      <template #default="{ row }">
                        {{ row.measuredValue.toFixed(3) }} {{ row.unit }}
                      </template>
                    </el-table-column>
                    <el-table-column label="规格范围" width="150">
                      <template #default="{ row }">
                        {{ row.minLimit }} ~ {{ row.maxLimit }} {{ row.unit }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="result" label="结果" width="80">
                      <template #default="{ row }">
                        <el-tag :type="row.result === 'pass' ? 'success' : 'danger'">
                          {{ row.result === 'pass' ? '通过' : '失败' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="cpk" label="Cpk" width="80">
                      <template #default="{ row }">
                        <span v-if="row.cpk" :class="getCpkClass(row.cpk)">
                          {{ row.cpk.toFixed(2) }}
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>

            <!-- SECS消息 -->
            <el-tab-pane label="SECS消息" name="secs">
              <div class="secs-messages">
                <div class="secs-controls">
                  <CButton size="small" @click="refreshSecsMessages">
                    <el-icon><RefreshRight /></el-icon>
                    刷新消息
                  </CButton>
                  <CButton size="small" @click="clearSecsMessages">
                    <el-icon><Delete /></el-icon>
                    清空消息
                  </CButton>
                </div>

                <el-table :data="secsMessages" :max-height="300" stripe>
                  <el-table-column prop="timestamp" label="时间" width="150">
                    <template #default="{ row }">
                      {{ formatTime(row.timestamp) }}
                    </template>
                  </el-table-column>
                  <el-table-column
label="消息类型" width="100"
>
                    <template #default="{ row }">
S{{ row.stream }}F{{ row.function }}
</template>
                  </el-table-column>
                  <el-table-column prop="direction" label="方向" width="120">
                    <template #default="{ row }">
                      <el-tag
                        :type="row.direction === 'host_to_equipment' ? 'primary' : 'success'"
                        size="small"
                      >
                        {{ row.direction === 'host_to_equipment' ? 'Host→EQP' : 'EQP→Host' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="80">
                    <template #default="{ row }">
                      <el-tag
:type="getSecsStatusType(row.status)" size="small"
>
                        {{ getSecsStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="data" label="消息内容" show-overflow-tooltip>
                    <template #default="{ row }">
                      {{ JSON.stringify(row.data) }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 新建测试弹窗 -->
    <el-dialog v-model="newTestDialogVisible" title="新建CP测试" width="60%">
      <el-form :model="newTestForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="晶圆批次" required>
              <el-input v-model="newTestForm.waferLot" placeholder="请输入晶圆批次" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="晶圆编号" required>
              <el-input-number v-model="newTestForm.waferNumber" :min="1" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户料号" required>
              <el-input v-model="newTestForm.customerPN" placeholder="请输入客户料号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品代码" required>
              <el-input v-model="newTestForm.productCode" placeholder="请输入产品代码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="测试设备" required>
              <el-select v-model="newTestForm.testerId" placeholder="请选择测试设备">
                <el-option
                  v-for="eq in availableTesters"
                  :key="eq.id"
                  :label="eq.name"
                  :value="eq.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="探针卡" required>
              <el-select v-model="newTestForm.probeCardId" placeholder="请选择探针卡">
                <el-option
                  v-for="card in availableProbeCards"
                  :key="card.id"
                  :label="card.cardNumber"
                  :value="card.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="测试程序" required>
              <el-input v-model="newTestForm.testProgram" placeholder="请输入测试程序" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测试温度" required>
              <el-input-number
v-model="newTestForm.temperature" :min="-40"
:max="150" :step="5"
/>
              <span style="margin-left: 8px">°C</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <CButton @click="newTestDialogVisible = false">取消</CButton>
        <CButton type="primary"
@click="createNewTest" :loading="creating"
>
创建测试
</CButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
  import type {
    CPTestRecord,
    Equipment,
    ProbeCard,
    DashboardData,
    ProcessParameter,
    SECSMessage,
    EquipmentStatus,
    ProcessStatus,
    WaferMapData
  } from '@/types/manufacturing'
  import CButton from '@/components/base/CButton.vue'
  import {
    ManufacturingEquipmentCard,
    ProcessParameterTable,
    WaferMapViewer
  } from '@/components/manufacturing'
  import { RefreshRight, Plus, Delete } from '@element-plus/icons-vue'

  // 响应式数据
  const loading = ref(false)
  const creating = ref(false)
  const parameterLoading = ref(false)
  const newTestDialogVisible = ref(false)
  const equipmentFilter = ref<'all' | EquipmentStatus>('all')
  const testStatusFilter = ref('')
  const activeTab = ref('wafer-map')

  // 模拟数据
  const dashboardData = ref<DashboardData>({
    totalEquipment: 12,
    runningEquipment: 8,
    overallOEE: 85.2,
    dailyOutput: 125000,
    cpTesting: {
      activeWafers: 24,
      avgYield: 94.2,
      totalTestedDie: 1250000,
      equipmentUtilization: 87.5
    },
    assembly: {
      activePackages: 0,
      avgCycleTime: 0,
      defectRate: 0,
      throughput: 0
    },
    finalTest: {
      testedUnits: 0,
      passRate: 0,
      avgTestTime: 0,
      handlerEfficiency: 0
    }
  })

  const testEquipments = ref<Equipment[]>([
    {
      id: 'tester-01',
      name: 'ATE-001',
      model: 'Advantest T5581',
      station: 'CP-ST01',
      status: 'running',
      lastUpdated: new Date().toISOString(),
      utilization: 95.2,
      oee: 92.1
    },
    {
      id: 'tester-02',
      name: 'ATE-002',
      model: 'Advantest T5581',
      station: 'CP-ST02',
      status: 'idle',
      lastUpdated: new Date().toISOString(),
      utilization: 78.3,
      oee: 85.4
    },
    {
      id: 'tester-03',
      name: 'ATE-003',
      model: 'Teradyne J750',
      station: 'CP-ST03',
      status: 'alarm',
      lastUpdated: new Date().toISOString(),
      utilization: 45.1,
      oee: 62.8
    }
  ])

  const probeCards = ref<ProbeCard[]>([
    {
      id: 'pc-001',
      cardNumber: 'PC-001',
      type: 'Cantilever',
      totalTouches: 875000,
      maxTouches: 1000000,
      lastCleanTime: '2024-01-15T08:00:00Z',
      nextPMTime: '2024-02-01T08:00:00Z',
      status: 'active',
      assignedTester: 'ATE-001'
    },
    {
      id: 'pc-002',
      cardNumber: 'PC-002',
      type: 'Vertical',
      totalTouches: 650000,
      maxTouches: 1000000,
      lastCleanTime: '2024-01-20T08:00:00Z',
      nextPMTime: '2024-02-05T08:00:00Z',
      status: 'active',
      assignedTester: 'ATE-002'
    }
  ])

  const currentTests = ref<CPTestRecord[]>([])
  const selectedTest = ref<CPTestRecord | null>(null)
  const testParametersData = ref<ProcessParameter[]>([])
  const secsMessages = ref<SECSMessage[]>([])

  const newTestForm = reactive({
    waferLot: '',
    waferNumber: 1,
    customerPN: '',
    productCode: '',
    testerId: '',
    probeCardId: '',
    testProgram: '',
    temperature: 25
  })

  // 计算属性
  const filteredEquipments = computed(() => {
    if (equipmentFilter.value === 'all') {
      return testEquipments.value
    }
    return testEquipments.value.filter(eq => eq.status === equipmentFilter.value)
  })

  const filteredTests = computed(() => {
    if (!testStatusFilter.value) {
      return currentTests.value
    }
    return currentTests.value.filter(test => test.waferInfo.status === testStatusFilter.value)
  })

  const availableTesters = computed(() => {
    return testEquipments.value.filter(eq => eq.status === 'idle' || eq.status === 'running')
  })

  const availableProbeCards = computed(() => {
    return probeCards.value.filter(pc => pc.status === 'active')
  })

  // 方法
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('zh-CN').format(num)
  }

  const formatDate = (dateStr: string): string => {
    return new Date(dateStr).toLocaleDateString('zh-CN')
  }

  const formatTime = (timeStr: string): string => {
    return new Date(timeStr).toLocaleString('zh-CN')
  }

  const getProbeCardColor = (ratio: number) => {
    if (ratio < 0.7) return '#67c23a'
    if (ratio < 0.9) return '#e6a23c'
    return '#f56c6c'
  }

  const getProbeCardStatusType = (status: string) => {
    const typeMap = {
      active: 'success',
      maintenance: 'warning',
      retired: 'danger'
    }
    return typeMap[status as keyof typeof typeMap] || 'info'
  }

  const getProbeCardStatusText = (status: string): string => {
    const textMap = {
      active: '使用中',
      maintenance: '维护中',
      retired: '已退役'
    }
    return textMap[status as keyof typeof textMap] || '未知'
  }

  const isNearPM = (pmTime: string): boolean => {
    const pm = new Date(pmTime)
    const now = new Date()
    const diff = pm.getTime() - now.getTime()
    return diff < 7 * 24 * 60 * 60 * 1000 // 7天内
  }

  const getTestStatusType = (status: ProcessStatus) => {
    const typeMap = {
      waiting: 'info',
      running: 'success',
      completed: 'success',
      failed: 'danger',
      skipped: 'warning'
    }
    return typeMap[status] || 'info'
  }

  const getTestStatusText = (status: ProcessStatus): string => {
    const textMap = {
      waiting: '等待中',
      running: '运行中',
      completed: '已完成',
      failed: '失败',
      skipped: '跳过'
    }
    return textMap[status] || '未知'
  }

  const getTestDuration = (startTime: string, endTime?: string): string => {
    const start = new Date(startTime)
    const end = endTime ? new Date(endTime) : new Date()
    const duration = Math.floor((end.getTime() - start.getTime()) / 60000) // 分钟
    return `${duration} min`
  }

  const getYieldClass = (yieldValue: number): string => {
    if (yieldValue >= 95) return 'yield-excellent'
    if (yieldValue >= 90) return 'yield-good'
    if (yieldValue >= 85) return 'yield-fair'
    return 'yield-poor'
  }

  const getCpkClass = (cpk: number): string => {
    if (cpk >= 1.67) return 'cpk-excellent'
    if (cpk >= 1.33) return 'cpk-good'
    if (cpk >= 1.0) return 'cpk-fair'
    return 'cpk-poor'
  }

  const getSecsStatusType = (status: string) => {
    const typeMap = {
      sent: 'success',
      received: 'primary',
      timeout: 'warning',
      error: 'danger'
    }
    return typeMap[status as keyof typeof typeMap] || 'info'
  }

  const getSecsStatusText = (status: string): string => {
    const textMap = {
      sent: '已发送',
      received: '已接收',
      timeout: '超时',
      error: '错误'
    }
    return textMap[status as keyof typeof textMap] || '未知'
  }

  // 事件处理方法
  const refreshData = async () => {
    loading.value = true
    try {
      // 刷新数据逻辑
      await loadCurrentTests()
      ElMessage.success('数据已刷新')
    } finally {
      loading.value = false
    }
  }

  const openNewTestDialog = () => {
    newTestDialogVisible.value = true
  }

  const createNewTest = async () => {
    creating.value = true
    try {
      // 创建新测试逻辑
      ElMessage.success('测试已创建')
      newTestDialogVisible.value = false
      await refreshData()
    } finally {
      creating.value = false
    }
  }

  const selectTest = (test: CPTestRecord) => {
    selectedTest.value = test
    loadTestParameters(test.id)
    loadSecsMessages(test.tester.id)
  }

  const viewEquipmentDetails = (equipmentId: string) => {
    // 查看设备详情
    console.log('查看设备详情:', equipmentId)
  }

  const controlEquipment = (equipmentId: string) => {
    // 控制设备
    console.log('控制设备:', equipmentId)
  }

  const manageProbeCard = (probeCard: ProbeCard) => {
    // 管理探针卡
    console.log('管理探针卡:', probeCard.id)
  }

  const onDieSelected = (die: WaferMapData) => {
    console.log('选中Die:', die)
  }

  const exportWaferMap = () => {
    ElMessage.success('晶圆图导出功能开发中')
  }

  const refreshParameters = () => {
    if (selectedTest.value) {
      loadTestParameters(selectedTest.value.id)
    }
  }

  const editParameter = (parameter: ProcessParameter) => {
    console.log('编辑参数:', parameter.id)
  }

  const refreshSecsMessages = () => {
    if (selectedTest.value) {
      loadSecsMessages(selectedTest.value.tester.id)
    }
  }

  const clearSecsMessages = () => {
    secsMessages.value = []
    ElMessage.success('消息已清空')
  }

  // 数据加载方法
  const loadCurrentTests = async () => {
    // 模拟API调用
    const mockTests: CPTestRecord[] = [
      {
        id: 'test-001',
        waferInfo: {
          id: 'wafer-001',
          waferLot: 'LOT001',
          waferNumber: 1,
          customerPN: 'ABC123',
          productCode: 'IC001',
          dieSize: { x: 2.5, y: 2.5 },
          totalDie: 10000,
          testedDie: 8500,
          passedDie: 8075,
          yield: 95.0,
          status: 'running',
          startTime: '2024-01-25T08:00:00Z'
        },
        probeCard: probeCards.value[0],
        tester: testEquipments.value[0],
        testProgram: 'TP_ABC123_V1.2',
        temperature: 25,
        testResults: generateTestResults(),
        waferMap: generateWaferMapData('wafer-001', 100, 100),
        operator: 'op001',
        startTime: '2024-01-25T08:00:00Z',
        endTime: '',
        duration: 120
      },
      {
        id: 'test-002',
        waferInfo: {
          id: 'wafer-002',
          waferLot: 'LOT002',
          waferNumber: 2,
          customerPN: 'XYZ456',
          productCode: 'IC002',
          dieSize: { x: 3.0, y: 3.0 },
          totalDie: 8000,
          testedDie: 8000,
          passedDie: 7520,
          yield: 94.0,
          status: 'completed',
          startTime: '2024-01-25T06:00:00Z'
        },
        probeCard: probeCards.value[1],
        tester: testEquipments.value[1],
        testProgram: 'TP_XYZ456_V2.0',
        temperature: 85,
        testResults: generateTestResults(),
        waferMap: generateWaferMapData('wafer-002', 90, 90),
        operator: 'op002',
        startTime: '2024-01-25T06:00:00Z',
        endTime: '2024-01-25T08:15:00Z',
        duration: 135
      }
    ]
    currentTests.value = mockTests
  }

  // 生成真实的晶圆图数据
  const generateWaferMapData = (waferId: string, maxX: number, maxY: number): WaferMapData => {
    const mapData: WaferMapData['mapData'] = []
    const centerX = maxX / 2
    const centerY = maxY / 2
    const radius = Math.min(maxX, maxY) / 2 - 5
    
    let totalDie = 0
    let passCount = 0
    let failCount = 0
    let retestCount = 0
    
    for (let x = 0; x < maxX; x++) {
      for (let y = 0; y < maxY; y++) {
        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2))
        
        // 只在圆形区域内生成die
        if (distance <= radius) {
          totalDie++
          
          // 基于位置和随机因素决定良率
          let result: 'pass' | 'fail' | 'retest' | 'pending'
          const edgeDistance = radius - distance
          const edgeFactor = edgeDistance / radius // 边缘系数，越接近边缘值越小
          
          const random = Math.random()
          
          // 边缘die良率较低
          if (edgeFactor < 0.1 && random < 0.3) {
            result = 'fail'
            failCount++
          } else if (edgeFactor < 0.2 && random < 0.15) {
            result = 'fail'
            failCount++
          } else if (random < 0.02) {
            result = 'retest'
            retestCount++
          } else if (random < 0.001) {
            result = 'pending'
          } else {
            result = 'pass'
            passCount++
          }
          
          mapData.push({
            x,
            y,
            result,
            binCode: result === 'pass' ? '1' : result === 'fail' ? '2' : '3',
            testData: {
              vdd: 5.0 + (Math.random() - 0.5) * 0.5,
              idd: 12.5 + (Math.random() - 0.5) * 2.0,
              frequency: 100 + (Math.random() - 0.5) * 10
            }
          })
        }
      }
    }
    
    const yieldPercentage = totalDie > 0 ? (passCount / totalDie) * 100 : 0
    
    return {
      waferId,
      mapData,
      dimensions: { maxX, maxY },
      binSummary: {
        '1': passCount,     // 良品
        '2': failCount,     // 不良品
        '3': retestCount,   // 重测
        '0': 0              // 废品
      },
      yield: yieldPercentage,
      generatedTime: new Date().toISOString()
    }
  }

  // 生成电测参数结果
  const generateTestResults = () => {
    return [
      {
        parameterName: 'VDD电流',
        measuredValue: 4.98 + Math.random() * 0.04,
        unit: 'mA',
        minLimit: 4.5,
        maxLimit: 5.5,
        result: 'pass' as const,
        cpk: 1.45 + Math.random() * 0.3
      },
      {
        parameterName: '输出高电平',
        measuredValue: 3.32 + Math.random() * 0.06,
        unit: 'V',
        minLimit: 3.1,
        maxLimit: 3.5,
        result: 'pass' as const,
        cpk: 1.33 + Math.random() * 0.2
      },
      {
        parameterName: '输出低电平',
        measuredValue: 0.38 + Math.random() * 0.04,
        unit: 'V',
        minLimit: 0.0,
        maxLimit: 0.5,
        result: 'pass' as const,
        cpk: 1.67 + Math.random() * 0.15
      },
      {
        parameterName: '工作频率',
        measuredValue: 99.8 + Math.random() * 0.4,
        unit: 'MHz',
        minLimit: 95,
        maxLimit: 105,
        result: 'pass' as const,
        cpk: 1.2 + Math.random() * 0.25
      },
      {
        parameterName: '功耗',
        measuredValue: 250 + Math.random() * 20,
        unit: 'mW',
        minLimit: 200,
        maxLimit: 300,
        result: 'pass' as const,
        cpk: 1.1 + Math.random() * 0.3
      }
    ]
  }

  const loadTestParameters = async (testId: string) => {
    parameterLoading.value = true
    try {
      // 模拟加载测试参数
      testParametersData.value = [
        {
          id: 'param-001',
          name: 'VDD电流',
          unit: 'mA',
          targetValue: 5.0,
          tolerance: 0.5,
          currentValue: 4.98,
          minLimit: 4.0,
          maxLimit: 6.0,
          status: 'normal'
        },
        {
          id: 'param-002',
          name: '输出高电平',
          unit: 'V',
          targetValue: 3.3,
          tolerance: 0.1,
          currentValue: 3.35,
          minLimit: 3.1,
          maxLimit: 3.5,
          status: 'warning'
        }
      ]
    } finally {
      parameterLoading.value = false
    }
  }

  const loadSecsMessages = async (equipmentId: string) => {
    // 模拟加载SECS消息
    secsMessages.value = [
      {
        id: 'msg-001',
        equipmentId,
        stream: 1,
        function: 1,
        direction: 'host_to_equipment',
        data: { command: 'GO_ONLINE' },
        timestamp: new Date().toISOString(),
        status: 'sent'
      },
      {
        id: 'msg-002',
        equipmentId,
        stream: 1,
        function: 2,
        direction: 'equipment_to_host',
        data: { response: 'ACK' },
        timestamp: new Date().toISOString(),
        status: 'received'
      }
    ]
  }

  // 生命周期
  onMounted(() => {
    loadCurrentTests()
  })

  let refreshTimer: NodeJS.Timeout

  onMounted(() => {
    // 设置定时刷新
    refreshTimer = setInterval(() => {
      if (selectedTest.value) {
        loadTestParameters(selectedTest.value.id)
      }
    }, 10000) // 10秒刷新一次参数
  })

  onUnmounted(() => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }
  })
</script>

<style lang="scss" scoped>
  .cp-testing-page {
    min-height: 100vh;
    padding: var(--spacing-6);
    background: var(--color-bg-page);
  }

  .page-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);

    .page-title {
      h1 {
        margin: 0 0 var(--spacing-1) 0;
        font-size: var(--font-size-2xl);
        font-weight: 600;
        color: var(--color-text-primary);
      }

      p {
        margin: 0;
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }
    }

    .page-actions {
      display: flex;
      gap: var(--spacing-2);
    }
  }

  .overview-section {
    margin-bottom: var(--spacing-6);
  }

  .kpi-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
  }

  .kpi-card {
    padding: var(--spacing-4);
    text-align: center;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);

    .kpi-value {
      margin-bottom: var(--spacing-2);
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-primary);
    }

    .kpi-label {
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .kpi-trend {
      font-size: var(--font-size-sm);
      font-weight: 600;

      &.up {
        color: var(--color-success);

        &::before {
          margin-right: 2px;
          content: '↑';
        }
      }

      &.down {
        color: var(--color-danger);

        &::before {
          margin-right: 2px;
          content: '↓';
        }
      }
    }
  }

  .main-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: var(--spacing-6);
  }

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);

    h2 {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--color-text-primary);
    }
  }

  .equipment-section {
    height: fit-content;
    padding: var(--spacing-4);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);
  }

  .equipment-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-6);
  }

  .probe-card-section {
    h3 {
      margin: 0 0 var(--spacing-3) 0;
      font-size: var(--font-size-base);
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .pm-warning {
      font-weight: 600;
      color: var(--color-warning);
    }
  }

  .testing-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .current-tests {
    padding: var(--spacing-4);
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);
  }

  .test-cards {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    max-height: 400px;
    overflow-y: auto;
  }

  .test-card {
    padding: var(--spacing-3);
    cursor: pointer;
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-base);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--color-primary);
      box-shadow: var(--shadow-md);
    }

    &--running {
      border-left: 4px solid var(--color-success);
    }

    &--completed {
      border-left: 4px solid var(--color-primary);
    }

    &--failed {
      border-left: 4px solid var(--color-danger);
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-3);

      .wafer-id {
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }

    &__content {
      .test-info {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-1);
        margin-bottom: var(--spacing-3);
        font-size: var(--font-size-sm);
        color: var(--color-text-secondary);
      }

      .test-progress {
        margin-bottom: var(--spacing-2);

        .progress-text {
          margin-top: var(--spacing-1);
          font-size: var(--font-size-sm);
          color: var(--color-text-secondary);
        }
      }

      .test-yield {
        font-size: var(--font-size-sm);

        .yield-excellent {
          color: var(--color-success);
        }

        .yield-good {
          color: var(--color-primary);
        }

        .yield-fair {
          color: var(--color-warning);
        }

        .yield-poor {
          color: var(--color-danger);
        }
      }
    }

    &__footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: var(--spacing-2);
      margin-top: var(--spacing-3);
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
      border-top: 1px solid var(--color-border-lighter);
    }
  }

  .test-details-panel {
    min-height: 500px;
    background: var(--color-bg-primary);
    border-radius: var(--radius-base);
  }

  .test-results-content {
    padding: var(--spacing-4);
  }

  .results-summary {
    margin-bottom: var(--spacing-6);

    h3 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
  }

  .summary-item {
    .summary-label {
      margin-bottom: var(--spacing-1);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .summary-value {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--color-text-primary);
    }
  }

  .electrical-results {
    h3 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-text-primary);
    }

    .cpk-excellent {
      color: var(--color-success);
    }

    .cpk-good {
      color: var(--color-primary);
    }

    .cpk-fair {
      color: var(--color-warning);
    }

    .cpk-poor {
      color: var(--color-danger);
    }
  }

  .secs-messages {
    padding: var(--spacing-4);
  }

  .secs-controls {
    display: flex;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-3);
  }

  @media (width <= 1200px) {
    .main-content {
      grid-template-columns: 1fr;
    }
  }

  @media (width <= 768px) {
    .cp-testing-page {
      padding: var(--spacing-4);
    }

    .page-header {
      flex-direction: column;
      gap: var(--spacing-3);
      align-items: stretch;

      .page-actions {
        justify-content: center;
      }
    }

    .kpi-cards {
      grid-template-columns: 1fr;
    }
  }
</style>
