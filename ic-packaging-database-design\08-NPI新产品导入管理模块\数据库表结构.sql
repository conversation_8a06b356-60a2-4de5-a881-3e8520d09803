-- ========================================
-- NPI新产品导入管理模块 - 数据库表结构
-- IC Assembly & Testing Manufacturing System
-- ========================================

-- NPI项目主表
CREATE TABLE npi_projects (
    project_id VARCHAR(32) PRIMARY KEY COMMENT 'NPI项目ID',
    project_code VARCHAR(50) NOT NULL UNIQUE COMMENT '项目编码',
    project_name VARCHAR(200) NOT NULL COMMENT '项目名称',
    customer_id VARCHAR(32) NOT NULL COMMENT '客户ID',
    customer_contact_id VARCHAR(32) COMMENT '客户联系人ID',
    
    -- 产品信息
    product_type VARCHAR(50) COMMENT '产品类型(MCU/Memory/RF/Power)',
    package_type VARCHAR(50) COMMENT '封装类型(QFP/BGA/CSP/FC)',
    die_size_x DECIMAL(8,3) COMMENT 'Die尺寸X(mm)',
    die_size_y DECIMAL(8,3) COMMENT 'Die尺寸Y(mm)',
    pin_count INT COMMENT '引脚数量',
    
    -- 项目状态
    project_status VARCHAR(20) NOT NULL DEFAULT 'INITIATED' COMMENT '项目状态',
    current_stage VARCHAR(20) COMMENT '当前阶段',
    priority_level VARCHAR(10) DEFAULT 'MEDIUM' COMMENT '优先级(HIGH/MEDIUM/LOW)',
    
    -- 时间计划
    planned_start_date DATE COMMENT '计划开始时间',
    planned_end_date DATE COMMENT '计划结束时间',
    actual_start_date DATE COMMENT '实际开始时间',
    actual_end_date DATE COMMENT '实际结束时间',
    
    -- 商务信息
    estimated_volume BIGINT COMMENT '预计年产量',
    target_cost DECIMAL(10,4) COMMENT '目标成本',
    contract_value DECIMAL(15,2) COMMENT '合同总价值',
    
    -- 项目管理
    project_manager_id VARCHAR(32) COMMENT '项目经理ID',
    technical_lead_id VARCHAR(32) COMMENT '技术负责人ID',
    sales_rep_id VARCHAR(32) COMMENT '销售代表ID',
    
    -- 风险等级
    technical_risk_level VARCHAR(10) DEFAULT 'MEDIUM' COMMENT '技术风险等级',
    schedule_risk_level VARCHAR(10) DEFAULT 'MEDIUM' COMMENT '进度风险等级',
    cost_risk_level VARCHAR(10) DEFAULT 'MEDIUM' COMMENT '成本风险等级',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version_no INT DEFAULT 1 COMMENT '版本号',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    INDEX idx_npi_customer (customer_id),
    INDEX idx_npi_status (project_status),
    INDEX idx_npi_stage (current_stage),
    INDEX idx_npi_manager (project_manager_id),
    INDEX idx_npi_create_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='NPI项目主表';

-- NPI项目阶段表
CREATE TABLE npi_project_stages (
    stage_id VARCHAR(32) PRIMARY KEY COMMENT '阶段ID',
    project_id VARCHAR(32) NOT NULL COMMENT 'NPI项目ID',
    stage_code VARCHAR(20) NOT NULL COMMENT '阶段编码',
    stage_name VARCHAR(100) NOT NULL COMMENT '阶段名称',
    stage_sequence INT NOT NULL COMMENT '阶段序号',
    
    -- 阶段状态
    stage_status VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '阶段状态',
    gate_review_status VARCHAR(20) COMMENT 'Gate评审状态',
    
    -- 时间计划
    planned_start_date DATE COMMENT '计划开始时间',
    planned_end_date DATE COMMENT '计划结束时间',
    actual_start_date DATE COMMENT '实际开始时间',
    actual_end_date DATE COMMENT '实际结束时间',
    
    -- 阶段负责人
    stage_owner_id VARCHAR(32) COMMENT '阶段负责人ID',
    
    -- Gate评审信息
    gate_review_date DATE COMMENT 'Gate评审日期',
    gate_review_result VARCHAR(20) COMMENT 'Gate评审结果',
    gate_review_comment TEXT COMMENT 'Gate评审意见',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (project_id) REFERENCES npi_projects(project_id) ON DELETE CASCADE,
    INDEX idx_npi_stage_project (project_id),
    INDEX idx_npi_stage_status (stage_status),
    INDEX idx_npi_stage_sequence (project_id, stage_sequence),
    UNIQUE KEY uk_npi_stage_code (project_id, stage_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='NPI项目阶段表';

-- NPI阶段任务表
CREATE TABLE npi_stage_tasks (
    task_id VARCHAR(32) PRIMARY KEY COMMENT '任务ID',
    stage_id VARCHAR(32) NOT NULL COMMENT '阶段ID',
    project_id VARCHAR(32) NOT NULL COMMENT '项目ID',
    task_code VARCHAR(50) NOT NULL COMMENT '任务编码',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    task_type VARCHAR(50) COMMENT '任务类型',
    
    -- 任务描述
    task_description TEXT COMMENT '任务描述',
    acceptance_criteria TEXT COMMENT '验收标准',
    
    -- 任务状态
    task_status VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '任务状态',
    completion_percentage DECIMAL(5,2) DEFAULT 0 COMMENT '完成百分比',
    
    -- 时间计划
    planned_start_date DATE COMMENT '计划开始时间',
    planned_end_date DATE COMMENT '计划结束时间',
    actual_start_date DATE COMMENT '实际开始时间',
    actual_end_date DATE COMMENT '实际结束时间',
    estimated_hours DECIMAL(8,2) COMMENT '预估工时',
    actual_hours DECIMAL(8,2) COMMENT '实际工时',
    
    -- 任务分配
    assignee_id VARCHAR(32) COMMENT '任务负责人ID',
    department_id VARCHAR(32) COMMENT '负责部门ID',
    priority_level VARCHAR(10) DEFAULT 'MEDIUM' COMMENT '优先级',
    
    -- 依赖关系
    predecessor_tasks JSON COMMENT '前置任务列表',
    successor_tasks JSON COMMENT '后续任务列表',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (stage_id) REFERENCES npi_project_stages(stage_id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES npi_projects(project_id) ON DELETE CASCADE,
    INDEX idx_npi_task_stage (stage_id),
    INDEX idx_npi_task_project (project_id),
    INDEX idx_npi_task_status (task_status),
    INDEX idx_npi_task_assignee (assignee_id),
    INDEX idx_npi_task_dates (planned_start_date, planned_end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='NPI阶段任务表';

-- NPI交付物管理表
CREATE TABLE npi_deliverables (
    deliverable_id VARCHAR(32) PRIMARY KEY COMMENT '交付物ID',
    project_id VARCHAR(32) NOT NULL COMMENT '项目ID',
    task_id VARCHAR(32) COMMENT '关联任务ID',
    deliverable_code VARCHAR(50) NOT NULL COMMENT '交付物编码',
    deliverable_name VARCHAR(200) NOT NULL COMMENT '交付物名称',
    deliverable_type VARCHAR(50) NOT NULL COMMENT '交付物类型',
    
    -- 交付物描述
    description TEXT COMMENT '交付物描述',
    template_id VARCHAR(32) COMMENT '模板ID',
    
    -- 交付物状态
    status VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '状态',
    version_no VARCHAR(20) COMMENT '版本号',
    
    -- 时间信息
    planned_delivery_date DATE COMMENT '计划交付时间',
    actual_delivery_date DATE COMMENT '实际交付时间',
    
    -- 负责人信息
    owner_id VARCHAR(32) COMMENT '负责人ID',
    reviewer_ids JSON COMMENT '评审人员ID列表',
    approver_id VARCHAR(32) COMMENT '批准人ID',
    
    -- 客户交付
    customer_required TINYINT(1) DEFAULT 0 COMMENT '是否需要客户交付',
    customer_approved TINYINT(1) DEFAULT 0 COMMENT '客户是否已批准',
    customer_approval_date DATE COMMENT '客户批准日期',
    customer_comments TEXT COMMENT '客户意见',
    
    -- 文件信息
    file_path VARCHAR(500) COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小(bytes)',
    file_checksum VARCHAR(64) COMMENT '文件校验和',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (project_id) REFERENCES npi_projects(project_id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES npi_stage_tasks(task_id) ON DELETE SET NULL,
    INDEX idx_npi_deliverable_project (project_id),
    INDEX idx_npi_deliverable_task (task_id),
    INDEX idx_npi_deliverable_status (status),
    INDEX idx_npi_deliverable_owner (owner_id),
    INDEX idx_npi_deliverable_type (deliverable_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='NPI交付物管理表';

-- NPI客户反馈表
CREATE TABLE npi_customer_feedback (
    feedback_id VARCHAR(32) PRIMARY KEY COMMENT '反馈ID',
    project_id VARCHAR(32) NOT NULL COMMENT '项目ID',
    stage_id VARCHAR(32) COMMENT '阶段ID',
    deliverable_id VARCHAR(32) COMMENT '交付物ID',
    
    -- 反馈信息
    feedback_type VARCHAR(50) NOT NULL COMMENT '反馈类型',
    feedback_category VARCHAR(50) COMMENT '反馈分类',
    feedback_title VARCHAR(200) NOT NULL COMMENT '反馈标题',
    feedback_content TEXT NOT NULL COMMENT '反馈内容',
    
    -- 严重程度
    severity_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '严重程度',
    priority_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '优先级',
    
    -- 反馈人信息
    customer_contact_id VARCHAR(32) NOT NULL COMMENT '客户联系人ID',
    submission_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    
    -- 处理信息
    status VARCHAR(20) NOT NULL DEFAULT 'OPEN' COMMENT '处理状态',
    assigned_to VARCHAR(32) COMMENT '分配给(内部处理人)',
    target_resolution_date DATE COMMENT '目标解决时间',
    actual_resolution_date DATE COMMENT '实际解决时间',
    
    -- 解决方案
    resolution_description TEXT COMMENT '解决方案描述',
    resolution_attachments JSON COMMENT '解决方案附件',
    
    -- 客户确认
    customer_satisfaction_level VARCHAR(20) COMMENT '客户满意度',
    customer_approval_status VARCHAR(20) COMMENT '客户确认状态',
    customer_approval_date DATE COMMENT '客户确认时间',
    customer_final_comments TEXT COMMENT '客户最终意见',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (project_id) REFERENCES npi_projects(project_id) ON DELETE CASCADE,
    FOREIGN KEY (stage_id) REFERENCES npi_project_stages(stage_id) ON DELETE SET NULL,
    FOREIGN KEY (deliverable_id) REFERENCES npi_deliverables(deliverable_id) ON DELETE SET NULL,
    INDEX idx_npi_feedback_project (project_id),
    INDEX idx_npi_feedback_status (status),
    INDEX idx_npi_feedback_customer (customer_contact_id),
    INDEX idx_npi_feedback_assigned (assigned_to),
    INDEX idx_npi_feedback_date (submission_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='NPI客户反馈表';

-- NPI风险管理表
CREATE TABLE npi_risk_management (
    risk_id VARCHAR(32) PRIMARY KEY COMMENT '风险ID',
    project_id VARCHAR(32) NOT NULL COMMENT '项目ID',
    risk_code VARCHAR(50) NOT NULL COMMENT '风险编码',
    risk_title VARCHAR(200) NOT NULL COMMENT '风险标题',
    risk_description TEXT NOT NULL COMMENT '风险描述',
    
    -- 风险分类
    risk_category VARCHAR(50) NOT NULL COMMENT '风险类别',
    risk_type VARCHAR(50) COMMENT '风险类型',
    risk_source VARCHAR(100) COMMENT '风险来源',
    
    -- FMEA评分
    severity_score INT DEFAULT 1 COMMENT '严重度(1-10)',
    occurrence_score INT DEFAULT 1 COMMENT '发生度(1-10)',
    detection_score INT DEFAULT 1 COMMENT '探测度(1-10)',
    rpn_score INT GENERATED ALWAYS AS (severity_score * occurrence_score * detection_score) COMMENT 'RPN值',
    
    -- 风险状态
    risk_status VARCHAR(20) NOT NULL DEFAULT 'IDENTIFIED' COMMENT '风险状态',
    risk_level VARCHAR(20) COMMENT '风险等级',
    
    -- 负责人
    risk_owner_id VARCHAR(32) COMMENT '风险负责人ID',
    
    -- 应对措施
    mitigation_strategy TEXT COMMENT '缓解策略',
    contingency_plan TEXT COMMENT '应急预案',
    prevention_actions TEXT COMMENT '预防措施',
    
    -- 时间信息
    identified_date DATE NOT NULL COMMENT '识别日期',
    target_closure_date DATE COMMENT '目标关闭日期',
    actual_closure_date DATE COMMENT '实际关闭日期',
    
    -- 影响评估
    cost_impact DECIMAL(15,2) COMMENT '成本影响',
    schedule_impact_days INT COMMENT '进度影响(天)',
    quality_impact TEXT COMMENT '质量影响',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (project_id) REFERENCES npi_projects(project_id) ON DELETE CASCADE,
    INDEX idx_npi_risk_project (project_id),
    INDEX idx_npi_risk_status (risk_status),
    INDEX idx_npi_risk_owner (risk_owner_id),
    INDEX idx_npi_risk_rpn (rpn_score DESC),
    INDEX idx_npi_risk_category (risk_category),
    INDEX idx_npi_risk_level (risk_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='NPI风险管理表';

-- NPI知识库表
CREATE TABLE npi_knowledge_base (
    knowledge_id VARCHAR(32) PRIMARY KEY COMMENT '知识ID',
    knowledge_code VARCHAR(50) NOT NULL UNIQUE COMMENT '知识编码',
    knowledge_title VARCHAR(200) NOT NULL COMMENT '知识标题',
    knowledge_type VARCHAR(50) NOT NULL COMMENT '知识类型',
    knowledge_category VARCHAR(50) NOT NULL COMMENT '知识分类',
    
    -- 知识内容
    knowledge_summary TEXT COMMENT '知识摘要',
    knowledge_content LONGTEXT COMMENT '知识内容',
    keywords VARCHAR(500) COMMENT '关键词',
    tags JSON COMMENT '标签',
    
    -- 来源信息
    source_project_id VARCHAR(32) COMMENT '来源项目ID',
    source_task_id VARCHAR(32) COMMENT '来源任务ID',
    source_document VARCHAR(200) COMMENT '来源文档',
    
    -- 适用范围
    applicable_product_types JSON COMMENT '适用产品类型',
    applicable_package_types JSON COMMENT '适用封装类型',
    applicable_process_steps JSON COMMENT '适用工艺步骤',
    
    -- 知识评价
    usefulness_rating DECIMAL(3,2) DEFAULT 0 COMMENT '有用性评分(0-5)',
    accuracy_rating DECIMAL(3,2) DEFAULT 0 COMMENT '准确性评分(0-5)',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    
    -- 状态管理
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态',
    approval_status VARCHAR(20) COMMENT '审批状态',
    approved_by VARCHAR(32) COMMENT '审批人ID',
    approved_date DATE COMMENT '审批日期',
    
    -- 版本管理
    version_no VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    parent_knowledge_id VARCHAR(32) COMMENT '父知识ID',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (source_project_id) REFERENCES npi_projects(project_id) ON DELETE SET NULL,
    INDEX idx_npi_knowledge_type (knowledge_type),
    INDEX idx_npi_knowledge_category (knowledge_category),
    INDEX idx_npi_knowledge_source (source_project_id),
    INDEX idx_npi_knowledge_rating (usefulness_rating DESC),
    INDEX idx_npi_knowledge_usage (usage_count DESC),
    FULLTEXT idx_npi_knowledge_content (knowledge_title, knowledge_summary, knowledge_content, keywords)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='NPI知识库表';

-- NPI项目变更管理表
CREATE TABLE npi_change_requests (
    change_id VARCHAR(32) PRIMARY KEY COMMENT '变更ID',
    project_id VARCHAR(32) NOT NULL COMMENT '项目ID',
    change_no VARCHAR(50) NOT NULL UNIQUE COMMENT '变更编号',
    change_title VARCHAR(200) NOT NULL COMMENT '变更标题',
    change_type VARCHAR(50) NOT NULL COMMENT '变更类型',
    change_category VARCHAR(50) COMMENT '变更分类',
    
    -- 变更描述
    change_description TEXT NOT NULL COMMENT '变更描述',
    change_reason TEXT NOT NULL COMMENT '变更原因',
    current_situation TEXT COMMENT '当前情况',
    proposed_solution TEXT COMMENT '建议解决方案',
    
    -- 变更影响分析
    technical_impact TEXT COMMENT '技术影响',
    schedule_impact_days INT COMMENT '进度影响天数',
    cost_impact DECIMAL(15,2) COMMENT '成本影响',
    quality_impact TEXT COMMENT '质量影响',
    risk_assessment TEXT COMMENT '风险评估',
    
    -- 变更状态
    status VARCHAR(20) NOT NULL DEFAULT 'SUBMITTED' COMMENT '变更状态',
    priority_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '优先级',
    urgency_level VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '紧急程度',
    
    -- 申请信息
    requester_id VARCHAR(32) NOT NULL COMMENT '申请人ID',
    request_date DATE NOT NULL COMMENT '申请日期',
    
    -- 审批信息
    approver_ids JSON COMMENT '审批人ID列表',
    approval_status VARCHAR(20) COMMENT '审批状态',
    approved_date DATE COMMENT '批准日期',
    approval_comments TEXT COMMENT '审批意见',
    
    -- 实施信息
    implementer_id VARCHAR(32) COMMENT '实施人ID',
    planned_implementation_date DATE COMMENT '计划实施日期',
    actual_implementation_date DATE COMMENT '实际实施日期',
    implementation_notes TEXT COMMENT '实施说明',
    
    -- 客户确认
    customer_notification_required TINYINT(1) DEFAULT 0 COMMENT '是否需要客户通知',
    customer_approval_required TINYINT(1) DEFAULT 0 COMMENT '是否需要客户批准',
    customer_notified_date DATE COMMENT '客户通知日期',
    customer_approved_date DATE COMMENT '客户批准日期',
    
    -- 审计字段
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(32) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (project_id) REFERENCES npi_projects(project_id) ON DELETE CASCADE,
    INDEX idx_npi_change_project (project_id),
    INDEX idx_npi_change_status (status),
    INDEX idx_npi_change_requester (requester_id),
    INDEX idx_npi_change_type (change_type),
    INDEX idx_npi_change_date (request_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='NPI项目变更管理表';

-- ========================================
-- 表关系说明
-- ========================================
/*
1. npi_projects: NPI项目主表，存储项目基本信息
2. npi_project_stages: 项目阶段表，一个项目包含多个阶段
3. npi_stage_tasks: 阶段任务表，一个阶段包含多个任务
4. npi_deliverables: 交付物表，可关联到项目或任务
5. npi_customer_feedback: 客户反馈表，记录客户意见和处理过程
6. npi_risk_management: 风险管理表，FMEA分析和风险应对
7. npi_knowledge_base: 知识库表，项目经验积累和复用
8. npi_change_requests: 变更管理表，项目变更申请和处理

核心业务流程:
项目立项 → 阶段执行 → 任务完成 → 交付物提交 → 客户确认 → 知识沉淀
*/