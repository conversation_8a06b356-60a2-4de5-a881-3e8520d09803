# NPI新产品导入管理模块设计

## 1. 模块概述

### 1.1 模块定位
NPI（New Product Introduction）新产品导入管理模块是IC封测CIM系统的核心业务模块，专门管理从客户新产品需求到量产转移的完整NPI流程。该模块涵盖产品评估、封装设计、工艺开发、测试开发、试制验证、可靠性测试、客户认证等全生命周期管理。

### 1.2 IC封测行业NPI特点
- **技术复杂性高**：涉及芯片结构、封装技术、测试技术等多个专业领域
- **客户定制化强**：每个新产品都有独特的封装和测试要求
- **质量要求严格**：需要通过JEDEC、AEC-Q100等严格的可靠性标准
- **周期压力大**：通常需要在6-12周内完成整个NPI过程
- **成本敏感性高**：NPI阶段的决策直接影响量产成本

### 1.3 核心业务价值
- **缩短NPI周期**：通过流程标准化和并行工程，将NPI周期缩短20-30%
- **提高成功率**：通过阶段门控和风险预警，将NPI成功率提升至95%以上
- **降低NPI成本**：通过经验复用和资源优化，降低NPI总成本15-25%
- **增强客户满意度**：通过透明化管理和实时沟通，提升客户满意度

### 1.4 应用场景覆盖
```
NPI新产品导入管理应用场景
├── NPI项目管理
│   ├── 项目立项和评估
│   ├── 项目计划制定
│   ├── 里程碑管控
│   └── 项目结项管理
├── 产品技术评估
│   ├── 产品可行性评估
│   ├── 封装方案评估
│   ├── 测试方案评估
│   └── 成本评估分析
├── 封装设计管理
│   ├── 封装类型选择
│   ├── 引脚定义设计
│   ├── 热仿真分析
│   └── 可靠性设计
├── 工艺开发管理
│   ├── Die Attach工艺开发
│   ├── Wire Bond工艺开发
│   ├── Molding工艺开发
│   └── 工艺参数优化
├── 测试开发管理
│   ├── 测试方案设计
│   ├── ATE程序开发
│   ├── 测试硬件开发
│   └── 测试条件验证
├── 试制与验证
│   ├── 工程批试制
│   ├── 电性能验证
│   ├── 物理性能验证
│   └── 工艺能力验证
├── 可靠性测试
│   ├── JEDEC标准测试
│   ├── AEC-Q100认证
│   ├── 客户特殊要求测试
│   └── 可靠性报告生成
├── 量产准备
│   ├── 工艺文件发布
│   ├── 测试程序发布
│   ├── 人员培训计划
│   └── 产能准备评估
└── 客户认证管理
    ├── 样品提交管理
    ├── 客户测试跟踪
    ├── 问题反馈处理
    └── 认证结果确认
```

## 2. NPI专业架构设计

### 2.1 技术架构
```
NPI新产品导入管理模块架构
├── NPI项目管理引擎        # 项目全生命周期管理
├── 技术评估决策系统        # 多维度技术评估和决策支持
├── 封装设计协同平台        # CAD集成和设计协作
├── 工艺开发实验系统        # 工艺参数开发和优化
├── 测试开发集成平台        # ATE程序和硬件开发
├── 试制执行管理系统        # 试制计划和执行跟踪
├── 可靠性测试管理          # 标准化可靠性测试流程
├── 客户协作门户            # 客户沟通和信息共享
├── 知识库管理系统          # NPI经验和知识积累
└── 数据分析决策中心        # NPI数据分析和持续改进
```

### 2.2 核心数据模型

#### 2.2.1 NPI项目管理
```sql
-- NPI项目基本信息表
CREATE TABLE npi_projects (
    project_id VARCHAR(30) PRIMARY KEY,
    project_code VARCHAR(50) UNIQUE,           -- 项目编码：NPI-2025-0001
    project_name VARCHAR(200),                 -- 项目名称
    customer_id VARCHAR(30),                   -- 客户ID
    customer_name VARCHAR(100),                -- 客户名称
    product_family VARCHAR(50),                -- 产品系列
    product_description TEXT,                  -- 产品描述
    package_type VARCHAR(50),                  -- 封装类型：QFP/BGA/CSP/FC等
    die_size_x DECIMAL(8,3),                  -- 芯片尺寸X(mm)
    die_size_y DECIMAL(8,3),                  -- 芯片尺寸Y(mm)
    die_thickness DECIMAL(8,3),               -- 芯片厚度(mm)
    pin_count INT,                            -- 引脚数量
    lead_pitch DECIMAL(8,3),                  -- 引脚间距(mm)
    project_priority ENUM('P0','P1','P2','P3'), -- 项目优先级
    business_type ENUM('automotive','consumer','industrial','telecom'), -- 应用领域
    target_volume_yearly BIGINT,              -- 年目标产量
    target_price DECIMAL(10,4),               -- 目标单价
    project_manager_id VARCHAR(20),           -- 项目经理
    project_status ENUM('evaluation','approved','development','validation','certification','mass_production','closed'), -- 项目状态
    planned_start_date DATE,                  -- 计划开始日期
    planned_end_date DATE,                    -- 计划结束日期
    actual_start_date DATE,                   -- 实际开始日期
    actual_end_date DATE,                     -- 实际结束日期
    estimated_npi_cost DECIMAL(12,2),        -- 预估NPI成本
    actual_npi_cost DECIMAL(12,2),           -- 实际NPI成本
    risk_level ENUM('low','medium','high','critical'), -- 风险等级
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_customer_status (customer_id, project_status),
    INDEX idx_pm_priority (project_manager_id, project_priority),
    INDEX idx_package_type (package_type),
    INDEX idx_planned_dates (planned_start_date, planned_end_date)
);

-- NPI阶段门控表
CREATE TABLE npi_stage_gates (
    gate_id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30),                   -- NPI项目ID
    stage_name ENUM('evaluation','design','development','validation','certification','ramp_up'), -- 阶段名称
    stage_sequence INT,                       -- 阶段顺序
    gate_name VARCHAR(100),                   -- 门控名称
    gate_criteria JSON,                       -- 门控标准(JSON格式)
    planned_date DATE,                        -- 计划日期
    actual_date DATE,                         -- 实际完成日期
    gate_status ENUM('not_started','in_progress','passed','failed','waived'), -- 门控状态
    decision_maker VARCHAR(20),               -- 决策人
    decision_date DATE,                       -- 决策日期
    decision_comments TEXT,                   -- 决策意见
    deliverables JSON,                        -- 交付物清单
    exit_criteria JSON,                       -- 退出标准
    created_at TIMESTAMP,
    
    INDEX idx_project_stage (project_id, stage_name),
    INDEX idx_planned_date (planned_date),
    INDEX idx_status (gate_status)
);

-- NPI任务管理表
CREATE TABLE npi_tasks (
    task_id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30),                   -- NPI项目ID
    parent_task_id VARCHAR(30),               -- 父任务ID(用于任务分解)
    task_code VARCHAR(50),                    -- 任务编码
    task_name VARCHAR(200),                   -- 任务名称
    task_type ENUM('evaluation','design','development','testing','validation','documentation'), -- 任务类型
    task_category ENUM('package_design','process_dev','test_dev','reliability','quality','project_mgmt'), -- 任务分类
    description TEXT,                         -- 任务描述
    assigned_to VARCHAR(20),                  -- 责任人
    assigned_department VARCHAR(50),          -- 责任部门
    planned_start_date DATE,                  -- 计划开始日期
    planned_end_date DATE,                    -- 计划结束日期
    actual_start_date DATE,                   -- 实际开始日期
    actual_end_date DATE,                     -- 实际结束日期
    estimated_hours DECIMAL(8,2),            -- 预估工时
    actual_hours DECIMAL(8,2),               -- 实际工时
    task_status ENUM('not_started','in_progress','completed','cancelled','on_hold'), -- 任务状态
    completion_percentage INT DEFAULT 0,      -- 完成百分比
    priority ENUM('low','normal','high','urgent'), -- 优先级
    predecessors JSON,                        -- 前置任务列表
    deliverables TEXT,                        -- 交付物
    comments TEXT,                            -- 备注
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_project_status (project_id, task_status),
    INDEX idx_assigned (assigned_to, task_status),
    INDEX idx_dates (planned_start_date, planned_end_date),
    INDEX idx_category (task_category, task_status)
);
```

#### 2.2.2 产品技术规格管理
```sql
-- 产品技术规格表
CREATE TABLE npi_product_specs (
    spec_id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30),                   -- NPI项目ID
    spec_version VARCHAR(20),                 -- 规格版本
    spec_type ENUM('electrical','mechanical','environmental','reliability'), -- 规格类型
    parameter_name VARCHAR(100),              -- 参数名称
    parameter_code VARCHAR(50),               -- 参数编码
    specification_min DECIMAL(15,6),          -- 规格下限
    specification_max DECIMAL(15,6),          -- 规格上限
    typical_value DECIMAL(15,6),             -- 典型值
    unit VARCHAR(20),                         -- 单位
    test_condition VARCHAR(200),              -- 测试条件
    test_method VARCHAR(100),                 -- 测试方法
    requirement_source ENUM('customer','jedec','aec_q100','company'), -- 需求来源
    criticality ENUM('critical','major','minor'), -- 重要性
    is_active BOOLEAN DEFAULT TRUE,
    comments TEXT,                            -- 备注
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_project_type (project_id, spec_type),
    INDEX idx_parameter (parameter_code),
    INDEX idx_version (spec_version, is_active)
);

-- 封装设计规格表
CREATE TABLE npi_package_specs (
    package_spec_id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30),                   -- NPI项目ID
    package_type VARCHAR(50),                 -- 封装类型
    package_size_x DECIMAL(8,3),             -- 封装尺寸X(mm)
    package_size_y DECIMAL(8,3),             -- 封装尺寸Y(mm)
    package_height DECIMAL(8,3),             -- 封装高度(mm)
    body_size_x DECIMAL(8,3),                -- 塑封体尺寸X(mm)
    body_size_y DECIMAL(8,3),                -- 塑封体尺寸Y(mm)
    body_thickness DECIMAL(8,3),             -- 塑封体厚度(mm)
    lead_frame_material VARCHAR(50),          -- 引脚框架材料
    die_attach_material VARCHAR(50),          -- 贴片材料
    wire_material VARCHAR(50),                -- 键合线材料
    wire_diameter DECIMAL(6,3),              -- 键合线直径(μm)
    molding_compound VARCHAR(50),             -- 塑封料
    lead_plating VARCHAR(50),                -- 引脚电镀
    msl_level ENUM('1','2','2a','3','4','5','5a','6'), -- 湿敏等级
    peak_reflow_temp DECIMAL(6,2),           -- 峰值回流温度(°C)
    thermal_resistance DECIMAL(8,3),         -- 热阻(°C/W)
    coplanarity DECIMAL(6,3),                -- 共面性(mm)
    lead_coplanarity DECIMAL(6,3),           -- 引脚共面性(mm)
    warpage DECIMAL(6,3),                    -- 翘曲度(mm)
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    
    INDEX idx_project (project_id),
    INDEX idx_package_type (package_type)
);
```

#### 2.2.3 工艺开发管理
```sql
-- 工艺开发计划表
CREATE TABLE npi_process_development (
    dev_id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30),                   -- NPI项目ID
    process_type ENUM('die_attach','wire_bond','molding','trim_form','marking','testing'), -- 工艺类型
    development_phase ENUM('concept','development','verification','validation','release'), -- 开发阶段
    process_engineer VARCHAR(20),             -- 工艺工程师
    target_parameters JSON,                   -- 目标工艺参数
    current_parameters JSON,                  -- 当前工艺参数
    optimization_status ENUM('not_started','in_progress','completed'), -- 优化状态
    cpk_target DECIMAL(4,2) DEFAULT 1.33,    -- 目标Cpk值
    current_cpk DECIMAL(4,2),                -- 当前Cpk值
    yield_target DECIMAL(5,2),               -- 目标良率%
    current_yield DECIMAL(5,2),              -- 当前良率%
    takt_time_target INT,                    -- 目标节拍时间(秒)
    current_takt_time INT,                   -- 当前节拍时间(秒)
    equipment_requirements JSON,              -- 设备需求
    material_requirements JSON,               -- 材料需求
    quality_requirements JSON,                -- 质量要求
    development_status ENUM('planning','developing','testing','validating','completed','cancelled'), -- 开发状态
    completion_date DATE,                     -- 完成日期
    comments TEXT,
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_project_process (project_id, process_type),
    INDEX idx_engineer_status (process_engineer, development_status),
    INDEX idx_phase (development_phase)
);

-- 工艺实验记录表
CREATE TABLE npi_process_experiments (
    experiment_id VARCHAR(30) PRIMARY KEY,
    dev_id VARCHAR(30),                       -- 工艺开发ID
    experiment_code VARCHAR(50),              -- 实验编号
    experiment_name VARCHAR(200),             -- 实验名称
    experiment_type ENUM('doe','optimization','verification','capability'), -- 实验类型
    experiment_date DATE,                     -- 实验日期
    operator VARCHAR(20),                     -- 操作员
    equipment_id VARCHAR(30),                 -- 使用设备
    input_parameters JSON,                    -- 输入参数
    output_results JSON,                      -- 输出结果
    sample_size INT,                          -- 样品数量
    pass_count INT,                          -- 通过数量
    fail_count INT,                          -- 失败数量
    yield_rate DECIMAL(5,2),                 -- 良率%
    cpk_value DECIMAL(4,2),                  -- Cpk值
    experiment_conclusion TEXT,               -- 实验结论
    next_actions TEXT,                        -- 后续行动
    attachments JSON,                         -- 附件列表
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    
    INDEX idx_dev_date (dev_id, experiment_date),
    INDEX idx_experiment_type (experiment_type),
    INDEX idx_operator (operator)
);
```

#### 2.2.4 测试开发管理
```sql
-- 测试开发计划表
CREATE TABLE npi_test_development (
    test_dev_id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30),                   -- NPI项目ID
    test_phase ENUM('cp','ft','burn_in','reliability'), -- 测试阶段
    test_type ENUM('dc','ac','functional','scan','boundary_scan'), -- 测试类型
    test_engineer VARCHAR(20),                -- 测试工程师
    ate_platform VARCHAR(50),                -- ATE平台
    test_program_version VARCHAR(20),         -- 测试程序版本
    test_hardware_version VARCHAR(20),        -- 测试硬件版本
    test_conditions JSON,                     -- 测试条件
    test_limits JSON,                         -- 测试限值
    test_coverage DECIMAL(5,2),              -- 测试覆盖率%
    test_time_target INT,                     -- 目标测试时间(ms)
    current_test_time INT,                    -- 当前测试时间(ms)
    dppm_target DECIMAL(8,2),                -- 目标DPPM
    current_dppm DECIMAL(8,2),               -- 当前DPPM
    development_status ENUM('planning','developing','debugging','validating','released'), -- 开发状态
    validation_sample_size INT,               -- 验证样品数量
    validation_pass_rate DECIMAL(5,2),       -- 验证通过率%
    correlation_status ENUM('not_started','in_progress','passed','failed'), -- 关联性验证状态
    release_date DATE,                        -- 发布日期
    comments TEXT,
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_project_phase (project_id, test_phase),
    INDEX idx_engineer_status (test_engineer, development_status),
    INDEX idx_ate_platform (ate_platform)
);

-- 测试参数定义表
CREATE TABLE npi_test_parameters (
    param_id VARCHAR(30) PRIMARY KEY,
    test_dev_id VARCHAR(30),                  -- 测试开发ID
    parameter_name VARCHAR(100),              -- 参数名称
    parameter_code VARCHAR(50),               -- 参数编码
    test_number INT,                          -- 测试号
    pin_list TEXT,                            -- 引脚列表
    test_condition VARCHAR(200),              -- 测试条件
    low_limit DECIMAL(15,6),                 -- 下限
    high_limit DECIMAL(15,6),                -- 上限
    typical_value DECIMAL(15,6),             -- 典型值
    unit VARCHAR(20),                         -- 单位
    test_method VARCHAR(100),                 -- 测试方法
    criticality ENUM('critical','major','minor'), -- 重要性
    is_datalog_required BOOLEAN DEFAULT TRUE, -- 是否需要数据记录
    is_spc_monitored BOOLEAN DEFAULT FALSE,   -- 是否SPC监控
    created_by VARCHAR(20),
    created_at TIMESTAMP,
    
    INDEX idx_test_dev (test_dev_id),
    INDEX idx_parameter_code (parameter_code),
    INDEX idx_test_number (test_number)
);
```

## 3. NPI项目管理引擎

### 3.1 项目生命周期管理
```java
@Service
public class NPIProjectManagementService {
    
    @Autowired
    private NPIProjectRepository projectRepository;
    
    @Autowired
    private StageGateRepository stageGateRepository;
    
    @Autowired
    private TaskManagementService taskService;
    
    @Autowired
    private RiskManagementService riskService;
    
    /**
     * 创建NPI项目
     */
    public NPIProject createProject(NPIProjectCreateRequest request) {
        // 1. 验证项目可行性
        validateProjectFeasibility(request);
        
        // 2. 生成项目编码
        String projectCode = generateProjectCode(request.getCustomerId());
        
        // 3. 创建项目主记录
        NPIProject project = new NPIProject();
        project.setProjectId(IdGenerator.generateId());
        project.setProjectCode(projectCode);
        project.setProjectName(request.getProjectName());
        project.setCustomerId(request.getCustomerId());
        project.setCustomerName(request.getCustomerName());
        project.setProductFamily(request.getProductFamily());
        project.setPackageType(request.getPackageType());
        project.setProjectPriority(request.getProjectPriority());
        project.setBusinessType(request.getBusinessType());
        project.setProjectManagerId(request.getProjectManagerId());
        project.setProjectStatus(NPIProjectStatus.EVALUATION);
        project.setPlannedStartDate(request.getPlannedStartDate());
        project.setPlannedEndDate(request.getPlannedEndDate());
        project.setRiskLevel(RiskLevel.MEDIUM);
        project.setCreatedBy(getCurrentUserId());
        
        project = projectRepository.save(project);
        
        // 4. 创建阶段门控
        createStageGates(project);
        
        // 5. 创建项目任务结构
        createProjectTasks(project);
        
        // 6. 初始化风险登记册
        riskService.initializeRiskRegister(project.getProjectId());
        
        // 7. 发送项目启动通知
        publishProjectCreatedEvent(project);
        
        return project;
    }
    
    /**
     * 创建标准阶段门控
     */
    private void createStageGates(NPIProject project) {
        List<StageGateTemplate> templates = getStageGateTemplates(project.getPackageType());
        
        for (StageGateTemplate template : templates) {
            NPIStageGate gate = new NPIStageGate();
            gate.setGateId(IdGenerator.generateId());
            gate.setProjectId(project.getProjectId());
            gate.setStageName(template.getStageName());
            gate.setStageSequence(template.getSequence());
            gate.setGateName(template.getGateName());
            gate.setGateCriteria(template.getCriteria());
            gate.setDeliverables(template.getDeliverables());
            gate.setExitCriteria(template.getExitCriteria());
            gate.setGateStatus(GateStatus.NOT_STARTED);
            
            // 计算计划日期
            gate.setPlannedDate(calculateGatePlannedDate(project, template));
            
            stageGateRepository.save(gate);
        }
    }
    
    /**
     * 阶段门控评审
     */
    public GateDecisionResult conductGateReview(String gateId, GateReviewRequest request) {
        NPIStageGate gate = stageGateRepository.findById(gateId)
            .orElseThrow(() -> new EntityNotFoundException("门控不存在"));
        
        NPIProject project = projectRepository.findById(gate.getProjectId())
            .orElseThrow(() -> new EntityNotFoundException("项目不存在"));
        
        // 1. 检查门控前置条件
        List<String> unmetCriteria = checkGateCriteria(gate, project);
        
        if (!unmetCriteria.isEmpty() && request.getDecision() != GateDecision.WAIVED) {
            return GateDecisionResult.builder()
                .success(false)
                .unmetCriteria(unmetCriteria)
                .message("门控标准未满足")
                .build();
        }
        
        // 2. 记录门控决策
        gate.setGateStatus(mapDecisionToStatus(request.getDecision()));
        gate.setDecisionMaker(getCurrentUserId());
        gate.setDecisionDate(LocalDate.now());
        gate.setDecisionComments(request.getComments());
        gate.setActualDate(LocalDate.now());
        
        stageGateRepository.save(gate);
        
        // 3. 更新项目状态
        if (request.getDecision() == GateDecision.PASS) {
            updateProjectStageStatus(project, gate.getStageName());
            
            // 启动下一阶段任务
            activateNextStageTasks(project.getProjectId(), gate.getStageName());
        }
        
        // 4. 发送门控决策通知
        publishGateDecisionEvent(gate, request.getDecision());
        
        return GateDecisionResult.builder()
            .success(true)
            .message("门控评审完成")
            .build();
    }
    
    /**
     * 项目风险评估
     */
    public ProjectRiskAssessment assessProjectRisk(String projectId) {
        NPIProject project = getProjectById(projectId);
        
        ProjectRiskAssessment assessment = new ProjectRiskAssessment();
        assessment.setProjectId(projectId);
        assessment.setAssessmentDate(LocalDateTime.now());
        
        // 1. 技术风险评估
        TechnicalRisk techRisk = assessTechnicalRisk(project);
        assessment.setTechnicalRisk(techRisk);
        
        // 2. 进度风险评估
        ScheduleRisk scheduleRisk = assessScheduleRisk(project);
        assessment.setScheduleRisk(scheduleRisk);
        
        // 3. 资源风险评估
        ResourceRisk resourceRisk = assessResourceRisk(project);
        assessment.setResourceRisk(resourceRisk);
        
        // 4. 质量风险评估
        QualityRisk qualityRisk = assessQualityRisk(project);
        assessment.setQualityRisk(qualityRisk);
        
        // 5. 综合风险等级计算
        RiskLevel overallRisk = calculateOverallRisk(techRisk, scheduleRisk, resourceRisk, qualityRisk);
        assessment.setOverallRiskLevel(overallRisk);
        
        // 6. 生成风险缓解建议
        List<RiskMitigationAction> mitigationActions = generateMitigationActions(assessment);
        assessment.setMitigationActions(mitigationActions);
        
        // 7. 更新项目风险等级
        project.setRiskLevel(overallRisk);
        projectRepository.save(project);
        
        return assessment;
    }
    
    /**
     * NPI成本跟踪
     */
    public NPICostTracking trackProjectCosts(String projectId) {
        NPIProject project = getProjectById(projectId);
        
        NPICostTracking costTracking = new NPICostTracking();
        costTracking.setProjectId(projectId);
        costTracking.setTrackingDate(LocalDateTime.now());
        
        // 1. 人力成本计算
        BigDecimal laborCost = calculateLaborCost(projectId);
        costTracking.setLaborCost(laborCost);
        
        // 2. 材料成本计算
        BigDecimal materialCost = calculateMaterialCost(projectId);
        costTracking.setMaterialCost(materialCost);
        
        // 3. 设备使用成本
        BigDecimal equipmentCost = calculateEquipmentCost(projectId);
        costTracking.setEquipmentCost(equipmentCost);
        
        // 4. 外协成本
        BigDecimal outsourceCost = calculateOutsourceCost(projectId);
        costTracking.setOutsourceCost(outsourceCost);
        
        // 5. 其他费用
        BigDecimal otherCost = calculateOtherCost(projectId);
        costTracking.setOtherCost(otherCost);
        
        // 6. 总成本计算
        BigDecimal totalCost = laborCost.add(materialCost)
            .add(equipmentCost).add(outsourceCost).add(otherCost);
        costTracking.setTotalCost(totalCost);
        
        // 7. 预算偏差分析
        BigDecimal budgetVariance = project.getEstimatedNpiCost().subtract(totalCost);
        costTracking.setBudgetVariance(budgetVariance);
        costTracking.setBudgetVariancePercentage(
            budgetVariance.divide(project.getEstimatedNpiCost(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
        );
        
        // 8. 成本趋势预测
        BigDecimal forecastedTotalCost = forecastProjectCost(project, totalCost);
        costTracking.setForecastedTotalCost(forecastedTotalCost);
        
        return costTracking;
    }
}
```

### 3.2 并行工程管理
```java
@Service
public class ConcurrentEngineeringService {
    
    @Autowired
    private TaskDependencyService dependencyService;
    
    @Autowired
    private ResourceAllocationService resourceService;
    
    /**
     * 并行任务调度优化
     */
    public ParallelScheduleResult optimizeParallelExecution(String projectId) {
        List<NPITask> allTasks = taskService.getProjectTasks(projectId);
        
        // 1. 构建任务依赖图
        TaskDependencyGraph dependencyGraph = buildDependencyGraph(allTasks);
        
        // 2. 识别可并行执行的任务
        List<TaskCluster> parallelClusters = identifyParallelTasks(dependencyGraph);
        
        // 3. 资源约束分析
        ResourceConstraint resourceConstraint = analyzeResourceConstraints(allTasks);
        
        // 4. 关键路径分析
        CriticalPath criticalPath = calculateCriticalPath(dependencyGraph);
        
        // 5. 优化调度方案
        ScheduleOptimizationResult optimization = optimizeSchedule(
            parallelClusters, resourceConstraint, criticalPath);
        
        // 6. 生成并行执行计划
        ParallelExecutionPlan executionPlan = generateExecutionPlan(optimization);
        
        // 7. 计算项目收益
        ProjectBenefit benefit = calculateParallelBenefit(allTasks, executionPlan);
        
        return ParallelScheduleResult.builder()
            .executionPlan(executionPlan)
            .criticalPath(criticalPath)
            .expectedTimeReduction(benefit.getTimeReduction())
            .expectedCostSaving(benefit.getCostSaving())
            .resourceUtilization(benefit.getResourceUtilization())
            .build();
    }
    
    /**
     * 跨部门协同管理
     */
    public CrossFunctionalCollaboration manageCrossFunctionalWork(String projectId) {
        NPIProject project = projectRepository.findById(projectId)
            .orElseThrow(() -> new EntityNotFoundException("项目不存在"));
        
        // 1. 识别跨部门依赖
        List<CrossDepartmentDependency> dependencies = identifyDepartmentDependencies(projectId);
        
        // 2. 建立协作机制
        CollaborationMechanism mechanism = establishCollaborationMechanism(dependencies);
        
        // 3. 设置协同里程碑
        List<CollaborationMilestone> milestones = defineCollaborationMilestones(project, dependencies);
        
        // 4. 信息共享机制
        InformationSharingPlan sharingPlan = createInformationSharingPlan(dependencies);
        
        // 5. 冲突解决机制
        ConflictResolutionProcess conflictProcess = defineConflictResolution(dependencies);
        
        return CrossFunctionalCollaboration.builder()
            .dependencies(dependencies)
            .mechanism(mechanism)
            .milestones(milestones)
            .sharingPlan(sharingPlan)
            .conflictProcess(conflictProcess)
            .build();
    }
    
    /**
     * 设计制造并行优化（DFM）
     */
    public DFMOptimizationResult optimizeDesignForManufacturing(String projectId) {
        NPIProject project = getProjectById(projectId);
        
        // 1. 获取产品设计规格
        NPIProductSpecs productSpecs = getProductSpecs(projectId);
        NPIPackageSpecs packageSpecs = getPackageSpecs(projectId);
        
        // 2. 制造能力分析
        ManufacturingCapability capability = analyzeManufacturingCapability(packageSpecs);
        
        // 3. DFM规则检查
        List<DFMViolation> violations = checkDFMRules(productSpecs, packageSpecs, capability);
        
        // 4. 成本影响分析
        CostImpactAnalysis costImpact = analyzeCostImpact(violations, project);
        
        // 5. 优化建议生成
        List<DFMRecommendation> recommendations = generateDFMRecommendations(
            violations, costImpact, capability);
        
        // 6. 实施优先级排序
        List<DFMRecommendation> prioritizedRecommendations = prioritizeDFMRecommendations(
            recommendations, project.getProjectPriority());
        
        return DFMOptimizationResult.builder()
            .violations(violations)
            .costImpact(costImpact)
            .recommendations(prioritizedRecommendations)
            .estimatedSavings(costImpact.getPotentialSavings())
            .implementationComplexity(assessImplementationComplexity(prioritizedRecommendations))
            .build();
    }
}
```

## 4. 知识库管理系统

### 4.1 NPI知识积累与复用
```java
@Service
public class NPIKnowledgeManagementService {
    
    @Autowired
    private KnowledgeRepository knowledgeRepository;
    
    @Autowired
    private LessonLearnedService lessonLearnedService;
    
    /**
     * NPI经验知识提取
     */
    public NPIKnowledge extractProjectKnowledge(String projectId) {
        NPIProject project = getCompletedProject(projectId);
        
        NPIKnowledge knowledge = new NPIKnowledge();
        knowledge.setKnowledgeId(IdGenerator.generateId());
        knowledge.setProjectId(projectId);
        knowledge.setKnowledgeType(KnowledgeType.PROJECT_EXPERIENCE);
        
        // 1. 技术知识提取
        TechnicalKnowledge techKnowledge = extractTechnicalKnowledge(project);
        knowledge.setTechnicalKnowledge(techKnowledge);
        
        // 2. 工艺知识提取
        ProcessKnowledge processKnowledge = extractProcessKnowledge(project);
        knowledge.setProcessKnowledge(processKnowledge);
        
        // 3. 质量知识提取
        QualityKnowledge qualityKnowledge = extractQualityKnowledge(project);
        knowledge.setQualityKnowledge(qualityKnowledge);
        
        // 4. 项目管理知识提取
        ProjectManagementKnowledge pmKnowledge = extractPMKnowledge(project);
        knowledge.setProjectManagementKnowledge(pmKnowledge);
        
        // 5. 经验教训总结
        List<LessonLearned> lessons = lessonLearnedService.extractLessonsLearned(project);
        knowledge.setLessonsLearned(lessons);
        
        // 6. 最佳实践识别
        List<BestPractice> bestPractices = identifyBestPractices(project);
        knowledge.setBestPractices(bestPractices);
        
        // 7. 知识标签化
        List<String> tags = generateKnowledgeTags(knowledge);
        knowledge.setTags(tags);
        
        return knowledgeRepository.save(knowledge);
    }
    
    /**
     * 智能知识推荐
     */
    public List<KnowledgeRecommendation> recommendRelevantKnowledge(String currentProjectId) {
        NPIProject currentProject = getProjectById(currentProjectId);
        
        // 1. 构建项目特征向量
        ProjectFeatureVector currentFeatures = buildProjectFeatures(currentProject);
        
        // 2. 检索相似历史项目
        List<NPIProject> similarProjects = findSimilarProjects(currentFeatures);
        
        // 3. 提取相关知识
        List<NPIKnowledge> relevantKnowledge = extractRelevantKnowledge(similarProjects);
        
        // 4. 计算知识相关性评分
        List<KnowledgeRecommendation> recommendations = new ArrayList<>();
        
        for (NPIKnowledge knowledge : relevantKnowledge) {
            double relevanceScore = calculateRelevanceScore(currentFeatures, knowledge);
            double confidenceScore = calculateConfidenceScore(knowledge);
            double applicabilityScore = calculateApplicabilityScore(currentProject, knowledge);
            
            if (relevanceScore > 0.6) { // 相关性阈值
                KnowledgeRecommendation recommendation = new KnowledgeRecommendation();
                recommendation.setKnowledge(knowledge);
                recommendation.setRelevanceScore(relevanceScore);
                recommendation.setConfidenceScore(confidenceScore);
                recommendation.setApplicabilityScore(applicabilityScore);
                recommendation.setOverallScore(
                    (relevanceScore * 0.4 + confidenceScore * 0.3 + applicabilityScore * 0.3));
                
                recommendations.add(recommendation);
            }
        }
        
        // 5. 按综合评分排序
        recommendations.sort((a, b) -> Double.compare(b.getOverallScore(), a.getOverallScore()));
        
        return recommendations.subList(0, Math.min(10, recommendations.size()));
    }
    
    /**
     * 决策支持系统
     */
    public DecisionSupportResult provideDecisionSupport(DecisionSupportRequest request) {
        // 1. 问题分类
        DecisionCategory category = classifyDecision(request);
        
        // 2. 历史案例检索
        List<HistoricalCase> historicalCases = retrieveHistoricalCases(category, request.getContext());
        
        // 3. 专家规则应用
        List<ExpertRule> applicableRules = getApplicableExpertRules(category);
        
        // 4. 数据分析
        AnalyticalInsight analyticalResult = performAnalyticalAnalysis(request.getContext());
        
        // 5. 决策选项生成
        List<DecisionOption> options = generateDecisionOptions(
            historicalCases, applicableRules, analyticalResult);
        
        // 6. 风险评估
        for (DecisionOption option : options) {
            RiskAssessment risk = assessDecisionRisk(option, request.getContext());
            option.setRiskAssessment(risk);
        }
        
        // 7. 推荐决策
        DecisionOption recommendedOption = selectRecommendedOption(options);
        
        return DecisionSupportResult.builder()
            .category(category)
            .historicalCases(historicalCases)
            .options(options)
            .recommendedOption(recommendedOption)
            .confidence(calculateConfidence(recommendedOption))
            .reasoning(generateReasoning(recommendedOption, historicalCases))
            .build();
    }
}
```

## 5. 客户协作门户

### 5.1 客户沟通管理
```java
@Service
public class CustomerCollaborationService {
    
    @Autowired
    private CustomerPortalService portalService;
    
    @Autowired
    private DocumentSharingService documentService;
    
    /**
     * 客户项目门户管理
     */
    public CustomerProjectPortal createCustomerPortal(String projectId, String customerId) {
        NPIProject project = getProjectById(projectId);
        
        // 1. 创建客户门户
        CustomerProjectPortal portal = new CustomerProjectPortal();
        portal.setPortalId(IdGenerator.generateId());
        portal.setProjectId(projectId);
        portal.setCustomerId(customerId);
        portal.setPortalStatus(PortalStatus.ACTIVE);
        
        // 2. 配置门户权限
        PortalPermissions permissions = configurePortalPermissions(project, customerId);
        portal.setPermissions(permissions);
        
        // 3. 创建仪表板
        CustomerDashboard dashboard = createCustomerDashboard(project);
        portal.setDashboard(dashboard);
        
        // 4. 设置通知规则
        List<NotificationRule> notificationRules = createNotificationRules(project, customerId);
        portal.setNotificationRules(notificationRules);
        
        // 5. 初始化文档共享区
        DocumentSharingArea sharingArea = documentService.createSharingArea(projectId, customerId);
        portal.setSharingArea(sharingArea);
        
        return portalService.save(portal);
    }
    
    /**
     * 客户反馈管理
     */
    public CustomerFeedback processFeedback(CustomerFeedbackRequest request) {
        // 1. 创建反馈记录
        CustomerFeedback feedback = new CustomerFeedback();
        feedback.setFeedbackId(IdGenerator.generateId());
        feedback.setProjectId(request.getProjectId());
        feedback.setCustomerId(request.getCustomerId());
        feedback.setFeedbackType(request.getFeedbackType());
        feedback.setFeedbackCategory(request.getCategory());
        feedback.setFeedbackContent(request.getContent());
        feedback.setPriority(request.getPriority());
        feedback.setSubmittedAt(LocalDateTime.now());
        feedback.setStatus(FeedbackStatus.NEW);
        
        // 2. 智能分类和路由
        FeedbackClassification classification = classifyFeedback(feedback);
        feedback.setClassification(classification);
        
        String assignedDepartment = routeFeedbackToDepartment(classification);
        String assignedUser = assignFeedbackToUser(assignedDepartment, classification);
        
        feedback.setAssignedDepartment(assignedDepartment);
        feedback.setAssignedTo(assignedUser);
        
        // 3. 自动响应时间设置
        LocalDateTime responseDeadline = calculateResponseDeadline(feedback.getPriority());
        feedback.setResponseDeadline(responseDeadline);
        
        // 4. 保存反馈
        feedback = customerFeedbackRepository.save(feedback);
        
        // 5. 发送通知
        sendFeedbackNotification(feedback);
        
        // 6. 创建处理任务
        createFeedbackProcessingTask(feedback);
        
        return feedback;
    }
    
    /**
     * 客户认证管理
     */
    public CustomerApprovalResult manageCustomerApproval(String projectId, ApprovalStage stage) {
        NPIProject project = getProjectById(projectId);
        
        CustomerApprovalResult result = new CustomerApprovalResult();
        result.setProjectId(projectId);
        result.setApprovalStage(stage);
        result.setRequestDate(LocalDateTime.now());
        
        switch (stage) {
            case DESIGN_APPROVAL:
                result = processDesignApproval(project);
                break;
            case SAMPLE_APPROVAL:
                result = processSampleApproval(project);
                break;
            case QUAL_APPROVAL:
                result = processQualificationApproval(project);
                break;
            case PRODUCTION_APPROVAL:
                result = processProductionApproval(project);
                break;
        }
        
        // 更新项目状态
        updateProjectStatusBasedOnApproval(project, result);
        
        // 发送通知
        notifyApprovalResult(project, result);
        
        return result;
    }
}
```

---

*NPI新产品导入管理模块为IC封测CIM系统提供了完整的新产品导入生命周期管理，涵盖从产品评估到量产转移的全过程，确保NPI项目的成功实施和知识积累*